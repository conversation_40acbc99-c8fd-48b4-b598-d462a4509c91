package xy.server.basic.i18n;

import com.xunyue.common.i18n.BaseResultErrorCode;

/**
 * <AUTHOR>
 * @Date 2023-07-14 17:48
 * <p>
 * 错误代码枚举
 * </p>
 **/
public enum ResultErrorCode implements BaseResultErrorCode {

    NO_REPETITION_CURRENCY("同一租户下币种名称不能重复", 1000000001),
    NO_DOMESTIC_CURRENCY("同一租户下只能有一个本币", 1000000002),
    NO_REPETITION_DATE_CROSS("同租户下同币种有效开始日期与有效结束日期不能有互相交叉", 1000000003),
    NO_REPETITION_FOLDER("同一租户下文件夹名称不能重复", 1000000004),
    NO_REPETITION_FILE("同一文件夹下文件名称不能重复", 1000000005),
    NO_REPETITION_INVOICETYPE("同一租户下发票类型不能重复", 1000000006),
    NO_REPETITION_CROSSINVOICETYPETAXRATE("同一租户下同一发票类型税率有效开始日期与有效结束日期不能有互相交叉", 1000000007),
    NO_REPETITION_PRODUCTIONPROCESSESTYPE("同一租户下工序类型名称不能重复", 1000000008),
    NO_REPETITION_PRODUCTIONPROCESSESTYPE_DEPT("同一部门下工序类型名称不能重复", **********),
    NO_REPETITION_PROCESSESTYPEPARAMETERITEMS("同一租户下工序类型参数项名称不能重复", 1000000009),
    NO_REPETITION_WORKING("正在被使用不可停用", 1000000010),
    NO_CURRENCY_USE_DELETE("有币种被使用不能删除", 1000000011),
    NO_CURRENCY_EXCHANGE_RATE_USE_DELETE("有汇率被使用不能删除", 1000000012),
    NO_REPETITION_PARAMETER_TYPE("同一租户下设备参数类型不能重复", 1000000013),
    NO_REPETITION_PARAMETER_ITEMS("同一租户下设备参数项不能重复", 1000000016),
    NO_REPETITION_EQUIPMENT_TYPE("同一租户下设备类型不能重复", 1000000017),
    NO_REPETITION_EQUIPMENT_MODEL("同一租户下模型不能重复", 1000000018),
    NO_REPETITION_EQUIPMENT_NAME("同一租户下设备名称和设备编码不能重复", 1000000019),
    NO_CURRENCY_PARAMETER_TYPE_USE_DELETE("设备参数类型有被使用不能删除", 1000000020),
    NO_CURRENCY_PARAMETER_ITEMS_USE_DELETE("设备参数项有被使用不能删除", 1000000021),
    NO_CURRENCY_EQUIPMENT_TYPE_USE_DELETE("设备类型有被使用不能删除", 1000000022),
    NO_CURRENCY_EQUIPMENT_MODEL_USE_DELETE("设备模型有被使用不能删除", 1000000023),
    NO_CURRENCY_EQUIPMENT_USE_DELETE("设备有被使用不能删除", 1000000024),
    NO_REPETITION_UNIT("同一租户下单位名称不能重复", 1000000025),
    NO_REPETITION_DELIVERY_TYPE("同一租户下送货类型名称不能重复", 1000000026),
    NO_REPETITION_SETTLEMENT_TYPE("同一租户下结算类型名称不能重复", 1000000027),
    NO_REPETITION_LABEL("同一租户下标签名称不能重复", 1000000028),
    NO_REPETITION_FSC_DELARATION("同一租户下FSC声明不能重复", 1000000029),
    NO_REPETITION_ADMINISTRATIVE_AREA("同一租户下行政区域不能重复", 1000000030),
    NO_REPETITION_IS_USED("有数据已被使用不能进行删除，只能进行停用", 1000000031),
    NO_REPETITION_NAME_NOT_NULL("名称不能为空", 1000000032),
    NO_REPETITION_DATA_NOT_EXIST("数据不存在", 1000000033),
    NO_REPETITION_BASIC_DICTIONARY_KEY("基础字典关键字不能重复", 1000000034),
    NO_REPETITION_BASIC_DICTIONARY_NAME("基础字典名字不能重复", 1000000035),
    NO_REPETITION_BASIC_WORK_OVERTIME("加班名称不能重复", 1000000036),
    NO_REPETITION_BASIC_WORK_OVERLAP("选择的时间，已有加班安排，请选择其他时间", 1000000037),
    NO_REPETITION_BASIC_TIME_EXPIRES("时间已经过期", 1000000038),
    NO_REPETITION_STARTDATE_IS_NOT_OVER_ENDADTE("加班开始时间不能大于加班结束时间", 1000000039),
    NO_REPETITION_BASIC_CONTAINER("集装箱名称不能重复", 1000000040),
    NO_REPETITION_BASIC_NO_OVERTIME("未加班", 1000000041),
    NO_REPETITION_BASIC_BE_WORKING_OVERTIME("加班中", 1000000042),
    NO_REPETITION_BASIC_HAVE_WORKED_OVERTIME("已加班", 1000000043),
    NO_REPETITION_WAREHOUSE_SPACE_NAME("同一租户下仓储空间名称不能重复", 1000000044),
    NO_USER_DELETE("正在使用不可以删除", 1000000045),
    NO_ISSUE_DELETE("正在发布不可以删除", 1000000046),
    NO_CHANGE_USING("正在使用不可以编辑", 1000000047),
    NO_EFFECT_ISSUE("公告已失效，不可发布", 1000000048),
    NO_REPETITION_INTIME("该数据未在发布期内", 1000000049),
    NO_REPETITION_HOLIDAYS_NAME("法定节假日名称不能重复", 1000000050),
    DATES_CANNOT_BE_CROSSED("节假日时间不能交叉", 1000000051),
    ORDER_NUMBER_RULE_VALUE_IS_EMPTY("规则类型为【自定义字符】时，单号规则字符值不能为空", 1000000052),
    ORDER_NUMBER_RULE_FORMAT_IS_EMPTY("规则类型为【时间格式】时，规则格式不能为空", 1000000053),
    ORDER_NUMBER_RULE_LENGTH_OR_STEP_IS_EMPTY("规则类型为【流水号】时，流水号长度和步数不能为空", 1000000054),
    ORDER_NUMBER_RULE_NAME_REPEAT("规则名称不能重复", 1000000055),
    ORDER_NUMBER_RULE_IS_USED("规则已被使用，不能删除", 1000000056),
    DAILY_NAME_REPEAT("同一租户下日模板名称不能相同", 1000000057),
    WEEKLY_NAME_REPEAT("同一租户下日模板名称不能相同", 1000000058),
    CONNECT_MINIO_FAIL("连接MinIO服务器失败", 1000000059),
    NEXT_MOTH_MESSAGE_ALREADY_EXISTS("下个月数据已经存在请删除后再同步", 1000000060),
    SHEBEISHIFOUKEYIKAISHI("当前设备还有任务未完成，不能开始新任务", 1000000061),
    UNABLE_TO_HAND_OVER("当前任务还未发生接收与领料,不能交出！", *********),
    ON_THE_COMPUTER("当前设备已被上机", **********),
    CURRENT_USER_HAS_LOGGED_IN("当前用户已上机", **********),
    BELOW_THE_CURRENT_DEVICE("当前设备已下机", **********),
    MaterialsNotYetShippedOut("亲物料还未出库哦", **********),
    KEY_NOT_NULL("没有选择需要的物料", **********),
    WAREHOUSE_NOT_ENOUGH("操作失败！仓存不足！", **********),
    PREVIOUS_PROCESS_NOT_SCHEDULED("上工序还未生产", **********),
    NO_REPETITION_SYSTEM_ACCOUNT("同一租户下账号不能重复", **********),
    NO_REPETITION_GONGSHI_ACCOUNT("同一租户下自能一个公司", **********),
    QUANTITY_OR_AMOUNT_IS_ZERO("下标${0}的数量或金额为0", **********),
    CURRENCY_IS_NOT_EXIST("币种不存在", **********),
    NO_REPETITION_ROLE_NAME("同一租户下角色名称不能重复", **********),
    NO_REPETITION_STAFF("${0}已经分配账号，无需再分配", **********),
    CURRENCY_IS_LOCAL("该币种是本币，不可删除，需要先取消是否本币才可删除", **********),
    THIS_DEVICE_IS_MISSING_WEEKLY_TEMPLATE("此设备缺少周模板:", **********),
    USER_NOT_FOUNT("${0}账号不存在,请联系管理员！", 50001),
    USER_IS_DISABLE("${0}账号已经停用，请联系管理员！", 50002),
    PASSWORD_MISTAKE("账号密码错误！", 50008),
    GRANT_TYPE_MISTAKE("授权方式错误",50003),
    REFRESH_TOKEN_MISTAKE("token错误",50004),

    ALREADY_IN_A_TERMINATED_STATE("已经是异常终止状态，不能再次终止",50006),
    DUPLICATE_GROUP_NAME("组名重复了！！",50007),
    ;


    private String msg;
    private int code;

    ResultErrorCode(String msg, int code) {
        this.msg = msg;
        this.code = code;

    }

    @Override
    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }


}
