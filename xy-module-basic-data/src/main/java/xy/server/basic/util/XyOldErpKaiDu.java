package xy.server.basic.util;

import xy.server.basic.entity.model.ro.CalcKaiDuRO;

/**
 * <AUTHOR>
 * @Date 2023-08-18 21:14
 **/
public class XyOldErpKaiDu {


    public double calc(CalcKaiDuRO ro) {

        double SrcLen = ro.getmSrcLen();
        double SrcWidth = ro.getmSrcWidth();
        double DstWidth = ro.getmDstWidth();
        double DstLen = ro.getmDstLen();
        double mKaiShu = 0;

        if (ro.getCutPaperTypeID().equals(0)) {
            double iKaidu1 = GetKaiDu(Src<PERSON><PERSON>, SrcWidth, DstLen, DstWidth);
            double iKaidu2 = GetKaiDu(Src<PERSON><PERSON>, SrcWidth, DstWidth, DstLen);
            double iKaidu3 = GetKaiDu(SrcWidth, Src<PERSON><PERSON>, <PERSON>st<PERSON><PERSON>, DstWidth);
            double iKaidu4 = GetKaiDu(<PERSON><PERSON><PERSON><PERSON><PERSON>, Src<PERSON><PERSON>, DstWidth, DstLen);
            mKai<PERSON>hu = Math.max(Math.max(iKaidu1, iKaidu2), Math.max(iKaidu3, iKaidu4));
        } else if (ro.getCutPaperTypeID().equals(1)) {
            mKaiShu = trunc(SrcLen / DstLen) * trunc(SrcWidth / DstWidth);
        } else if (ro.getCutPaperTypeID().equals(2)) {
            mKaiShu = trunc(SrcWidth / DstLen) * trunc(SrcLen / DstWidth);
        }

//        System.out.println("=======原ERP算法最大开度数量:" + mKaiShu);
//        System.out.println("------------------------------------------------------------------------------------");
        return mKaiShu;

    }

    public double GetKaiDu(double mSrcLen, double mSrcWidth, double mDstLen, double mDstWidth) {
        double mSrcWidth1 = 0;
        double mKaiShu = 0;
        double Result = 0;
        for (int i = 1; i < trunc(mSrcWidth / mDstWidth); i++) {
            mSrcWidth1 = mSrcWidth - (i * mDstWidth);
            mKaiShu = trunc(mSrcLen / mDstLen) * i + Math.max(trunc(mSrcWidth1 / mDstLen) * trunc(mSrcLen / mDstWidth),
                    trunc(mSrcLen / mDstLen) * trunc(mSrcWidth1 / mDstWidth));

            if (Result < mKaiShu) {
                Result = mKaiShu;
            }
        }
        return Result;
    }

    private double trunc(double num) {
        String numStr = Double.toString(num);
        return Double.parseDouble(numStr.substring(0, numStr.indexOf(".")));
    }
}
