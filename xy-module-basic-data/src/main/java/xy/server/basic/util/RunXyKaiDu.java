package xy.server.basic.util;

import com.alibaba.fastjson.JSON;
import com.xunyue.common.kaidu.XyKaiDu;

/**
 * <AUTHOR>
 * @Date 2023-08-16 21:23
 **/
public class RunXyKaiDu {
    public static void main(String[] args) {
        // 记录算法开始时间
        long startTime = System.currentTimeMillis();

        XyKaiDu xy = new XyKaiDu(991, 934, 184, 257, 0);

        XyKaiDu.BoxInBox boxInBox = xy.xyKaiDuCalc();

        System.out.println(JSON.toJSONString(boxInBox));
        System.out.println("共拼了" + boxInBox.getMaxKaiDu() + "个");


        // 输出相关信息
        System.out.println("------------------------------------------------------------------------------------");
        System.out.println("求解用时:" + (System.currentTimeMillis() - startTime) / 1000.0 + " s");
    }
}
