package xy.server.basic.util;

import cn.hutool.core.util.RandomUtil;
import com.xunyue.common.kaidu.XyKaiDu;
import xy.server.basic.entity.model.ro.CalcKaiDuRO;

import java.io.FileWriter;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2023-08-18 21:58
 * <p>
 * 测试开度算法是否正确
 * </p>
 **/
public class ViolenceTestKaiDu {
    public static void main(String[] args) throws IOException {
        Double minWidth = 500.00;
        Double maxWidth = 1194.00;
        Double minLen = 500.00;
        Double maxLen = 1194.00;

        Double minDstLen = 100.00;
        Double maxDstLen = 500.00;
        Double minDstWidth = 100.00;
        Double maxDstWidth = 500.00;


        FileWriter writer = null;
        try {
            writer = new FileWriter("kaiDu.txt");

            double d = 0;
            for (int i = 0; i < 1000; i++) {

                double srcWidth = RandomUtil.randomInt(minWidth.intValue(), maxWidth.intValue());
                double srcLen = RandomUtil.randomInt(minLen.intValue(), maxLen.intValue());

                double dstLen = RandomUtil.randomInt(minDstLen.intValue(), maxDstLen.intValue());
                double dstWidth = RandomUtil.randomInt(minDstWidth.intValue(), maxDstWidth.intValue());

                CalcKaiDuRO ro = new CalcKaiDuRO();
                ro.setmDstLen(dstLen);
                ro.setmDstWidth(dstWidth);
                ro.setmSrcLen(srcLen);
                ro.setmSrcWidth(srcWidth);
                ro.setCutPaperTypeID(0);

                XyKaiDu xy = new XyKaiDu(ro.getmSrcLen(), ro.getmSrcWidth(), ro.getmDstLen(), ro.getmDstWidth(), ro.getCutPaperTypeID());
                XyKaiDu.BoxInBox boxInBox = xy.xyKaiDuCalc();

                XyOldErpKaiDu xyOldErpKaiDu = new XyOldErpKaiDu();

                boxInBox.setOldErpKaiDu(xyOldErpKaiDu.calc(ro));

                if (boxInBox.getMaxKaiDu() != boxInBox.getOldErpKaiDu() && boxInBox.getOldErpKaiDu() != 0) {
                    writer.write("原纸规格：" + srcLen + " * " + srcWidth + "，产出规格：" + dstLen + " * " + dstWidth);
                    writer.write("（开度）当前算法：" + boxInBox.getMaxKaiDu() + "，旧算法：" + boxInBox.getOldErpKaiDu() + "，不一致！！\n");
                    d += 1;
                }
//                else {
//                    writer.write("\n");
//                }
            }
            writer.write("不一致个数：" + d + "\n");
            writer.write("一致率：" + (1 - d / 1000) * 100 + "%");
            writer.flush();
        } catch (Exception e) {
            e.printStackTrace();
            if (writer != null) {
                writer.close();
            }
        }
    }
}
