package xy.server.basic.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 语言QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "LangListQO对象", description = "语言")
public class LangListQO {

    @ApiModelProperty(value = "")
    private String localeLangGuid;

    @ApiModelProperty(value = "语言")
    private String localeLangName;

    @ApiModelProperty(value = "语言标识")
    private String localeLangValue;

    @ApiModelProperty(value = "使用状态")
    private Boolean state;

    @ApiModelProperty(value = "是否默认语言")
    private Boolean isDefault;

}