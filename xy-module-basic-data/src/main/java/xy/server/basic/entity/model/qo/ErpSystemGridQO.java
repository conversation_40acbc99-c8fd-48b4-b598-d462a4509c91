package xy.server.basic.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 自定义表格VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpSystemGridQO对象", description = "自定义表格")
public class ErpSystemGridQO {

    @ApiModelProperty(value = "列配置主id")
    private String systemGridGuid;

    @ApiModelProperty(value = "列配置明细id")
    private List<String> systemGridColumnGuid;
}