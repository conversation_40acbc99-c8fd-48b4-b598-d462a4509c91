package xy.server.basic.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 语言字典QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "LangDictionaryQO对象", description = "语言字典")
public class LangDictionaryQO {

    @ApiModelProperty(value = "租户")
    private String tenantGuid;

    @ApiModelProperty(value = "平台")
    private String langDictionaryPlat;

    @ApiModelProperty(value = "语言标识")
    private String langDictionaryLocale;

}