package xy.server.basic.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 公司表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpSystemMgtCompanyQO对象", description = "公司表")
public class ErpSystemMgtCompanyQO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String companyGuid;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "企业税号")
    private String enterpriseTaxIdentificationNumber;

    @ApiModelProperty(value = "详细注册地址")
    private String registeredAddress;

    @ApiModelProperty(value = "详细注册地址行政区域")
    private String registeredAddressAdministrativeAreaGuid;

    @ApiModelProperty(value = "详细经营地址")
    private String operatingAddress;

    @ApiModelProperty(value = "详细经营地址行政区域")
    private String operatingAddressAdministrativeAreaGuid;

    @ApiModelProperty(value = "开户银行")
    private String openingBank;

    @ApiModelProperty(value = "电话")
    private String telephone;

    @ApiModelProperty(value = "银行帐号")
    private String bankAccount;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表user_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表user_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "顺序号")
    private Integer serialNumbe;

}