package xy.server.basic.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 币种QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtCurrencyQO对象", description = "币种")
public class ErpBasicMgtCurrencyQO {

    @ApiModelProperty(value = "关键字")
    private String keyword;

    private List<String> keywords;

    @ApiModelProperty(value = "")
    private String tenantGuid;

    @ApiModelProperty(value = "")
    private String currencyGuid;

    @ApiModelProperty(value = "")
    private List<String> currencyGuids;

    //    @NotNull(message = "币种名称不能为空")
    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    @ApiModelProperty(value = "币种符号")
    private String currencySign;

    @ApiModelProperty(value = "是否本币(只允许一个)0-否 1-是")
    private Boolean isLocalCurrency;

    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "已被使用(0未使用，1已使用)")
    private Boolean isUsed;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "false查全部，true只查启用的")
    private Boolean type;
}
