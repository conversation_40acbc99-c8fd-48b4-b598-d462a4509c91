package xy.server.basic.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 文件管理表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtFileQO对象", description = "文件管理表")
public class ErpBasicMgtFileQO {

    @ApiModelProperty(value = "")
    private String fileGuid;

    //    @NotNull(message = "文件名称不能为空")
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "ERP_FileMGT_Folder GUID")
    private String folderGuid;

    @ApiModelProperty(value = "文件路径")
    private String path;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人GUID(取用户表UserGUID)")
    private String creatorGuid;

    @ApiModelProperty(value = "")
    private String keyword;

    private List<String> keywords;
}
