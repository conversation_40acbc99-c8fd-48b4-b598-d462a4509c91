package xy.server.basic.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 集装箱管理 QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtContainerQO对象", description = "集装箱管理")
public class ErpBasicMgtContainerQO {
    @ApiModelProperty(value = "租户Guid")
    private String tenantGuid;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "启用")
    private Boolean type;

    @ApiModelProperty(value = "关键字列表")
    private List<String> keywords;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

}
