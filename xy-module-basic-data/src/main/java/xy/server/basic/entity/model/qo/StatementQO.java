package xy.server.basic.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "StatementQO对象", description = "报表")
public class StatementQO {

    @ApiModelProperty(name = "表单来源类型值id")
    private String formSourceTypeValueGuid;

    @ApiModelProperty(name = "报表名称")
    private String reportName;

    @ApiModelProperty(name = "报表作者")
    private String reportAuthor;

    @ApiModelProperty(name = "报表编码")
    private String reportCode;

    @ApiModelProperty(name = "报表类型")
    private String reportType;

    @ApiModelProperty(name = "倒序-DESC")
    private String order = "DESC";
    @ApiModelProperty(name = "排序字段-update_time")
    private String sort = "update_time";
    private Integer offset;
    private List<Long> ids;
}
