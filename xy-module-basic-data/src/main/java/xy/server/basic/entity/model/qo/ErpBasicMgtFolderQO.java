package xy.server.basic.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 文件夹管理表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtFolderQO对象", description = "文件夹管理表")
public class ErpBasicMgtFolderQO {

    @ApiModelProperty(value = "租户GUID")
    private String tenantGuid;

    @ApiModelProperty(value = "")
    private String folderGuid;

    @ApiModelProperty(value = "")
    private String keyword;

    private List<String> keywords;

    @NotNull(message = "文件夹名称不能为空")
    @ApiModelProperty(value = "文件夹名称")
    private String folderName;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @NotNull(message = "顺序号(每一个层级有对应的顺序号)不能为空")
    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点GUID")
    private String parentClassificationGuid;

    @NotNull(message = "已被使用(0未使用，1已使用)不能为空")
    @ApiModelProperty(value = "已被使用(0未使用，1已使用)")
    private Boolean isUsed;

    @NotNull(message = "状态(0停用，1启用)不能为空")
    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "创建人GUID(取用户表UserGUID)")
    private String creatorGuid;

    @NotNull(message = "创建人不能为空")
    @ApiModelProperty(value = "创建人")
    private String creator;

    @NotNull(message = "创建日期不能为空")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人GUID(取用户表UserGUID)")
    private String lastUpdaterGuid;

    @NotNull(message = "最后修改人不能为空")
    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @NotNull(message = "最后修改日期不能为空")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @NotNull(message = "逻辑删除不能为空")
    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

    @ApiModelProperty(value = "false查全部，true只查启用的")
    private Boolean type;

}
