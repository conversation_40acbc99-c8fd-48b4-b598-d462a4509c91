package xy.server.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/8/7
 * @description
 **/
@Getter
@AllArgsConstructor
public enum ExceptionHandleSourceEnum {

    BUSINESS_ORDER("1", "订单", "order_state", "order_data_guid", "erp_business_mgt_order_data"),
    WORK_ORDER("2", "工单", "current_status", "workorder_guid", "erp_production_mgt_workorder"),
    INVENTORY_ORDER("3", "库单", "inventory_receipt_state", "inventory_receipt_detail_guid", "erp_inventory_mgt_inventory_receipt_detail"),

    ;


    private final String code;

    private final String desc;

    private final String statusField;

    private final String idField;

    private final String tableName;
}
