package xy.server.basic.minio;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import io.minio.MinioClient;
import lombok.Data;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import xy.server.basic.entity.model.vo.MinioClientConfigVO;

import javax.annotation.Resource;

@Data
@Component
public class MinIoConfig {

    @Resource
    private MinioClientConfigVO minioClientConfigVO;

    /**
     * 注入minio 客户端
     *
     * @return
     */
    @Bean
    public MinioClient minioClient() {


        // 如果没有配置内网端点则使用外网端点
        if (ObjUtil.isEmpty(minioClientConfigVO.getInternalEndPoint())) {
            minioClientConfigVO.setInternalEndPoint(minioClientConfigVO.getEndPoint());
        } else {
            // 如果有值拼上http，和原来的逻辑保持一致
            minioClientConfigVO.setInternalEndPoint(StrUtil.format("http://{}" , minioClientConfigVO.getInternalEndPoint()));
        }

        return MinioClient.builder()
                .endpoint(minioClientConfigVO.getInternalEndPoint().concat(":").concat(minioClientConfigVO.getInternalPort().toString()))
                .credentials(minioClientConfigVO.getAccessKey(), minioClientConfigVO.getSecretKey())
                .build();
    }

}
