package xy.server.basic.minio;

import io.minio.BucketExistsArgs;
import io.minio.MinioClient;
import io.minio.messages.Bucket;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import xy.server.basic.entity.model.vo.MinioClientConfigVO;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;


@Slf4j
@Component
@Configuration
public class MinioClientConfig {

    private static MinioClient minioClient;
    @Autowired
    private MinIoConfig storageProperty;
    @Resource
    private MinioClientConfigVO minioClientConfigVO;

    /**
     * @return io.minio.MinioClient
     * @description: 获取minioClient
     * @date 2021/6/22 16:55
     */
    public static MinioClient getMinioClient() {
        return minioClient;
    }

    /**
     * 判断 bucket是否存在
     *
     * @param bucketName: 桶名
     * @return: boolean
     * @date : 2020/8/16 20:53
     */
    @SneakyThrows(Exception.class)
    public static boolean bucketExists(String bucketName) {
        return minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
    }


    /**
     * 获取全部bucket
     *
     * @param :
     * @return: java.util.List<io.minio.messages.Bucket>
     * @date : 2020/8/16 23:28
     */
    @SneakyThrows(Exception.class)
    public static List<Bucket> getAllBuckets() {
        return minioClient.listBuckets();
    }

    /**
     * 初始化minio配置
     *
     * @param :
     * @return: void
     * @date : 2020/8/16 20:56
     */
    @PostConstruct
    public void init() {
        try {
            minioClient = MinioClient.builder()
                    .endpoint(minioClientConfigVO.getEndPoint().concat(":").concat(minioClientConfigVO.getPort().toString()))
                    .credentials(minioClientConfigVO.getAccessKey(), minioClientConfigVO.getSecretKey())
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("初始化minio配置异常: 【{}】", e.fillInStackTrace());
        }
    }

}