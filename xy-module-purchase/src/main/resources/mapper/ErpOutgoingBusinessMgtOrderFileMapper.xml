<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpOutgoingBusinessMgtOrderFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpOutgoingBusinessMgtOrderFileVO">
        <id column="order_file_guid" property="orderFileGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="order_guid" property="orderGuid"/>
        <result column="file_guid" property="fileGuid"/>
        <result column="file_type" property="fileType"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="to_json" property="toJson"  typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select *
        from erp_business_mgt_order_file
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select *
        from erp_business_mgt_order_file
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select *
        from erp_business_mgt_order_file
    </select>
</mapper>
