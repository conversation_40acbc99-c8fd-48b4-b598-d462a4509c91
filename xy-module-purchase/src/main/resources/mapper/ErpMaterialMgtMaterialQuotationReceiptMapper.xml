<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpMaterialMgtMaterialQuotationReceiptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationReceiptVO">
        <id column="material_quotation_receipt_guid" property="materialQuotationReceiptGuid"/>
        <result column="material_quotation_receipt_type" property="materialQuotationReceiptType"/>
        <result column="material_quotation_receipt_numbers" property="materialQuotationReceiptNumbers"/>
        <result column="receipt_date" property="receiptDate"/>
        <result column="description" property="description"/>
        <result column="customer_or_supplier_guid" property="customerOrSupplierGuid"/>
        <result column="customerOrSupplierName" property="customerOrSupplierName"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_date" property="auditDate"/>
        <result column="effective_date" property="effectiveDate"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="material_quotation_receipt_properties" property="materialQuotationReceiptProperties"/>
        <result column="effectiveStatus" property="effectiveStatus"/>
        <result column="print_times" property="printTimes"/>
        <result column="real_effective_date" property="realEffectiveDate"/>
<!--        <collection property="detailList" ofType="xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationReceiptDetailVO"-->
<!--                    column="{materialQuotationReceiptGuid=material_quotation_receipt_guid}" select="xy.server.purchase.mapper.ErpMaterialMgtMaterialQuotationReceiptDetailMapper.getList"/>-->
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select emmmqr.*,coalesce(esms.supplier_short_name,ecmc.customer_short_name) as customerOrSupplierName,
        ,
        case
        when emmmqr.effective_date >= now() then true
        else false end as effectiveStatus
        from erp_material_mgt_material_quotation_receipt emmmqr
        left join(select emmmqrd.material_quotation_receipt_guid,emmm.material_code,emmmqrd.material_classification_guid,
                         emmm.material_name,
                      emmmc.material_classification_name    from
        erp_material_mgt_material_quotation_receipt_detail
        emmmqrd
        left join erp_material_mgt_material emmm on emmmqrd.material_guid=emmm.material_guid
        left join erp_material_mgt_material_classification emmmc on emmmc.material_classification_guid=emmmqrd.material_classification_guid
        where emmmqrd.deleted=false
        ) as a on a.material_quotation_receipt_guid=emmmqr.material_quotation_receipt_guid
        left join erp_supplier_mgt_supplier esms on supplier_guid=emmmqr.customer_or_supplier_guid
        left join erp_customer_mgt_customer ecmc on customer_guid=emmmqr.customer_or_supplier_guid
        where emmmqr.deleted=false and
        emmmqr.material_quotation_receipt_properties=#{model.materialQuotationReceiptProperties}
        <if test="model.startTime !=null and model.endTime !=null">
            and (emmmqr.receipt_date &gt;= #{model.startTime}
            and emmmqr.receipt_date &lt;= #{model.endTime})
        </if>
        <if test="model.keyword != null and model.keyword != '' ">
            and (emmmqr.material_quotation_receipt_numbers like CONCAT('%',#{model.keyword},'%')
            or esms.supplier_short_name like CONCAT('%',#{model.keyword},'%')
            or ecmc.customer_short_name like CONCAT('%',#{model.keyword},'%')
            or a.material_name like CONCAT('%',#{model.keyword},'%')
            or a.material_code like CONCAT('%',#{model.keyword},'%')
            or a.material_classification_name like CONCAT('%',#{model.keyword},'%')
            )
        </if>
        <if test="model.materialClassificationGuidS != null and model.materialClassificationGuidS.size() != 0 ">
            and a.material_classification_guid in
            <foreach item="item" collection="model.materialClassificationGuidS" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by emmmqr.create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select emmmqr.*,coalesce(esms.supplier_short_name,ecmc.customer_short_name) as customerOrSupplierName,
        case
        when emmmqr.effective_date >= now() then true
        else false end as effectiveStatus
        from erp_material_mgt_material_quotation_receipt emmmqr
        left join(select emmmqrd.material_quotation_receipt_guid,emmm.material_code,emmmqrd.material_classification_guid,
                         emmm.material_name,
        emmmc.material_classification_name    from
        erp_material_mgt_material_quotation_receipt_detail
        emmmqrd
        left join erp_material_mgt_material emmm on emmmqrd.material_guid=emmm.material_guid
        left join erp_material_mgt_material_classification emmmc on emmmc.material_classification_guid=emmmqrd.material_classification_guid
        where emmmqrd.deleted=false
        ) as a on a.material_quotation_receipt_guid=emmmqr.material_quotation_receipt_guid
        left join erp_supplier_mgt_supplier esms on supplier_guid=emmmqr.customer_or_supplier_guid
        left join erp_customer_mgt_customer ecmc on customer_guid=emmmqr.customer_or_supplier_guid
        where emmmqr.deleted=false and
        emmmqr.material_quotation_receipt_properties=#{model.materialQuotationReceiptProperties}
        <if test="model.startTime !=null and model.endTime !=null">
            and (emmmqr.receipt_date &gt;= #{model.startTime}
            and emmmqr.receipt_date &lt;= #{model.endTime})
        </if>
        <if test="model.keyword != null and model.keyword != '' ">
            and (emmmqr.material_quotation_receipt_numbers like CONCAT('%',#{model.keyword},'%')
            or esms.supplier_short_name like CONCAT('%',#{model.keyword},'%')
            or ecmc.customer_short_name like CONCAT('%',#{model.keyword},'%')
            or a.material_name like CONCAT('%',#{model.keyword},'%')
            or a.material_code like CONCAT('%',#{model.keyword},'%')
            or a.material_classification_name like CONCAT('%',#{model.keyword},'%')
            )
        </if>
        <if test="model.materialClassificationGuidS != null and model.materialClassificationGuidS.size() != 0 ">
            and a.material_classification_guid in
            <foreach item="item" collection="model.materialClassificationGuidS" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by emmmqr.create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select emmmqr.*,coalesce(esms.supplier_short_name,ecmc.customer_short_name) as customerOrSupplierName
                ,
               case
                   when emmmqr.effective_date >= now() then true
                   else false end as effectiveStatus
        from erp_material_mgt_material_quotation_receipt emmmqr
                 left join erp_supplier_mgt_supplier esms on supplier_guid=emmmqr.customer_or_supplier_guid
                 left join erp_customer_mgt_customer ecmc on customer_guid=emmmqr.customer_or_supplier_guid
        where emmmqr.material_quotation_receipt_guid = #{guid}
    </select>
    <select id="getSupplierGuid" resultType="java.lang.String">
        select esms.supplier_guid  from erp_supplier_mgt_supplier esms
        where esms.supplier_code =#{customerOrSupplierGuid}
        limit 1
    </select>
    <select id="getCustomerGuid" resultType="java.lang.String">
        select ecmc.customer_guid
        from erp_customer_mgt_customer ecmc
        where ecmc.customer_code =#{customerOrSupplierGuid}
        limit 1
    </select>
    <select id="getExperienceExternal"
            resultType="xy.server.purchase.entity.model.vo.ErpMaterialQuotationVO">
        select
        epmeppe.experience_production_process_external_guid,emmmqrd.quotation_unit_price_including_tax,
        epmepp.material_guid as materialBomGuid,
        emmm.material_name,emmm.material_code,
        emmmqrd.material_guid
        from
        erp_material_mgt_material_quotation_receipt emmmqr
        left join erp_material_mgt_material_quotation_receipt_detail emmmqrd
        on emmmqr.material_quotation_receipt_guid = emmmqrd.material_quotation_receipt_guid
        left join erp_material_mgt_material emmm on emmm.material_guid=emmmqrd.material_guid
        left join erp_production_mgt_experience_production_process epmepp on emmmqrd.material_guid=epmepp.material_guid
        and epmepp.experience_production_process_properties=14
        left join erp_production_mgt_experience_production_process_external epmeppe on
        epmepp.experience_production_process_guid
        =epmeppe.experience_production_process_guid and emmmqr.customer_or_supplier_guid=epmeppe.customer_guid
        where
        emmmqr.effective_status = true and
        emmmqr.material_quotation_receipt_properties=1
        and epmepp.experience_production_process_guid is not null
        <if test="list != null and list.size() != 0 ">
            and emmmqr.material_quotation_receipt_guid in
            <foreach item="item" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <resultMap id="EoResultMap" type="xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationVO">
        <id column="material_quotation_receipt_detail_guid" property="materialQuotationReceiptDetailGuid"/>
        <result column="material_quotation_receipt_type" property="materialQuotationReceiptType"/>
        <result column="material_quotation_receipt_numbers" property="materialQuotationReceiptNumbers"/>
        <result column="receipt_date" property="receiptDate"/>
        <result column="description" property="description"/>
        <result column="customer_or_supplier_guid" property="customerOrSupplierGuid"/>
        <result column="customerOrSupplierName" property="customerOrSupplierName"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_date" property="auditDate"/>
        <result column="effective_date" property="effectiveDate"/>
        <result column="material_quotation_receipt_properties" property="materialQuotationReceiptProperties"/>
        <result column="effective_status" property="effectiveStatus"/>
        <result column="print_times" property="printTimes"/>
        <result column="real_effective_date" property="realEffectiveDate"/>
        <result column="material_quotation_receipt_guid" property="materialQuotationReceiptGuid"/>
        <result column="material_classification_guid" property="materialClassificationGuid"/>
        <result column="material_classification_name" property="materialClassificationName"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="min_quantity" property="minQuantity"/>
        <result column="max_quantity" property="maxQuantity"/>
        <result column="quotation_unit_price_including_tax" property="quotationUnitPriceIncludingTax"/>
        <result column="adjustment_magnitude" property="adjustmentMagnitude"/>
        <result column="external_material_code" property="externalMaterialCode"/>
        <result column="customerOrSupplierCode" property="customerOrSupplierCode"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <association property="materialObj" javaType="xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO"
                     column="{guid=material_guid}" select="xy.server.material.mapper.ErpMaterialMgtMaterialMapper.getDataByGuid"/>
    </resultMap>
    <select id="findDetailPage" resultMap="EoResultMap">
        select emmmqrd.material_quotation_receipt_guid,
        emmmqrd.material_quotation_receipt_detail_guid,
        coalesce(esms.supplier_short_name, ecmc.customer_short_name) as customerOrSupplierName,
        coalesce(esms.supplier_code, ecmc.customer_code) as customerOrSupplierCode,
        emmmqr.material_quotation_receipt_type,
        emmmqr.material_quotation_receipt_numbers,
        emmmqr.receipt_date,
        emmmqrd.description,
        emmmqr.customer_or_supplier_guid,
        emmmqr.create_date,
        emmmqr.creator,
        emmmqr.last_updater,
        emmmqr.last_updater,
        emmmqr.process_instance_id,
        emmmqr.audit_status,
        emmmqr.audit_date,
        emmmqr.effective_date,
        emmmqr.effective_status,
        emmmqr.material_quotation_receipt_properties,
        emmmqr.print_times,
        emmmqr.real_effective_date,
        emmmqrd.material_classification_guid,
        emmmc.material_classification_name,
        emmmqrd.material_guid,
        emmmqrd.max_quantity,
        emmmqrd.min_quantity,
        emmmqrd.quotation_unit_price_including_tax,
        emmmqrd.adjustment_magnitude,
        emmmqrd.external_material_code
        from erp_material_mgt_material_quotation_receipt_detail emmmqrd
        left join erp_material_mgt_material_quotation_receipt emmmqr
        on emmmqr.material_quotation_receipt_guid = emmmqrd.material_quotation_receipt_guid
        left join erp_supplier_mgt_supplier esms on supplier_guid = emmmqr.customer_or_supplier_guid
        left join erp_customer_mgt_customer ecmc on customer_guid = emmmqr.customer_or_supplier_guid
        left join erp_material_mgt_material_classification emmmc
        on emmmc.material_classification_guid = emmmqrd.material_classification_guid
        left join erp_material_mgt_material emmm on emmm.material_guid=emmmqrd.material_guid
        where emmmqr.deleted=false and
        emmmqr.material_quotation_receipt_properties=#{model.materialQuotationReceiptProperties}
        and emmmqrd.material_classification_guid is not null
        <if test="model.startTime !=null and model.endTime !=null">
            and (emmmqr.receipt_date &gt;= #{model.startTime}
            and emmmqr.receipt_date &lt;= #{model.endTime})
        </if>
        <if test="model.customerName != null and model.customerName != '' ">
            and ecmc.customer_short_name like CONCAT('%',#{model.customerName},'%')
        </if>
        <if test="model.materialQuotationReceiptNumbers != null and model.materialQuotationReceiptNumbers != '' ">
            and emmmqr.material_quotation_receipt_numbers like CONCAT('%',#{model.materialQuotationReceiptNumbers},'%')
        </if>
        <if test="model.customerCode != null and model.customerCode != '' ">
            and ecmc.customer_code like CONCAT('%',#{model.customerCode},'%')
        </if>
        <if test="model.materialName != null and model.materialName != '' ">
            and emmm.material_name like CONCAT('%',#{model.materialName},'%')
        </if>
        <if test="model.materialCode != null and model.materialCode != '' ">
            and emmm.material_code like CONCAT('%',#{model.materialCode},'%')
        </if>
        <if test="model.externalMaterialCode != null and model.externalMaterialCode != '' ">
            and emmmqrd.external_material_code like CONCAT('%',#{model.externalMaterialCode},'%')
        </if>
        <if test="model.customerOrSupplierGuid != null and model.customerOrSupplierGuid != '' ">
            and emmmqr.customer_or_supplier_guid=#{model.customerOrSupplierGuid}
        </if>
        <if test="model.materialCodeS != null and model.materialCodeS.size() != 0 ">
            and emmm.material_code in
            <foreach item="item" collection="model.materialCodeS" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getNewMoney"
            resultType="xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationReceiptDetailVO">
        select coalesce(emmmqrd.external_material_code,'') as
        external_material_code,coalesce(emmmqrd.quotation_unit_price_including_tax,0) as
        quotation_unit_price_including_tax,emmmc.material_classification_guid
        from erp_material_mgt_material_quotation_receipt_detail emmmqrd
        left join erp_material_mgt_material_quotation_receipt emmmqr
        on emmmqrd.material_quotation_receipt_guid = emmmqr.material_quotation_receipt_guid
        left join erp_material_mgt_material_classification emmmc on emmmqrd.material_classification_guid=emmmc.material_classification_guid
        where emmmqr.customer_or_supplier_guid=#{qo.customerOrSupplierGuid}
        <if test="qo.materialClassificationGuidS != null and qo.materialClassificationGuidS.size() != 0 ">
            and emmmqrd.material_classification_guid in
            <foreach item="item" collection="qo.materialClassificationGuidS" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by emmmqr.effective_date desc
    </select>
</mapper>
