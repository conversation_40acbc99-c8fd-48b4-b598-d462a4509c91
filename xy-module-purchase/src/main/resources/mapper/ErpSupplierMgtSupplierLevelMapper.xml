<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpSupplierMgtSupplierLevelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierLevelVO">
        <id column="supplier_level_guid" property="supplierLevelGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="supplier_level_name" property="supplierLevelName"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="parent_classification_guid" property="parentClassificationGuid"/>
        <result column="state" property="state"/>
        <result column="is_used" property="isUsed"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="type" property="type"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <collection property="children" ofType="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierLevelVO"
                    column="{parentClassificationGuid=supplier_level_guid,type=type}" select="getByParentClassificationGuid"/>
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select *,${model.type} as type
        from erp_supplier_mgt_supplier_level where parent_classification_guid = ''
        <if test="model.keywords != null and model.keywords.size() != 0">
            and
            <foreach collection="model.keywords" item="item" index="index" open="(" separator="or" close=")" >
                supplier_level_name ILIKE CONCAT('%',#{item},'%')
                or description ILIKE CONCAT('%',#{item},'%')
            </foreach>
        </if>
        and (state=true or state=#{model.type})
        order by serial_number asc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select *,${model.type} as type
        from erp_supplier_mgt_supplier_level where parent_classification_guid = ''
        <if test="model.keywords != null and model.keywords.size() != 0">
            and
            <foreach collection="model.keywords" item="item" index="index" open="(" separator="or" close=")" >
                supplier_level_name ILIKE CONCAT('%',#{item},'%')
                or description ILIKE CONCAT('%',#{item},'%')
            </foreach>
        </if>
        and (state=true or state=#{model.type})
        order by serial_number asc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select *,state as type
        from erp_supplier_mgt_supplier_level
        where supplier_level_guid = #{guid}
    </select>

    <select id="getByParentClassificationGuid" resultMap="VoResultMap">
        select *,${type} AS type
        from erp_supplier_mgt_supplier_level
        where parent_classification_guid = #{parentClassificationGuid}
          and (state=true or state=#{type})
        order by serial_number asc
    </select>

    <select id="getListByParentClassificationGuid" resultType="xy.server.purchase.entity.ErpSupplierMgtSupplierLevel">
        with RECURSIVE cte as (
            SELECT a.*
            FROM erp_supplier_mgt_supplier_level a
            WHERE a.supplier_level_guid = #{parentClassificationGuid}
            union ALL
            SELECT b.*
            FROM erp_supplier_mgt_supplier_level b
                     inner join cte c on c.supplier_level_guid = b.parent_classification_guid
        )
        SELECT *
        from cte;
    </select>

    <select id="getDataByGuids" resultMap="VoResultMap">
        select *,state as type
        from erp_supplier_mgt_supplier_level
        where supplier_level_guid in
        <foreach collection="guids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
