<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpOutgoingDataProcessMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpOutgoingDataProcessVO">
        <id column="workorder_data_process_guid" property="workorderDataProcessGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="workorder_guid" property="workorderGuid"/>
        <result column="modulus" property="modulus"/>
        <result column="loss_quantity" property="lossQuantity"/>
        <result column="adjust_loss_quantity" property="adjustLossQuantity"/>
        <result column="output_quantity" property="outputQuantity"/>
        <result column="output_unit_guid" property="outputUnitGuid"/>
        <result column="output_specifications_length" property="outputSpecificationsLength"/>
        <result column="output_specifications_width" property="outputSpecificationsWidth"/>
        <result column="output_specifications_height" property="outputSpecificationsHeight"/>
        <result column="is_production_scheduling" property="isProductionScheduling"/>
        <result column="production_scheduling_state" property="productionSchedulingState"/>
        <result column="production_scheduled_quantity" property="productionScheduledQuantity"/>
        <result column="is_outsource" property="isOutsource"/>
        <result column="outsource_requisition_state" property="outsourceRequisitionState"/>
        <result column="outsource_state" property="outsourceState"/>
        <result column="produced_state" property="producedState"/>
        <result column="produced_quantity" property="producedQuantity"/>
        <result column="outsourced_requisition_quantity" property="outsourcedRequisitionQuantity"/>
        <result column="outsourced_quantity" property="outsourcedQuantity"/>
        <result column="equipment_model_guid" property="equipmentModelGuid"/>
        <result column="mold_model_guid" property="moldModelGuid"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="source_guid" property="sourceGuid"/>
        <result column="source_value" property="sourceValue"/>
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select *
        from erp_production_mgt_workorder_data_process
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select *
        from erp_production_mgt_workorder_data_process
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select *
        from erp_production_mgt_workorder_data_process
        where workorder_data_process_guid = #{guid}
    </select>
    <select id="selectBySon"
            resultType="xy.server.purchase.entity.model.vo.WorkOrderProcessAndOutgoingApplicationVO">
        select epmw1.quantity sonOutgoingQuantity,epmwdp.workorder_data_process_guid workOrderProcessGuid
        from
        erp_production_mgt_workorder epmw1
        left join erp_production_mgt_workorder epmw on epmw1.parent_classification_guid=epmw.workorder_guid
        left join erp_production_mgt_workorder_data_process epmwdp on epmw.source_guid=epmwdp.workorder_data_process_guid
        where epmw1.workorder_guid in
        <foreach collection="collect1" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="getByWorkOrderGuid" resultType="xy.server.purchase.entity.ErpOutgoingDataProcess">
        select * from erp_production_mgt_workorder
        where workorder_guid=#{sourceGuid}
    </select>
    <select id="getSpecifications"
            resultType="xy.server.purchase.entity.model.ro.ErpOutgoingApplicationFormRO">
        WITH sales_data as (select emmst.specification_type,
                                   emmsv.specification_value,
                                   a.workorder_data_process_guid
                            from (select epmw3.material_guid, epmwdp.workorder_data_process_guid
                                  from erp_production_mgt_workorder_data_process epmwdp
                                           left join erp_production_mgt_workorder epmw
                                                     on epmw.workorder_guid = epmwdp.workorder_guid
                                           left join erp_production_mgt_workorder epmw2
                                                     on (epmw2.serial_number = epmw.serial_number - 1 and
                                                         epmw.workorder_properties =
                                                         epmw2.workorder_properties)
                                           left join erp_production_mgt_workorder epmw3
                                                     on epmw2.workorder_guid = epmw3.parent_classification_guid
                                  where epmw3.material_guid is not null
                                    and epmw3.deleted = false
                                    and epmwdp.workorder_data_process_guid = #{workorderDataProcessGuid}
                                  order by epmw3.create_date asc
                                  limit 1) as a
                                     left join erp_material_mgt_specification_value emmsv
                                               on emmsv.material_guid = a.material_guid
                                     left join erp_material_mgt_specification_type emmst
                                               on emmst.specification_type_guid = emmsv.specification_type_guid),
             data1 as (select coalesce(specification_value, '1') as specificationsLength,
                              workorder_data_process_guid        as a
                       from sales_data
                       where specification_type = '长'),
             data2 as (select coalesce(specification_value, '1') as specificationsWidth,
                              workorder_data_process_guid        as b
                       from sales_data
                       where specification_type = '宽'),
             data3 as (select coalesce(specification_value, '1') as specificationsHeight,
                              workorder_data_process_guid        as c
                       from sales_data
                       where specification_type = '高')
        select coalesce(data1.specificationsLength, '1') as specificationsLength,
               coalesce(data2.specificationsWidth, '1')  as specificationsWidth,
               coalesce(data3.specificationsHeight, '1') as specificationsHeight
        from data1
                 left join data2 on data1.a = data2.b
                 left join data3 on c = data2.b
    </select>

    <select id="getSpecificationsBatch" resultType="xy.server.purchase.entity.model.ro.ErpOutgoingApplicationFormRO">
        SELECT
            epmw3.material_guid,
            epmwdp.workorder_data_process_guid
        FROM
            erp_production_mgt_workorder_data_process epmwdp
                LEFT JOIN erp_production_mgt_workorder epmw ON epmw.workorder_guid = epmwdp.workorder_guid
                -- 相同工单 相同上级 表示同一个生产部件
                LEFT JOIN erp_production_mgt_workorder epmw2 ON epmw.workorder_number = epmw2.workorder_number and epmw.parent_classification_guid =epmw2.parent_classification_guid
        LEFT JOIN erp_production_mgt_workorder epmw3 ON epmw2.workorder_guid = epmw3.parent_classification_guid
        WHERE
            epmw3.material_guid IS NOT NULL
          and epmw.workorder_properties = 2
          -- 如果是第一个工序外发暂时找不到
          and epmw2.serial_number = epmw.serial_number - 1
          and epmw2.workorder_properties = 2
          AND epmwdp.workorder_data_process_guid IN
            <foreach collection="workorderDataProcessGuids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>
</mapper>
