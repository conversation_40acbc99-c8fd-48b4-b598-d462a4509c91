<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpPurchaseMgtPurchaseOrderMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseOrderVO">
        <id column="order_guid" property="orderGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="order_number" property="orderNumber"/>
        <result column="order_demand_guid" property="orderDemandGuid"/>
        <result column="receipt_date" property="receiptDate"/>
        <result column="order_type" property="orderType"/>
        <result column="total_expenses_including_tax" property="totalExpensesIncludingTax"/>
        <result column="total_expenses_without_tax" property="totalExpensesWithoutTax"/>
        <result column="local_currency_total_expenses" property="localCurrencyTotalExpenses"/>
        <result column="settlement_total_expenses" property="settlementTotalExpenses"/>
        <result column="total_volume" property="totalVolume"/>
        <result column="total_weight" property="totalWeight"/>
        <result column="decimal_method" property="decimalMethod"/>
        <result column="settlement_unit_price_keep_decimal_place" property="settlementUnitPriceKeepDecimalPlace"/>
        <result column="settlement_total_amount_keep_decimal_place" property="settlementTotalAmountKeepDecimalPlace"/>
        <result column="invoice_type_tax_rate_guid" property="invoiceTypeTaxRateGuid"/>
        <result column="currency_exchange_rate_guid" property="currencyExchangeRateGuid"/>
        <result column="is_urgent" property="isUrgent"/>
        <result column="print_times" property="printTimes"/>
        <result column="customer_or_supplier_guid" property="customerOrSupplierGuid"/>
        <result column="contact_name" property="contactName"/>
        <result column="mobilephone" property="mobilephone"/>
        <result column="settlement_customer_or_supplier_guid" property="settlementCustomerOrSupplierGuid"/>
        <result column="delivery_customer_or_supplier_guid" property="deliveryCustomerOrSupplierGuid"/>
        <result column="delivery_type_guid" property="deliveryTypeGuid"/>
        <result column="settlement_type_guid" property="settlementTypeGuid"/>
        <result column="source_guid" property="sourceGuid"/>
        <result column="source_value" property="sourceValue"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_date" property="auditDate"/>
        <result column="exchange_rate" property="exchangeRate"/>
        <result column="tax_rate" property="taxRate"/>
        <result column="currency_name" property="currencyName"/>
        <result column="is_local_currency" property="isLocalCurrency"/>
        <!--&lt;!&ndash;到货情况&ndash;&gt;-->
        <!--<association property="arrivalState" javaType="java.lang.String" column="{orderGuid=order_guid}" select="getArrivalState"/>-->
        <!--&lt;!&ndash;供应商Obj&ndash;&gt;-->
        <!--<association property="supplierObj" javaType="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierVO"-->
        <!--             column="{guid=customer_or_supplier_guid}" select="xy.server.purchase.mapper.ErpSupplierMgtSupplierMapper.getDataByGuid"/>-->
        <!--&lt;!&ndash;结算供应商Obj&ndash;&gt;-->
        <!--        <association property="settlementSupplierObj" javaType="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierVO"-->
        <!--                     column="{guid=settlement_customer_or_supplier_guid}" select="xy.server.purchase.mapper.ErpSupplierMgtSupplierMapper.getDataByGuid"/>-->
        <!--采购订单明细列表-->
        <!--<collection property="detailList" ofType="xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseOrderDataVO"-->
        <!--            column="{orderGuid=order_guid}" select="xy.server.purchase.mapper.ErpPurchaseMgtPurchaseOrderDataMapper.findListByOrderGuid"/>-->
        <!--采购订单文件列表-->
        <!--<collection property="fileList" ofType="xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseOrderFileVO"-->
        <!--            column="{orderGuid=order_guid}" select="xy.server.purchase.mapper.ErpPurchaseMgtPurchaseOrderFileMapper.findListByOrderGuid"/>-->
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select ebmo.*,
               case
                   when ebmo.currency_exchange_rate_guid = '1' then '1'
                   else ebmcer.exchange_rate end as exchange_rate
                ,
               case
                   when ebmo.invoice_type_tax_rate_guid = '1' then 0
                   else ebmittr.tax_rate end     as tax_rate,
               ebmc2.currency_name,
               ebmc2.is_local_currency,
               (
                   select CASE
                              WHEN arrivalState.all_count = arrivalState.un_count THEN '0'
                              WHEN arrivalState.all_count = arrivalState.full_count + arrivalState.terminate_count
                                  THEN '2'
                              ELSE '1'
                              END AS arrival_state
                   from (
                            SELECT count(1)                                                 AS all_count,
                                   sum(CASE WHEN completion_status = '0' THEN 1 ELSE 0 END) AS un_count,
                                   sum(CASE WHEN completion_status = '1' THEN 1 ELSE 0 END) AS partial_count,
                                   sum(CASE WHEN completion_status = '2' THEN 1 ELSE 0 END) AS full_count,
                                   sum(CASE WHEN completion_status = '3' THEN 1 ELSE 0 END) AS terminate_count
                            FROM erp_business_mgt_order_data
                            WHERE order_guid = ebmo.order_guid
                        ) arrivalState
               )                                 as arrival_state
        from erp_business_mgt_order ebmo
        <!--        LEFT JOIN erp_business_mgt_order_data ebmod ON ebmod.order_guid = ebmo.order_guid-->
        <!--        LEFT JOIN erp_material_mgt_material emmm ON emmm.material_guid = ebmod.material_guid-->
        left join erp_basic_mgt_currency_exchange_rate ebmcer on
            ebmcer.currency_exchange_rate_guid = ebmo.currency_exchange_rate_guid
        left join erp_supplier_mgt_supplier ebmc on ebmo.settlement_customer_or_supplier_guid = ebmc.supplier_guid
        left join erp_basic_mgt_currency ebmc2 on ebmc2.currency_guid = ebmc.default_currency_guid
        left join erp_supplier_mgt_supplier ebmc1 on ebmo.customer_or_supplier_guid = ebmc1.supplier_guid
        left join erp_basic_mgt_invoice_type_tax_rate ebmittr on
            ebmo.invoice_type_tax_rate_guid = ebmittr.invoice_type_tax_rate_guid
        <!--        LEFT JOIN  erp_business_mgt_order_data od on  od.parent_classification_guid =ebmod.order_data_guid-->
        <!--        LEFT JOIN erp_production_mgt_workorder wo ON od.source_guid = wo.workorder_guid-->
        <!--        LEFT JOIN erp_production_mgt_workorder pwo ON wo.parent_classification_guid = pwo.workorder_guid-->
        <!--        left join erp_cost_scheme ecs on ecs.cost_scheme_guid = od.cost_scheme_guid-->
        <!--        LEFT JOIN view_erp_data_source v ON wo.source_guid = v.source_guid-->
        -- 到货情况
        <!--        LEFT JOIN (-->
        <!--            SELECT-->
        <!--                order_guid,-->
        <!--                count(1) AS all_count,-->
        <!--                sum(CASE WHEN completion_status = '0' THEN 1 ELSE 0 END) AS un_count,-->
        <!--                sum(CASE WHEN completion_status = '1' THEN 1 ELSE 0 END) AS partial_count,-->
        <!--                sum(CASE WHEN completion_status = '2' THEN 1 ELSE 0 END) AS full_count,-->
        <!--                sum(CASE WHEN completion_status = '3' THEN 1 ELSE 0 END) AS terminate_count-->
        <!--            FROM-->
        <!--                erp_business_mgt_order_data-->
        <!--            GROUP BY-->
        <!--                order_guid-->
        <!--        ) arrivalState ON arrivalState.order_guid = ebmo.order_guid-->
        where ebmo.order_properties = ${@com.xunyue.common.enums.OrderPropertiesEnum@PURCHASE_ORDER.getCode()}
        <if test="model.orderNumber != null and model.orderNumber != ''">
            and ebmo.order_number ILIKE CONCAT('%', #{model.orderNumber}, '%')
        </if>
        <if test="model.orderNumberList != null and model.orderNumberList.size() > 0">
            and ebmo.order_number in
            <foreach collection="model.orderNumberList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.supplierName != null and model.supplierName != ''">
            and (ebmc1.supplier_code ILIKE CONCAT('%', #{model.supplierName}, '%')
                or ebmc1.supplier_short_name ILIKE CONCAT('%', #{model.supplierName}, '%')
                or ebmc1.supplier_full_name ILIKE CONCAT('%', #{model.supplierName}, '%'))
        </if>
        <if test="model.supplierGuid != null and model.supplierGuid != ''">
            and ebmc1.supplier_guid = #{model.supplierGuid}
        </if>
        <if test="model.materialName != null and model.materialName != ''">
            and ebmo.order_guid in (
                select ebmod.order_guid
                from erp_business_mgt_order_data ebmod
                         LEFT JOIN erp_material_mgt_material emmm ON emmm.material_guid = ebmod.material_guid
                where (emmm.material_code ILIKE CONCAT('%', #{model.materialName}, '%')
                    or emmm.material_name ILIKE CONCAT('%', #{model.materialName}, '%'))
                group by ebmod.order_guid
            )
        </if>
        <if test="model.purchaseWorkorderNumber != null and model.purchaseWorkorderNumber != ''">
            and ebmo.order_guid in (
                select ebmod.order_guid
                from erp_business_mgt_order_data ebmod
                         LEFT JOIN erp_business_mgt_order_data od
                                   on od.parent_classification_guid = ebmod.order_data_guid
                         LEFT JOIN erp_production_mgt_workorder wo ON od.source_guid = wo.workorder_guid
                where wo.workorder_number ILIKE CONCAT('%', #{model.purchaseWorkorderNumber}, '%')
                group by ebmod.order_guid
            )
        </if>
        <if test="model.sourceNumber != null and model.sourceNumber != ''">
            <!--            and  v.source_number ILIKE CONCAT('%',#{model.sourceNumber},'%')-->
            and ebmo.order_guid in (
                select ebmod.order_guid
                from erp_business_mgt_order_data ebmod
                         LEFT JOIN erp_business_mgt_order_data od
                                   on od.parent_classification_guid = ebmod.order_data_guid
                         LEFT JOIN erp_production_mgt_workorder wo ON od.source_guid = wo.workorder_guid
                         LEFT JOIN view_erp_data_source v ON wo.source_guid = v.source_guid
                where v.source_number ILIKE CONCAT('%', #{model.sourceNumber}, '%')
                group by ebmod.order_guid
            )
        </if>
        <if test="model.creator != null and model.creator != ''">
            and ebmo.creator ILIKE CONCAT('%', #{model.creator}, '%')
        </if>
        <if test="model.orderTypes != null and model.orderTypes.size() > 0">
            and ebmo.order_type in
            <foreach item="item" collection="model.orderTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.auditStatus != null and model.auditStatus.size() > 0">
            and ebmo.audit_status in
            <foreach item="item" collection="model.auditStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.processInstanceIds != null and model.processInstanceIds.size() != 0">
            and ebmo.process_instance_id in
            <foreach item="item" collection="model.processInstanceIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.startDate != null">
            and to_char(ebmo.receipt_date, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp, 'YYYY-MM-DD')
        </if>
        <if test="model.endDate != null">
            and to_char(ebmo.receipt_date, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp, 'YYYY-MM-DD')
        </if>
        <if test="model.materialType != null and model.materialType != ''">
            and ebmo.order_guid in (
                select ebmod.order_guid
                from erp_business_mgt_order_data ebmod
                         LEFT JOIN erp_material_mgt_material emmm ON emmm.material_guid = ebmod.material_guid
                where emmm.material_type = #{model.materialType}
                group by ebmod.order_guid
            )
        </if>
        <if test="model.orderDataGuid != null and model.orderDataGuid.size() != 0 ">
            and ebmo.order_guid in (
            select ebmod.order_guid
            from erp_business_mgt_order_data ebmod
            where
            ebmod.order_data_guid in
            <foreach item="item" collection="model.orderDataGuid" open="(" separator="," close=")">
                #{item}
            </foreach>
            group by ebmod.order_guid
            )
        </if>

        <if test="model.businessKeys != null and model.businessKeys.size() != 0">
            and ebmo.order_guid in
            <foreach item="item" collection="model.businessKeys" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by ebmo.receipt_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select ebmo.*,
               case
                   when ebmo.currency_exchange_rate_guid = '1' then '1'
                   else ebmcer.exchange_rate end as exchange_rate
                ,
               case
                   when ebmo.invoice_type_tax_rate_guid = '1' then 0
                   else ebmittr.tax_rate end     as tax_rate,
               ebmc2.currency_name,
               ebmc2.is_local_currency,
               CASE
                   WHEN arrivalState.all_count = arrivalState.un_count THEN '0'
                   WHEN arrivalState.all_count = arrivalState.full_count + arrivalState.terminate_count THEN '2'
                   ELSE '1'
                   END                           AS arrival_state
        from erp_business_mgt_order ebmo
                 LEFT JOIN erp_business_mgt_order_data ebmod ON ebmod.order_guid = ebmo.order_guid
                 LEFT JOIN erp_material_mgt_material emmm ON emmm.material_guid = ebmod.material_guid
                 left join erp_basic_mgt_currency_exchange_rate ebmcer on
            ebmcer.currency_exchange_rate_guid = ebmo.currency_exchange_rate_guid
                 left join erp_supplier_mgt_supplier ebmc
                           on ebmo.settlement_customer_or_supplier_guid = ebmc.supplier_guid
                 left join erp_basic_mgt_currency ebmc2 on ebmc2.currency_guid = ebmc.default_currency_guid
                 left join erp_supplier_mgt_supplier ebmc1 on ebmo.customer_or_supplier_guid = ebmc1.supplier_guid
                 left join erp_basic_mgt_invoice_type_tax_rate ebmittr on
            ebmo.invoice_type_tax_rate_guid = ebmittr.invoice_type_tax_rate_guid
                 LEFT JOIN erp_business_mgt_order_data od on od.parent_classification_guid = ebmod.order_data_guid
                 LEFT JOIN erp_production_mgt_workorder wo ON od.source_guid = wo.workorder_guid
                 LEFT JOIN erp_production_mgt_workorder pwo ON wo.parent_classification_guid = pwo.workorder_guid
                 left join erp_cost_scheme ecs on ecs.cost_scheme_guid = od.cost_scheme_guid
            -- 到货情况
                 LEFT JOIN (
            SELECT order_guid,
                   count(1)                                                 AS all_count,
                   sum(CASE WHEN completion_status = '0' THEN 1 ELSE 0 END) AS un_count,
                   sum(CASE WHEN completion_status = '1' THEN 1 ELSE 0 END) AS partial_count,
                   sum(CASE WHEN completion_status = '2' THEN 1 ELSE 0 END) AS full_count,
                   sum(CASE WHEN completion_status = '3' THEN 1 ELSE 0 END) AS terminate_count
            FROM erp_business_mgt_order_data
            GROUP BY order_guid
        ) arrivalState ON arrivalState.order_guid = ebmo.order_guid
        where ebmo.order_properties = ${@com.xunyue.common.enums.OrderPropertiesEnum@PURCHASE_ORDER.getCode()}
        <if test="model.orderNumber != null and model.orderNumber != ''">
            and ebmo.order_number ILIKE CONCAT('%', #{model.orderNumber}, '%')
        </if>
        <if test="model.supplierName != null and model.supplierName != ''">
            and (ebmc1.supplier_code ILIKE CONCAT('%', #{model.supplierName}, '%')
                or ebmc1.supplier_short_name ILIKE CONCAT('%', #{model.supplierName}, '%')
                or ebmc1.supplier_full_name ILIKE CONCAT('%', #{model.supplierName}, '%')
                )
        </if>
        <if test="model.materialName != null and model.materialName != ''">
            and (emmm.material_code ILIKE CONCAT('%', #{model.materialName}, '%')
                or emmm.material_name ILIKE CONCAT('%', #{model.materialName}, '%'))
        </if>
        <if test="model.purchaseWorkorderNumber != null and model.purchaseWorkorderNumber != ''">
            and wo.workorder_number ILIKE CONCAT('%', #{model.purchaseWorkorderNumber}, '%')
        </if>
        <if test="model.sourceNumber != null and model.sourceNumber != ''">
            -- LEFT JOIN view_erp_data_source v ON wo.source_guid = v.source_guid
              and exists(
                    select 1
                    from view_erp_data_source
                    where source_number ILIKE CONCAT('%', #{model.sourceNumber}, '%')
                      and source_guid = wo.source_guid
                )
        </if>
        <if test="model.creator != null and model.creator != ''">
            and ebmo.creator ILIKE CONCAT('%', #{model.creator}, '%')
        </if>
        <if test="model.orderTypes != null and model.orderTypes.size() > 0">
            and ebmo.order_type in
            <foreach item="item" collection="model.orderTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.auditStatus != null and model.auditStatus.size() > 0">
            and ebmo.audit_status in
            <foreach item="item" collection="model.auditStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.processInstanceIds != null and model.processInstanceIds.size() != 0">
            and ebmo.process_instance_id in
            <foreach item="item" collection="model.processInstanceIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.startDate != null">
            and to_char(ebmo.receipt_date, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp, 'YYYY-MM-DD')
        </if>
        <if test="model.endDate != null">
            and to_char(ebmo.receipt_date, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp, 'YYYY-MM-DD')
        </if>
        <if test="model.materialType != null and model.materialType != ''">
            and emmm.material_type = #{model.materialType}
        </if>
        <if test="model.businessKeys != null and model.businessKeys.size() != 0">
            and ebmo.order_guid in
            <foreach item="item" collection="model.businessKeys" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY ebmo.order_guid,
                 ebmo.receipt_date,
                 ebmittr.tax_rate,
                 ebmcer.exchange_rate,
                 ebmc2.currency_name,
                 ebmc2.is_local_currency,
                 arrivalState.all_count,
                 arrivalState.un_count,
                 arrivalState.full_count,
                 arrivalState.partial_count,
                 arrivalState.terminate_count
        order by ebmo.receipt_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select ebmo.*,
               case
                   when ebmo.currency_exchange_rate_guid = '1' then '1'
                   else ebmcer.exchange_rate end as exchange_rate
                ,
               case
                   when ebmo.invoice_type_tax_rate_guid = '1' then 0
                   else ebmittr.tax_rate end     as tax_rate,
               ebmc2.currency_name,
               ebmc2.is_local_currency
        from erp_business_mgt_order ebmo
                 left join erp_basic_mgt_currency_exchange_rate ebmcer on
            ebmcer.currency_exchange_rate_guid = ebmo.currency_exchange_rate_guid
                 left join erp_supplier_mgt_supplier ebmc
                           on ebmo.settlement_customer_or_supplier_guid = ebmc.supplier_guid
                 left join erp_basic_mgt_currency ebmc2 on ebmc2.currency_guid = ebmc.default_currency_guid
                 left join erp_basic_mgt_invoice_type_tax_rate ebmittr on
            ebmo.invoice_type_tax_rate_guid = ebmittr.invoice_type_tax_rate_guid
        where order_guid = #{guid}
    </select>

    <!--获取订单到货情况-->
    <select id="getArrivalState" resultType="java.lang.String">
        select case
                   when aa.count = aa.not_finish_count then '${@<EMAIL>()}'
                   when aa.count = aa.stop_finish_count
                       then '${@<EMAIL>()}'
                   when aa.count = (aa.all_finish_count + aa.stop_finish_count)
                       then '${@<EMAIL>()}'
                   else '${@<EMAIL>()}'
                   end
        from (
                 select count(1)                                                                        as count,
                        (select count(1)
                         from erp_business_mgt_order_data
                         where order_guid = #{orderGuid}
                           and deleted = false
                           and completion_status =
                               '${@com.xunyue.common.enums.CompletionStatusEnum@NOT_FINISH.getCode()}')  as not_finish_count,
                        (select count(1)
                         from erp_business_mgt_order_data
                         where order_guid = #{orderGuid}
                           and deleted = false
                           and completion_status =
                               '${@com.xunyue.common.enums.CompletionStatusEnum@ALL_FINISH.getCode()}')  as all_finish_count,
                        (select count(1)
                         from erp_business_mgt_order_data
                         where order_guid = #{orderGuid}
                           and deleted = false
                           and completion_status =
                               '${@com.xunyue.common.enums.CompletionStatusEnum@STOP_FINISH.getCode()}') as stop_finish_count
                 from erp_business_mgt_order_data
                 where order_guid = #{orderGuid}
                   and deleted = false
             ) as aa
    </select>

    <!--待开列表结果集映射-->
    <resultMap id="WorkorderVOResultMap"
               type="xy.server.purchase.entity.model.vo.ErpPurchaseInventoryWorkorderDetailVO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="workorder_number" property="workorderNumber"/>
        <result column="parent_classification_guid" property="parentClassificationGuid"/>
        <result column="source_value" property="sourceValue"/>
        <result column="source_guid" property="sourceGuid"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="quantity" property="quantity"/>
        <result column="not_billed_quantity" property="notBilledQuantity"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="unit_guid" property="unitGuid"/>
        <result column="required_delivery_time" property="requiredDeliveryTime"/>
        <result column="description" property="description"/>
        <result column="unit_name" property="unitName"/>
        <result column="material_name" property="materialName"/>
        <result column="customer_or_supplier_guid" property="parentCustomerGuid"/>
        <result column="customer_or_supplier_guid" property="customerGuid"/>
        <result column="settlement_customer_or_supplier_guid" property="settlementCustomerOrSupplierGuid"/>
        <result column="supplier_short_name" property="customerOrSupplierName"/>
        <result column="supplierName" property="supplierName"/>
        <result column="settlementCustomerOrSupplierName" property="settlementCustomerOrSupplierName"/>
        <result column="invoice_type_tax_rate_guid" property="invoiceTypeTaxRateGuid"/>
        <result column="currency_exchange_rate_guid" property="currencyExchangeRateGuid"/>
        <result column="tax_rate" property="taxRate"/>
        <result column="exchange_rate" property="exchangeRate"/>
        <result column="currency_name" property="currencyName"/>
        <result column="is_local_currency" property="isLocalCurrency"/>
        <result column="cost_scheme_guid" property="costSchemeGuid"/>
        <result column="scheme_name" property="schemeName"/>
        <result column="default_currency_guid" property="currencyGuid"/>
        <result column="unit_price_including_tax" property="unitPriceIncludingTax"/>
        <result column="totalAmountIncludingTax" property="totalAmountIncludingTax"/>
        <result column="unit_price_without_tax" property="unitPriceWithoutTax"/>
        <result column="totalAmountWithoutTax" property="totalAmountWithoutTax"/>
        <result column="quotation_unit_price_without_tax" property="quotationUnitPriceWithoutTax"/>
        <result column="quotation_unit_price_including_tax" property="quotationUnitPriceIncludingTax"/>
        <result column="settlement_unit_price" property="settlementUnitPrice"/>
        <result column="settlementTotalAmount" property="settlementTotalAmount"/>
        <result column="local_currency_unit_price" property="localCurrencyUnitPrice"/>
        <result column="localCurrencyTotalAmount" property="localCurrencyTotalAmount"/>
        <result column="packaging_quantity" property="packagingOrderQuantity"/>
        <result column="packagingWaitingQuantity" property="packagingQuantity"/>
        <result column="order_data_guid" property="orderDataGuid"/>
    </resultMap>

    <sql id="findNotBilledListSql">
        FROM
        (SELECT *
        FROM erp_production_mgt_workorder
        WHERE parent_classification_guid IS NOT NULL
        -- 物料到货父工单
        AND workorder_properties = 10
        -- 采购订单
        AND source_value = '1'
        AND deleted = false) wo
        LEFT JOIN erp_material_mgt_material m ON wo.material_guid = m.material_guid
        LEFT JOIN erp_basic_mgt_unit u ON u.unit_guid = wo.unit_guid
        LEFT JOIN (
            SELECT SUM(quantity) AS quantity,
        parent_classification_guid
        FROM
        erp_production_mgt_workorder epmw
<!--        left join erp_inventory_mgt_notice_arrival_return_data eimnard on eimnard.workorder_guid=epmw.workorder_guid-->
            WHERE epmw.deleted = FALSE
        -- and eimnard.deleted=false
              AND parent_classification_guid IS NOT NULL
            GROUP BY parent_classification_guid
        ) lbx ON lbx.parent_classification_guid = wo.workorder_guid
        LEFT JOIN erp_production_mgt_workorder pwo ON wo.parent_classification_guid = pwo.workorder_guid
        LEFT JOIN erp_business_mgt_order_data ebmod ON wo.source_guid = ebmod.order_data_guid
        LEFT JOIN erp_business_mgt_purchase_order_data_extend ebmpode on ebmod.order_data_guid = ebmpode.order_data_guid
        -- 生产工单号
        LEFT JOIN erp_cost_scheme ecs ON ecs.cost_scheme_guid = ebmod.cost_scheme_guid
        LEFT JOIN erp_business_mgt_order ebmo ON ebmo.order_guid = ebmod.order_guid
        LEFT JOIN erp_supplier_mgt_supplier esms ON ebmo.customer_or_supplier_guid = esms.supplier_guid
        LEFT JOIN erp_supplier_mgt_supplier esms2 ON ebmo.settlement_customer_or_supplier_guid = esms2.supplier_guid
        LEFT JOIN erp_basic_mgt_invoice_type_tax_rate ebmittr
        ON ebmittr.invoice_type_tax_rate_guid = ebmo.invoice_type_tax_rate_guid
        LEFT JOIN erp_basic_mgt_currency_exchange_rate ebmcer
        ON ebmcer.currency_exchange_rate_guid = ebmo.currency_exchange_rate_guid
        LEFT JOIN erp_basic_mgt_currency ebmc ON ebmc.currency_guid = esms2.default_currency_guid
        WHERE ebmod.order_state != '3'
        <if test="model.isIgnore == null or model.isIgnore == false">
            AND (ebmod.completion_status = '0'
               or  ebmod.completion_status = '1')
        </if>
        <if test="model.orderNumber != null and model.orderNumber != ''">
            AND wo.workorder_number ILIKE CONCAT('%', #{model.orderNumber}, '%')
        </if>
        <if test="model.sourceNumber != null and model.sourceNumber != ''">
            AND wo.workorder_number ILIKE CONCAT('%', #{model.sourceNumber}, '%')
        </if>
        <if test="model.supplierName != null and model.supplierName != ''">
            and (esms.supplier_code ILIKE CONCAT('%', #{model.supplierName}, '%') or
            esms.supplier_short_name ILIKE CONCAT('%', #{model.supplierName}, '%') or
            esms.supplier_full_name ILIKE CONCAT('%', #{model.supplierName}, '%'))
        </if>
        <if test="model.materialName != null and model.materialName != ''">
            AND m.material_name ILIKE CONCAT('%', #{model.materialName}, '%')
        </if>
        <if test="model.materialType != null and model.materialType != ''">
            and m.material_type = #{model.materialType}
        </if>
        <if test="model.supplierGuid != null and model.supplierGuid != ''">
            and ebmo.customer_or_supplier_guid = #{model.supplierGuid}
        </if>
        <if test="model.startDate != null">
            AND to_char(pwo.receipt_date, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp, 'YYYY-MM-DD')
        </if>
        <if test="model.endDate != null">
            AND to_char(pwo.receipt_date, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp, 'YYYY-MM-DD')
        </if>
        <if test="model.workorderGuids != null and model.workorderGuids.size() > 0">
            and wo.workorder_guid in
            <foreach collection="model.workorderGuids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.sourceProductOrderNumber != null and model.sourceProductOrderNumber != ''">
            <!--            AND processWorkOrder.workorder_number ILIKE CONCAT('%',#{model.sourceProductOrderNumber},'%')-->
            and exists(
            select 1
            from erp_business_mgt_order_data purchaseOrderDetail
            left join erp_business_mgt_order purchaseOrder
            on purchaseOrder.order_guid = purchaseOrderDetail.order_guid
            left join erp_business_mgt_order_data purchaseOrderDetail2
            on purchaseOrderDetail2.parent_classification_guid =
            purchaseOrderDetail.order_data_guid
            left join erp_production_mgt_workorder purchaseApplyVirtual
            on purchaseApplyVirtual.workorder_guid = purchaseOrderDetail2.source_guid
            left join erp_production_mgt_workorder purchaseApplyDetail
            on purchaseApplyDetail.workorder_guid =
            purchaseApplyVirtual.parent_classification_guid
            -- 工单
            left join erp_production_mgt_workorder_material_usage epmwmu
            on epmwmu.workorder_material_usage_guid = purchaseApplyDetail.source_guid
            left join erp_production_mgt_workorder processWorkOrder
            on processWorkOrder.workorder_guid = epmwmu.workorder_guid
            where processWorkOrder.workorder_guid is not null
            and purchaseOrder.order_properties = 2
            and processWorkOrder.workorder_properties = 2
            and purchaseApplyDetail.workorder_properties = 3
            and purchaseApplyDetail.workorder_properties = 3
            and purchaseOrderDetail.order_data_guid = ebmod.order_data_guid
            and processWorkOrder.workorder_number ILIKE CONCAT('%', #{model.sourceProductOrderNumber}, '%')
            union all
            select 1
            from erp_business_mgt_order_data outgoingOrderDetail
            left join erp_business_mgt_order purchaseOrder
            on purchaseOrder.order_guid = outgoingOrderDetail.order_guid
            left join erp_production_mgt_workorder outgoingApplyVirtual
            on outgoingApplyVirtual.workorder_guid = outgoingOrderDetail.source_guid
            left join erp_production_mgt_workorder outgoingApplyDetail
            on outgoingApplyDetail.workorder_guid =
            outgoingApplyVirtual.parent_classification_guid
            -- 工单
            left join erp_production_mgt_workorder_data_process epmwdp
            on epmwdp.workorder_data_process_guid = outgoingApplyDetail.source_guid
            left join erp_production_mgt_workorder processWorkOrder
            on processWorkOrder.workorder_guid = epmwdp.workorder_guid
            where processWorkOrder.workorder_guid is not null
            and purchaseOrder.order_properties = 3
            and processWorkOrder.workorder_properties = 2
            and outgoingApplyDetail.workorder_properties = 4
            and outgoingOrderDetail.order_data_guid = ebmod.order_data_guid
            and processWorkOrder.workorder_number ILIKE CONCAT('%', #{model.sourceProductOrderNumber}, '%')
            )
        </if>
        ORDER BY wo.required_delivery_time
    </sql>

    <select id="findNotBilledList" resultMap="WorkorderVOResultMap">
        SELECT wo.*,
               m.material_name,
               wo.workorder_number                                          AS source_number,
               wo.quantity                                                  AS source_quantity,
               u.unit_name,
               ebmo.customer_or_supplier_guid,
               ebmo.settlement_customer_or_supplier_guid,
               ebmo.invoice_type_tax_rate_guid,
               ebmo.currency_exchange_rate_guid,
               esms.supplier_short_name,
               COALESCE(ebmittr.tax_rate, 0)                                AS tax_rate,
               COALESCE(ebmcer.exchange_rate, '1')                          AS exchange_rate,
               ebmc.currency_name,
               ebmc.is_local_currency,
               esms2.default_currency_guid,
               ebmod.cost_scheme_guid,
               ecs.scheme_name,
               esms2.supplier_short_name                                    AS settlementCustomerOrSupplierName,
               esms.supplier_short_name || '+' || esms2.supplier_short_name as supplierName,
               ebmod.unit_price_including_tax,
               ebmod.unit_price_without_tax,
               ebmod.quotation_unit_price_without_tax,
               ebmod.quotation_unit_price_including_tax,
               ebmod.settlement_unit_price,
               ebmod.local_currency_unit_price,
                ebmod.order_data_guid,

        coalesce(ebmpode.packaging_quantity,0) as packaging_quantity

        <!-- 切换到内存里面 -->
        <!--        <if test="model.isIgnore == null or model.isIgnore == false">-->
        <!--            AND (COALESCE(wo.quantity, 0) - COALESCE(lbx.quantity, 0)) &gt; 0-->
        <!--        </if>-->
        <!--        <if test="model.isPackagingQuantity != null and model.isPackagingQuantity == false">-->
        <!--            AND COALESCE(ebmpode.packaging_quantity, 0) - COALESCE(lbx.packaging_quantity, 0) &gt; 0-->
        <!--        </if>-->
        <include refid="findNotBilledListSql"/>
    </select>


    <select id="findNotBilledListSimple" resultMap="WorkorderVOResultMap">
        SELECT wo.workorder_guid,
        wo.quantity,
        coalesce(ebmpode.packaging_quantity, 0) as packaging_quantity
        <include refid="findNotBilledListSql"/>
    </select>

    <select id="findNotBilledListNotBilledQuantity"
            resultType="xy.server.purchase.entity.model.vo.ErpPurchaseInventoryWorkorderDetailVO">
        SELECT coalesce(SUM(quantity), 0)                   AS quantity,
               coalesce(sum(eimnard.packaging_quantity), 0) as packaging_quantity,
               parent_classification_guid                   as workorder_guid
        FROM erp_production_mgt_workorder epmw
                 left join erp_inventory_mgt_notice_arrival_return_data eimnard
                           on eimnard.workorder_guid = epmw.workorder_guid
        WHERE epmw.deleted = FALSE
          AND parent_classification_guid IS NOT NULL
          and parent_classification_guid in
        <foreach collection="workorderGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY parent_classification_guid
    </select>

    <select id="findNotBilledListWorkorderNumber"
            resultType="xy.server.purchase.entity.model.vo.ErpPurchaseInventoryWorkorderDetailVO">
        select string_agg(distinct processWorkOrder.workorder_number, ',') as sourceProductOrderNumber,
               purchaseOrderDetail.order_data_guid
        from erp_business_mgt_order_data purchaseOrderDetail
                 left join erp_business_mgt_order purchaseOrder
                           on purchaseOrder.order_guid = purchaseOrderDetail.order_guid
                 left join erp_business_mgt_order_data purchaseOrderDetail2
                           on purchaseOrderDetail2.parent_classification_guid = purchaseOrderDetail.order_data_guid
                 left join erp_production_mgt_workorder purchaseApplyVirtual
                           on purchaseApplyVirtual.workorder_guid = purchaseOrderDetail2.source_guid
                 left join erp_production_mgt_workorder purchaseApplyDetail
                           on purchaseApplyDetail.workorder_guid = purchaseApplyVirtual.parent_classification_guid
            -- 工单
                 left join erp_production_mgt_workorder_material_usage epmwmu
                           on epmwmu.workorder_material_usage_guid = purchaseApplyDetail.source_guid
                 left join erp_production_mgt_workorder processWorkOrder
                           on processWorkOrder.workorder_guid = epmwmu.workorder_guid
        where processWorkOrder.workorder_guid is not null
          and purchaseOrder.order_properties = 2
          and processWorkOrder.workorder_properties = 2
          and purchaseApplyDetail.workorder_properties = 3
          and purchaseApplyDetail.workorder_properties = 3
          and purchaseOrderDetail.order_data_guid in
        <foreach collection="orderDataGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by purchaseOrderDetail.order_data_guid
        union all
        select string_agg(distinct processWorkOrder.workorder_number, ',') as sourceProductOrderNumber,
               outgoingOrderDetail.order_data_guid
        from erp_business_mgt_order_data outgoingOrderDetail
                 left join erp_business_mgt_order purchaseOrder
                           on purchaseOrder.order_guid = outgoingOrderDetail.order_guid
                 left join erp_production_mgt_workorder outgoingApplyVirtual
                           on outgoingApplyVirtual.workorder_guid = outgoingOrderDetail.source_guid
                 left join erp_production_mgt_workorder outgoingApplyDetail
                           on outgoingApplyDetail.workorder_guid = outgoingApplyVirtual.parent_classification_guid
            -- 工单
                 left join erp_production_mgt_workorder_data_process epmwdp
                           on epmwdp.workorder_data_process_guid = outgoingApplyDetail.source_guid
                 left join erp_production_mgt_workorder processWorkOrder
                           on processWorkOrder.workorder_guid = epmwdp.workorder_guid
        where processWorkOrder.workorder_guid is not null
          and purchaseOrder.order_properties = 3
          and processWorkOrder.workorder_properties = 2
          and outgoingApplyDetail.workorder_properties = 4
          and outgoingOrderDetail.order_data_guid in
        <foreach collection="orderDataGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by outgoingOrderDetail.order_data_guid
    </select>

    <!--查询采购订单待开数量-->
    <select id="selectCountPendingList" resultType="java.math.BigDecimal">
        SELECT COUNT(1)
        FROM erp_production_mgt_workorder pwo
        WHERE pwo.workorder_properties = ${@<EMAIL>()}
          AND pwo.audit_status = '${@<EMAIL>()}'
          AND (
                  select COUNT(1)
                  from erp_production_mgt_workorder cwo
                  where cwo.parent_classification_guid = pwo.workorder_guid
                    and (cwo.workorder_state = '${@<EMAIL>()}' or
                         cwo.workorder_state = '${@<EMAIL>()}')
              ) > 0
    </select>

    <!--获取顶级来源备注和图片-->
    <select id="getSourceDescriptionAndPicture"
            resultType="com.xunyue.common.entity.model.vo.SourceDescriptionAndPictureVO">
        SELECT epmw.source_value,
               source_order.source_product_name,
               source_order.source_product_code,
               CASE
                   WHEN
                       epmw.source_value = 1 THEN
                       source_workorder.source_guid
                   ELSE source_order.source_guid
                   END source_guid,
               CASE
                   WHEN epmw.source_value = 1 THEN
                       source_workorder.source_number
                   ELSE source_order.source_number
                   END source_number,
               CASE
                   WHEN epmw.source_value = 1 THEN
                       source_workorder.source_description
                   ELSE source_order.source_description
                   END source_description,
               CASE
                   WHEN epmw.source_value = 1 THEN
                       source_workorder.source_picture_names
                   ELSE source_order.source_picture_names
                   END source_picture_names
        FROM erp_production_mgt_workorder epmw
                 -- 工单
                 LEFT JOIN (
            SELECT epmwmu.workorder_material_usage_guid AS source_guid,
                   epmw13.workorder_number              AS source_number,
                   epmw13.description                   AS source_description,
                   string_agg(ebmf.file_name, ',')      AS source_picture_names
            FROM erp_production_mgt_workorder_material_usage epmwmu
                     LEFT JOIN erp_production_mgt_workorder epmw ON epmw.workorder_guid = epmwmu.workorder_guid
                     LEFT JOIN erp_production_mgt_workorder epmw13 ON epmw13.workorder_number = epmw.workorder_number
                AND epmw13.workorder_properties = 13
                     LEFT JOIN erp_production_mgt_workorder_file epmwf ON epmwf.workorder_guid = epmw13.workorder_guid
                AND epmwf.file_type = 1
                     LEFT JOIN erp_basic_mgt_file ebmf ON ebmf.file_guid = epmwf.file_guid
            GROUP BY epmwmu.workorder_material_usage_guid,
                     epmw13.workorder_guid
        ) source_workorder ON source_workorder.source_guid = epmw.source_guid
            -- 订单
                 LEFT JOIN (
            SELECT ebmod.order_data_guid           AS source_guid,
                   ebmo.order_number               AS source_number,
                   emmm.material_name              AS source_product_name,
                   emmm.material_code              AS source_product_code,
                   ebmo.description                AS source_description,
                   string_agg(ebmf.file_name, ',') AS source_picture_names
            FROM erp_business_mgt_order_data ebmod
                     LEFT JOIN erp_business_mgt_order ebmo ON ebmo.order_guid = ebmod.order_guid
                     LEFT JOIN erp_material_mgt_material emmm ON emmm.material_guid = ebmod.material_guid
                     LEFT JOIN erp_business_mgt_order_file ebmof ON ebmof.order_guid = ebmo.order_guid
                AND ebmof.order_data_guid = ebmod.order_data_guid
                AND ebmof.file_type = 1
                     LEFT JOIN erp_basic_mgt_file ebmf ON ebmf.file_guid = ebmof.file_guid
            GROUP BY ebmod.order_data_guid,
                     ebmo.order_guid,
                     emmm.material_guid
        ) source_order ON source_order.source_guid = epmw.source_guid
        WHERE
            epmw.workorder_guid IN
        <foreach item="item" collection="sourceGuids" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND (epmw.source_value = 1 OR epmw.source_value = 2)
    </select>

    <!--分页查看历史单价列表-->
    <select id="selectHistoricalUnitPricePage"
            resultType="xy.server.purchase.entity.model.vo.ErpPurchaseMgtHistoricalUnitPriceVO">
        SELECT c_eimnard.act_material_guid                                  as material_guid,
               emmm.material_name,
               emmm.material_code,
               emmm.material_classification_guid,
               emmmc.material_classification_name,
               emmm.material_type,
               get_material_specification_type(c_eimnard.act_material_guid) as specification_type_str,
               esms.supplier_guid,
               esms.supplier_short_name,
               esms.supplier_full_name,
               esms.supplier_code,
               c_epmw.quantity,
               c_eimnard.act_quantity,
               c_eimnard.quotation_unit_price_including_tax,
               c_eimnard.total_amount_including_tax,
               p_epmw.receipt_date,
               p_epmw.workorder_number,
               p_epmw.workorder_guid
        FROM erp_inventory_mgt_notice_arrival_return_data p_eimnard
                 LEFT JOIN erp_production_mgt_workorder p_epmw ON p_epmw.workorder_guid = p_eimnard.workorder_guid
                 LEFT JOIN erp_production_mgt_workorder c_epmw
                           ON c_epmw.parent_classification_guid = p_epmw.workorder_guid
                 LEFT JOIN erp_inventory_mgt_notice_arrival_return_data c_eimnard
                           ON c_eimnard.workorder_guid = c_epmw.workorder_guid
                 LEFT JOIN erp_material_mgt_material emmm ON emmm.material_guid = c_eimnard.act_material_guid
                 LEFT JOIN erp_material_mgt_material_classification emmmc
                           ON emmmc.material_classification_guid = emmm.material_classification_guid
                 LEFT JOIN erp_supplier_mgt_supplier esms ON esms.supplier_guid = p_epmw.customer_guid
        WHERE p_epmw.workorder_properties = 9
          AND (p_epmw.parent_classification_guid IS NULL OR p_epmw.parent_classification_guid = '')
          AND p_epmw.audit_status = '${@<EMAIL>()}'
        <if test="model.materialGuid != null and model.materialGuid != ''">
            AND c_eimnard.act_material_guid = #{model.materialGuid}
        </if>
        <if test="model.materialGuids != null and model.materialGuids.size() > 0">
            AND c_eimnard.act_material_guid IN
            <foreach item="item" collection="model.materialGuids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.supplierGuid != null and model.supplierGuid != ''">
            AND esms.supplier_guid = #{model.supplierGuid}
        </if>
        <if test="model.startDate != null">
            AND p_epmw.receipt_date &gt;= CONCAT(#{model.startDate}, ' 00:00:00')::timestamp
        </if>
        <if test="model.endDate != null">
            AND p_epmw.receipt_date &lt;= CONCAT(#{model.endDate}, ' 23:59:59')::timestamp
        </if>
        ORDER BY emmm.material_code desc,
                 p_epmw.create_date desc
    </select>

    <!--获取最新到货单价-->
    <select id="getLatestArrivalPrice" resultType="java.math.BigDecimal">
        SELECT COALESCE(c_eimnard.quotation_unit_price_including_tax, 0)
        FROM erp_inventory_mgt_notice_arrival_return_data p_eimnard
                 LEFT JOIN erp_production_mgt_workorder p_epmw ON p_epmw.workorder_guid = p_eimnard.workorder_guid
                 LEFT JOIN erp_production_mgt_workorder c_epmw
                           ON c_epmw.parent_classification_guid = p_epmw.workorder_guid
                 LEFT JOIN erp_inventory_mgt_notice_arrival_return_data c_eimnard
                           ON c_eimnard.workorder_guid = c_epmw.workorder_guid
        WHERE p_epmw.workorder_properties = 9
          AND (p_epmw.parent_classification_guid IS NULL OR p_epmw.parent_classification_guid = '')
          AND p_epmw.audit_status = '${@<EMAIL>()}'
          AND c_eimnard.act_material_guid = #{materialGuid }
          AND p_epmw.customer_guid = #{supplierGuid }
        ORDER BY p_epmw.create_date DESC
        LIMIT 1
    </select>
    <select id="getNewData" resultType="java.math.BigDecimal">
        select epmw3.proportion
        from erp_material_mgt_material_batch_goods_detail emmmbgd
                 left join erp_material_mgt_material_batch emmmb
                           on emmmb.material_batch_guid = emmmbgd.material_batch_guid
                 left join erp_production_mgt_workorder epmw on emmmb.source_guid = epmw.workorder_guid
                 left join erp_production_mgt_workorder epmw2 on epmw.parent_classification_guid = epmw2.workorder_guid
                 left join erp_material_mgt_material_batch_goods emmmbg
                           on emmmbg.material_batch_goods_guid = emmmbgd.material_batch_goods_guid
                 left join erp_production_mgt_workorder epmw3 on emmmbg.source_guid = epmw3.workorder_guid
        where emmmbgd.material_guid = #{qo.materialGuid}
          and epmw2.customer_guid = #{qo.customerOrSupplierGuid}
        order by epmw3.create_date desc
        limit 1
    </select>

    <select id="findNextFlow" resultType="int">
        SELECT count(1)
        FROM
            erp_inventory_mgt_notice_arrival_return_data eimnard
                LEFT JOIN erp_production_mgt_workorder epmw ON epmw.workorder_guid = eimnard.workorder_guid
                LEFT JOIN erp_production_mgt_workorder workorder ON workorder.parent_classification_guid = epmw.workorder_guid
                LEFT JOIN erp_production_mgt_workorder epmw1 ON epmw1.workorder_guid = workorder.source_guid
                LEFT JOIN erp_production_mgt_workorder epmw2 ON epmw2.workorder_guid = epmw1.parent_classification_guid
        WHERE
            epmw.workorder_properties = 9
          AND (epmw.parent_classification_guid = '' OR epmw.parent_classification_guid IS NULL)
          AND epmw2.workorder_number = #{workorderNumber}
    </select>

</mapper>
