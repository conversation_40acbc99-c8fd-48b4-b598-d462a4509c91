<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpPurchaseMgtPurchaseRequisitionMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseRequisitionVO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="workorder_number" property="workorderNumber"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="print_times" property="printTimes"/>
        <result column="receipt_date" property="receiptDate"/>
        <result column="workorder_type_guid" property="workorderTypeGuid"/>
        <result column="source_value" property="sourceValue"/>
        <result column="is_urgent" property="isUrgent"/>
        <result column="description" property="description"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_date" property="auditDate"/>
        <result column="creator" property="creator"/>
        <result column="source_value" property="sourceValue"/>
        <result column="create_date" property="createDate"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <!--采购情况-->
        <association property="workorderState" javaType="java.lang.String" column="{workorderGuid=workorder_guid}"
                     select="getPurchaseState"/>
        <!--采购申请单明细-->
        <collection property="detailList"
                    ofType="xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseRequisitionVO"
                    column="{pGuid=workorder_guid}"
                    select="findDetailList"/>
    </resultMap>

    <!--采购申请单明细查询映射结果-->
    <resultMap id="DetailVoResultMap" type="xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseRequisitionVO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="workorder_number" property="workorderNumber"/>
        <result column="parent_classification_guid" property="parentClassificationGuid"/>
        <result column="source_value" property="sourceValue"/>
        <result column="source_guid" property="sourceGuid"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="quantity" property="quantity"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="unit_guid" property="unitGuid"/>
        <result column="required_delivery_time" property="requiredDeliveryTime"/>
        <result column="description" property="description"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <result column="unit_name" property="unitName"/>
        <result column="source_number" property="sourceNumber"/>
        <result column="source_quantity" property="sourceQuantity"/>
        <result column="not_billed_quantity" property="notBilledQuantity"/>
        <result column="current_status" property="currentStatus"/>
        <result column="customer_guid" property="customerGuid"/>
        <result column="customer_short_name" property="customerShortName"/>
        <result column="product_name" property="productName"/>
        <!--物料-->
        <association property="materialObj" javaType="xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO"
                     column="{guid=material_guid}"
                     select="xy.server.material.mapper.ErpMaterialMgtMaterialMapper.getDataByGuid"/>
    </resultMap>
    <update id="updateSaleOrderData">
        update erp_business_mgt_sales_order_data
        set purchase_requisition_quantity=${total},
            purchase_requisition_status=${value}
        where sales_order_data_guid = #{saleOrderDataGuid}
    </update>

    <select id="getOneById" resultMap="VoResultMap">
        select *
        from erp_production_mgt_workorder
        where workorder_guid = #{guid}
          and parent_classification_guid is null
    </select>

    <!-- 通用查询映射结果 无明细数据 -->
    <resultMap id="VoResultNoDetail" type="xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseRequisitionVO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="workorder_number" property="workorderNumber"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="print_times" property="printTimes"/>
        <result column="receipt_date" property="receiptDate"/>
        <result column="workorder_type_guid" property="workorderTypeGuid"/>
        <result column="is_urgent" property="isUrgent"/>
        <result column="description" property="description"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_date" property="auditDate"/>
        <result column="creator" property="creator"/>
        <result column="source_value" property="sourceValue"/>
        <result column="create_date" property="createDate"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
    </resultMap>

    <sql id="findPageSql">
        select epmw3.workorder_guid,
               epmw3.workorder_number,
               epmw3.serial_number,
               epmw3.print_times,
               epmw3.receipt_date,
               epmw3.workorder_type_guid,
               epmw3.source_guid,
               epmw3.is_urgent,
               epmw3.description,
               epmw3.process_instance_id,
               epmw3.audit_status,
               epmw3.audit_date,
               epmw3.creator,
               epmw3.create_date,
               epmw3.source_value,
               epmw3.to_json
        from erp_production_mgt_workorder epmw3
        <!--        left join erp_administration_mgt_staff eams on eams.staff_guid=epmw3.creator_guid-->
        <!--        left join erp_production_mgt_workorder aa on aa.parent_classification_guid = epmw3.workorder_guid-->
        <!--        left join erp_material_mgt_material emmm on aa.material_guid = emmm.material_guid-->
        <!--        left join erp_business_mgt_order_data ebmod on aa.source_guid = ebmod.order_data_guid-->
        <!--        left join erp_business_mgt_order ebmo on ebmo.order_guid = ebmod.order_guid-->
        <!--        left join erp_production_mgt_workorder_material_usage epmwmu-->
        <!--        on aa.source_guid = epmwmu.workorder_material_usage_guid-->
        <!--        left join erp_production_mgt_workorder epmw on epmw.workorder_guid = epmwmu.workorder_guid-->
        where (epmw3.parent_classification_guid is null or epmw3.parent_classification_guid='') and
        epmw3.workorder_properties =
        ${@<EMAIL>()}
        <if test="model.processInstanceIds != null and model.processInstanceIds.size() != 0 ">
            and epmw3.process_instance_id in
            <foreach item="item" collection="model.processInstanceIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.workorderNumber != null and model.workorderNumber != '' ">
            and epmw3.workorder_number ILIKE CONCAT('%',#{model.workorderNumber},'%')
        </if>

        <if test="model.materialName != null and model.materialName != '' ">
            and exists(
            select 1
            from erp_production_mgt_workorder aa
            left join erp_material_mgt_material emmm on aa.material_guid = emmm.material_guid
            where aa.parent_classification_guid = epmw3.workorder_guid
            and (emmm.material_code ILIKE CONCAT('%', #{model.materialName}, '%')
            or emmm.material_name ILIKE CONCAT('%', #{model.materialName}, '%')
            )
            )
        </if>

        <if test="model.sourceNumber != null and model.sourceNumber != '' ">
            and exists(
            select 1
            from erp_production_mgt_workorder aa
            left join erp_business_mgt_order_data ebmod on aa.source_guid = ebmod.order_data_guid
            left join erp_business_mgt_order ebmo on ebmo.order_guid = ebmod.order_guid
            left join erp_production_mgt_workorder_material_usage epmwmu
            on aa.source_guid = epmwmu.workorder_material_usage_guid
            left join erp_production_mgt_workorder epmw on epmw.workorder_guid = epmwmu.workorder_guid
            where aa.parent_classification_guid = epmw3.workorder_guid
            and (epmw.workorder_number ILIKE CONCAT('%', #{model.sourceNumber}, '%')
            or ebmo.order_number ILIKE CONCAT('%', #{model.sourceNumber}, '%')
            )
            )
        </if>

        <if test="model.creator != null and model.creator != '' ">
            and exists(
            select 1
            from erp_administration_mgt_staff eams
            where eams.staff_guid = epmw3.creator_guid
            and (eams.staff_code ILIKE CONCAT('%', #{model.creator}, '%')
            or eams.staff_short_name ILIKE CONCAT('%', #{model.creator}, '%')
            )
            )
        </if>
        <if test="model.auditStatus != null and model.auditStatus.size() > 0 ">
            and epmw3.audit_status in
            <foreach item="item" collection="model.auditStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.workorderTypeGuids != null and model.workorderTypeGuids.size() > 0 ">
            and epmw3.workorder_type_guid in
            <foreach item="item" collection="model.workorderTypeGuids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.startDate != null">
            and to_char(epmw3.receipt_date, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp , 'YYYY-MM-DD')
        </if>
        <if test="model.endDate != null">
            and to_char(epmw3.receipt_date, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp , 'YYYY-MM-DD')
        </if>
        <if test="model.businessKeys != null and model.businessKeys.size() != 0 ">
            and epmw3.workorder_guid in
            <foreach item="item" collection="model.businessKeys" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by epmw3.create_date desc
    </sql>

    <select id="findList" resultMap="VoResultNoDetail">
        <include refid="findPageSql"/>
    </select>

    <select id="findPage" resultMap="VoResultNoDetail">
        <include refid="findPageSql"/>
    </select>

    <sql id="findDetailListSql">
        SELECT aa.*,
               (
                   SELECT
                       string_agg ( customer_short_name, ',' ) customer_short_name
                   FROM
                       erp_customer_mgt_customer
                   WHERE
                           customer_guid :: VARCHAR IN ( SELECT * FROM regexp_split_to_table( aa.customer_guid, ',' ) )
               ) customer_short_name,
               (
                   SELECT
                       string_agg ( material_name, ',' )
                   FROM
                       erp_material_mgt_material
                   WHERE
                           material_guid :: VARCHAR IN ( SELECT * FROM regexp_split_to_table( epmw.product_material_guid, ',' ) )
               ) product_name,
               case
                   when aa.source_value = 1 or aa.source_value = 5 then epmw.workorder_number
                   when aa.source_value = 2 then ebmo.order_number
                   else null end source_number,
               case
                   when aa.source_value = 1 then epmw.quantity
                   when aa.source_value = 2 then ebmod.quantity
                   else null end not_billed_quantity,
               ebmod.external_material_bar_code,
               ebmod.external_material_code,
               ebmod.external_material_name,
               ebmod.external_tracking_number,
               u.unit_name,
               aa.serial_number
        FROM erp_production_mgt_workorder aa
                 left join erp_material_mgt_material emmm on aa.material_guid = emmm.material_guid
                 left join erp_basic_mgt_unit u on emmm.unit_guid = u.unit_guid
                 left join erp_business_mgt_order_data ebmod on aa.source_guid = ebmod.order_data_guid
                 left join erp_business_mgt_order ebmo on ebmo.order_guid = ebmod.order_guid
                 left join erp_production_mgt_workorder_material_usage epmwmu
                           on aa.source_guid = epmwmu.workorder_material_usage_guid
                 left join erp_production_mgt_workorder epmw on (epmw.workorder_guid = epmwmu.workorder_guid OR epmw.workorder_guid = aa.source_guid)
    </sql>

    <select id="findDetailList" resultMap="DetailVoResultMap">
        <include refid="findDetailListSql"/>
        WHERE aa.parent_classification_guid = #{pGuid}
          AND aa.deleted = false
        ORDER BY aa.serial_number;
    </select>

    <!--获取采购申请单采购情况-->
    <select id="getPurchaseState" resultType="java.lang.String">
        select case
                   when aa.count = aa.un_count then '${@<EMAIL>()}'
                   when aa.count = aa.terminate_count
                       then '${@<EMAIL>()}'
                   when aa.count = (aa.all_count + aa.terminate_count + aa.excess_count)
                       then '${@<EMAIL>()}'
                   else '${@<EMAIL>()}'
                   end
        from (select count(1)                                                                    as count,
                     (select count(1)
                      from erp_production_mgt_workorder
                      where parent_classification_guid = #{workorderGuid}
                        and deleted = false
                        and workorder_state =
                            '${@<EMAIL>()}')        as un_count,
                     (select count(1)
                      from erp_production_mgt_workorder
                      where parent_classification_guid = #{workorderGuid}
                        and deleted = false
                        and workorder_state =
                            '${@<EMAIL>()}')      as all_count,
                     (select count(1)
                      from erp_production_mgt_workorder
                      where parent_classification_guid = #{workorderGuid}
                        and deleted = false
                        and workorder_state =
                            '${@<EMAIL>()}') as terminate_count,
                     (select count(1)
                      from erp_production_mgt_workorder
                      where parent_classification_guid = #{workorderGuid}
                        and deleted = false
                        and workorder_state =
                            '${@<EMAIL>()}')    as excess_count
              from erp_production_mgt_workorder
              where parent_classification_guid = #{workorderGuid}
                and deleted = false) as aa
    </select>

    <resultMap id="findNotBilledListResultMap" type="xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseRequisitionVO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="workorder_number" property="workorderNumber"/>
        <result column="parent_classification_guid" property="parentClassificationGuid"/>
        <result column="source_value" property="sourceValue"/>
        <result column="source_guid" property="sourceGuid"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="quantity" property="quantity"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="unit_guid" property="unitGuid"/>
        <result column="required_delivery_time" property="requiredDeliveryTime"/>
        <result column="description" property="description"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <result column="unit_name" property="unitName"/>
        <result column="source_number" property="sourceNumber"/>
        <result column="source_quantity" property="sourceQuantity"/>
        <result column="not_billed_quantity" property="notBilledQuantity"/>
        <result column="current_status" property="currentStatus"/>
        <result column="customer_guid" property="customerGuid"/>
        <result column="customer_short_name" property="customerShortName"/>
        <result column="product_name" property="productName"/>
        <result column="billed_quantity" property="billedQuantity"/>
        <result column="receipt_date" property="receiptDate"/>
    </resultMap>
    <select id="findNotBilledList" resultMap="findNotBilledListResultMap">
<!--        SELECT-->
<!--        aa.*,-->
<!--        COALESCE ( aa.quantity, 0 ) - COALESCE ( aa.billed_quantity, 0 ) AS not_billed_quantity-->
<!--        FROM-->
<!--        (-->
        SELECT
        -- 单据时间
        pwo.receipt_date,
        wo.*,
<!--        v.source_number,-->
<!--        v.source_quantity,-->
        u.unit_name
<!--        ( SELECT SUM ( quantity ) AS billed_quantity FROM erp_production_mgt_workorder WHERE parent_classification_guid-->
<!--        = wo.workorder_guid AND deleted = FALSE ) AS billed_quantity-->
        FROM
        erp_production_mgt_workorder wo
        LEFT JOIN erp_production_mgt_workorder pwo ON wo.parent_classification_guid = pwo.workorder_guid
        LEFT JOIN erp_material_mgt_material m ON wo.material_guid = m.material_guid
        LEFT JOIN erp_material_mgt_material_classification emmmc ON emmmc.material_classification_guid = m.material_classification_guid
<!--        LEFT JOIN view_erp_data_source v ON wo.source_guid = v.source_guid-->
        LEFT JOIN erp_basic_mgt_unit u ON u.unit_guid = m.unit_guid
        WHERE
        wo.parent_classification_guid IS NOT NULL
        AND wo.workorder_properties = ${@<EMAIL>()}
        AND pwo.audit_status = '${@<EMAIL>()}'
        AND (wo.workorder_state = '${@<EMAIL>()}' or wo.workorder_state =
        '${@<EMAIL>()}')
        AND wo.deleted = FALSE
        and wo.current_status !='3'
        <if test="model.workorderNumber != null and model.workorderNumber != ''">
            AND wo.workorder_number ILIKE CONCAT('%',#{model.workorderNumber},'%')
        </if>
        <if test="model.sourceNumber != null and model.sourceNumber != ''">
<!--            AND v.source_number ILIKE CONCAT('%',#{model.sourceNumber},'%')-->
            AND wo.source_guid in (
                select source_guid from view_erp_data_source where source_number ILIKE CONCAT('%',#{model.sourceNumber},'%')
            )
        </if>
        <if test="model.materialName != null and model.materialName != ''">
            AND m.material_name ILIKE CONCAT('%',#{model.materialName},'%')
        </if>
        <if test="model.materialType != null and model.materialType != ''">
            AND m.material_type = #{model.materialType}
        </if>
        <if test="model.searchCode != null and model.searchCode != ''">
            AND emmmc.search_code ilike CONCAT('%',#{model.searchCode},'%')
        </if>
<!--        <if test="model.widthOfFabric != null and model.widthOfFabric != ''">-->
<!--            AND specification_value like CONCAT('%',#{model.widthOfFabric},'%')-->
<!--        </if>-->
        <if test="model.startDate != null">
            AND to_char(pwo.receipt_date, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp , 'YYYY-MM-DD')
        </if>
        <if test="model.endDate != null">
            AND to_char(pwo.receipt_date, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp , 'YYYY-MM-DD')
        </if>
        ORDER BY
        pwo.receipt_date desc , wo.serial_number
    </select>

    <select id="getBilledQuantity" resultMap="findNotBilledListResultMap">
        SELECT SUM(quantity) AS billed_quantity,
               parent_classification_guid
        FROM erp_production_mgt_workorder WHERE parent_classification_guid in
        <foreach collection="workorderGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY parent_classification_guid

    </select>

    <select id="waitingFindList" resultMap="waitingFindListMap">
        select * from view_purchase_requisition_source vprs
        LEFT join erp_material_mgt_material emmm on emmm.material_guid=vprs.material_guid
        <where>
            <if test="model.sourceValue != null and model.sourceValue != 3">
                vprs.waiting_quantity &gt; 0
            </if>
            <if test="model.sourceNumber != null and model.sourceNumber != ''">
                AND vprs.source_number ILIKE CONCAT('%',#{model.sourceNumber},'%')
            </if>
            <if test="model.materialName != null and model.materialName != ''">
                AND vprs.material_name ILIKE CONCAT('%',#{model.materialName},'%')
            </if>
            <if test="model.startDate != null">
                AND to_char(vprs.create_date, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp , 'YYYY-MM-DD')
            </if>
            <if test="model.endDate != null">
                AND to_char(vprs.create_date, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp , 'YYYY-MM-DD')
            </if>
            <if test="model.sourceValue != null">
                AND vprs.source_value=#{model.sourceValue}
            </if>
            <if test="model.materialTypes != null and model.materialTypes.size() > 0 ">
                and
                <foreach item="item" collection="model.materialTypes" open="(" separator="or" close=")">
                    emmm.material_type = #{item}
                </foreach>
            </if>
        </where>
        order by vprs.create_date desc
        limit 50
    </select>
    <select id="getWorkOrderWaitingQuantity" resultType="java.math.BigDecimal">
        SELECT COUNT(DISTINCT pwo.order_guid)
        FROM erp_business_mgt_order pwo
                 JOIN erp_business_mgt_order_data cwo ON pwo.order_guid = cwo.order_guid
                 LEFT JOIN (
            SELECT source_guid, SUM(quantity) AS total_quantity
            FROM erp_production_mgt_workorder
            WHERE deleted = FALSE
            GROUP BY source_guid
        ) ebmpld ON cwo.order_data_guid = ebmpld.source_guid
        WHERE pwo.order_properties = ${@<EMAIL>()}
          AND pwo.audit_status = '${@<EMAIL>()}'
          AND pwo.deleted IS FALSE
          AND cwo.deleted IS FALSE
          AND (ebmpld.total_quantity &lt; cwo.quantity OR ebmpld.total_quantity IS NULL)
    </select>
    <select id="selectOrderData" resultType="xy.server.purchase.entity.model.vo.ErpOrderDataVO"
            parameterType="java.lang.String">
        select ebmod.quantity                       as orderDataQuantity,
               ebmsod.purchase_requisition_quantity as purchaseQuantity,
               ebmsod.sales_order_data_guid         as saleOrderDataGuid
        from erp_business_mgt_order_data ebmod
                 left join erp_business_mgt_sales_order_data ebmsod on ebmsod.order_data_guid = ebmod.order_data_guid
        where ebmod.order_data_guid = #{sourceGuid}
    </select>


    <resultMap id="waitingFindListMap" type="xy.server.purchase.entity.model.vo.ErpPurchaseSourceRequisitionVO">
        <id column="source_number" property="sourceNumber"/>
        <id column="source_quantity" property="sourceQuantity"/>
        <id column="source_value" property="sourceValue"/>
        <id column="description" property="description"/>
        <id column="material_guid" property="materialGuid"/>
        <id column="source_guid" property="sourceGuid"/>
        <id column="waiting_quantity" property="waitingQuantity"/>
        <id column="customer_guid" property="customerGuid"/>
        <id column="customer_short_name" property="customerShortName"/>
        <id column="create_date"/>
        <!--赠送数量-->
        <result column="gift_quantity" property="giftQuantity"/>
        <!--备用数量-->
        <result column="spare_quantity" property="spareQuantity"/>
        <association property="materialObj" javaType="java.lang.String"
                     column="{guid=material_guid}"
                     select="xy.server.material.mapper.ErpMaterialMgtMaterialMapper.getDataByGuid"/>
    </resultMap>


    <select id="waitingFindLists" resultMap="waitingFindListMap">
        SELECT
        vprs.source_number,
        vprs.source_quantity,
        vprs.source_value,
        vprs.description,
        vprs.material_guid,
        vprs.material_code,
        vprs.material_name,
        (
        SELECT
        string_agg ( customer_short_name, ',' ) customer_short_name
        FROM
        erp_customer_mgt_customer
        WHERE
        customer_guid :: VARCHAR IN ( SELECT * FROM regexp_split_to_table( vprs.customer_guid, ',' ) )
        ) customer_short_name,
        vprs.customer_guid,
        vprs.source_guid,
        vprs.waiting_quantity,
        vprs.create_date
        from ( SELECT epmw.workorder_number AS source_number,
        epmwmu.material_usage_quantity AS source_quantity,
        1 AS source_value,
        epmwmu.description,
        emmm.material_guid,
        emmm.material_code,
        emmm.material_name,
        epmw13.customer_guid,
        epmwmu.workorder_material_usage_guid AS source_guid,
        epmwmu.material_usage_quantity - COALESCE(epmw3.quantity, 0::numeric) AS waiting_quantity,
        epmwmu.create_date,
        epmw.serial_number
        FROM erp_production_mgt_workorder epmw
        LEFT JOIN erp_production_mgt_workorder epmw13 ON epmw13.workorder_number = epmw.workorder_number
        AND epmw13.workorder_properties = ${@<EMAIL>()}
        LEFT JOIN erp_production_mgt_workorder_material_usage epmwmu ON epmw.workorder_guid::text =
        epmwmu.workorder_guid::text
        LEFT JOIN erp_material_mgt_material emmm ON emmm.material_guid::text = epmwmu.material_guid::text
        LEFT JOIN (SELECT sum(epmw2.quantity) as quantity ,epmw2.source_guid
        FROM erp_production_mgt_workorder epmw2
        WHERE epmw2.workorder_properties = 3
        GROUP BY epmw2.source_guid) epmw3 on epmwmu.workorder_material_usage_guid = epmw3.source_guid
        WHERE
            epmw.workorder_properties = 2
        <if test="model.auditStatus != null and model.auditStatus.size() > 0 "   >
            AND epmw13.audit_status in
            <foreach item="item" collection="model.auditStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
            AND epmwmu.is_purchase_requisition = true AND epmw.deleted = false
        <if test="model.materialTypes != null and model.materialTypes.size() > 0 ">
            and
            <foreach item="item" collection="model.materialTypes" open="(" separator="or" close=")">
                emmm.material_type = #{item}
            </foreach>
        </if>
        <if test="model.sourceGuid != null and model.sourceGuid.size() > 0 ">
            and
            <foreach item="item" collection="model.sourceGuid" open="(" separator="or" close=")">
                epmwmu.workorder_material_usage_guid = #{item}
            </foreach>
        </if>
        ) vprs
        <where>
            <if test="model.sourceValue != null and model.sourceValue != 3">
                <if test="model.isIgnore == null or model.isIgnore == false">
                    and vprs.waiting_quantity &gt; 0
                </if>
            </if>
            <if test="model.sourceNumber != null and model.sourceNumber != ''">
                AND vprs.source_number ILIKE CONCAT('%',#{model.sourceNumber},'%')
            </if>
            <if test="model.materialName != null and model.materialName != ''">
                AND vprs.material_name ILIKE CONCAT('%',#{model.materialName},'%')
            </if>
            <if test="model.startDate != null">
                AND to_char(vprs.create_date, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp , 'YYYY-MM-DD')
            </if>
            <if test="model.endDate != null">
                AND to_char(vprs.create_date, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp , 'YYYY-MM-DD')
            </if>
            <if test="model.sourceValue != null">
                AND vprs.source_value=#{model.sourceValue}
            </if>
        </where>
        order by vprs.source_number DESC, vprs.serial_number ASC
        limit ${model.size}
    </select>



    <select id="partWaitingFindLists" resultMap="waitingFindListMap">
        SELECT
        vprs.source_number,
        vprs.source_quantity,
        vprs.source_value,
        vprs.description,
        vprs.material_guid,
        vprs.material_code,
        vprs.material_name,
        (
        SELECT
        string_agg ( customer_short_name, ',' ) customer_short_name
        FROM
        erp_customer_mgt_customer
        WHERE
        customer_guid :: VARCHAR IN ( SELECT * FROM regexp_split_to_table( vprs.customer_guid, ',' ) )
        ) customer_short_name,
        vprs.customer_guid,
        vprs.source_guid,
        vprs.waiting_quantity,
        vprs.create_date
        from ( SELECT epmw.workorder_number AS source_number,
        coalesce(epmwmu.material_usage_quantity , epmw.quantity) AS source_quantity,
        4 AS source_value,
        epmwmu.description,
        emmm.material_guid,
        emmm.material_code,
        emmm.material_name,
        epmw13.customer_guid,
        epmw.workorder_guid AS source_guid,
        coalesce(epmwmu.material_usage_quantity , epmw.quantity) - COALESCE(epmw3.quantity, 0::numeric) AS waiting_quantity,
        coalesce(epmwmu.create_date , epmw.create_date) as create_date
        FROM erp_production_mgt_workorder epmw
        LEFT JOIN erp_production_mgt_workorder epmw13 ON epmw13.workorder_number = epmw.workorder_number
        AND epmw13.workorder_properties = ${@<EMAIL>()}
        LEFT JOIN erp_production_mgt_workorder_material_usage epmwmu ON epmw.workorder_guid::text =
        epmwmu.workorder_guid::text
        LEFT JOIN erp_material_mgt_material emmm ON emmm.material_guid::text = coalesce(epmwmu.material_guid :: text , epmw.material_guid)
        LEFT JOIN (SELECT sum(epmw2.quantity) as quantity ,epmw2.source_guid
        FROM erp_production_mgt_workorder epmw2
        WHERE epmw2.workorder_properties = 3
        GROUP BY epmw2.source_guid) epmw3 on epmwmu.workorder_material_usage_guid = epmw3.source_guid
        WHERE
        epmw.workorder_properties = 17
        <if test="model.auditStatus != null and model.auditStatus.size() > 0 "   >
            AND epmw13.audit_status in
            <foreach item="item" collection="model.auditStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
       AND epmw.deleted = false
        <if test="model.materialTypes != null and model.materialTypes.size() > 0 ">
            and
            <foreach item="item" collection="model.materialTypes" open="(" separator="or" close=")">
                emmm.material_type = #{item}
            </foreach>
        </if>
        <if test="model.sourceGuid != null and model.sourceGuid.size() > 0 ">
            and
            <foreach item="item" collection="model.sourceGuid" open="(" separator="or" close=")">
                (epmwmu.workorder_material_usage_guid = #{item} or epmw.workorder_guid = #{item})
            </foreach>
        </if>
        ) vprs
        <where>
            <if test="model.sourceValue != null and model.sourceValue != 3">
                <if test="model.isIgnore == null or model.isIgnore == false">
                    and vprs.waiting_quantity &gt; 0
                </if>
            </if>
            <if test="model.sourceNumber != null and model.sourceNumber != ''">
                AND vprs.source_number ILIKE CONCAT('%',#{model.sourceNumber},'%')
            </if>
            <if test="model.materialName != null and model.materialName != ''">
                AND vprs.material_name ILIKE CONCAT('%',#{model.materialName},'%')
            </if>
            <if test="model.startDate != null">
                AND to_char(vprs.create_date, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp , 'YYYY-MM-DD')
            </if>
            <if test="model.endDate != null">
                AND to_char(vprs.create_date, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp , 'YYYY-MM-DD')
            </if>
            <if test="model.sourceValue != null">
                AND vprs.source_value=#{model.sourceValue}
            </if>
        </where>
        order by vprs.create_date desc
        limit ${model.size}
    </select>



    <select id="orderDataFindList" resultMap="waitingFindListMap">
        SELECT vprs.* from ( SELECT ebmo.order_number AS source_number,
        ebmod.quantity AS source_quantity,
        ebmod.gift_quantity,
        ebmod.spare_quantity,
        ebmod.serial_number,
        emmm.material_code,
        emmm.material_name,
        ebmu.unit_guid,
        ebmu.unit_name,
        2 AS source_value,
        ebmod.description,
        emmm.material_guid,
        ebmo.customer_or_supplier_guid customer_guid,
        ecmc.customer_short_name,
        ebmod.order_data_guid AS source_guid,
        (ebmod.quantity + ebmod.gift_quantity + ebmod.spare_quantity) - COALESCE(epmw3.quantity, 0::numeric) AS waiting_quantity,
        ebmod.create_date
        FROM erp_business_mgt_order ebmo
        LEFT JOIN erp_business_mgt_order_data ebmod ON ebmod.order_guid::text = ebmo.order_guid::text
        LEFT JOIN erp_customer_mgt_customer ecmc ON ebmo.customer_or_supplier_guid = ecmc.customer_guid
        LEFT JOIN erp_material_mgt_material emmm ON emmm.material_guid::text = ebmod.material_guid::text
        LEFT JOIN erp_material_mgt_material_classification emmmc ON emmm.material_classification_guid::text =
        emmmc.material_classification_guid::text
        LEFT JOIN erp_basic_mgt_unit ebmu ON ebmu.unit_guid::text = emmmc.unit_guid::text
        LEFT JOIN erp_business_mgt_sales_order_data ebmsod ON ebmsod.order_data_guid::text = ebmod.order_data_guid::text
        LEFT JOIN ( SELECT sum(epmw2.quantity) quantity,epmw2.source_guid
        FROM erp_production_mgt_workorder epmw2
        WHERE epmw2.deleted = false AND epmw2.workorder_properties = 3
        GROUP BY epmw2.source_guid) epmw3 on ebmod.order_data_guid = epmw3.source_guid
        WHERE ebmo.order_properties = 1 AND ebmo.deleted = false AND ebmod.deleted = false AND ebmsod.is_purchase = true
        <if test="model.auditStatus != null and model.auditStatus.size() > 0 "   >
            AND ebmo.audit_status in
            <foreach item="item" collection="model.auditStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.materialTypes != null and model.materialTypes.size() > 0 ">
            and
            <foreach item="item" collection="model.materialTypes" open="(" separator="or" close=")">
                emmm.material_type = #{item}
            </foreach>
        </if>
        <if test="model.sourceGuid != null and model.sourceGuid.size() > 0 ">
            and
            <foreach item="item" collection="model.sourceGuid" open="(" separator="or" close=")">
                ebmod.order_data_guid = #{item}
            </foreach>
        </if>
        ) vprs
        <where>
            <if test="model.sourceValue != null and model.sourceValue != 3">
                <if test="model.isIgnore == null or model.isIgnore == false">
                    and vprs.waiting_quantity &gt; 0
                </if>
            </if>
            <if test="model.sourceNumber != null and model.sourceNumber != ''">
                AND vprs.source_number ILIKE CONCAT('%',#{model.sourceNumber},'%')
            </if>
            <if test="model.materialName != null and model.materialName != ''">
                AND vprs.material_name ILIKE CONCAT('%',#{model.materialName},'%')
            </if>
            <if test="model.startDate != null">
                AND to_char(vprs.create_date, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp , 'YYYY-MM-DD')
            </if>
            <if test="model.endDate != null">
                AND to_char(vprs.create_date, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp , 'YYYY-MM-DD')
            </if>
            <if test="model.sourceValue != null">
                AND vprs.source_value=#{model.sourceValue}
            </if>
        </where>
        order by vprs.source_number DESC, vprs.serial_number ASC
        limit ${model.size}
    </select>
    <select id="materialFindList" resultMap="waitingFindListMap">
        SELECT vprs.* from ( SELECT ''::character varying AS source_number,
        NULL::numeric AS source_quantity,
        emmm2.material_code,
        emmm2.material_name,
        ebmu.unit_guid,
        ebmu.unit_name,
        3 AS source_value,
        ''::text AS description,
        emmm2.material_guid,
        ''::character varying AS source_guid,
        NULL::numeric AS waiting_quantity,
        emmm2.create_date
        FROM erp_material_mgt_material emmm2
        LEFT JOIN erp_material_mgt_material_classification emmmc ON emmm2.material_classification_guid::text =
        emmmc.material_classification_guid::text
        LEFT JOIN erp_basic_mgt_unit ebmu ON ebmu.unit_guid::text = emmmc.unit_guid::text
        WHERE emmm2.deleted = false AND emmm2.material_name IS NOT NULL AND emmm2.material_name::text != ''::text
        <if test="model.materialTypes != null and model.materialTypes.size() > 0 ">
            and
            <foreach item="item" collection="model.materialTypes" open="(" separator="or" close=")">
                emmm2.material_type = #{item}
            </foreach>
        </if>
        ) vprs
        <where>
            <if test="model.sourceValue != null and model.sourceValue != 3">
                and vprs.waiting_quantity &gt; 0
            </if>
            <if test="model.sourceNumber != null and model.sourceNumber != ''">
                AND vprs.source_number ILIKE CONCAT('%',#{model.sourceNumber},'%')
            </if>
            <if test="model.materialName != null and model.materialName != ''">
                AND vprs.material_name ILIKE CONCAT('%',#{model.materialName},'%')
            </if>
            <if test="model.startDate != null">
                AND to_char(vprs.create_date, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp , 'YYYY-MM-DD')
            </if>
            <if test="model.endDate != null">
                AND to_char(vprs.create_date, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp , 'YYYY-MM-DD')
            </if>
            <if test="model.sourceValue != null">
                AND vprs.source_value=#{model.sourceValue}
            </if>
        </where>
        order by vprs.create_date desc
        limit ${model.size}
    </select>

    <!--采购申请单明细查询映射结果 无明细数据-->
    <resultMap id="DetailVoResultMapNoDetail" type="xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseRequisitionVO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="workorder_number" property="workorderNumber"/>
        <result column="parent_classification_guid" property="parentClassificationGuid"/>
        <result column="source_value" property="sourceValue"/>
        <result column="source_guid" property="sourceGuid"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="quantity" property="quantity"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="unit_guid" property="unitGuid"/>
        <result column="required_delivery_time" property="requiredDeliveryTime"/>
        <result column="description" property="description"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <result column="unit_name" property="unitName"/>
        <result column="source_number" property="sourceNumber"/>
        <result column="source_quantity" property="sourceQuantity"/>
        <result column="not_billed_quantity" property="notBilledQuantity"/>
        <result column="current_status" property="currentStatus"/>
        <result column="customer_guid" property="customerGuid"/>
        <result column="customer_short_name" property="customerShortName"/>
        <result column="product_name" property="productName"/>
    </resultMap>

    <select id="findDetailListBatch" resultMap="DetailVoResultMapNoDetail">
        <include refid="findDetailListSql"/>
        WHERE aa.parent_classification_guid in
        <foreach collection="pGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND aa.deleted = false
        ORDER BY aa.serial_number ASC
    </select>

    <select id="getPurchaseStateBatch" resultType="xy.server.work.entity.model.dto.BaseMapContainer">
        select
        case
        when aa.order_count = aa.un_count
        then '0'
        when aa.order_count = aa.terminate_count
        then '3'
        when aa.order_count = (aa.all_count + aa.terminate_count + aa.excess_count)
        then '2'
        else '1'
        end as value,
        aa.parent_classification_guid as name
        from (
        SELECT sum(order_count) as order_count,
               sum(un_count) as un_count,
               sum(all_count) as all_count,
               sum(terminate_count) as terminate_count,
               sum(excess_count) as excess_count,
               t1.parent_classification_guid
        from (
                 SELECT
                     COUNT ( 1 ) AS order_count,
                     0 as un_count,
                     0 as all_count,
                     0 as terminate_count,
                     0 as excess_count,
                     parent_classification_guid
                 FROM
                     erp_production_mgt_workorder
                 WHERE
                     deleted = FALSE
                   and parent_classification_guid in
                    <foreach collection="workorderGuids" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                 GROUP BY
                     parent_classification_guid
                 UNION all
                 SELECT
                     0 AS order_count,
                     COUNT ( 1 ) as un_count,
                     0 as all_count,
                     0 as terminate_count,
                     0 as excess_count,
                     parent_classification_guid
                 FROM
                     erp_production_mgt_workorder
                 WHERE
                     deleted = FALSE
                   and workorder_state = '0'
                   and parent_classification_guid in
                    <foreach collection="workorderGuids" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                 GROUP BY
                     parent_classification_guid
                 UNION all
                 SELECT
                     0 AS order_count,
                     0 as un_count,
                     COUNT ( 1 ) as all_count,
                     0 as terminate_count,
                     0 as excess_count,
                     parent_classification_guid
                 FROM
                     erp_production_mgt_workorder
                 WHERE
                     deleted = FALSE
                   and workorder_state = '2'
                   and parent_classification_guid in
                    <foreach collection="workorderGuids" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                 GROUP BY
                     parent_classification_guid
                 UNION all
                 SELECT
                     0 AS order_count,
                     0 as un_count,
                     0 as all_count,
                     COUNT ( 1 ) as terminate_count,
                     0 as excess_count,
                     parent_classification_guid
                 FROM
                     erp_production_mgt_workorder
                 WHERE
                     deleted = FALSE
                   and workorder_state = '3'
                   and parent_classification_guid in
                    <foreach collection="workorderGuids" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                 GROUP BY
                     parent_classification_guid
                 UNION all
                 SELECT
                     0 AS order_count,
                     0 as un_count,
                     0 as all_count,
                     0 as terminate_count,
                     COUNT ( 1 ) as excess_count,
                     parent_classification_guid
                 FROM
                     erp_production_mgt_workorder
                 WHERE
                     deleted = FALSE
                   and workorder_state = '4'
                   and parent_classification_guid in
        <foreach collection="workorderGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
                 GROUP BY
                     parent_classification_guid
             ) t1 GROUP BY t1.parent_classification_guid
        ) aa
    </select>

    <!--工单-模具来源待开数量-->
    <select id="mouldWaitingFindList"
            resultType="xy.server.purchase.entity.model.vo.ErpPurchaseSourceRequisitionVO">
        SELECT
            5 AS source_value,
            epmw2.workorder_guid AS source_guid,
            epmw2.material_guid,
            epmw13.customer_guid,
            (
                SELECT
                    string_agg ( customer_short_name, ',' ) customer_short_name
                FROM
                    erp_customer_mgt_customer
                WHERE
                    customer_guid :: VARCHAR IN ( SELECT * FROM regexp_split_to_table( epmw13.customer_guid, ',' ) )
            ) AS customer_short_name,
            epmw2.description,
            epmw13.workorder_number AS source_number,
            epmw2.serial_number
        FROM
            erp_production_mgt_workorder epmw2
            LEFT JOIN erp_production_mgt_workorder epmw13 ON epmw13.workorder_number = epmw2.workorder_number
            LEFT JOIN erp_material_mgt_material emmm ON emmm.material_guid = epmw2.material_guid
            LEFT JOIN (
                SELECT
                    epmw3.source_guid,
                    sum(epmw3.quantity) total_quantity
                FROM
                    erp_production_mgt_workorder epmw3
                WHERE
                    epmw3.workorder_properties = 3
                    AND epmw3.parent_classification_guid IS NOT NULL
                GROUP BY
                    epmw3.source_guid
            ) epmw3 ON epmw3.source_guid = epmw2.workorder_guid
        WHERE
            epmw2.workorder_properties = 2
            AND epmw13.workorder_properties = 13
            AND emmm.material_type = '4'
            <if test="model.auditStatus != null and model.auditStatus.size() > 0 "   >
                AND epmw13.audit_status IN
                <foreach item="item" collection="model.auditStatus" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="model.sourceGuid != null and model.sourceGuid.size() > 0 ">
                AND epmw2.workorder_guid IN
                <foreach item="item" collection="model.sourceGuid" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="model.materialName != null and model.materialName != ''">
                AND emmm.material_name ILIKE CONCAT('%',#{model.materialName},'%')
            </if>
            <if test="model.sourceNumber != null and model.sourceNumber != ''">
                AND epmw13.workorder_number ILIKE CONCAT('%',#{model.sourceNumber},'%')
            </if>
            <if test="model.isIgnore == null or model.isIgnore == false">
                AND COALESCE(epmw3.total_quantity, 0) = 0
            </if>
        ORDER BY
            epmw13.workorder_number DESC, epmw2.serial_number ASC
        limit ${model.size}
    </select>

    <select id="getViewErpDataSourceBySourceGuids" resultType="xy.server.basic.entity.model.vo.ViewErpDataSourceVO">
        select *
        from view_erp_data_source where source_guid in
        <foreach collection="sourceGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <resultMap id="DetailNewVoResultMap" type="xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseRequisitionVO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="workorder_number" property="workorderNumber"/>
        <result column="parent_classification_guid" property="parentClassificationGuid"/>
        <result column="source_value" property="sourceValue"/>
        <result column="source_guid" property="sourceGuid"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="quantity" property="quantity"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="unit_guid" property="unitGuid"/>
        <result column="required_delivery_time" property="requiredDeliveryTime"/>
        <result column="description" property="description"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <result column="unit_name" property="unitName"/>
        <result column="source_number" property="sourceNumber"/>
        <result column="source_quantity" property="sourceQuantity"/>
        <result column="not_billed_quantity" property="notBilledQuantity"/>
        <result column="current_status" property="currentStatus"/>
    </resultMap>
    <select id="findNotBilledListES"
            resultMap="DetailNewVoResultMap">
        with a as (
        SELECT SUM ( quantity ) AS billed_quantity,parent_classification_guid,workorder_properties FROM
        erp_production_mgt_workorder
        where deleted=false group by parent_classification_guid,workorder_properties
        )
        SELECT
        aa.*,
        COALESCE ( aa.total_quantity, 0 ) - COALESCE ( aa.billed_quantity, 0 ) AS not_billed_quantity
        FROM
        (
        SELECT
        wo.*,
        v.source_number,
        v.source_quantity,
        u.unit_name,
        billed_quantity
        FROM
        erp_production_mgt_workorder wo
        LEFT JOIN erp_production_mgt_workorder pwo ON wo.parent_classification_guid = pwo.workorder_guid
        LEFT JOIN erp_material_mgt_material m ON wo.material_guid = m.material_guid
        left join erp_material_mgt_material_classification emmmc on m.material_classification_guid=emmmc.material_classification_guid
        left join erp_material_mgt_specification_value emmsv on m.material_guid=emmsv.material_guid
        and specification_type_guid='a1a8e4972eba7fb28f7d8b9cc406a109'
        LEFT JOIN view_erp_data_source v ON wo.source_guid = v.source_guid
        LEFT JOIN erp_basic_mgt_unit u ON u.unit_guid = m.unit_guid
        left join  a on a.parent_classification_guid= wo.workorder_guid AND a.workorder_properties = wo.workorder_properties
        WHERE
        wo.parent_classification_guid IS NOT NULL
        AND pwo.audit_status = '${@<EMAIL>()}'
        AND (wo.workorder_state = '${@<EMAIL>()}' or wo.workorder_state =
        '${@<EMAIL>()}')
        AND wo.deleted = FALSE
        and wo.current_status !='3'
        <if test="model.sourceValue != null and model.sourceValue == 1">
            AND wo.workorder_properties = ${@<EMAIL>()}
        </if>
        <if test="model.sourceValue != null and model.sourceValue == 2">
            AND wo.workorder_properties = ${@com.xunyue.common.enums.WorkorderPropertiesEnum@PRODUCTION_FORECAST.getCode()}
        </if>
        <if test="model.workorderNumber != null and model.workorderNumber != ''">
            AND wo.workorder_number like CONCAT('%',#{model.workorderNumber},'%')
        </if>
        <if test="model.sourceNumber != null and model.sourceNumber != ''">
            AND v.source_number like CONCAT('%',#{model.sourceNumber},'%')
        </if>
        <if test="model.materialName != null and model.materialName != ''">
            AND m.material_name like CONCAT('%',#{model.materialName},'%')
        </if>
        <if test="model.searchCode != null and model.searchCode != ''">
            AND emmmc.search_code ilike CONCAT('%',#{model.searchCode},'%')
        </if>
        <if test="model.widthOfFabric != null and model.widthOfFabric != ''">
            AND specification_value like CONCAT('%',#{model.widthOfFabric},'%')
        </if>
        <if test="model.materialType != null and model.materialType != ''">
            AND m.material_type = #{model.materialType}
        </if>
        <if test="model.startDate != null">
            AND to_char(pwo.receipt_date, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp , 'YYYY-MM-DD')
        </if>
        <if test="model.endDate != null">
            AND to_char(pwo.receipt_date, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp , 'YYYY-MM-DD')
        </if>
        ORDER BY
        required_delivery_time
        ) AS aa
        where COALESCE ( aa.total_quantity, 0 )  &gt; COALESCE ( aa.billed_quantity, 0 )
    </select>
</mapper>
