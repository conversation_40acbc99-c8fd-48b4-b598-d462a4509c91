<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpSupplierMgtSupplierContactMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierContactVO">
        <id column="supplier_contact_guid" property="supplierContactGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="supplier_guid" property="supplierGuid"/>
        <result column="contact_name" property="contactName"/>
        <result column="telephone" property="telephone"/>
        <result column="mobilephone" property="mobilephone"/>
        <result column="fax" property="fax"/>
        <result column="email" property="email"/>
        <result column="qq_number" property="qqNumber"/>
        <result column="wechat_number" property="wechatNumber"/>
        <result column="is_default" property="isDefault"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select *
        from erp_supplier_mgt_supplier_contact
        <where>
            <if test="model.supplierGuid != '' and model.supplierGuid != null">
                and supplier_guid = #{model.supplierGuid}
            </if>
            <if test="model.keywords != null and model.keywords.size() != 0">
                and
                <foreach collection="model.keywords" item="item" index="index" open="(" separator="or" close=")" >
                    contact_name ILIKE CONCAT('%',#{item},'%')
                    or telephone ILIKE CONCAT('%',#{item},'%')
                    or mobilephone ILIKE CONCAT('%',#{item},'%')
                    or description ILIKE CONCAT('%',#{item},'%')
                </foreach>
            </if>
        </where>
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select *
        from erp_supplier_mgt_supplier_contact
        <where>
            <if test="model.supplierGuid != '' and model.supplierGuid != null">
                and supplier_guid = #{model.supplierGuid}
            </if>
            <if test="model.keywords != null and model.keywords.size() != 0">
                and
                <foreach collection="model.keywords" item="item" index="index" open="(" separator="or" close=")" >
                    contact_name ILIKE CONCAT('%',#{item},'%')
                    or telephone ILIKE CONCAT('%',#{item},'%')
                    or mobilephone ILIKE CONCAT('%',#{item},'%')
                    or description ILIKE CONCAT('%',#{item},'%')
                </foreach>
            </if>
        </where>
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select *
        from erp_supplier_mgt_supplier_contact where supplier_contact_guid = #{guid}
    </select>

    <select id="getDataBySupplierGuids" resultMap="VoResultMap">
        select *
        from erp_supplier_mgt_supplier_contact where supplier_guid in
        <foreach collection="guids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
