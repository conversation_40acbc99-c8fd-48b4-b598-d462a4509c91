<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpProductionMgtProductionForecastMapper">

    <!--主表查询映射结果-->
    <resultMap id="MainResultMap" type="xy.server.purchase.entity.model.vo.ErpProductionMgtProductionForecastVO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="workorder_number" property="workorderNumber"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="print_times" property="printTimes"/>
        <result column="receipt_date" property="receiptDate"/>
        <result column="workorder_type_guid" property="workorderTypeGuid"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_date" property="auditDate"/>
        <result column="inventory_quantity" property="inventoryQuantity"/>
        <result column="workorder_properties" property="workorderProperties"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
<!--        &lt;!&ndash;明细列表&ndash;&gt;-->
<!--        <collection property="detailList" ofType="xy.server.purchase.entity.model.vo.ErpProductionMgtProductionForecastVO"-->
<!--                    column="{guid=workorder_guid}" select="selectDetailList"/>-->
    </resultMap>

    <!-- 明细查询映射结果 -->
    <resultMap id="DetailResultMap" type="xy.server.purchase.entity.model.vo.ErpProductionMgtProductionForecastVO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="workorderNumber" property="workorderNumber"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="parent_classification_guid" property="parentClassificationGuid"/>
        <result column="workorder_state" property="workorderState"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="workorder_properties" property="workorderProperties"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="material_name" property="materialName"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_specification" property="materialSpecification"/>
        <result column="search_code" property="searchCode"/>
        <result column="quantity" property="quantity"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="inventoryTotalQuantity" property="inventoryTotalQuantity"/>
        <result column="inventory_quantity" property="inventoryQuantity"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <!--物料-->
        <!--<association property="materialObj" javaType="xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO"-->
        <!--             column="{guid=material_guid}" select="xy.server.material.mapper.ErpMaterialMgtMaterialMapper.getDataByGuid"/>-->
        <!--来源明细列表-->
<!--        <collection property="sourceDetailList" ofType="xy.server.purchase.entity.model.vo.ErpProductionMgtProductionForecastVO"-->
<!--                    column="{guid=workorder_guid}" select="selectSourceDetailList"/>-->
    </resultMap>

    <!--来源数据（业务预测）VO映射结果-->
    <resultMap id="SourceDataResultMap" type="xy.server.purchase.entity.model.vo.ErpProductionMgtProductionForecastVO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="workorder_number" property="workorderNumber"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="workorder_properties" property="workorderProperties"/>
        <result column="customer_guid" property="customerGuid"/>
        <result column="product_specification" property="productSpecification"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="material_name" property="materialName"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_specification" property="materialSpecification"/>
        <result column="search_code" property="searchCode"/>
        <result column="product_quantity" property="productQuantity"/>
        <result column="material_usage_quantity" property="materialUsageQuantity"/>
        <result column="product_unit_name" property="productUnitName"/>
        <result column="material_unit_name" property="materialUnitName"/>
        <result column="quantity" property="quantity"/>
        <result column="source_guid" property="sourceGuid"/>
        <result column="source_value" property="sourceValue"/>
        <result column="inventory_quantity" property="inventoryQuantity"/>
        <result column="inventoryTotalQuantity" property="inventoryTotalQuantity"/>
        <!--客户名称-->
        <result column="customer_short_name" property="customerShortName"/>
<!--        &lt;!&ndash;物料&ndash;&gt;-->
<!--        <association property="materialObj" javaType="xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO"-->
<!--                     column="{guid=materialGuid}" select="xy.server.material.mapper.ErpMaterialMgtMaterialMapper.getDataByGuid"/>-->
    </resultMap>


    <select id="findPage" resultMap="MainResultMap">
        select
            main_tab.*
        from
            erp_production_mgt_workorder main_tab
        where
            main_tab.workorder_properties = ${@com.xunyue.common.enums.WorkorderPropertiesEnum@PRODUCTION_FORECAST.getCode()}
            and (main_tab.parent_classification_guid is null or main_tab.parent_classification_guid = '')
            <if test="model.workorderNumber != null and model.workorderNumber != ''">
                and main_tab.workorder_number like CONCAT('%',#{model.workorderNumber},'%')
            </if>
            <if test="model.auditStatus != null and model.auditStatus.size() > 0 ">
                and main_tab.audit_status in
                <foreach item="item" collection="model.auditStatus" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="model.startDate != null">
                and to_char(main_tab.receipt_date, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp , 'YYYY-MM-DD')
            </if>
            <if test="model.endDate != null">
                and to_char(main_tab.receipt_date, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp , 'YYYY-MM-DD')
            </if>
             <if test="model.workorderGuid != null and model.workorderGuid != ''">
                and workorder_guid=#{model.workorderGuid}
            </if>
        order by
            main_tab.create_date desc
    </select>

    <select id="findList" resultMap="MainResultMap">
        select
            main_tab.*
        from
            erp_production_mgt_workorder main_tab
        where
            main_tab.workorder_properties = ${@com.xunyue.common.enums.WorkorderPropertiesEnum@PRODUCTION_FORECAST.getCode()}
            and (main_tab.parent_classification_guid is null or main_tab.parent_classification_guid = '')
            <if test="model.workorderNumber != null and model.workorderNumber != ''">
                and main_tab.workorder_number like CONCAT('%',#{model.workorderNumber},'%')
            </if>
            <if test="model.auditStatus != null and model.auditStatus.size() > 0 ">
                and main_tab.audit_status in
                <foreach item="item" collection="model.auditStatus" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="model.startDate != null">
                and to_char(main_tab.receipt_date, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp , 'YYYY-MM-DD')
            </if>
            <if test="model.endDate != null">
                and to_char(main_tab.receipt_date, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp , 'YYYY-MM-DD')
            </if>
        <if test="model.workorderGuid != null and model.workorderGuid != ''">
            and workorder_guid=#{model.workorderGuid}
        </if>
        order by
            main_tab.create_date desc
    </select>

    <select id="getDataByGuid" resultMap="MainResultMap">
        select *
        from erp_production_mgt_workorder
        where workorder_guid = #{guid}
    </select>

    <select id="selectDetailList" resultMap="DetailResultMap">
        select
            epmw.*,
            emmm.material_guid,
            emmm.material_name,
            emmm.material_code,
            get_unit_name(emmm.unit_guid) as material_unit_name,
            emmmc.search_code,
            coalesce(eimmi.inventory_quantity,0) as inventory_quantity,
            a.totalQuantity as inventoryTotalQuantity,
            get_material_specification_type(emmm.material_guid) as material_specification,
            epmw2.workorder_number as workorderNumber
        from
            erp_production_mgt_workorder epmw
                left join erp_production_mgt_workorder epmw2 on epmw2.workorder_guid=epmw.source_guid and epmw2.workorder_properties = 110
            left join erp_material_mgt_material emmm on emmm.material_guid = epmw.material_guid
            left join erp_material_mgt_material_classification emmmc on emmmc.material_classification_guid = emmm.material_classification_guid
            left join erp_material_mgt_specification_value emmsv1 on emmm.material_guid=emmsv1.material_guid
                and specification_type_guid='a1a8e4972eba7fb28f7d8b9cc406a109'
            left join erp_inventory_mgt_material_inventory eimmi on emmm.material_guid=eimmi.material_guid
            left join (select sum(inventory_quantity) as totalQuantity,SUBSTRING(emmm2.search_code FROM 1 FOR 2) as
                                                         search_code,emmsv.specification_value from erp_inventory_mgt_material_inventory eimmi2
                                                                                                        left join erp_material_mgt_material emmm3 on emmm3.material_guid=eimmi2.material_guid
                                                                                                        left join erp_material_mgt_specification_value emmsv on emmm3.material_guid=emmsv.material_guid
                                                                                                        left join erp_material_mgt_material_classification emmm2 on
                eimmi2.material_classification_guid=emmm2.material_classification_guid
                       where LENGTH(emmm2.search_code) &gt; 4 and specification_type_guid='a1a8e4972eba7fb28f7d8b9cc406a109'
                       group by SUBSTRING(emmm2.search_code FROM 1 FOR 2),emmsv.specification_value
            ) as a on a.search_code=SUBSTRING(emmmc.search_code FROM 1 FOR 2)
                and emmsv1.specification_value=a.specification_value
        where
            epmw.parent_classification_guid = #{guid}
        order by
            epmw.serial_number
    </select>

    <select id="selectDetailByIds" resultMap="DetailResultMap">
        select
            epmw.*,
            emmm.material_guid,
            emmm.material_name,
            emmm.material_code,
            get_unit_name(emmm.unit_guid) as material_unit_name,
            emmmc.search_code,
            coalesce(eimmi.inventory_quantity,0) as inventory_quantity,
            a.totalQuantity as inventoryTotalQuantity,
            get_material_specification_type(emmm.material_guid) as material_specification,
            epmw2.workorder_number as workorderNumber
        from
            erp_production_mgt_workorder epmw
                left join erp_production_mgt_workorder epmw2 on epmw2.workorder_guid=epmw.source_guid and epmw2.workorder_properties = 110
            left join erp_material_mgt_material emmm on emmm.material_guid = epmw.material_guid
            left join erp_material_mgt_material_classification emmmc on emmmc.material_classification_guid = emmm.material_classification_guid
            left join erp_material_mgt_specification_value emmsv1 on emmm.material_guid=emmsv1.material_guid
                and specification_type_guid='a1a8e4972eba7fb28f7d8b9cc406a109'
            left join erp_inventory_mgt_material_inventory eimmi on emmm.material_guid=eimmi.material_guid
            left join (select sum(inventory_quantity) as totalQuantity,SUBSTRING(emmm2.search_code FROM 1 FOR 2) as
                                                         search_code,emmsv.specification_value from erp_inventory_mgt_material_inventory eimmi2
                                                                                                        left join erp_material_mgt_material emmm3 on emmm3.material_guid=eimmi2.material_guid
                                                                                                        left join erp_material_mgt_specification_value emmsv on emmm3.material_guid=emmsv.material_guid
                                                                                                        left join erp_material_mgt_material_classification emmm2 on
                eimmi2.material_classification_guid=emmm2.material_classification_guid
                       where LENGTH(emmm2.search_code) &gt; 4 and specification_type_guid='a1a8e4972eba7fb28f7d8b9cc406a109'
                       group by SUBSTRING(emmm2.search_code FROM 1 FOR 2),emmsv.specification_value
            ) as a on a.search_code=SUBSTRING(emmmc.search_code FROM 1 FOR 2)
                and emmsv1.specification_value=a.specification_value
        where
            epmw.parent_classification_guid in
            <foreach item="item" collection="guidList" separator="," index="index" open="(" close=")">
                #{item}
            </foreach>
        order by
            epmw.serial_number
    </select>

    <select id="selectSourceDetailList" resultMap="SourceDataResultMap">
        select
            epmw.workorder_guid,
            epmw.parent_classification_guid,
            epmw.workorder_properties,
            epmw.source_guid,
            epmw.quantity,
            epmw.description,
            p_epmw110.workorder_number,
            p_epmw110.required_delivery_time,
            get_material_specification_type(p_epmw110.material_guid) as product_specification,
            epwmms.material_series_guid,
            ebmms.material_series_name,
            p_epmw110.quantity as product_quantity,
            epmw110.quantity as material_usage_quantity,
            emmm.material_guid as materialGuid,
            emmm.material_name,
            emmm.material_code,
            get_unit_name(emmm.unit_guid) as material_unit_name,
            emmmc.search_code,
            get_material_specification_type(emmm.material_guid) as material_specification,
            ecmc.customer_guid,
            ecmc.customer_short_name
        from
            erp_production_mgt_workorder epmw
            left join erp_production_mgt_workorder epmw110 on epmw110.workorder_guid = epmw.source_guid
            left join erp_production_mgt_workorder p_epmw110 on p_epmw110.workorder_guid = epmw110.parent_classification_guid
            left join erp_production_work_mgt_material_series epwmms on epwmms.workorder_guid = p_epmw110.workorder_guid
            left join erp_basic_mgt_material_series ebmms on epwmms.material_series_guid = ebmms.material_series_guid
            left join erp_material_mgt_material emmm on emmm.material_guid = epmw110.material_guid
            left join erp_material_mgt_material_classification emmmc on emmmc.material_classification_guid = emmm.material_classification_guid
            left join erp_customer_mgt_customer ecmc on ecmc.customer_guid = epmw110.customer_guid
        where
            epmw.parent_classification_guid = #{guid}
            and epmw.workorder_properties = ${@com.xunyue.common.enums.WorkorderPropertiesEnum@PRODUCTION_FORECAST_SOURCE_BUSINESS_FORECASTING.getCode()}
        order by
            epmw.serial_number
    </select>

    <select id="selectSourceDetailByGuids" resultMap="SourceDataResultMap">
        select
            epmw.workorder_guid,
            epmw.parent_classification_guid,
            epmw.workorder_properties,
            epmw.source_guid,
            epmw.quantity,
            epmw.description,
            p_epmw110.workorder_number,
            p_epmw110.required_delivery_time,
            get_material_specification_type(p_epmw110.material_guid) as product_specification,
            epwmms.material_series_guid,
            ebmms.material_series_name,
            p_epmw110.quantity as product_quantity,
            epmw110.quantity as material_usage_quantity,
            emmm.material_guid as materialGuid,
            emmm.material_name,
            emmm.material_code,
            get_unit_name(emmm.unit_guid) as material_unit_name,
            emmmc.search_code,
            get_material_specification_type(emmm.material_guid) as material_specification,
            ecmc.customer_guid,
            ecmc.customer_short_name
        from
            erp_production_mgt_workorder epmw
            left join erp_production_mgt_workorder epmw110 on epmw110.workorder_guid = epmw.source_guid
            left join erp_production_mgt_workorder p_epmw110 on p_epmw110.workorder_guid = epmw110.parent_classification_guid
            left join erp_production_work_mgt_material_series epwmms on epwmms.workorder_guid = p_epmw110.workorder_guid
            left join erp_basic_mgt_material_series ebmms on epwmms.material_series_guid = ebmms.material_series_guid
            left join erp_material_mgt_material emmm on emmm.material_guid = epmw110.material_guid
            left join erp_material_mgt_material_classification emmmc on emmmc.material_classification_guid = emmm.material_classification_guid
            left join erp_customer_mgt_customer ecmc on ecmc.customer_guid = epmw110.customer_guid
        where
            epmw.parent_classification_guid in
            <foreach item="item" collection="guidList" separator="," index="index" open="(" close=")">
                #{item}
            </foreach>
            and epmw.workorder_properties = ${@com.xunyue.common.enums.WorkorderPropertiesEnum@PRODUCTION_FORECAST_SOURCE_BUSINESS_FORECASTING.getCode()}
        order by
            epmw.serial_number
    </select>

    <!--查询待开采购预测的业务预测列表-->
    <select id="findNotBilledList" resultMap="SourceDataResultMap">
        select
        c_epmw.workorder_guid,
        c_epmw.workorder_number,
        c_epmw.workorder_properties,
        c_epmw.material_guid,
        epmw.required_delivery_time,
        epmw.description,
        epmw.create_date,
        epmw.creator,
        epmw.last_updater,
        epmw.last_update_date,
        epwmms.material_series_guid,
        ebmms.material_series_name,
        get_material_specification_type(epmw.material_guid) as product_specification,
        emmm.material_name,
        emmm.material_code,
        emmmc.search_code,
        emmm.material_guid as materialGuid,
        epmw.quantity as product_quantity,
        c_epmw.quantity as material_usage_quantity,
        get_unit_name(emmm.unit_guid) as material_unit_name,
        ecmc.customer_guid,
        ecmc.customer_short_name,
        (coalesce(c_epmw.quantity, 0) - coalesce(sub_table.total_quantity, 0)) as quantity,
        a.totalQuantity as inventoryTotalQuantity,
        eimmi.inventory_quantity
        from
        erp_production_mgt_workorder epmw
        left join erp_production_mgt_workorder p_epmw on p_epmw.workorder_guid = epmw.parent_classification_guid
        left join erp_production_mgt_workorder c_epmw on c_epmw.parent_classification_guid = epmw.workorder_guid
        left join erp_production_work_mgt_material_series epwmms on epwmms.workorder_guid = epmw.workorder_guid
        left join erp_basic_mgt_material_series ebmms on epwmms.material_series_guid = ebmms.material_series_guid
        left join erp_customer_mgt_customer ecmc on ecmc.customer_guid = c_epmw.customer_guid
        left join erp_material_mgt_material emmm on emmm.material_guid = c_epmw.material_guid
        left join erp_material_mgt_specification_value emmsv1 on emmm.material_guid=emmsv1.material_guid
        and specification_type_guid='a1a8e4972eba7fb28f7d8b9cc406a109'
        left join erp_material_mgt_material_classification emmmc2 on
        emmmc2.material_classification_guid=emmm.material_classification_guid
        left join erp_inventory_mgt_material_inventory eimmi on emmm.material_guid=eimmi.material_guid
        left join (select sum(inventory_quantity) as totalQuantity,SUBSTRING(emmm2.search_code FROM 1 FOR 2) as
        search_code,emmsv.specification_value from erp_inventory_mgt_material_inventory eimmi2
        left join erp_material_mgt_material emmm3 on emmm3.material_guid=eimmi2.material_guid
        left join erp_material_mgt_specification_value emmsv on emmm3.material_guid=emmsv.material_guid
        left join erp_material_mgt_material_classification emmm2 on
        eimmi2.material_classification_guid=emmm2.material_classification_guid
        where LENGTH(emmm2.search_code) &gt; 4 and specification_type_guid='a1a8e4972eba7fb28f7d8b9cc406a109'
        group by SUBSTRING(emmm2.search_code FROM 1 FOR 2),emmsv.specification_value
        ) as a on a.search_code=SUBSTRING(emmmc2.search_code FROM 1 FOR 2)
            and emmsv1.specification_value=a.specification_value
        left join erp_material_mgt_material_classification emmmc on emmmc.material_classification_guid =
        emmm.material_classification_guid
        left join (
        select
        source_guid,
        sum(quantity) as total_quantity
        from
        erp_production_mgt_workorder
        where
        workorder_properties =
        ${@com.xunyue.common.enums.WorkorderPropertiesEnum@PRODUCTION_FORECAST_SOURCE_BUSINESS_FORECASTING.getCode()}
        and parent_classification_guid is not null
        and source_guid is not null
        and deleted=false
        group by
        source_guid
        ) sub_table on sub_table.source_guid = epmw.workorder_guid
        where
        epmw.workorder_properties = ${@com.xunyue.common.enums.WorkorderPropertiesEnum@BUSINESS_FORECASTING.getCode()}
        and p_epmw.audit_status = '${@<EMAIL>()}'
        and epmw.parent_classification_guid is not null
        and emmm.material_type='1'
        and (c_epmw.workorder_state = '${@com.xunyue.common.enums.CompletionStatusEnum@NOT_FINISH.getCode()}'
        or c_epmw.workorder_state = '${@com.xunyue.common.enums.CompletionStatusEnum@PARTIALLY_FINISH.getCode()}')
        <if test="model.customerShortName != null and model.customerShortName != ''">
            and ecmc.customer_short_name like CONCAT('%',#{model.customerShortName},'%')
        </if>
        <if test="model.materialName != null and model.materialName != ''">
            and emmm.material_name like CONCAT('%',#{model.materialName},'%')
        </if>
        <if test="model.startDate != null">
            and to_char(epmw.required_delivery_time, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp ,
            'YYYY-MM-DD')
        </if>
        <if test="model.endDate != null">
            and to_char(epmw.required_delivery_time, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp ,
            'YYYY-MM-DD')
        </if>
        order by
        epmw.create_date
    </select>

    <!--采购预测采购情况-->
    <select id="getPurchaseState" resultType="java.lang.String">
        select
            case
                when aa.count = aa.un_count then '${@<EMAIL>()}'
                when aa.count = aa.terminate_count then '${@<EMAIL>()}'
                when aa.count = (aa.all_count+aa.terminate_count+aa.excess_count) then '${@<EMAIL>()}'
                else '${@<EMAIL>()}'
                end from (
                             select
                                 count(1) as count,
                                 (select count(1) from erp_production_mgt_workorder where parent_classification_guid = #{workorderGuid} and deleted = false
                                                                                      and workorder_state = '${@<EMAIL>()}') as un_count,
                                 (select count(1) from erp_production_mgt_workorder where parent_classification_guid = #{workorderGuid} and deleted = false
                                                                                      and workorder_state = '${@<EMAIL>()}') as all_count,
                                 (select count(1) from erp_production_mgt_workorder where parent_classification_guid = #{workorderGuid} and deleted = false
                                                                                      and workorder_state = '${@<EMAIL>()}') as terminate_count,
                                 (select count(1) from erp_production_mgt_workorder where parent_classification_guid = #{workorderGuid} and deleted = false
                                                                                      and workorder_state = '${@<EMAIL>()}') as excess_count
                             from erp_production_mgt_workorder where parent_classification_guid = #{workorderGuid} and deleted = false
                         ) as aa
    </select>

    <!--查询待开总数量-->
    <select id="selectCountPendingList" resultType="java.math.BigDecimal">
        SELECT
            COUNT(1)
        FROM
            erp_production_mgt_workorder epmw
            LEFT JOIN erp_production_mgt_workorder p_epmw ON p_epmw.workorder_guid = epmw.parent_classification_guid
            LEFT JOIN erp_production_mgt_workorder c_epmw ON c_epmw.parent_classification_guid = epmw.workorder_guid
        WHERE
            epmw.workorder_properties = ${@com.xunyue.common.enums.WorkorderPropertiesEnum@BUSINESS_FORECASTING.getCode()}
            AND epmw.parent_classification_guid IS NOT NULL
            AND p_epmw.audit_status = '${@<EMAIL>()}'
            AND (c_epmw.workorder_state = '${@<EMAIL>()}'
                     OR c_epmw.workorder_state = '${@<EMAIL>()}')
    </select>
    <select id="getPurchaseRequisition"
            resultType="xy.server.purchase.entity.ErpPurchaseMgtPurchaseRequisition">
select epmw3.* from erp_production_mgt_workorder epmw
         left join erp_production_mgt_workorder epmw2 on epmw2.source_guid=epmw.workorder_guid
         left join erp_production_mgt_workorder epmw3 on epmw2.parent_classification_guid=epmw3.workorder_guid
where epmw.parent_classification_guid=#{businessKey}
limit 1
</select>
</mapper>
