<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpPurchaseProductionMgtWorkorderOrderDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpPurchaseProductionMgtWorkorderOrderDataVO">
        <id column="workorder_order_data_guid" property="workorderOrderDataGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="workorder_guid" property="workorderGuid"/>
        <result column="order_data_guid" property="orderDataGuid"/>
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select *
        from erp_production_mgt_workorder_order_data
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select *
        from erp_production_mgt_workorder_order_data
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select *
        from erp_production_mgt_workorder_order_data
        where workorder_order_data_guid = #{guid}
    </select>
</mapper>
