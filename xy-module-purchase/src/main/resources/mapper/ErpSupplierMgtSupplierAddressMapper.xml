<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpSupplierMgtSupplierAddressMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierAddressVO">
        <id column="supplier_address_guid" property="supplierAddressGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="supplier_guid" property="supplierGuid"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="administrative_area_guid" property="administrativeAreaGuid"/>
        <result column="address" property="address"/>
        <result column="is_default" property="isDefault"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <association property="administrativeAreaVO" javaType="xy.server.basic.entity.model.vo.ErpBasicMgtAdministrativeAreaVO">
            <id column="administrative_area_guid" property="administrativeAreaGuid"/>
            <result column="b_tenant_guid" property="tenantGuid"/>
            <result column="b_administrative_area_name" property="administrativeAreaName"/>
            <result column="b_level" property="level"/>
            <result column="b_postal_code" property="postalCode"/>
            <result column="b_description" property="description"/>
            <result column="b_serial_number" property="serialNumber"/>
            <result column="b_parent_classification_guid" property="parentClassificationGuid"/>
            <result column="b_is_used" property="isUsed"/>
            <result column="b_state" property="state"/>
            <result column="b_creator_guid" property="creatorGuid"/>
            <result column="b_creator" property="creator"/>
            <result column="b_create_date" property="createDate"/>
            <result column="b_last_updater_guid" property="lastUpdaterGuid"/>
            <result column="b_last_updater" property="lastUpdater"/>
            <result column="b_last_update_date" property="lastUpdateDate"/>
            <result column="b_deleted" property="deleted"/>
            <result column="b_to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        </association>
    </resultMap>

    <sql id="baseSql">
        select
            a.*,
            b.tenant_guid as b_tenant_guid,
            b.administrative_area_name as b_administrative_area_name,
            b.level as b_level,
            b.postal_code as b_postal_code,
            b.description as b_description,
            b.serial_number as b_serial_number,
            b.parent_classification_guid as b_parent_classification_guid,
            b.is_used as b_is_used,
            b.state as b_state,
            b.creator_guid as b_creator_guid,
            b.creator as b_creator,
            b.create_date as b_create_date,
            b.last_updater_guid as b_last_updater_guid,
            b.last_updater as b_last_updater,
            b.last_update_date as b_last_update_date,
            b.deleted as b_deleted,
            b.to_json as b_to_json
        from erp_supplier_mgt_supplier_address a
                 left join erp_basic_mgt_administrative_area b on a.administrative_area_guid = b.administrative_area_guid
    </sql>
    <select id="findPage" resultMap="VoResultMap">
        <include refid="baseSql"/>
        <where>
            <if test="model.supplierGuid != '' and model.supplierGuid != null">
                and a.supplier_guid = #{model.supplierGuid}
            </if>
            <if test="model.keywords != null and model.keywords.size() != 0">
                and
                <foreach collection="model.keywords" item="item" index="index" open="(" separator="or" close=")" >
                    a.address ILIKE CONCAT('%',#{item},'%')
                    or b.administrative_area_name ILIKE CONCAT('%',#{item},'%')
                    or a.description ILIKE CONCAT('%',#{item},'%')
                </foreach>
            </if>
        </where>
        order by serial_number asc
    </select>

    <select id="findList" resultMap="VoResultMap">
        <include refid="baseSql"/>
        <where>
            <if test="model.supplierGuid != '' and model.supplierGuid != null">
                and a.supplier_guid = #{model.supplierGuid}
            </if>
            <if test="model.keywords != null and model.keywords.size() != 0">
                and
                <foreach collection="model.keywords" item="item" index="index" open="(" separator="or" close=")" >
                    a.address ILIKE CONCAT('%',#{item},'%')
                    or b.administrative_area_name ILIKE CONCAT('%',#{item},'%')
                    or a.description ILIKE CONCAT('%',#{item},'%')
                </foreach>
            </if>
        </where>
        order by serial_number asc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        <include refid="baseSql"/>
        where supplier_address_guid = #{guid}
    </select>

    <select id="getDataBySupplierGuids" resultMap="VoResultMap">
        <include refid="baseSql"/>
        where supplier_guid in
        <foreach collection="guids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
