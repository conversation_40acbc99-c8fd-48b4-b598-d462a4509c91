<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpOutgoingBusinessMgtOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpOutgoingBusinessMgtOrderVO">
        <id column="order_guid" property="orderGuid"/>
        <result column="order_number" property="orderNumber"/>
        <result column="order_type" property="orderType"/>
        <result column="customer_or_supplier_guid" property="customerOrSupplierGuid"/>
        <result column="contact_name" property="contactName"/>
        <result column="mobilephone" property="mobilephone"/>
        <result column="settlement_customer_or_supplier_guid" property="settlementCustomerOrSupplierGuid"/>
        <result column="decimal_method" property="decimalMethod"/>
        <result column="settlement_unit_price_keep_decimal_place" property="settlementUnitPriceKeepDecimalPlace"/>
        <result column="settlement_total_amount_keep_decimal_place" property="settlementTotalAmountKeepDecimalPlace"/>
        <result column="invoice_type_tax_rate_guid" property="invoiceTypeTaxRateGuid"/>
        <result column="currency_exchange_rate_guid" property="currencyExchangeRateGuid"/>
        <result column="is_urgent" property="isUrgent"/>
        <result column="print_times" property="printTimes"/>
        <result column="delivery_customer_or_supplier_guid" property="deliveryCustomerOrSupplierGuid"/>
        <result column="description" property="description"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="tax_rate" property="taxRate"/>
        <result column="exchange_rate" property="exchangeRate"/>
        <result column="currency_name" property="currencyName"/>
        <result column="is_local_currency" property="isLocalCurrency"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <association property="customerOrSupplierName"
                     javaType="java.lang.String"
                     column="{orderGuid=order_guid}"
                     select="xy.server.purchase.mapper.ErpOutgoingBusinessMgtOrderMapper.getSupplierName"/>
        <association property="settlementCustomerOrSupplierName"
                     javaType="java.lang.String"
                     column="{orderGuid=order_guid}"
                     select="xy.server.purchase.mapper.ErpOutgoingBusinessMgtOrderMapper.getSettlementSupplierName"/>
        <collection property="fileGuid" ofType="xy.server.purchase.entity.model.vo.FileGuidAndNameVO"
                    column="{orderGuid=order_guid}" select="xy.server.purchase.mapper.ErpOutgoingBusinessMgtOrderMapper.getOrderFileGuid"/>
        <collection property="specificList" ofType="xy.server.purchase.entity.model.vo.ErpOutgoingOrderDataVO"
                    column="{orderGuid=order_guid}" select="xy.server.purchase.mapper.ErpOutgoingBusinessMgtOrderMapper.getOrderDataList"/>
        <collection property="fileGuid" ofType="xy.server.purchase.entity.model.vo.FileGuidAndNameVO"
                    column="{orderGuid=order_guid}" select="xy.server.purchase.mapper.ErpOutgoingBusinessMgtOrderMapper.getFileList"/>
    </resultMap>

    <insert id="saveOrUpdateOtherExpensesBatch">
        <if test="otherExpensesList != null and otherExpensesList.size() != 0">
            <foreach collection="otherExpensesList" item="model" open="" separator=";" close="">
                INSERT INTO "public"."erp_business_mgt_other_expenses" (
                "tenant_guid",
                "other_expenses_guid",
                "source_guid",
                "source_valus",
                "other_expenses_type",
                "other_expenses_name",
                "description",
                "other_expenses_including_tax",
                "other_expenses_without_tax",
                "local_currency_other_expenses",
                "creator_guid",
                "creator",
                "create_date",
                "last_updater_guid",
                "last_updater",
                "last_update_date",
                "deleted" )
                values ( #{model.tenantGuid},#{model.otherExpensesGuid},#{model.sourceGuid},#{model.sourceValus},#{model.otherExpensesType},#{model.otherExpensesName}
                , #{model.description}, #{model.otherExpensesIncludingTax}, #{model.otherExpensesWithoutTax}, #{model.localCurrencyOtherExpenses},
                #{model.creatorGuid}, #{model.creator}, #{model.createDate}, #{model.lastUpdaterGuid}, #{model.lastUpdater}, #{model.lastUpdateDate},'f')
                on conflict(other_expenses_guid) do update set
                other_expenses_guid = #{model.otherExpensesGuid},
                source_guid = #{model.sourceGuid},
                source_valus = #{model.sourceValus},
                other_expenses_type = #{model.otherExpensesType},
                other_expenses_name = #{model.otherExpensesName},
                description = #{model.description},
                other_expenses_including_tax = #{model.otherExpensesIncludingTax},
                other_expenses_without_tax = #{model.otherExpensesWithoutTax},
                local_currency_other_expenses = #{model.localCurrencyOtherExpenses},
                last_updater_guid = #{model.lastUpdaterGuid},
                last_updater = #{model.lastUpdater},
                last_update_date = #{model.lastUpdateDate}
            </foreach>
        </if>
    </insert>


    <select id="findPage" resultMap="VoResultMap">
        select *
        from erp_business_mgt_order
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select *
        from erp_business_mgt_order
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select ebmo.*,
               case
                   when ebmo.currency_exchange_rate_guid = '1' then '1'
                   else ebmcer.exchange_rate end as exchange_rate
                ,
               case
                   when ebmo.invoice_type_tax_rate_guid = '1' then 0
                   else ebmittr.tax_rate end     as tax_rate,
               ebmc2.currency_name,
               ebmc2.is_local_currency
        from erp_business_mgt_order ebmo
                 left join erp_basic_mgt_currency_exchange_rate ebmcer on
            ebmcer.currency_exchange_rate_guid = ebmo.currency_exchange_rate_guid
                 left join erp_supplier_mgt_supplier ebmc
                           on ebmo.settlement_customer_or_supplier_guid = ebmc.supplier_guid
                 left join erp_basic_mgt_currency ebmc2 on ebmc2.currency_guid = ebmc.default_currency_guid
                 left join erp_basic_mgt_invoice_type_tax_rate ebmittr on
            ebmo.invoice_type_tax_rate_guid = ebmittr.invoice_type_tax_rate_guid
        where ebmo.order_guid = #{guid}
    </select>
    <select id="getSupplierName" resultType="java.lang.String">
        select esms.supplier_short_name
        from erp_business_mgt_order ebmo
                 left join erp_supplier_mgt_supplier esms on esms.supplier_guid=ebmo.customer_or_supplier_guid
        where ebmo.order_guid=#{orderGuid}
    </select>
    <select id="getSettlementSupplierName" resultType="java.lang.String">
        select esms.supplier_short_name
        from erp_business_mgt_order ebmo
                 left join erp_supplier_mgt_supplier esms on esms.supplier_guid=ebmo.settlement_customer_or_supplier_guid
        where ebmo.order_guid=#{orderGuid}
    </select>
    <select id="getOrderFileGuid" resultType="xy.server.purchase.entity.model.vo.FileGuidAndNameVO">
        select ebmof.file_guid fileGuid
        from erp_business_mgt_order ebmo
                 left join erp_business_mgt_order_file ebmof on ebmof.order_guid=ebmo.order_guid
        where ebmo.order_guid=#{orderGuid}
    </select>
    <select id="getOrderFileGuids" resultType="xy.server.purchase.entity.model.vo.FileGuidAndNameVO">
        select ebmof.file_guid fileGuid,
               ebmo.order_guid
        from erp_business_mgt_order ebmo
                 left join erp_business_mgt_order_file ebmof on ebmof.order_guid=ebmo.order_guid
        where ebmo.order_guid in
        <foreach collection="orderGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <!-- 通用查询映射结果 -->
    <resultMap id="DoResultMap" type="xy.server.purchase.entity.model.vo.ErpOutgoingOrderDataVO">
        <result column="applicationNumber" property="applicationNumber"/>
        <result column="workorderNumber" property="workorderNumber"/>
        <result column="productionProcessesTypeGuid" property="productionProcessesTypeGuid"/>
        <result column="totalQuantity" property="totalQuantity"/>
        <result column="partName" property="partName"/>
        <result column="productName" property="productName"/>
        <result column="productionWorkOrderNumber" property="productionWorkOrderNumber"/>
        <result column="workOrderProcessGuid" property="workOrderProcessGuid"/>
        <result column="quotationUnitPriceIncludingTax" property="quotationUnitPriceIncludingTax"/>
        <result column="quotationUnitPriceWithoutTax" property="quotationUnitPriceWithoutTax"/>
        <result column="totalAmountIncludingTax" property="totalAmountIncludingTax"/>
        <result column="totalAmountWithoutTax" property="totalAmountWithoutTax"/>
        <result column="unitPriceWithoutTax" property="unitPriceWithoutTax"/>
        <result column="unitPriceIncludingTax" property="unitPriceIncludingTax"/>
        <result column="settlementTotalAmount" property="settlementTotalAmount"/>
        <result column="settlementUnitPrice" property="settlementUnitPrice"/>
        <result column="localCurrencyTotalAmount" property="localCurrencyTotalAmount"/>
        <result column="localCurrencyUnitPrice" property="localCurrencyUnitPrice"/>
        <result column="deliveryDate" property="deliveryDate"/>
        <result column="description" property="description"/>
        <result column="quantity" property="quantity"/>
        <result column="completionStatus" property="completionStatus"/>
        <result column="workorderGuid" property="workorderGuid"/>
        <result column="address" property="address"/>
        <result column="administrativeAreaGuid" property="administrativeAreaGuid"/>
        <result column="administrativeAreaName" property="administrativeAreaName"/>
        <result column="order_state" property="orderState"/>
        <result column="cost_scheme_guid" property="costSchemeGuid"/>
        <result column="scheme_name" property="schemeName"/>
        <result column="order_state" property="orderState"/>
        <result column="specifications_length" property="specificationsLength"/>
        <result column="specifications_width" property="specificationsWidth"/>
        <result column="specifications_height" property="specificationsHeight"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <association property="materialObj"
                     javaType="xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO"
                     column="{materialGuid=material_guid}"
                     select="xy.server.material.mapper.ErpMaterialMgtMaterialMapper.getDataByGuid"/>
        <!--其他费用-->
        <collection property="otherExpensesList" ofType="xy.server.purchase.entity.model.vo.ErpBusinessOtherExpensesVO"
                    column="{model.sourceGuid=order_data_guid,model.sourceValus=other_source_valus}"
                    select="xy.server.basic.mapper.ErpBusinessMgtOtherExpensesMapper.findListGuid"/>
    </resultMap>
    <sql id="getOrderDataListSql">
        select ebmo.order_guid,
               epmw3.workorder_number                                applicationNumber,
               epmw1.workorder_number                                workorderNumber,
               epmw1.production_processes_type_guid                  productionProcessesTypeGuid,
               epmw1.total_quantity                                  totalQuantity,
               emmm2.material_name                                   partName,
               emmm.material_name                                    productName,
               epmw2.workorder_number                                productionWorkOrderNumber,
               epmw2.production_processes_description                productionProcessesDescription,
               ebmppt.production_processes_type_name                 processName,
               epmwdp.workorder_data_process_guid                    workOrderProcessGuid,
               epmwdp.workorder_guid                                 processWorkOrderGuid,
               ebmod.quotation_unit_price_including_tax              quotationUnitPriceIncludingTax,
               ebmod.quotation_unit_price_without_tax                quotationUnitPriceWithoutTax,
               ebmod.total_amount_including_tax                      totalAmountIncludingTax,
               ebmod.total_amount_without_tax                        totalAmountWithoutTax,
               ebmod.unit_price_without_tax                       as unitPriceWithoutTax,
               ebmod.unit_price_including_tax                     as unitPriceIncludingTax,
               ebmod.settlement_total_amount                      as settlementTotalAmount,
               ebmod.settlement_unit_price                        as settlementUnitPrice,
               ebmod.local_currency_total_amount                  as localCurrencyTotalAmount,
               ebmod.local_currency_unit_price                    as localCurrencyUnitPrice,
               ebmod.delivery_date                                   deliveryDate,
               ebmod.description                                  as description,
               ebmod.quantity                                     as quantity,
               ebmod.completion_status                               completionStatus,
               epmw.parent_classification_guid                       workorderGuid,
               ebmopod.address                                    as address,
               ebmopod.administrative_area_guid                      administrativeAreaGuid,
               ebmaa.administrative_area_name                     as administrativeAreaName,
               ebmod.to_json                                      as to_json,
               ebmod.order_state                                  as order_state,
               ebmod.cost_scheme_guid                             as cost_scheme_guid,
               ecs.scheme_name                                    as scheme_name,
               '3'                                                as other_source_valus,
               ebmod.order_data_guid,
               epmw1.specifications_length,
               epmw1.specifications_width,
               epmw1.specifications_height,
               ebmod.material_guid,
               epmw2.quantity                                        processCount,
               epmwdp.loss_quantity + epmwdp.adjust_loss_quantity AS lossCount,
               epmw2.total_quantity                                   totality
        from erp_business_mgt_order ebmo
                 left join erp_business_mgt_order_data ebmod on ebmod.order_guid = ebmo.order_guid
--            中间子工单
                 left join erp_production_mgt_workorder epmw on epmw.workorder_guid = ebmod.source_guid
            --申请单子工单
                 left join erp_production_mgt_workorder epmw1 on epmw1.workorder_guid = epmw.parent_classification_guid
            --申请单的主工单
                 left join erp_production_mgt_workorder epmw3 on epmw3.workorder_guid = epmw1.parent_classification_guid
            --申请单子工单的工序工单
                 left join erp_production_mgt_workorder_data_process epmwdp
                           on epmwdp.workorder_data_process_guid = epmw1.source_guid
            --工序工单对应的工单
                 left join erp_production_mgt_workorder epmw2 on epmw2.workorder_guid = epmwdp.workorder_guid
                 left join erp_material_mgt_material emmm on epmw1.product_material_guid = emmm.material_guid
                 left join erp_production_mgt_workorder epmw4 on epmw2.parent_classification_guid = epmw4.workorder_guid
                 left join erp_material_mgt_material emmm2 on emmm2.material_guid = epmw4.material_guid
                 left join erp_basic_mgt_production_processes_type ebmppt
                           on epmw1.production_processes_type_guid = ebmppt.production_processes_type_guid
                 left join erp_business_mgt_order_place_of_delivery ebmopod
                           on ebmopod.order_data_guid = ebmod.order_data_guid
                 left join erp_basic_mgt_administrative_area ebmaa
                           on ebmaa.administrative_area_guid = ebmopod.administrative_area_guid
                 left join erp_basic_mgt_unit ebmu on ebmu.unit_guid = output_unit_guid
                 left join erp_cost_scheme ecs on ecs.cost_scheme_guid = ebmod.cost_scheme_guid
    </sql>
    <select id="getOrderDataList" resultMap="DoResultMap">
        <include refid="getOrderDataListSql">
        </include>
        where ebmo.order_guid = #{orderGuid}
          and ebmod.deleted = false
          and epmw.deleted = false
    </select>

    <resultMap id="DoResultMapNoDetail" type="xy.server.purchase.entity.model.vo.ErpOutgoingOrderDataVO">
        <result column="order_guid" property="orderGuid"/>
        <result column="order_data_guid" property="orderDataGuid"/>
        <result column="applicationNumber" property="applicationNumber"/>
        <result column="workorderNumber" property="workorderNumber"/>
        <result column="productionProcessesTypeGuid" property="productionProcessesTypeGuid"/>
        <result column="totalQuantity" property="totalQuantity"/>
        <result column="partName" property="partName"/>
        <result column="productName" property="productName"/>
        <result column="productionWorkOrderNumber" property="productionWorkOrderNumber"/>
        <result column="workOrderProcessGuid" property="workOrderProcessGuid"/>
        <result column="quotationUnitPriceIncludingTax" property="quotationUnitPriceIncludingTax"/>
        <result column="quotationUnitPriceWithoutTax" property="quotationUnitPriceWithoutTax"/>
        <result column="totalAmountIncludingTax" property="totalAmountIncludingTax"/>
        <result column="totalAmountWithoutTax" property="totalAmountWithoutTax"/>
        <result column="unitPriceWithoutTax" property="unitPriceWithoutTax"/>
        <result column="unitPriceIncludingTax" property="unitPriceIncludingTax"/>
        <result column="settlementTotalAmount" property="settlementTotalAmount"/>
        <result column="settlementUnitPrice" property="settlementUnitPrice"/>
        <result column="localCurrencyTotalAmount" property="localCurrencyTotalAmount"/>
        <result column="localCurrencyUnitPrice" property="localCurrencyUnitPrice"/>
        <result column="deliveryDate" property="deliveryDate"/>
        <result column="description" property="description"/>
        <result column="quantity" property="quantity"/>
        <result column="completionStatus" property="completionStatus"/>
        <result column="workorderGuid" property="workorderGuid"/>
        <result column="address" property="address"/>
        <result column="administrativeAreaGuid" property="administrativeAreaGuid"/>
        <result column="administrativeAreaName" property="administrativeAreaName"/>
        <result column="order_state" property="orderState"/>
        <result column="cost_scheme_guid" property="costSchemeGuid"/>
        <result column="scheme_name" property="schemeName"/>
        <result column="specifications_length" property="specificationsLength"/>
        <result column="specifications_width" property="specificationsWidth"/>
        <result column="specifications_height" property="specificationsHeight"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>

    </resultMap>
    <select id="getOrderDataListBatch" resultMap="DoResultMapNoDetail">
        <include refid="getOrderDataListSql">
        </include>
        where ebmo.order_guid in
        <foreach collection="orderGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and ebmod.deleted = false
        and epmw.deleted = false
    </select>

    <select id="selectOutgoing" resultType="java.lang.String">
        select ebmo.order_guid from erp_business_mgt_order ebmo
        left join erp_business_mgt_order_data ebmod on ebmod.order_guid = ebmo.order_guid
        -- 中间子工单
        left join erp_production_mgt_workorder epmw on epmw.workorder_guid = ebmod.source_guid
        --申请单子工单
        left join erp_production_mgt_workorder epmw1 on epmw1.workorder_guid = epmw.parent_classification_guid
        --申请单的主工单
        left join erp_production_mgt_workorder epmw3 on epmw3.workorder_guid = epmw1.parent_classification_guid
        --申请单子工单的工序工单
        left join erp_production_mgt_workorder_data_process epmwdp
        on (epmwdp.workorder_data_process_guid = epmw1.source_guid or epmwdp.workorder_guid = epmw1.source_guid)
        --工序工单对应的工单
        left join erp_production_mgt_workorder epmw2 on epmw2.workorder_guid = epmwdp.workorder_guid
        left join erp_material_mgt_material emmm on epmw2.product_material_guid = emmm.material_guid
        -- 结算加工商和加工商
        left join erp_supplier_mgt_supplier sebmc on sebmc.supplier_guid = ebmo.settlement_customer_or_supplier_guid
        left join erp_supplier_mgt_supplier ebmc on ebmc.supplier_guid = ebmo.customer_or_supplier_guid
        <where>
            ebmo.order_properties =3
            <if test="qo.processInstanceIds != null and qo.processInstanceIds.size() != 0 ">
                and ebmo.process_instance_id in
                <foreach item="item" collection="qo.processInstanceIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="qo.startTime !=null and qo.endTime !=null">
                and (ebmo.create_date &gt;= #{qo.startTime}
                and ebmo.create_date &lt;= #{qo.endTime})
            </if>
            <if test="qo.keywords!=null  and  qo.keywords.size() > 0">
                and
                <foreach collection="qo.keywords" item="key" separator="or" open="(" close=")">
                    ebmo.order_number ILIKE CONCAT('%',#{key},'%')
                </foreach>
            </if>
            <if test="qo.workorderTypeGuid!=null  and  qo.workorderTypeGuid.size() > 0">
                and ebmo.order_type in
                <foreach collection="qo.workorderTypeGuid" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="qo.workorderState!=null  and  qo.workorderState.size() > 0">
                and
                <foreach collection="qo.workorderState" item="key" separator="or" open="(" close=")">
                    ebmod.completion_status=#{key}
                </foreach>
            </if>
            <if test="qo.auditStatus != null and qo.auditStatus.size() > 0 ">
                and ebmo.audit_status in
                <foreach item="item" collection="qo.auditStatus" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="qo.creator != null and qo.creator != '' " >
                and ebmo.creator ILIKE CONCAT('%',#{qo.creator},'%')
            </if>

            <if test="qo.outgoingApplyOrderNumber != null and qo.outgoingApplyOrderNumber != '' ">
                and epmw3.workorder_number ILIKE CONCAT('%',#{qo.outgoingApplyOrderNumber},'%')
            </if>

            <if test="qo.productionWorkOrderNumber != null and qo.productionWorkOrderNumber != '' ">
                and epmw2.workorder_number ILIKE CONCAT('%',#{qo.productionWorkOrderNumber},'%')
            </if>

            <if test="qo.production != null and qo.production != ''" >
                and (emmm.material_name ILIKE CONCAT('%',#{qo.production},'%') or emmm.material_code ILIKE CONCAT('%',#{qo.production},'%'))
            </if>

            <if test="qo.processSupplier != null and qo.processSupplier != '' " >
                and (ebmc.supplier_short_name ILIKE CONCAT('%',#{qo.processSupplier},'%') or ebmc.supplier_full_name ILIKE CONCAT('%',#{qo.processSupplier},'%') or ebmc.supplier_code ILIKE CONCAT('%',#{qo.processSupplier},'%'))
            </if>

            <if test="qo.settlementSupplier != null and qo.settlementSupplier != '' " >
                and (sebmc.supplier_short_name ILIKE CONCAT('%',#{qo.settlementSupplier},'%') or sebmc.supplier_full_name ILIKE CONCAT('%',#{qo.settlementSupplier},'%') or sebmc.supplier_code ILIKE CONCAT('%',#{qo.settlementSupplier},'%'))
            </if>

            <if test="qo.businessKeys != null and qo.businessKeys.size()>0">
                and ebmo.order_guid in
                <foreach collection="qo.businessKeys" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        </where>
        group by ebmo.order_guid  , ebmo.create_date
        order by ebmo.create_date desc
    </select>
    <select id="getFileList" resultType="xy.server.purchase.entity.model.vo.FileGuidAndNameVO">
        select ebmof.file_guid fileGuid,ebmf.file_name fileName
        from erp_business_mgt_order ebmo
                 left join erp_business_mgt_order_file ebmof on ebmo.order_guid = ebmof.order_guid
        left join erp_basic_mgt_file ebmf on ebmf.file_guid=ebmof.file_guid
        where ebmo.order_guid=#{orderGuid}
    </select>
    <select id="getFileListBatch" resultType="xy.server.purchase.entity.model.vo.FileGuidAndNameVO">
        select ebmof.file_guid fileGuid,ebmf.file_name fileName,ebmo.order_guid
        from erp_business_mgt_order ebmo
                 left join erp_business_mgt_order_file ebmof on ebmo.order_guid = ebmof.order_guid
                 left join erp_basic_mgt_file ebmf on ebmf.file_guid=ebmof.file_guid
        where ebmo.order_guid in
        <foreach collection="orderGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <resultMap id="SoResultMap" type="xy.server.purchase.entity.model.vo.ErpProcessOutgoingOrderVO">
        <result column="order_number" property="orderNumber"/>
        <result column="workorder_number" property="workorderNumber"/>
        <result column="order_data_guid" property="sourceGuid"/>
        <result column="description" property="description"/>
        <result column="product_material_guid" property="materialGuid"/>
        <result column="partName" property="partName"/>
        <result column="production_processes_type_guid" property="productionProcessesTypeGuid"/>
        <result column="production_processes_type_name" property="processName"/>
        <result column="quantity" property="quantity"/>
        <result column="loss_quantity" property="lossQuantity"/>
        <result column="output_quantity" property="outputQuantity"/>
        <result column="required_delivery_time" property="requiredDeliveryTime"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="quotation_unit_price_including_tax" property="quotationUnitPriceIncludingTax"/>
        <result column="quotation_unit_price_without_tax" property="quotationUnitPriceWithoutTax"/>
        <result column="total_amount_without_tax" property="totalAmountWithoutTax"/>
        <result column="total_amount_including_tax" property="totalAmountIncludingTax"/>
        <result column="workorder_data_process_guid" property="workOrderProcessGuid"/>
        <result column="contact_name" property="contactName"/>
        <result column="mobilephone" property="mobilephone"/>
        <result column="advance_payment_detail_guid" property="advancePaymentDetailGuid"/>
        <association property="productName"
                     javaType="java.lang.String"
                     column="{workOrderProcessGuid=workorder_data_process_guid}"
                     select="xy.server.purchase.mapper.ErpOutgoingMgtApplicationMapper.selectproductName"/>
    </resultMap>
    <select id="processFindList" resultMap="SoResultMap">
        select distinct ebmo.order_number,
        epmw.workorder_number,
        epmw.product_material_guid,
        epmw.production_processes_type_guid,
        epmw.required_delivery_time,
        epmw.total_quantity,
        epmw2.description as partName,
        emmm1.material_name productName,
        ebmod.order_data_guid,
        ebmod.description,
        ebmod.quantity,
        ebmo.contact_name,
        ebmo.mobilephone,
        ebmod.quotation_unit_price_including_tax,
        ebmod.quotation_unit_price_without_tax,
        ebmod.total_amount_without_tax,
        ebmod.total_amount_including_tax,
        ebmppt.production_processes_type_name,
        epmwdp.loss_quantity,
        epmwdp.output_quantity,
        epmwdp.workorder_data_process_guid,
        efmapd.advance_payment_detail_guid
        from erp_business_mgt_order ebmo
        left join erp_business_mgt_order_data ebmod on ebmo.order_guid = ebmod.order_guid
        left join erp_finance_mgt_advance_payment_detail efmapd on efmapd.source_guid=ebmod.order_data_guid
        left join erp_production_mgt_workorder_order_data epmwod
        on ebmod.order_data_guid = epmwod.order_data_guid
        left join erp_production_mgt_workorder_data_process epmwdp on
        epmwdp.workorder_guid=epmwod.workorder_guid
        left join erp_production_mgt_workorder epmw on epmw.workorder_guid=epmwdp.workorder_guid
        left join erp_production_mgt_workorder epmw2 on epmw2.workorder_guid=epmw.parent_classification_guid
        left join erp_material_mgt_material emmm on epmw.product_material_guid = emmm.material_guid
        left join erp_material_mgt_material emmm1 on epmw.material_guid = emmm1.material_guid
        left join erp_basic_mgt_production_processes_type ebmppt
        on epmw.production_processes_type_guid = ebmppt.production_processes_type_guid
        where ebmo.order_properties = 3 and ebmod.deleted=false and ebmo.deleted=false
        <if test="qo.orderNumber!=null  and  qo.orderNumber!=''">
            and ebmo.order_number ILIKE CONCAT('%',#{qo.orderNumber},'%')
        </if>
        <if test="qo.workorderNumber!=null  and  qo.workorderNumber!=''">
            and epmw.workorder_number ILIKE CONCAT('%',#{qo.workorderNumber},'%')
        </if>
        <if test="qo.partName!=null  and  qo.partName!=''">
            and emmm.material_name ILIKE CONCAT('%',#{qo.partName},'%')
        </if>
        <if test="qo.processName!=null  and  qo.processName!=''">
            and ebmppt.production_processes_type_name ILIKE CONCAT('%',#{qo.processName},'%')
        </if>
        <if test="qo.startTime !=null and qo.endTime !=null">
            and (ebmo.create_date &gt;= #{qo.startTime}
            and ebmo.create_date &lt;= #{qo.endTime})
        </if>
    </select>
    <!--待开列表结果集映射-->
    <resultMap id="WorkorderVOResultMap" type="xy.server.purchase.entity.model.vo.ErpPurchaseInventoryWorkorderDetailVO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="workorder_number" property="workorderNumber"/>
        <result column="parent_classification_guid" property="parentClassificationGuid"/>
        <result column="source_value" property="sourceValue"/>
        <result column="source_guid" property="sourceGuid"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="quantity" property="quantity"/>
        <result column="not_billed_quantity" property="notBilledQuantity"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="unit_guid" property="unitGuid"/>
        <result column="required_delivery_time" property="requiredDeliveryTime"/>
        <result column="description" property="description"/>
        <result column="unit_name" property="unitName"/>
        <result column="tax_rate" property="taxRate"/>
        <result column="exchange_rate" property="exchangeRate"/>
        <result column="currency_name" property="currencyName"/>
        <result column="currency_guid" property="currencyGuid"/>
        <result column="is_local_currency" property="isLocalCurrency"/>
        <result column="quotation_unit_price_including_tax" property="quotationUnitPriceIncludingTax"/>
        <result column="total_amount_including_tax" property="totalAmountIncludingTax"/>
        <result column="quotation_unit_price_without_tax" property="quotationUnitPriceWithoutTax"/>
        <result column="total_amount_without_tax" property="totalAmountWithoutTax"/>
        <result column="unit_price_including_tax" property="unitPriceIncludingTax"/>
        <result column="unit_price_without_tax" property="unitPriceWithoutTax"/>
        <result column="local_currency_unit_price" property="localCurrencyUnitPrice"/>
        <result column="local_currency_total_amount" property="localCurrencyTotalAmount"/>
        <result column="settlement_unit_price" property="settlementUnitPrice"/>
        <result column="settlement_total_amount" property="settlementTotalAmount"/>
        <result column="settlement_customer_or_supplier_guid" property="settlementCustomerOrSupplierGuid"/>
        <result column="customer_or_supplier_guid" property="parentCustomerGuid"/>
        <result column="customer_or_supplier_guid" property="customerGuid"/>
        <result column="settlementCustomerOrSupplierName" property="settlementCustomerOrSupplierName"/>
        <result column="customerOrSupplierName" property="customerOrSupplierName"/>
        <result column="cost_scheme_guid" property="costSchemeGuid"/>
        <result column="scheme_name" property="schemeName"/>
        <result column="specificationsLength" property="specificationsLength"/>
        <result column="specificationsWidth" property="specificationsWidth"/>
        <result column="specificationsHeight" property="specificationsHeight"/>
        <!--物料Obj-->
<!--        <association property="materialObj" javaType="xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO"-->
<!--                     column="{guid=material_guid}" select="xy.server.material.mapper.ErpMaterialMgtMaterialMapper.getDataByGuid"/>-->
        <!--<association property="outgoingOrderVO" javaType="xy.server.purchase.entity.model.vo.ErpOutgoingArrivalVO"
                     column="sourceGuid=source_guid" select="xy.server.purchase.mapper.ErpOutgoingBusinessMgtOrderMapper.getOutgoingOrderVO"/>-->
    </resultMap>

    <select id="findNotBilledList" resultMap="WorkorderVOResultMap">
        select * from (
        SELECT
        wo.*,
        case
        when ebmo.currency_exchange_rate_guid = '1' then '1'
        else ebmcer.exchange_rate end as exchange_rate,
        case
        when ebmo.invoice_type_tax_rate_guid = '1' then 0
        else ebmittr.tax_rate end as tax_rate,
        ebmc2.currency_name,
        wo.workorder_number as source_number,
        wo.quantity as source_quantity,
        u.unit_name,
        ( COALESCE ( wo.quantity, 0 ) - COALESCE (pwo1.pwo1SumQuantity,0 ) )AS
        not_billed_quantity,
        ebmod.quotation_unit_price_including_tax ,
        ebmod.quotation_unit_price_without_tax ,
        ebmod.total_amount_including_tax ,
        ebmod.total_amount_without_tax ,
        ebmod.unit_price_without_tax,
        ebmod.unit_price_including_tax,
        ebmod.settlement_total_amount,
        ebmod.settlement_unit_price,
        ebmod.local_currency_total_amount,
        ebmod.local_currency_unit_price,
        ebmc2.is_local_currency,
        epmw3.specifications_length as specificationsLength,
        epmw3.specifications_width as specificationsLength,
        epmw3.specifications_height as specificationsHeight,
        ebmo.customer_or_supplier_guid,
        ebmo.settlement_customer_or_supplier_guid,
        ebmc.supplier_short_name as settlementCustomerOrSupplierName,
        esms1.supplier_short_name as CustomerOrSupplierName,
        ebmo.invoice_type_tax_rate_guid,
        ebmo.currency_exchange_rate_guid,
        ebmod.cost_scheme_guid,
        ecs.scheme_name,
        ebmc2.currency_guid
        FROM
        erp_production_mgt_workorder wo

        -- 暂时优化一下这里，随着外发到货单数量越来越多，一样会卡
        LEFT JOIN (
        SELECT
        SUM( epmw.quantity ) AS pwo1SumQuantity,
        epmwf.parent_classification_guid
        FROM
        erp_production_mgt_workorder epmw
        LEFT JOIN erp_production_mgt_workorder epmwf on epmwf.workorder_guid=epmw.source_guid
        WHERE
        epmw.workorder_properties = 29
        and epmw.source_guid is not null
        GROUP BY
        epmwf.parent_classification_guid
        ) pwo1 ON pwo1.parent_classification_guid = wo.workorder_guid
        LEFT JOIN erp_production_mgt_workorder pwo ON wo.parent_classification_guid = pwo.workorder_guid
        left join erp_business_mgt_order_data ebmod on wo.source_guid=ebmod.order_data_guid
        left join erp_production_mgt_workorder epmw2 on ebmod.source_guid=epmw2.workorder_guid
        left join erp_production_mgt_workorder epmw3 on epmw2.parent_classification_guid=epmw3.workorder_guid
        left join erp_business_mgt_order ebmo on ebmod.order_guid=ebmo.order_guid
        left join erp_basic_mgt_currency_exchange_rate ebmcer on
        ebmcer.currency_exchange_rate_guid = ebmo.currency_exchange_rate_guid
        left join erp_supplier_mgt_supplier ebmc
        on ebmo.settlement_customer_or_supplier_guid = ebmc.supplier_guid
        left join erp_basic_mgt_currency ebmc2 on ebmc2.currency_guid = ebmc.default_currency_guid
        left join erp_basic_mgt_invoice_type_tax_rate ebmittr on
        ebmo.invoice_type_tax_rate_guid = ebmittr.invoice_type_tax_rate_guid
        LEFT JOIN (
        SELECT epmw.material_guid,min(eodwor.order_data_guid) order_data_guid  FROM
        erp_production_mgt_workorder_order_data eodwor
        LEFT JOIN erp_production_mgt_workorder epmw ON epmw.workorder_guid = eodwor.workorder_guid and epmw.workorder_properties='29'
        GROUP BY  epmw.material_guid
        ) epmw4 ON epmw4.order_data_guid = wo.source_guid
        LEFT JOIN erp_material_mgt_material m ON epmw4.material_guid = m.material_guid

        LEFT JOIN erp_material_mgt_material emmm ON wo.material_guid = emmm.material_guid

        LEFT JOIN erp_basic_mgt_unit u ON u.unit_guid = m.unit_guid
        left join erp_supplier_mgt_supplier esms1 on ebmo.customer_or_supplier_guid=esms1.supplier_guid
        left join erp_cost_scheme ecs on ebmod.cost_scheme_guid=ecs.cost_scheme_guid
        WHERE
        wo.parent_classification_guid IS NOT NULL
        and wo.deleted=false
        AND wo.workorder_properties =
        ${@com.xunyue.common.enums.WorkorderPropertiesEnum@MATERIAL_DELIVERY_FATHER.getCode()}
        and ebmod.order_state !='3'
        AND wo.source_value = '${@com.xunyue.common.enums.DeliveryOrderDataSourceEnum@OUTGOING_ORDER.getCode()}'
        <if test="model.orderNumber != null and model.orderNumber != ''">
            AND (wo.workorder_number ILIKE CONCAT('%',#{model.orderNumber},'%')
<!-- 报错了这里不知道是要查询什么表先注释掉了（没有from epmw这个表的别名） or epmw.workorder_number ILIKE CONCAT('%',#{model.orderNumber},'%')-->
            or m.material_name ILIKE CONCAT('%',#{model.orderNumber},'%'))
        </if>
        <if test="model.sourceNumber != null and model.sourceNumber != ''">
            AND wo.workorder_number ILIKE CONCAT('%',#{model.sourceNumber},'%')
        </if>
        <if test="model.materialName != null and model.materialName != ''">
            AND emmm.material_name ILIKE CONCAT('%',#{model.materialName},'%')
        </if>
        <if test="model.startDate != null">
            AND to_char(pwo.receipt_date, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp , 'YYYY-MM-DD')
        </if>
        <if test="model.endDate != null">
            AND to_char(pwo.receipt_date, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp , 'YYYY-MM-DD')
        </if>
        ORDER BY
        required_delivery_time
        ) t1
        -- 过滤未开的单
        where t1.not_billed_quantity > 0
    </select>
    <resultMap id="OoResultMap" type="xy.server.purchase.entity.model.vo.ErpOutgoingArrivalVO">
        <result column="order_number" property="orderNumber"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="product_material_guid" property="productMaterialGuid"/>
        <result column="partName" property="partName"/>
        <result column="productName" property="productName"/>
        <result column="production_processes_type_guid" property="productionProcessesTypeGuid"/>
        <result column="production_processes_type_name" property="processName"/>
        <result column="quantity" property="quantity"/>
        <result column="quantity" property="sourceQuantity"/>
        <result column="delivery_date" property="requiredDeliveryTime"/>
        <result column="workOrderProcessGuid" property="workOrderProcessGuid"/>
        <result column="order_data_guid" property="orderDataGuid"/>
        <result column="actProductMaterialGuid" property="actMaterialGuid"/>
        <result column="actMaterialName" property="actMaterialName"/>
        <result column="sourceProcessNumber" property="sourceProcessNumber"/>
        <collection property="sonList"
                    ofType="xy.server.purchase.entity.model.vo.ErpOutgoingApplicationVO"
                     column="{workorderGuid=workOrderProcessGuid,quantity=quantity}"
                     select="xy.server.purchase.mapper.ErpOutgoingMgtApplicationMapper.getSonList"/>
    </resultMap>
    <sql id="getOutgoingOrderVOSql">
        select distinct ebmo.order_number,
                        epmw.product_material_guid,
                        epmw.production_processes_type_guid,
                        epmw.workorder_number       as sourceProcessNumber,
                        ebmod.material_guid,
                        epmw.required_delivery_time,
                        epmw.total_quantity,
                        emmm1.material_name           as partName,
                        emmm.material_name         as productName,
                        ebmod.order_data_guid,
                        ebmod.description,
                        ebmod.quantity,
                        ebmppt.production_processes_type_name,
                        epmwdp.output_specifications_length,
                        epmwdp.output_specifications_width,
                        epmwdp.output_specifications_height,
                        epmwdp.output_unit_guid     as unitGuid,
                        epmwdp.workorder_guid       as workOrderProcessGuid,
                        ebmod.delivery_date,
                        epmw4.product_material_guid as actMaterialGuid,
                        epmw4.proportion            as proportion,
                        emmm2.material_name         as actMaterialName
        from erp_business_mgt_order_data ebmod
                 left join erp_business_mgt_order ebmo on ebmo.order_guid = ebmod.order_guid
                 left join erp_production_mgt_workorder epmw3 on ebmod.source_guid = epmw3.workorder_guid
                 left join erp_production_mgt_workorder epmw4 on epmw3.parent_classification_guid = epmw4.workorder_guid
                 left join erp_material_mgt_material emmm2 on emmm2.material_guid = epmw4.product_material_guid
                 left join erp_production_mgt_workorder_order_data epmwod
                           on ebmod.order_data_guid = epmwod.order_data_guid
                 left join erp_production_mgt_workorder epmw on epmw.workorder_guid = epmwod.workorder_guid
                 left join erp_production_mgt_workorder_data_process epmwdp on
            epmwdp.workorder_guid = epmw.workorder_guid
                 left join erp_production_mgt_workorder epmw2 on epmw2.workorder_guid = epmw.parent_classification_guid
                 left join erp_material_mgt_material emmm on epmw.product_material_guid = emmm.material_guid
                 left join erp_material_mgt_material emmm1 on epmw2.material_guid = emmm1.material_guid
                 left join erp_basic_mgt_production_processes_type ebmppt
                           on epmw.production_processes_type_guid = ebmppt.production_processes_type_guid
    </sql>
     <select id="getOutgoingOrderVO" resultMap="OoResultMap">
         <include refid="getOutgoingOrderVOSql">
         </include>
         where ebmod.order_data_guid = #{sourceGuid}
          and epmw.workorder_properties = 2
          and epmwdp.deleted = false
          and ebmod.deleted = false
    </select>
    <resultMap id="OoResultMapNoDetail" type="xy.server.purchase.entity.model.vo.ErpOutgoingArrivalVO">
        <result column="order_number" property="orderNumber"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="product_material_guid" property="productMaterialGuid"/>
        <result column="partName" property="partName"/>
        <result column="productName" property="productName"/>
        <result column="production_processes_type_guid" property="productionProcessesTypeGuid"/>
        <result column="production_processes_type_name" property="processName"/>
        <result column="quantity" property="quantity"/>
        <result column="quantity" property="sourceQuantity"/>
        <result column="delivery_date" property="requiredDeliveryTime"/>
        <result column="workOrderProcessGuid" property="workOrderProcessGuid"/>
        <result column="order_data_guid" property="orderDataGuid"/>
        <result column="actProductMaterialGuid" property="actMaterialGuid"/>
        <result column="actMaterialName" property="actMaterialName"/>
        <result column="sourceProcessNumber" property="sourceProcessNumber"/>
    </resultMap>
    <select id="getOutgoingOrderVOBatch" resultMap="OoResultMapNoDetail">
        <include refid="getOutgoingOrderVOSql">
        </include>
        where ebmod.order_data_guid in
        <foreach collection="sourceGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and epmw.workorder_properties = 2
        and epmwdp.deleted = false
        and ebmod.deleted = false
    </select>
    <select id="getWaitingQuantity" resultType="java.math.BigDecimal">
        SELECT
            COUNT(1)
        FROM
            erp_production_mgt_workorder pwo
        WHERE
                pwo.workorder_properties = ${@<EMAIL>()}
          AND pwo.audit_status = '${@<EMAIL>()}'
          AND (select COUNT(1)
               from erp_production_mgt_workorder cwo
               where cwo.parent_classification_guid = pwo.workorder_guid
                 and coalesce((select coalesce(sum(ebmpld.quantity), 0)
                               from erp_production_mgt_workorder ebmpld
                               where cwo.workorder_guid = ebmpld.parent_classification_guid
                                 and ebmpld.deleted = false
                               group by cwo.workorder_guid), 0) &lt; cwo.total_quantity) > 0
    </select>

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMapNoDetail" type="xy.server.purchase.entity.model.vo.ErpOutgoingBusinessMgtOrderVO">
        <id column="order_guid" property="orderGuid"/>
        <result column="order_number" property="orderNumber"/>
        <result column="order_type" property="orderType"/>
        <result column="customer_or_supplier_guid" property="customerOrSupplierGuid"/>
        <result column="contact_name" property="contactName"/>
        <result column="mobilephone" property="mobilephone"/>
        <result column="settlement_customer_or_supplier_guid" property="settlementCustomerOrSupplierGuid"/>
        <result column="decimal_method" property="decimalMethod"/>
        <result column="settlement_unit_price_keep_decimal_place" property="settlementUnitPriceKeepDecimalPlace"/>
        <result column="settlement_total_amount_keep_decimal_place" property="settlementTotalAmountKeepDecimalPlace"/>
        <result column="invoice_type_tax_rate_guid" property="invoiceTypeTaxRateGuid"/>
        <result column="currency_exchange_rate_guid" property="currencyExchangeRateGuid"/>
        <result column="is_urgent" property="isUrgent"/>
        <result column="print_times" property="printTimes"/>
        <result column="delivery_customer_or_supplier_guid" property="deliveryCustomerOrSupplierGuid"/>
        <result column="description" property="description"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="tax_rate" property="taxRate"/>
        <result column="exchange_rate" property="exchangeRate"/>
        <result column="currency_name" property="currencyName"/>
        <result column="is_local_currency" property="isLocalCurrency"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
    </resultMap>

    <select id="findDataByGuids" resultMap="VoResultMapNoDetail">
        select ebmo.*,
            case
            when ebmo.currency_exchange_rate_guid = '1' then '1'
            else ebmcer.exchange_rate end as exchange_rate
            ,
            case
            when ebmo.invoice_type_tax_rate_guid = '1' then 0
            else ebmittr.tax_rate end     as tax_rate,
            ebmc2.currency_name,
            ebmc2.is_local_currency,
            esms.supplier_short_name as customerOrSupplierName,
            esms_customer.supplier_short_name as settlementCustomerOrSupplierName
        from erp_business_mgt_order ebmo
        left join erp_basic_mgt_currency_exchange_rate ebmcer on
        ebmcer.currency_exchange_rate_guid = ebmo.currency_exchange_rate_guid
        left join erp_supplier_mgt_supplier ebmc
        on ebmo.settlement_customer_or_supplier_guid = ebmc.supplier_guid
        left join erp_basic_mgt_currency ebmc2 on ebmc2.currency_guid = ebmc.default_currency_guid
        left join erp_basic_mgt_invoice_type_tax_rate ebmittr on
        ebmo.invoice_type_tax_rate_guid = ebmittr.invoice_type_tax_rate_guid
        left join erp_supplier_mgt_supplier esms on esms.supplier_guid=ebmo.customer_or_supplier_guid
        left join erp_supplier_mgt_supplier esms_customer on esms_customer.supplier_guid=ebmo.settlement_customer_or_supplier_guid
        where ebmo.order_guid in <foreach item="item" collection="guids" open="(" separator="," close=")">#{item}</foreach>
    </select>

    <!--获取顶级来源备注和图片-->
    <select id="getSourceDescriptionAndPicture"
            resultType="com.xunyue.common.entity.model.vo.SourceDescriptionAndPictureVO">
        SELECT
            epmw13.workorder_guid AS source_guid,
            epmw13.workorder_number AS source_number,
            epmw13.description AS source_description,
            string_agg ( ebmf.file_name, ',' ) AS source_picture_names
        FROM
            erp_production_mgt_workorder epmw13
            LEFT JOIN erp_production_mgt_workorder_file epmwf ON epmwf.workorder_guid = epmw13.workorder_guid
            AND file_type = 1
            LEFT JOIN erp_basic_mgt_file ebmf ON ebmf.file_guid = epmwf.file_guid
        WHERE
            epmw13.workorder_number IN
            <foreach item="item" collection="sourceNumbers" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND epmw13.workorder_properties = 13
        GROUP BY
            epmw13.workorder_guid
    </select>

    <!--获取已开外发到货单号列表-->
    <select id="selectOutgoingArrivalNumbers" resultType="java.lang.String">
        SELECT DISTINCT
            epmw.workorder_number
        FROM
            erp_business_mgt_order_data ebmod
            LEFT JOIN erp_production_mgt_workorder_order_data epmwo ON epmwo.order_data_guid = ebmod.order_data_guid
            LEFT JOIN erp_production_mgt_workorder epmw ON epmw.workorder_guid = epmwo.workorder_guid
            AND epmw.workorder_properties = 29
        WHERE
            ebmod.order_guid = #{orderGuid}
            AND epmw.workorder_number IS NOT NULL
    </select>

    <select id="selectDescriptionByOrderNumber" resultType="java.lang.String">
        SELECT ebmo.description
        FROM
            erp_business_mgt_order ebmo
        WHERE ebmo.order_properties = 3 AND (ebmo.order_number ILIKE CONCAT ('%', #{orderNumber}, '%'))
    </select>
</mapper>
