<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpSupplierMgtSupplierClassificationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierClassificationVO">
        <id column="supplier_classification_guid" property="supplierClassificationGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="supplier_classification_name" property="supplierClassificationName"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="parent_classification_guid" property="parentClassificationGuid"/>
        <result column="supplier_classification_attribute_value" property="supplierClassificationAttributeValue"/>
        <result column="state" property="state"/>
        <result column="is_used" property="isUsed"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="type" property="type"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <collection property="children" ofType="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierClassificationVO"
                    column="{parentClassificationGuid=supplier_classification_guid,type=type}" select="getByParentClassificationGuid"/>
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select *,${model.type} as type
        from erp_supplier_mgt_supplier_classification where parent_classification_guid = ''
        and (state=true or state=#{model.type})
        <if test="model.keywords != null and model.keywords.size() != 0">
            and
            <foreach collection="model.keywords" item="item" index="index" open="(" separator="or" close=")">
                supplier_classification_name ILIKE CONCAT('%',#{item},'%')
                or description ILIKE CONCAT('%',#{item},'%')
            </foreach>
        </if>
        order by serial_number asc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select esmsc.*,${model.type} as type
        from erp_supplier_mgt_supplier_classification esmsc where parent_classification_guid = ''
        and (state=true or state=#{model.type})
        <if test="model.keywords != null and model.keywords.size() != 0">
            and
            <foreach collection="model.keywords" item="item" index="index" open="(" separator="or" close=")">
                supplier_classification_name ILIKE CONCAT('%',#{item},'%')
                or description ILIKE CONCAT('%',#{item},'%')
            </foreach>
        </if>
        order by serial_number asc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select *,state as type
        from erp_supplier_mgt_supplier_classification
        where supplier_classification_guid = #{guid}
    </select>

    <select id="getByParentClassificationGuid" resultMap="VoResultMap">
        select esmsc.*,${type} as type
        from erp_supplier_mgt_supplier_classification esmsc
        where parent_classification_guid = #{parentClassificationGuid}
        and (state=true or state=#{type})
        order by serial_number asc
    </select>

    <select id="getListByParentClassificationGuid" resultType="xy.server.purchase.entity.ErpSupplierMgtSupplierClassification">
        with RECURSIVE cte as (
            SELECT a.*
            FROM erp_supplier_mgt_supplier_classification a
            WHERE a.supplier_classification_guid = #{parentClassificationGuid}
            union ALL
            SELECT b.*
            FROM erp_supplier_mgt_supplier_classification b
                     inner join cte c on c.supplier_classification_guid = b.parent_classification_guid
        )
        SELECT *
        from cte;
    </select>

    <select id="getDataByGuids" resultMap="VoResultMap">
        select *,state as type
        from erp_supplier_mgt_supplier_classification
        where supplier_classification_guid in
        <foreach collection="guids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
