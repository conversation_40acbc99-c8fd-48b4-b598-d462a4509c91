<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpMaterialMgtMaterialQuotationReceiptDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap"
               type="xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationReceiptDetailVO">
        <id column="material_quotation_receipt_detail_guid" property="materialQuotationReceiptDetailGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="material_quotation_receipt_guid" property="materialQuotationReceiptGuid"/>
        <result column="material_classification_guid" property="materialClassificationGuid"/>
        <result column="material_classification_name" property="materialClassificationName"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="min_quantity" property="minQuantity"/>
        <result column="max_quantity" property="maxQuantity"/>
        <result column="quotation_unit_price_including_tax" property="quotationUnitPriceIncludingTax"/>
        <result column="description" property="description"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="old_quotation_unit_price_including_tax" property="oldQuotationUnitPriceIncludingTax"/>
        <result column="adjustment_magnitude" property="adjustmentMagnitude"/>
        <result column="external_material_code" property="externalMaterialCode"/>
        <result column="search_code" property="searchCode"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <association property="materialObj" javaType="xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO"
                     column="{guid=material_guid}" select="xy.server.material.mapper.ErpMaterialMgtMaterialMapper.getDataByGuid"/>
        <collection property="classificationDetailROS"
                    ofType="xy.server.material.entity.model.ro.ErpMaterialMgtMaterialClassificationDetailRO"
                    column="{materialClassificationGuid=material_classification_guid}"
                    select="getByParent"/>
    </resultMap>
    <select id="getList" resultMap="VoResultMap">
        select emmmqrd.*,emmmc.material_classification_name, emmmc.search_code
        from erp_material_mgt_material_quotation_receipt_detail emmmqrd
        left join erp_material_mgt_material_classification emmmc on emmmc.material_classification_guid=emmmqrd.material_classification_guid
        where material_quotation_receipt_guid = #{materialQuotationReceiptGuid}
    </select>
    <select id="getReceiptDetailGuid" resultType="java.lang.String">
        select emmmqrd.material_quotation_receipt_detail_guid
        from erp_material_mgt_material_quotation_receipt_detail emmmqrd
                 left join erp_material_mgt_material_quotation_receipt emmmqr
                           on emmmqr.material_quotation_receipt_guid = emmmqrd.material_quotation_receipt_guid
        where emmmqr.customer_or_supplier_guid=#{receiptQO.customerOrSupplierGuid}
        and emmmqr.effective_date=#{receiptQO.effectiveDate}
        <if test="receiptQO.materialClassificationGuid != null and receiptQO.materialClassificationGuid != '' ">
            and emmmqrd.material_classification_guid=#{receiptQO.materialClassificationGuid}
        </if>
        <if test="receiptQO.materialGuid != null and receiptQO.materialGuid != '' ">
            and emmmqrd.material_guid=#{receiptQO.materialGuid}
        </if>
        limit 1
    </select>
    <select id="getMaterialClassification" resultType="java.lang.String">
        select material_classification_guid from erp_material_mgt_material_classification
        where search_code=#{searchCode}
    </select>
    <select id="getByParent"
            resultType="xy.server.material.entity.model.ro.ErpMaterialMgtMaterialClassificationDetailRO">
        SELECT
            datail.*,
            ctype.specification_type AS specificationTypeName,
            coalesce(ctype.specification_type_value,datail.material_classification_name) AS specificationTypeValue ,
            ebmu.unit_name,
            ebmu.unit_guid
        FROM
            erp_material_mgt_material_classification_detail AS datail
                LEFT JOIN erp_material_mgt_specification_type AS ctype ON ctype.specification_type_guid = datail.specification_type_guid
                LEFT JOIN erp_basic_mgt_unit ebmu on ebmu.unit_guid=ctype.unit_guid
        WHERE
            datail.material_classification_guid = #{materialClassificationGuid}
        ORDER BY
            CASE WHEN specification_type_value IS NULL and ctype.specification_type !='克重' THEN 0 ELSE 1 END,
            -- 将specificationTypeValue为空的排在前面
            datail.serial_number asc,
            CASE WHEN ctype.specification_type = '长' THEN 1
                 WHEN ctype.specification_type = '宽' THEN 2
                 WHEN ctype.specification_type = '高' THEN 3
                 ELSE 4 END; -- 按照长宽高的顺序排序
    </select>
    <select id="getDataList"
            resultType="xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationReceiptDetailVO">
        select emmmqrd.*,emmmc.material_classification_name, emmmc.search_code
        from erp_material_mgt_material_quotation_receipt_detail emmmqrd
                 left join erp_material_mgt_material_classification emmmc on emmmc.material_classification_guid=emmmqrd.material_classification_guid
        where material_quotation_receipt_guid in
        <foreach item="item" collection="materialQuotationIds" separator="," open="(" close=")" index="index">
            #{item}
        </foreach>
    </select>
    <select id="getMaterialClassificationValues"
            resultType="xy.server.material.entity.model.ro.ErpMaterialMgtMaterialClassificationDetailRO">
        SELECT
            datail.*,
            ctype.specification_type AS specificationTypeName,
            coalesce(ctype.specification_type_value,datail.material_classification_name) AS specificationTypeValue ,
            ebmu.unit_name,
            ebmu.unit_guid
        FROM
            erp_material_mgt_material_classification_detail AS datail
                LEFT JOIN erp_material_mgt_specification_type AS ctype ON ctype.specification_type_guid = datail.specification_type_guid
                LEFT JOIN erp_basic_mgt_unit ebmu on ebmu.unit_guid=ctype.unit_guid
        WHERE
            datail.material_classification_guid in
        <foreach item="item" collection="materialClassifications" separator="," open="(" close=")" index="index">
            #{item}
        </foreach>
        ORDER BY
            CASE WHEN specification_type_value IS NULL and ctype.specification_type !='克重' THEN 0 ELSE 1 END,
            -- 将specificationTypeValue为空的排在前面
            datail.serial_number asc,
            CASE WHEN ctype.specification_type = '长' THEN 1
                 WHEN ctype.specification_type = '宽' THEN 2
                 WHEN ctype.specification_type = '高' THEN 3
                 ELSE 4 END; -- 按照长宽高的顺序排序
    </select>
</mapper>
