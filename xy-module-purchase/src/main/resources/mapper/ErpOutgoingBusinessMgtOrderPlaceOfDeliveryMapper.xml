<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpOutgoingBusinessMgtOrderPlaceOfDeliveryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpOutgoingBusinessMgtOrderPlaceOfDeliveryVO">
        <id column="order_place_of_delivery_guid" property="orderPlaceOfDeliveryGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="order_guid" property="orderGuid"/>
        <result column="administrative_area_guid" property="administrativeAreaGuid"/>
        <result column="address" property="address"/>
        <result column="contact_name" property="contactName"/>
        <result column="telephone" property="telephone"/>
        <result column="mobile_phone" property="mobilePhone"/>
        <result column="fax" property="fax"/>
        <result column="email" property="email"/>
        <result column="qq_number" property="qqNumber"/>
        <result column="wechat_number" property="wechatNumber"/>
        <result column="is_default" property="isDefault"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select *
        from erp_business_mgt_order_place_of_delivery
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select *
        from erp_business_mgt_order_place_of_delivery
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select *
        from erp_business_mgt_order_place_of_delivery
        where order_place_of_delivery_guid = #{guid}
    </select>
</mapper>
