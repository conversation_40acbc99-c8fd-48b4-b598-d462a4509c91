<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpPurchaseMgtPurchaseOrderFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseOrderFileVO">
        <id column="order_file_guid" property="orderFileGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="order_guid" property="orderGuid"/>
        <result column="file_guid" property="fileGuid"/>
        <result column="file_name" property="fileName"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <result column="file_type" property="fileType"/>
    </resultMap>
    <select id="findListByOrderGuid" resultMap="VoResultMap">
        SELECT
            ebmof.*,
            ebmf.file_name
        FROM
            erp_business_mgt_order_file ebmof
            LEFT JOIN erp_basic_mgt_file ebmf ON ebmof.file_guid = ebmf.file_guid
        WHERE
            order_guid = #{ orderGuid}
    </select>
    <select id="findListByOrderGuids" resultMap="VoResultMap">
        SELECT
            ebmof.*,
            ebmf.file_name
        FROM
            erp_business_mgt_order_file ebmof
            LEFT JOIN erp_basic_mgt_file ebmf ON ebmof.file_guid = ebmf.file_guid
        WHERE
            order_guid IN
            <foreach collection="orderGuids" item="orderGuid" open="(" separator="," close=")">
                #{orderGuid}
            </foreach>
    </select>


</mapper>
