<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpSupplierMgtSupplierMapper">

    <resultMap id="VoResultMapBase" type="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierVO">
        <id column="supplier_guid" property="supplierGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_short_name" property="supplierShortName"/>
        <result column="supplier_full_name" property="supplierFullName"/>
        <result column="supplier_classification_guid" property="supplierClassificationGuid"/>
        <result column="supplier_level_guid" property="supplierLevelGuid"/>
        <result column="telephone" property="telephone"/>
        <result column="mobilephone" property="mobilephone"/>
        <result column="fax" property="fax"/>
        <result column="email" property="email"/>
        <result column="opening_bank" property="openingBank"/>
        <result column="bank_account" property="bankAccount"/>
        <result column="enterprise_tax_identification_number" property="enterpriseTaxIdentificationNumber"/>
        <result column="default_currency_guid" property="defaultCurrencyGuid"/>
        <result column="default_invoice_type_tax_rate_guid" property="defaultInvoiceTypeTaxRateGuid"/>
        <result column="default_delivery_type_guid" property="defaultDeliveryTypeGuid"/>
        <result column="default_settlement_type_guid" property="defaultSettlementTypeGuid"/>
        <result column="account_period" property="accountPeriod"/>
        <result column="state" property="state"/>
        <result column="is_used" property="isUsed"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_date" property="auditDate"/>
        <result column="reconciliation_date" property="reconciliationDate"/>

    </resultMap>
    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierVO" extends="VoResultMapBase">

        <!--供应商分类-->
        <association property="supplierClassificationVO" javaType="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierClassificationVO"
                     column="{guid=supplier_classification_guid}"
                     select="xy.server.purchase.mapper.ErpSupplierMgtSupplierClassificationMapper.getDataByGuid"/>
        <!--供应商级别-->
        <association property="supplierLevelVO" javaType="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierLevelVO"
                     column="{guid=supplier_level_guid}"
                     select="xy.server.purchase.mapper.ErpSupplierMgtSupplierLevelMapper.getDataByGuid"/>
        <!--默认币种-->
        <association property="defaultCurrencyVO" javaType="xy.server.basic.entity.model.vo.ErpBasicMgtCurrencyVO"
                     column="{guid=default_currency_guid}"
                     select="xy.server.basic.mapper.ErpBasicMgtCurrencyMapper.getDataByGuid"/>
        <!--默认发票类型税率-->
        <association property="defaultInvoiceTypeTaxRateVO" javaType="xy.server.basic.entity.model.vo.ErpBasicMgtInvoiceTypeTaxRateVO"
                     column="{guid=default_invoice_type_tax_rate_guid}"
                     select="xy.server.basic.mapper.ErpBasicMgtInvoiceTypeTaxRateMapper.getDataByGuid"/>
        <!--默认送货类型-->
        <association property="defaultDeliveryTypeVO" javaType="xy.server.basic.entity.model.vo.ErpBasicMgtDeliveryTypeVO"
                     column="{guid=default_delivery_type_guid}"
                     select="xy.server.basic.mapper.ErpBasicMgtDeliveryTypeMapper.getDataByGuid"/>
        <!--默认结算类型-->
        <association property="defaultSettlementTypeVO" javaType="xy.server.basic.entity.model.vo.ErpBasicMgtSettlementTypeVO"
                     column="{guid=default_settlement_type_guid}"
                     select="xy.server.basic.mapper.ErpBasicMgtSettlementTypeMapper.getDataByGuid"/>
        <!--供应商文件列表-->
        <collection property="supplierFileVOList" ofType="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierFileVO"
                    column="{model.supplierGuid=supplier_guid}" select="xy.server.purchase.mapper.ErpSupplierMgtSupplierFileMapper.findList"/>
        <!--供应商地址列表-->
        <collection property="supplierAddressVOList" ofType="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierAddressVO"
                    column="{model.supplierGuid=supplier_guid}" select="xy.server.purchase.mapper.ErpSupplierMgtSupplierAddressMapper.findList"/>
        <!--供应商联系人列表-->
        <collection property="supplierContactVOList" ofType="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierContactVO"
                    column="{model.supplierGuid=supplier_guid}" select="xy.server.purchase.mapper.ErpSupplierMgtSupplierContactMapper.findList"/>

    </resultMap>

    <select id="findPage" resultMap="VoResultMapBase">
        select a.*
        from erp_supplier_mgt_supplier a
        left join erp_supplier_mgt_supplier_classification b on a.supplier_classification_guid =
        b.supplier_classification_guid
        left join erp_supplier_mgt_supplier_level c on a.supplier_level_guid = c.supplier_level_guid
        left join erp_basic_mgt_currency d on a.default_currency_guid = d.currency_guid
        left join erp_supplier_mgt_supplier_contact esmsc on esmsc.supplier_guid = a.supplier_guid
        <where>
            and (a.state=true or a.state = #{model.type})
            <if test="model.keywords != null and model.keywords.size() != 0">
                and
                <foreach collection="model.keywords" item="item" index="index" open="(" separator="or" close=")">
                    a.supplier_code ILIKE CONCAT('%',#{item},'%')
                    or a.supplier_short_name ILIKE CONCAT('%',#{item},'%')
                    or a.supplier_full_name ILIKE CONCAT('%',#{item},'%')
                    or a.description ILIKE CONCAT('%',#{item},'%')
                    or b.supplier_classification_name ILIKE CONCAT('%',#{item},'%')
                    or c.supplier_level_name ILIKE CONCAT('%',#{item},'%')
                    or d.currency_name ILIKE CONCAT('%',#{item},'%')
                </foreach>
            </if>
            <if test="model.processInstanceIds != null and model.processInstanceIds.size() != 0 ">
                and a.process_instance_id in
                <foreach item="item" collection="model.processInstanceIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="model.auditStatus != null and model.auditStatus.size() != 0">
                and
                <foreach collection="model.auditStatus" item="key" separator="or" open="(" close=")">
                    a.audit_status=#{key}
                </foreach>
            </if>
            <if test="model.supplierClassificationAttributeValue != null and model.supplierClassificationAttributeValue.size() != 0 ">
                and b.supplier_classification_attribute_value in
                <foreach item="item" collection="model.supplierClassificationAttributeValue" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="model.supplierLevelGuid != null and model.supplierLevelGuid != ''">
                and a.supplier_level_guid = #{model.supplierLevelGuid}
            </if>
            <if test="model.businessKeys != null and model.businessKeys.size() != 0 ">
                and a.supplier_guid in
                <foreach item="item" collection="model.businessKeys" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="model.supplierCode != null and model.supplierCode != ''">
                and a.supplier_code ILIKE CONCAT('%',#{model.supplierCode},'%')
            </if>

            <if test="model.supplierName != null and model.supplierName != ''">
                and (a.supplier_short_name ILIKE CONCAT('%',#{model.supplierName},'%') or a.supplier_full_name ILIKE
                CONCAT('%',#{model.supplierName},'%'))
            </if>

            <if test="model.supplierLevelGuids != null and model.supplierLevelGuids.size() > 0">
                and a.supplier_level_guid in
                <foreach item="item" collection="model.supplierLevelGuids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="model.states != null and model.states.size() > 0">
                and a.state in
                <foreach item="item" collection="model.states" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="model.creator != null and model.creator != ''">
                and a.creator ILIKE CONCAT('%',#{model.creator} , '%')
            </if>

            <if test="model.contactName != null and model.contactName != ''">
                and esmsc.contact_name ILIKE CONCAT('%',#{model.contactName} , '%')
            </if>

            <if test="model.contactPhone != null and model.contactPhone != ''">
                and (esmsc.mobilephone ILIKE CONCAT('%',#{model.contactPhone} , '%') or esmsc.telephone ILIKE
                CONCAT('%',#{model.contactPhone} , '%'))
            </if>

            <if test="model.startDate != null">
                and to_char(a.create_date, 'YYYY-MM-DD') &gt;= to_char(#{model.startDate}::timestamp , 'YYYY-MM-DD')
            </if>

            <if test="model.endDate != null">
                and to_char(a.create_date, 'YYYY-MM-DD') &lt;= to_char(#{model.endDate}::timestamp , 'YYYY-MM-DD')
            </if>

            <if test="model.supplierClassificationGuids != null and model.supplierClassificationGuids.size() > 0">
                and a.supplier_classification_guid in
                <foreach item="item" collection="model.supplierClassificationGuids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by a.supplier_guid , a.create_date
        order by a.create_date desc

    </select>

    <select id="findList" resultMap="VoResultMapBase">
        select a.*
        from erp_supplier_mgt_supplier a
        left join erp_supplier_mgt_supplier_classification b on a.supplier_classification_guid = b.supplier_classification_guid
        left join erp_supplier_mgt_supplier_level c on a.supplier_level_guid = c.supplier_level_guid
        left join erp_basic_mgt_currency d on a.default_currency_guid = d.currency_guid
        <where>
            and (a.state=true or a.state = #{model.type})
            <if test="model.keywords != null and model.keywords.size() != 0">
                and
                <foreach collection="model.keywords" item="item" index="index" open="(" separator="or" close=")">
                    a.supplier_code ILIKE CONCAT('%',#{item},'%')
                    or a.supplier_short_name ILIKE CONCAT('%',#{item},'%')
                    or a.supplier_full_name ILIKE CONCAT('%',#{item},'%')
                    or a.description ILIKE CONCAT('%',#{item},'%')
                    or b.supplier_classification_name ILIKE CONCAT('%',#{item},'%')
                    or c.supplier_level_name ILIKE CONCAT('%',#{item},'%')
                    or d.currency_name ILIKE CONCAT('%',#{item},'%')
                </foreach>
            </if>
            <if test="model.processInstanceIds != null and model.processInstanceIds.size() != 0 ">
                and a.process_instance_id in
                <foreach item="item" collection="model.processInstanceIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="model.supplierClassificationGuids != null and model.supplierClassificationGuids.size() != 0 ">
                and b.supplier_classification_guid in
                <foreach item="item" collection="model.supplierClassificationGuids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="model.supplierClassificationAttributeValue != null and model.supplierClassificationAttributeValue.size() != 0 ">
                and b.supplier_classification_attribute_value in
                <foreach item="item" collection="model.supplierClassificationAttributeValue" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="model.auditStatus != null and model.auditStatus.size() != 0">
                and
                <foreach collection="model.auditStatus" item="key" separator="or" open="(" close=")">
                    a.audit_status=#{key}
                </foreach>
            </if>
            <if test="model.supplierLevelGuid != null and model.supplierLevelGuid != ''">
                and a.supplier_level_guid = #{model.supplierLevelGuid}
            </if>
            <if test="model.businessKeys != null and model.businessKeys.size() != 0 ">
                and a.supplier_guid in
                <foreach item="item" collection="model.businessKeys" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="model.supplierGuid != null and model.supplierGuid != ''">
                and a.supplier_guid = #{model.supplierGuid}
            </if>
            <if test="model.supplierGuids != null and model.supplierGuids.size() != 0 ">
                and a.supplier_guid in
                <foreach item="item" collection="model.supplierGuids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMapBase">
        select *
        from erp_supplier_mgt_supplier where supplier_guid = #{guid}
    </select>
    <select id="dropDownSelect" resultType="xy.server.purchase.entity.ErpSupplierMgtSupplier">
        select esms.supplier_guid as supplierGuid, esms.supplier_short_name as supplierShortName
        from erp_supplier_mgt_supplier esms
        order by create_date desc
    </select>

    <select id="getInvoiceTypeTaxRateGuid" resultType="java.lang.String">
        SELECT
            ebmittr.invoice_type_tax_rate_guid
        FROM
            erp_basic_mgt_invoice_type_tax_rate ebmittr
            LEFT JOIN erp_basic_mgt_invoice_type ebmit ON ebmittr.invoice_type_guid = ebmit.invoice_type_guid
        WHERE
            ebmit.invoice_type_name = #{invoiceTypeName}
            AND ebmittr.tax_rate = #{taxRate}
            AND ebmittr.effective_start_time &lt; now()
            AND ebmittr.effective_end_time &gt; now()
    </select>

    <select id="getDataByGuids" resultMap="VoResultMapBase">
        select *
        from erp_supplier_mgt_supplier
        where supplier_guid in
        <foreach collection="guids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
