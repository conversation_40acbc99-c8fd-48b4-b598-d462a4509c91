<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpOutgoingMgtApplicationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpOutgoingMgtApplicationVO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="workorder_number" property="workorderNumber"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="parent_classification_guid" property="parentClassificationGuid"/>
        <result column="print_times" property="printTimes"/>
        <result column="receipt_date" property="receiptDate"/>
        <result column="workorder_type_guid" property="workorderTypeGuid"/>
        <result column="source_guid" property="sourceGuid"/>
        <result column="source_value" property="sourceValue"/>
        <result column="workorder_state" property="workorderState"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_date" property="auditDate"/>
        <result column="workorder_properties" property="workorderProperties"/>
        <result column="is_urgent" property="isUrgent"/>
        <result column="fsc_declaration_guid" property="fscDeclarationGuid"/>
        <result column="customer_guid" property="customerGuid"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="production_processes_type_guid" property="productionProcessesTypeGuid"/>
        <result column="proportion" property="proportion"/>
        <result column="production_processes_description" property="productionProcessesDescription"/>
        <result column="quantity" property="quantity"/>
        <result column="using_inventory_quantity" property="usingInventoryQuantity"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="unit_guid" property="unitGuid"/>
        <result column="specifications_length" property="specificationsLength"/>
        <result column="specifications_width" property="specificationsWidth"/>
        <result column="specifications_height" property="specificationsHeight"/>
        <result column="required_delivery_time" property="requiredDeliveryTime"/>
        <result column="product_material_guid" property="productMaterialGuid"/>
        <result column="process_flowchart" property="processFlowchart"/>
        <result column="to_json" property="toJson"  typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select *
        from erp_production_mgt_workorder
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select *
        from erp_production_mgt_workorder
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select *
        from erp_production_mgt_workorder
        where workorder_guid = #{guid}
    </select>
    <select id="selectOutgoingData" resultType="xy.server.work.entity.ErpProductionMgtWorkorder">
        select * from erp_production_mgt_workorder epmw
        where epmw.parent_classification_guid=#{workorderGuid}
    </select>
    <select id="selectMateilData" resultType="java.lang.String">
        select epmw2.description
        from erp_production_mgt_workorder epmw
                 left join erp_production_mgt_workorder epmw2 on epmw.parent_classification_guid = epmw2.workorder_guid
        where epmw.workorder_guid = #{workorderGuid}
    </select>
    <select id="selectproductData" resultType="java.lang.String">
        SELECT STRING_AGG(emmm.material_name, ',') AS material_names
        FROM erp_production_mgt_workorder epmw
                 LEFT JOIN erp_material_mgt_material emmm ON emmm.material_guid = ANY(string_to_array(epmw.product_material_guid, ','))
        WHERE epmw.workorder_guid = #{workorderGuid}
        GROUP BY epmw.workorder_guid
    </select>
    <select id="selectOutgoing" resultType="java.lang.String">
        select epmw.workorder_guid
        from erp_production_mgt_workorder epmw
        left join erp_production_mgt_workorder childEpmw on childEpmw.parent_classification_guid = epmw.workorder_guid
        left join erp_basic_mgt_production_processes_type ebmppt ON ebmppt.production_processes_type_guid = epmw.production_processes_type_guid
        left join erp_production_mgt_workorder_data_process epmwdp on childEpmw.source_guid = epmwdp.workorder_data_process_guid
        left join erp_production_mgt_workorder epmw2 on epmw2.workorder_guid = epmwdp.workorder_guid
        left join erp_basic_mgt_production_processes_type ebmppt2 ON ebmppt2.production_processes_type_guid = epmw2.production_processes_type_guid
        left join erp_production_mgt_workorder epmw3 on epmw3.workorder_guid = epmw2.parent_classification_guid
        LEFT JOIN erp_material_mgt_material emmm ON emmm.material_guid = epmw2.product_material_guid
        left join erp_material_mgt_material emmm2 on emmm2.material_guid = epmw3.material_guid
        <where>
            epmw.workorder_properties =4 and (epmw.parent_classification_guid ='' or epmw.parent_classification_guid is null)
            <if test="qo.processInstanceIds != null and qo.processInstanceIds.size() != 0 ">
                and epmw.process_instance_id in
                <foreach item="item" collection="qo.processInstanceIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="qo.startTime !=null and qo.endTime !=null">
                and (epmw.create_date &gt;= #{qo.startTime}
                and epmw.create_date &lt;= #{qo.endTime})
            </if>
            <if test="qo.keywords!=null  and  qo.keywords.size() != 0">
                and
                <foreach collection="qo.keywords" item="key" separator="or" open="(" close=")">
                    (epmw.workorder_number ILIKE CONCAT('%',#{key},'%')
                    or ebmppt.production_processes_type_name ILIKE CONCAT('%',#{key},'%'))
                </foreach>
            </if>
            <if test="qo.workorderTypeGuid!=null  and  qo.workorderTypeGuid.size() != 0">
                and
                <foreach collection="qo.workorderTypeGuid" item="key" separator="or" open="(" close=")">
                    epmw.workorder_type_guid=#{key}
                </foreach>
            </if>
            <if test="qo.workorderState!=null  and  qo.workorderState.size() != 0">
                and
                <foreach collection="qo.workorderState" item="key" separator="or" open="(" close=")">
                    epmw.workorder_state=#{key}
                </foreach>
            </if>
            <if test="qo.auditStatus != null and qo.auditStatus.size() > 0 ">
                and epmw.audit_status in
                <foreach item="item" collection="qo.auditStatus" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="qo.applicationNumber != null and qo.applicationNumber != ''">
                and epmw.workorder_number ILIKE CONCAT('%',#{qo.applicationNumber},'%')
            </if>

            <if test="qo.workorderNumber != null and qo.workorderNumber != ''">
                and epmw2.workorder_number ILIKE CONCAT('%',#{qo.workorderNumber},'%')
            </if>

            <if test="qo.production != null and qo.production != ''" >
                and (emmm.material_name ILIKE CONCAT('%',#{qo.production},'%') or emmm.material_code ILIKE CONCAT('%',#{qo.production},'%'))
            </if>

            <if test="qo.partName != null and qo.partName != ''" >
                and (emmm2.material_name ILIKE CONCAT('%',#{qo.partName},'%') or emmm2.material_code ILIKE CONCAT('%',#{qo.partName},'%'))
            </if>

            <if test="qo.procedureName != null and qo.procedureName != ''" >
                and ebmppt2.production_processes_type_name ILIKE CONCAT('%',#{qo.procedureName},'%')
            </if>

            <if test="qo.creator != null and qo.creator != ''" >
                and epmw.creator ILIKE CONCAT('%',#{qo.creator},'%')
            </if>
        </where>
        GROUP BY epmw.workorder_guid , epmw.create_date
        order by epmw.workorder_number desc
    </select>
    <select id="selectMessages" resultType="xy.server.purchase.entity.model.ro.ErpOutgoingApplicationFormRO">
       select distinct epmwdp.* from  (SELECT distinct epmwdp1.workorder_data_process_guid,epmwdp1.create_date
        FROM erp_production_mgt_workorder_data_process epmwdp1
        left JOIN erp_production_mgt_workorder epmw ON epmwdp1.workorder_guid = epmw.workorder_guid
        LEFT JOIN erp_production_mgt_workorder epmw13 ON epmw13.workorder_number = epmw.workorder_number
        AND epmw13.workorder_properties = ${@com.xunyue.common.enums.WorkorderPropertiesEnum @SON.getCode()}
        left JOIN erp_material_mgt_material emmm ON emmm.material_guid = epmw.material_guid
        left JOIN erp_basic_mgt_production_processes_type ebmppt ON ebmppt.production_processes_type_guid =
        epmw.production_processes_type_guid
        <where>
            epmwdp1.is_outsource = true and epmw.deleted=false
            and (epmw.this_outsource_state = '0' or epmw.this_outsource_state = '1')
            <if test="qo.auditStatus != null and qo.auditStatus.size() > 0 "   >
                AND epmw13.audit_status in
                <foreach item="item" collection="qo.auditStatus" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND epmw.workorder_type = 2
            AND epmw.workorder_properties=2
            and (epmwdp1.outsourced_requisition_quantity &lt; epmw.quantity
            <if test="qo. isAll!=null and qo. isAll ==true">
                or epmwdp1.outsourced_requisition_quantity = epmw.quantity
            </if>)
            <if test="qo.startTime !=null and qo.endTime !=null">
                and (epmwdp1.create_date &gt;= #{qo.startTime}
                and epmwdp1.create_date &lt;= #{qo.endTime})
            </if>
            <if test="qo.workorderNumber!=null and qo.workorderNumber!=''">
                and epmw.workorder_number ILIKE CONCAT('%',#{qo.workorderNumber},'%')
            </if>
            <if test="qo.partName!=null and qo.partName!=''">
                and emmm.material_name ILIKE CONCAT('%',#{qo.partName},'%')
            </if>
            <if test="qo.processName!=null and qo.processName!=''">
                and ebmppt.production_processes_type_name ILIKE CONCAT('%',#{qo.processName},'%')
            </if>
        </where>
        order by epmwdp1.create_date desc) datae
        left join erp_production_mgt_workorder_data_process epmwdp on epmwdp.workorder_data_process_guid=datae.workorder_data_process_guid
       <where>
           <if test="qo.workOrderProcessGuid != null and qo.workOrderProcessGuid.size() != 0 ">
               and  epmwdp.workorder_data_process_guid in
               <foreach item="item" collection="qo.workOrderProcessGuid" open="(" separator="," close=")">
                   #{item}
               </foreach>
           </if>

       </where>
    </select>
    <select id="selectMosterGuid" resultType="xy.server.work.entity.ErpProductionMgtWorkorder">
        select epmw.*
        from erp_production_mgt_workorder epmw
        left join erp_production_mgt_workorder_data_process epmwdp on epmw.source_guid = epmwdp.workorder_guid
        left join erp_production_mgt_workorder epmw2 on epmw2.workorder_guid = epmwdp.workorder_guid
        LEFT JOIN erp_material_mgt_material emmm ON emmm.material_guid = epmw2.product_material_guid
        where epmw.workorder_properties=4 and (epmw.parent_classification_guid ='' or epmw.parent_classification_guid is null) and epmw.audit_status='finish'
        <if test="qo.processInstanceIds != null and qo.processInstanceIds.size() != 0 ">
            and epmw.process_instance_id in
            <foreach item="item" collection="qo.processInstanceIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="qo.applicationNumber != null and qo.applicationNumber != ''">
            and epmw.workorder_number ILIKE CONCAT('%',#{qo.applicationNumber},'%')
        </if>

        <if test="qo.workorderNumber != null and qo.workorderNumber != ''">
            and epmw2.workorder_number ILIKE CONCAT('%',#{qo.workorderNumber},'%')
        </if>

        <if test="qo.production != null and qo.production != ''" >
            and (emmm.material_name ILIKE CONCAT('%',#{qo.production},'%') or emmm.material_code ILIKE CONCAT('%',#{qo.production},'%'))
        </if>
        group by epmw.workorder_guid , epmw.create_date
        order by epmw.create_date desc
    </select>
    <select id="specificFindList" resultType="xy.server.work.entity.ErpProductionMgtWorkorder">
        select epmw.* from
        erp_production_mgt_workorder epmw
        left join erp_material_mgt_material emmm on epmw.material_guid=emmm.material_guid
        left join erp_basic_mgt_production_processes_type ebmppt ON ebmppt.production_processes_type_guid =
        epmw.production_processes_type_guid
        where epmw.workorder_properties='4' and epmw.parent_classification_guid=#{workorderGuid}
          and epmw.current_status !='3'
        <if test="qo.startTime !=null and qo.endTime !=null">
            and (epmw.create_date &gt;= #{qo.startTime}
            and epmw.create_date &lt;= #{qo.endTime})
        </if>
        <if test="qo.workorderNumber!=null and qo.workorderNumber!=''">
            and epmw.workorder_number ILIKE CONCAT('%',#{qo.workorderNumber},'%')
        </if>
        <if test="qo.applicationNumber!=null and qo.applicationNumber!=''">
            and epmw.workorder_number ILIKE CONCAT('%',#{qo.applicationNumber},'%')
        </if>
        <if test="qo.partName!=null and qo.partName!=''">
            and emmm.material_name ILIKE CONCAT('%',#{qo.partName},'%')
        </if>
        <if test="qo.processName!=null and qo.processName!=''">
            and ebmppt.production_processes_type_name ILIKE CONCAT('%',#{qo.processName},'%')
        </if>
        order by epmw.create_date desc
    </select>
    <select id="selectSourceProcess" resultType="xy.server.purchase.entity.ErpOutgoingDataProcess">
        select epmwdp.*
        from erp_production_mgt_workorder_data_process epmwdp
                 left join erp_production_mgt_workorder epmw on epmw.workorder_guid = epmwdp.workorder_guid
                 left join erp_production_mgt_workorder epmw2 on epmw2.source_guid=epmw.workorder_guid
        where epmw2.workorder_guid=#{workorderGuid}
    </select>

    <select id="selectPartName" resultType="java.lang.String">
        select emmm.material_name
        from erp_production_mgt_workorder_data_process epmwdp
                 left join erp_production_mgt_workorder epmw on epmw.workorder_guid = epmwdp.workorder_guid
        left join erp_production_mgt_workorder epmw2 on epmw.parent_classification_guid=epmw2.workorder_guid
        left join erp_material_mgt_material emmm on emmm.material_guid=epmw2.material_guid
        where (epmwdp.workorder_data_process_guid = #{workOrderProcessGuid} or epmwdp.workorder_guid = #{workOrderProcessGuid})
    </select>

    <select id="selectproductName" resultType="java.lang.String">
        SELECT emmm.material_name
        FROM erp_production_mgt_workorder_data_process epmwdp
                 left join erp_production_mgt_workorder epmw on epmw.workorder_guid = epmwdp.workorder_guid
                 LEFT JOIN erp_material_mgt_material emmm
                           ON emmm.material_guid = epmw.product_material_guid
        WHERE (epmwdp.workorder_data_process_guid = #{workOrderProcessGuid} or epmwdp.workorder_guid = #{workOrderProcessGuid})
    </select>

    <!-- 通用查询映射结果 -->
    <resultMap id="SoResultMap" type="xy.server.purchase.entity.model.vo.ErpOutgoingApplicationVO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="parent_classification_guid" property="parentWorkOrderGuid"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="proportion" property="proportion"/>
        <result column="quantity" property="outputQuantity"/>
        <!--物料Obj-->
        <association property="materialObj" javaType="xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO"
                     column="{guid=material_guid}" select="xy.server.material.mapper.ErpMaterialMgtMaterialMapper.getDataByGuid"/>
    </resultMap>
    <select id="getSonList" resultMap="SoResultMap">
        select epmw.workorder_guid,
               epmw.parent_classification_guid,
               epmw.material_guid,
               epmw.proportion,
               ${quantity}*epmw.proportion as quantity
        from erp_production_mgt_workorder epmw
        where epmw.parent_classification_guid = #{workorderGuid}
    </select>
    <select id="selectPartMaterialGuid" resultType="java.lang.String">
        select emmm.material_guid
        from erp_production_mgt_workorder_data_process epmwdp
                 left join erp_production_mgt_workorder epmw on epmw.workorder_guid = epmwdp.workorder_guid
                 left join erp_production_mgt_workorder epmw2 on epmw.parent_classification_guid=epmw2.workorder_guid
                 left join erp_material_mgt_material emmm on emmm.material_guid=epmw2.material_guid
        where (epmwdp.workorder_data_process_guid = #{workOrderProcessGuid} or epmwdp.workorder_guid = #{workOrderProcessGuid})
    </select>
    <resultMap id="ToResultMap" type="xy.server.purchase.entity.model.ro.ErpOutgoingApplicationFormRO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="description" property="description"/>
        <result column="workorder_data_process_guid" property="workorderDataProcessGuid"/>
        <result column="work_order_process_guid" property="workOrderProcessGuid"/>
        <result column="workorder_number" property="applicationNumber"/>
        <result column="workorderNumber" property="workorderNumber"/>
        <result column="outputQuantity" property="outputQuantity"/>
        <result column="outputQuantity" property="processorQuantity"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="loss_quantity" property="lossQuantity"/>
        <result column="act_quantity" property="actQuantity"/>
        <result column="partName" property="partName"/>
        <result column="productName" property="productName"/>
        <result column="material_code" property="materialCode"/>
        <result column="partNameGuid" property="materialGuid"/>
        <result column="production_processes_type_name" property="processName"/>
        <result column="parent_classification_guid" property="parentClassificationGuid"/>
        <result column="waitOutgoingQuantity" property="waitOutgoingQuantity"/>
        <result column="production_processes_description" property="productionProcessesDescription"/>
        <result column="required_delivery_time" property="requiredDeliveryTime"/>
        <association property="materialObj" javaType="xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO"
                     column="{guid=partNameGuid}" select="xy.server.material.mapper.ErpMaterialMgtMaterialMapper.getDataByGuid"/>
        <association property="processParameters" javaType="java.lang.String"
                     column="{workOrderProcessGuid=workorder_data_process_guid}" select="getProcessParameters"/>
        <collection property="sonList" ofType="xy.server.purchase.entity.model.vo.ErpOutgoingApplicationVO"
                    column="{workorderGuid=guid,quantity=total_quantity}" select="getSonList"/>
    </resultMap>
    <select id="getProcessParameters" resultType="java.lang.String">
        select string_agg(a.b, ',')
        from (SELECT CONCAT(ebmpptpi.production_processes_type_parameter_items, ':',
                            string_agg(ebmpptpv.production_processes_type_parameter_value, '+')) as b
              FROM erp_production_mgt_workorder_data_process epmwdp
                       left join erp_production_mgt_workorder epmw on epmw.workorder_guid = epmwdp.workorder_guid
                       left join erp_basic_mgt_production_processes_type ebmppt
                                 on ebmppt.production_processes_type_guid = epmw.production_processes_type_guid
                       LEFT JOIN erp_basic_mgt_production_processes_type_and_items ebmpptai
                                 ON ebmppt.production_processes_type_guid = ebmpptai.production_processes_type_guid
                       LEFT JOIN erp_basic_mgt_production_processes_type_parameter_items ebmpptpi
                                 ON ebmpptai.production_processes_type_parameter_items_guid =
                                    ebmpptpi.production_processes_type_parameter_items_guid
                       LEFT JOIN erp_basic_mgt_production_processes_type_parameter_value ebmpptpv
                                 ON ebmpptpi.production_processes_type_parameter_items_guid =
                                    ebmpptpv.production_processes_type_parameter_items_guid
              WHERE epmwdp.workorder_data_process_guid = #{workOrderProcessGuid}
                and ebmpptai.deleted = false
                and ebmpptpv.source_guid=epmw.workorder_guid
              group by ebmpptpi.production_processes_type_parameter_items,
                       ebmpptpv.production_processes_type_parameter_value, ebmppt.production_processes_type_guid) as a
    </select>

    <resultMap id="getNewFindListNoDetail" type="xy.server.purchase.entity.model.ro.ErpOutgoingApplicationFormRO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="description" property="description"/>
        <result column="workorder_data_process_guid" property="workorderDataProcessGuid"/>
        <result column="work_order_process_guid" property="workOrderProcessGuid"/>
        <result column="workorder_number" property="applicationNumber"/>
        <result column="workorderNumber" property="workorderNumber"/>
        <result column="outputQuantity" property="outputQuantity"/>
        <result column="outputQuantity" property="processorQuantity"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="loss_quantity" property="lossQuantity"/>
        <result column="act_quantity" property="actQuantity"/>
        <result column="partName" property="partName"/>
        <result column="productName" property="productName"/>
        <result column="material_code" property="materialCode"/>
        <result column="partNameGuid" property="materialGuid"/>
        <result column="production_processes_type_guid" property="productionProcessesTypeGuid"/>
        <result column="production_processes_type_name" property="processName"/>
        <result column="parent_classification_guid" property="parentClassificationGuid"/>
        <result column="waitOutgoingQuantity" property="waitOutgoingQuantity"/>
        <result column="production_processes_description" property="productionProcessesDescription"/>
        <result column="required_delivery_time" property="requiredDeliveryTime"/>
        <result column="guid" property="tmpGuid"/>
    </resultMap>
    <select id="getNewFindList" resultMap="getNewFindListNoDetail">
        select epmw.workorder_guid,
               epmwdp.workorder_data_process_guid,
               epmw.workorder_number,
               epmw.parent_classification_guid,
               epmwdp.workorder_data_process_guid                             as work_order_process_guid,
               epmw2.workorder_number                                         as workorderNumber,
               (epmw.quantity + epmwdp.loss_quantity)                         as outputQuantity,
               epmw2.workorder_guid                                           as guid,
               epmw.required_delivery_time,
               coalesce(CEIL(epmw.quantity / epmw.proportion), 0)             AS loss_quantity,
               CEIL((epmw.quantity + epmwdp.loss_quantity) / epmw.proportion) AS act_quantity,
               emmm1.material_name                                            as partName,
               emmm1.material_guid                                            as partNameGuid,
               emmm.material_name                                             as productName,
               emmm.material_code,
               ebmppt2.production_processes_type_guid,
               ebmppt2.production_processes_type_name,
               epmw.total_quantity,
               epmw.production_processes_description,
               coalesce((epmw.total_quantity - coalesce(a.quantity, 0)), 0)   as waitOutgoingQuantity,
               -- 备注
               epmw.description
        from erp_production_mgt_workorder epmw
                 left join erp_production_mgt_workorder_data_process epmwdp
                           on epmwdp.workorder_data_process_guid = epmw.source_guid
                 left join erp_production_mgt_workorder epmw2 on epmw2.workorder_guid = epmwdp.workorder_guid
                 left join erp_basic_mgt_production_processes_type ebmppt2 on
                epmw2.production_processes_type_guid = ebmppt2.production_processes_type_guid
                 LEFT JOIN erp_material_mgt_material emmm ON emmm.material_guid = epmw2.product_material_guid
                 left join erp_production_mgt_workorder epmw3 on epmw2.parent_classification_guid = epmw3.workorder_guid
                 left join erp_material_mgt_material emmm1 on emmm1.material_guid = epmw3.material_guid
                 left join (select sum(sub_epmw.quantity) as quantity, sub_epmw.parent_classification_guid
                            from erp_production_mgt_workorder sub_epmw
                                     left join erp_production_mgt_workorder p_epmw
                                               on p_epmw.workorder_guid = sub_epmw.parent_classification_guid
                            where p_epmw.workorder_properties = 4
                              and p_epmw.parent_classification_guid is not null
                              -- 过滤外发
                            group by sub_epmw.parent_classification_guid) as a on epmw.workorder_guid
            = a.parent_classification_guid
        where epmw.workorder_properties = 4
          and epmw.parent_classification_guid is not null
          and (epmw.total_quantity - coalesce(a.quantity, 0)) &gt; 0
          and epmw.current_status != '3'
        <if test="qo.startTime !=null and qo.endTime !=null">
            and (epmw.create_date &gt;= #{qo.startTime}
            and epmw.create_date &lt;= #{qo.endTime})
        </if>
        <if test="qo.workorderTypeGuid!=null  and  qo.workorderTypeGuid.size() != 0">
            and
            <foreach collection="qo.workorderTypeGuid" item="key" separator="or" open="(" close=")">
                epmw.workorder_type_guid=#{key}
            </foreach>
        </if>
        <if test="qo.workorderState!=null  and  qo.workorderState.size() != 0">
            and
            <foreach collection="qo.workorderState" item="key" separator="or" open="(" close=")">
                epmw.workorder_state=#{key}
            </foreach>
        </if>
        <if test="qo.auditStatus != null and qo.auditStatus.size() > 0 ">
            and epmw.audit_status in
            <foreach item="item" collection="qo.auditStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="qo.applicationNumber != null and qo.applicationNumber != ''">
            and epmw.workorder_number ILIKE CONCAT('%',#{qo.applicationNumber},'%')
        </if>
        <if test="qo.workorderNumber != null and qo.workorderNumber != ''">
            and epmw2.workorder_number ILIKE CONCAT('%',#{qo.workorderNumber},'%')
        </if>
        <if test="qo.processName != null and qo.processName != ''">
            and ebmppt2.production_processes_type_name ILIKE CONCAT('%',#{qo.processName},'%')
        </if>
        <if test="qo.production != null and qo.production != ''">
            and (emmm.material_name ILIKE CONCAT('%',#{qo.production},'%') or emmm.material_code ILIKE
            CONCAT('%',#{qo.production},'%'))
        </if>
        order by epmw.required_delivery_time desc
    </select>

    <select id="selectProduct" resultType="xy.server.work.entity.model.vo.MaterialMessageVO">
        SELECT emmm.*
        FROM erp_production_mgt_workorder_data_process epmwdp
                 left join erp_production_mgt_workorder epmw on epmw.workorder_guid = epmwdp.workorder_guid
                 LEFT JOIN erp_material_mgt_material emmm
                           ON emmm.material_guid = epmw.product_material_guid
        WHERE (epmwdp.workorder_data_process_guid = #{workOrderProcessGuid} or epmwdp.workorder_guid = #{workOrderProcessGuid})
    </select>

    <!--获取已开外发订单号列表-->
    <select id="selectOutgoingOrderNumbers" resultType="java.lang.String">
        SELECT DISTINCT
            ebmo.order_number
        FROM
            erp_production_mgt_workorder epmw
            LEFT JOIN erp_production_mgt_workorder c_epmw ON c_epmw.parent_classification_guid = epmw.workorder_guid
            LEFT JOIN erp_business_mgt_order_data ebmod ON ebmod.source_guid = c_epmw.workorder_guid
            LEFT JOIN erp_business_mgt_order ebmo ON ebmo.order_guid = ebmod.order_guid
        WHERE
            epmw.parent_classification_guid = #{workorderGuid}
            AND ebmo.order_number IS NOT NULL
    </select>

    <select id="selectOutgoingDataBatch" resultType="xy.server.work.entity.ErpProductionMgtWorkorder">
        select * from erp_production_mgt_workorder epmw
        where epmw.parent_classification_guid in
        <foreach collection="workorderGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectPartMaterialGuidBatch" resultType="xy.server.work.entity.model.dto.BaseMapContainer">
        select epmwdp.workorder_data_process_guid as name,
        emmm.material_guid as value
        from erp_production_mgt_workorder_data_process epmwdp
                 left join erp_production_mgt_workorder epmw on epmw.workorder_guid = epmwdp.workorder_guid
                 left join erp_production_mgt_workorder epmw2 on epmw.parent_classification_guid=epmw2.workorder_guid
                 left join erp_material_mgt_material emmm on emmm.material_guid=epmw2.material_guid
        where epmwdp.workorder_data_process_guid in
            <foreach collection="workOrderProcessGuids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>
    <select id="selectPartMaterialGuidBatchWorkorderGuid" resultType="xy.server.work.entity.model.dto.BaseMapContainer">
        select epmwdp.workorder_guid as name,
               emmm.material_guid as value
        from erp_production_mgt_workorder_data_process epmwdp
                 left join erp_production_mgt_workorder epmw on epmw.workorder_guid = epmwdp.workorder_guid
                 left join erp_production_mgt_workorder epmw2 on epmw.parent_classification_guid=epmw2.workorder_guid
                 left join erp_material_mgt_material emmm on emmm.material_guid=epmw2.material_guid
        where epmwdp.workorder_guid in
        <foreach collection="workOrderProcessGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectPartNameBatch" resultType="xy.server.work.entity.model.dto.BaseMapContainer">
        select
        coalesce(emmm.material_name,'') as value,
        epmwdp.workorder_data_process_guid as name
        from erp_production_mgt_workorder_data_process epmwdp
                 left join erp_production_mgt_workorder epmw on epmw.workorder_guid = epmwdp.workorder_guid
                 left join erp_production_mgt_workorder epmw2 on epmw.parent_classification_guid=epmw2.workorder_guid
                 left join erp_material_mgt_material emmm on emmm.material_guid=epmw2.material_guid
        where epmwdp.workorder_data_process_guid in
        <foreach collection="workOrderProcessGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 通用查询映射结果 -->
    <resultMap id="getSonListBatch" type="xy.server.purchase.entity.model.vo.ErpOutgoingApplicationVO">
        <id column="workorder_guid" property="workorderGuid"/>
        <result column="parent_classification_guid" property="parentWorkOrderGuid"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="proportion" property="proportion"/>
        <result column="quantity" property="outputQuantity"/>
    </resultMap>
    <select id="getSonListBatch" resultMap="getSonListBatch">
        select epmw.workorder_guid,
               epmw.parent_classification_guid,
               epmw.material_guid,
               epmw.proportion
        from erp_production_mgt_workorder epmw
        where epmw.parent_classification_guid in
        <foreach collection="workorderGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectproductNameBatch" resultType="xy.server.work.entity.model.dto.BaseMapContainer">
        SELECT
        coalesce(emmm.material_name,'') as value,
        epmwdp.workorder_data_process_guid as name
        FROM erp_production_mgt_workorder_data_process epmwdp
                 left join erp_production_mgt_workorder epmw on epmw.workorder_guid = epmwdp.workorder_guid
                 LEFT JOIN erp_material_mgt_material emmm
                           ON emmm.material_guid = epmw.product_material_guid
        WHERE epmwdp.workorder_data_process_guid in
        <foreach collection="workOrderProcessGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectproductNameBatchs" resultType="xy.server.work.entity.model.dto.BaseMapContainer">
        SELECT
        (
        SELECT
        string_agg ( material_name, ',' )
        FROM
        erp_material_mgt_material
        WHERE
        material_guid :: VARCHAR IN ( SELECT * FROM regexp_split_to_table( epmw.product_material_guid, ',' ) )
        ) processDataName,
        (
        SELECT
        string_agg ( material_code, ',' )
        FROM
        erp_material_mgt_material
        WHERE
        material_guid :: VARCHAR IN ( SELECT * FROM regexp_split_to_table( epmw.product_material_guid, ',' ) )
        ) processDataCode,
        epmwdp.workorder_data_process_guid AS NAME
        FROM
        erp_production_mgt_workorder_data_process epmwdp
        LEFT JOIN erp_production_mgt_workorder epmw ON epmw.workorder_guid = epmwdp.workorder_guid
        WHERE epmwdp.workorder_data_process_guid in
        <foreach collection="workOrderProcessGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getMaterialUsageListBatch"
            resultType="xy.server.work.entity.model.vo.ErpProductionMgtWorkorderMaterialUsageVO">
         select epmwmu.*
         from erp_production_mgt_workorder_material_usage epmwmu
         <where>
             and epmwmu.workorder_guid in
             <foreach collection="workorderGuids" item="item" open="(" separator="," close=")">
                     #{item}
             </foreach>
         </where>
    </select>
    <select id="getPreviousOutputWorkorderBatch"
            resultType="xy.server.purchase.entity.model.vo.ErpOutgoingApplicationVO">
        select
            outputEpmw.workorder_guid,
            outputEpmw.parent_classification_guid,
            outputEpmw.material_guid,
            outputEpmw.proportion ,
            epmw.workorder_guid processWorkorderGuid
        from erp_production_mgt_workorder epmw
                 left join erp_production_mgt_workorder allProcessEpmw on allProcessEpmw.parent_classification_guid = epmw.parent_classification_guid
                 left join erp_production_mgt_workorder outputEpmw on outputEpmw.parent_classification_guid = allProcessEpmw.workorder_guid
        <where>
          epmw.workorder_properties = 2 and allProcessEpmw.workorder_properties = 2 and outputEpmw.workorder_properties = 19
          and allProcessEpmw.serial_number =  epmw.serial_number - 1
          and epmw.workorder_guid in
          <foreach collection="workorderGuids" item="item" open="(" separator="," close=")">
              #{item}
          </foreach>
        </where>
    </select>
</mapper>
