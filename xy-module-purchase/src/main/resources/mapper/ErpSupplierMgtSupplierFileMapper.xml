<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpSupplierMgtSupplierFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierFileVO">
        <id column="supplier_file_guid" property="supplierFileGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="supplier_guid" property="supplierGuid"/>
        <result column="file_type" property="fileType"/>
        <result column="file_guid" property="fileGuid"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <association property="fileVO" javaType="xy.server.basic.entity.model.vo.ErpBasicMgtFileVO">
            <id column="f_file_guid" property="fileGuid"/>
            <result column="f_file_name" property="fileName"/>
            <result column="f_folder_guid" property="folderGuid"/>
            <result column="f_path" property="path"/>
            <result column="f_description" property="description"/>
            <result column="f_creator_guid" property="creatorGuid"/>
            <result column="f_creator" property="creator"/>
            <result column="f_create_date" property="createDate"/>
            <result column="f_last_updater_guid" property="lastUpdaterGuid"/>
            <result column="f_last_updater" property="lastUpdater"/>
            <result column="f_last_update_date" property="lastUpdateDate"/>
            <result column="f_deleted" property="deleted"/>
            <result column="f_to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        </association>

    </resultMap>

    <sql id="baseSql">
        select
            a.*,
            b.file_guid as f_file_guid,
            b.file_name as f_file_name,
            b.folder_guid as f_folder_guid,
            b.path as f_path,
            b.description as f_file_guid,
            b.creator_guid as f_creator_guid,
            b.creator as f_creator,
            b.create_date as f_create_date,
            b.last_updater_guid as f_last_updater_guid,
            b.last_updater as f_last_updater,
            b.last_update_date as f_last_update_date,
            b.deleted as f_deleted,
            b.to_json as f_to_json,
            b.tenant_guid as f_tenant_guid
        from erp_supplier_mgt_supplier_file a
                 left join erp_basic_mgt_file b on a.file_guid = b.file_guid
    </sql>


    <select id="findPage" resultMap="VoResultMap">
        <include refid="baseSql"/>
        <where>
            <if test="model.supplierGuid != '' and model.supplierGuid != null">
                and a.supplier_guid = #{model.supplierGuid}
            </if>
            <if test="model.fileType != null">
                and a.file_type = #{fileType}
            </if>
            <if test="model.keywords != null and model.keywords.size() != 0">
                and
                <foreach collection="model.keywords" item="item" index="index" open="(" separator="or" close=")" >
                    b.file_name = ILIKE CONCAT('%',#{item},'%')
                    or a.description ILIKE CONCAT('%',#{item},'%')
                </foreach>
            </if>
        </where>
        order by serial_number asc
    </select>

    <select id="findList" resultMap="VoResultMap">
        <include refid="baseSql"/>
        <where>
            <if test="model.supplierGuid != '' and model.supplierGuid != null">
                and a.supplier_guid = #{model.supplierGuid}
            </if>
            <if test="model.fileType != null">
                and a.file_type = #{fileType}
            </if>
            <if test="model.keywords != null and model.keywords.size() != 0">
                and
                <foreach collection="model.keywords" item="item" index="index" open="(" separator="or" close=")" >
                    b.file_name = ILIKE CONCAT('%',#{item},'%')
                    or a.description ILIKE CONCAT('%',#{item},'%')
                </foreach>
            </if>
        </where>
        order by serial_number asc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        <include refid="baseSql"/>
        where supplier_file_guid = #{guid}
    </select>

    <select id="getDataBySupplierGuids" resultMap="VoResultMap">
        <include refid="baseSql"/>
        where supplier_guid in
        <foreach collection="guids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
