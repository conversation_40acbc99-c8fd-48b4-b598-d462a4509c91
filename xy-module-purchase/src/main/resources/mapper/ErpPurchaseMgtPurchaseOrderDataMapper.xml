<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpPurchaseMgtPurchaseOrderDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseOrderDataVO">
        <id column="order_data_guid" property="orderDataGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="parent_classification_guid" property="parentClassificationGuid"/>
        <result column="order_guid" property="orderGuid"/>
        <result column="material_classification_guid" property="materialClassificationGuid"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="unit_guid" property="unitGuid"/>
        <result column="fsc_declaration_guid" property="fscDeclarationGuid"/>
        <result column="experience_production_process_guid" property="experienceProductionProcessGuid"/>
        <result column="quantity" property="quantity"/>
        <result column="gift_quantity" property="giftQuantity"/>
        <result column="spare_quantity" property="spareQuantity"/>
        <result column="using_inventory_quantity" property="usingInventoryQuantity"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="quotation_unit_price_including_tax" property="quotationUnitPriceIncludingTax"/>
        <result column="unit_price_including_tax" property="unitPriceIncludingTax"/>
        <result column="total_amount_including_tax" property="totalAmountIncludingTax"/>
        <result column="quotation_unit_price_without_tax" property="quotationUnitPriceWithoutTax"/>
        <result column="unit_price_without_tax" property="unitPriceWithoutTax"/>
        <result column="total_amount_without_tax" property="totalAmountWithoutTax"/>
        <result column="local_currency_unit_price" property="localCurrencyUnitPrice"/>
        <result column="local_currency_total_amount" property="localCurrencyTotalAmount"/>
        <result column="settlement_unit_price" property="settlementUnitPrice"/>
        <result column="settlement_total_amount" property="settlementTotalAmount"/>
        <result column="external_tracking_number" property="externalTrackingNumber"/>
        <result column="external_material_bar_code" property="externalMaterialBarCode"/>
        <result column="external_material_code" property="externalMaterialCode"/>
        <result column="external_material_name" property="externalMaterialName"/>
        <result column="delivery_date" property="deliveryDate"/>
        <result column="delivery_type_guid" property="deliveryTypeGuid"/>
        <result column="completion_status" property="completionStatus"/>
        <result column="completion_quantity" property="completionQuantity"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <result column="source_guid" property="sourceGuid"/>
        <result column="source_value" property="sourceValue"/>
        <result column="source_p_guid" property="sourcePGuid"/>
        <result column="administrative_area_name" property="administrativeAreaName"/>
        <result column="quotation_quantity" property="quotationQuantity"/>
        <result column="cost_scheme_guid" property="costSchemeGuid"/>
        <result column="scheme_name" property="schemeName"/>
        <result column="order_state" property="orderState"/>
        <result column="expected_receipt_date" property="expectedReceiptDate"/>
        <!--行政区域 _guid-->
        <result column="administrative_area_guid" property="administrativeAreaGuid"/>
        <!--详细地址-->
        <result column="address" property="address"/>
        <!--单位名称-->
        <result column="unit_name" property="unitName"/>
        <!--申购单号-->
        <result column="workorder_number" property="purchaseWorkorderNumber"/>
        <!--申购数量-->
        <result column="workorder_quantity" property="purchaseWorkorderQuantity"/>
        <result column="lock_price" property="lockPrice"/>
        <result column="purchase_order_data_extend_guid" property="purchaseOrderDataExtendGuid"/>

        <!--&lt;!&ndash;物料&ndash;&gt;
        <association property="materialObj" javaType="xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO"
                     column="{guid=material_guid}" select="xy.server.material.mapper.ErpMaterialMgtMaterialMapper.getDataByGuid"/>

        <collection property="detailList" ofType="xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseOrderDataVO"
                    column="{parentClassificationGuid=order_data_guid}" select="findListByParentGuid"/>

        &lt;!&ndash;其他费用&ndash;&gt;
        <collection property="otherExpensesList" ofType="xy.server.purchase.entity.model.vo.ErpBusinessOtherExpensesVO"
                    column="{model.sourceGuid=order_data_guid,model.sourceValus=other_source_valus}"
                    select="xy.server.basic.mapper.ErpBusinessMgtOtherExpensesMapper.findListGuid"/>-->

    </resultMap>

    <update id="deleteByOtherExpensesBatch">
        UPDATE "public"."erp_business_mgt_other_expenses"
        SET "deleted" = 't'
        <where>
            <if test="delDetailIds != null and delDetailIds.size() != 0">
                other_expenses_guid IN
                <foreach collection="delDetailIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>

    <insert id="saveOrUpdateOtherExpensesBatch">
        <if test="otherExpensesList != null and otherExpensesList.size() != 0">
            <foreach collection="otherExpensesList" item="model" open="" separator=";" close="">
                INSERT INTO "public"."erp_business_mgt_other_expenses" (
                "tenant_guid",
                "other_expenses_guid",
                "source_guid",
                "source_valus",
                "other_expenses_type",
                "other_expenses_name",
                "description",
                "other_expenses_including_tax",
                "other_expenses_without_tax",
                "local_currency_other_expenses",
                "creator_guid",
                "creator",
                "create_date",
                "last_updater_guid",
                "last_updater",
                "last_update_date",
                "deleted" )
                values ( #{model.tenantGuid},#{model.otherExpensesGuid},#{model.sourceGuid},#{model.sourceValus},#{model.otherExpensesType},#{model.otherExpensesName}
                , #{model.description}, #{model.otherExpensesIncludingTax}, #{model.otherExpensesWithoutTax}, #{model.localCurrencyOtherExpenses},
                #{model.creatorGuid}, #{model.creator}, #{model.createDate}, #{model.lastUpdaterGuid}, #{model.lastUpdater}, #{model.lastUpdateDate},'f')
                on conflict(other_expenses_guid) do update set
                other_expenses_guid = #{model.otherExpensesGuid},
                source_guid = #{model.sourceGuid},
                source_valus = #{model.sourceValus},
                other_expenses_type = #{model.otherExpensesType},
                other_expenses_name = #{model.otherExpensesName},
                description = #{model.description},
                other_expenses_including_tax = #{model.otherExpensesIncludingTax},
                other_expenses_without_tax = #{model.otherExpensesWithoutTax},
                local_currency_other_expenses = #{model.localCurrencyOtherExpenses},
                last_updater_guid = #{model.lastUpdaterGuid},
                last_updater = #{model.lastUpdater},
                last_update_date = #{model.lastUpdateDate}
            </foreach>
        </if>
    </insert>

    <select id="findListByOrderGuid" resultMap="VoResultMap">
        SELECT od.*,
               u.unit_name,
               '2' as other_source_valus,
               wo.workorder_number,
               pwo.workorder_guid as source_p_guid,
               pwo.quantity       AS workorder_quantity,
               pod.administrative_area_guid,
               pod.address,
               ebmaa.administrative_area_name,
               od.cost_scheme_guid,
               ecs.scheme_name
        FROM erp_business_mgt_order_data od
                 LEFT JOIN erp_business_mgt_order_place_of_delivery pod ON od.order_data_guid = pod.order_data_guid
                 LEFT JOIN erp_basic_mgt_administrative_area ebmaa
                           on pod.administrative_area_guid = ebmaa.administrative_area_guid
                 LEFT JOIN erp_basic_mgt_unit u ON od.unit_guid = u.unit_guid
                 LEFT JOIN erp_production_mgt_workorder wo ON od.source_guid = wo.workorder_guid
                 LEFT JOIN erp_production_mgt_workorder pwo ON wo.parent_classification_guid = pwo.workorder_guid
                 left join erp_cost_scheme ecs on ecs.cost_scheme_guid = od.cost_scheme_guid
        where od.deleted = false
          and od.order_guid = #{orderGuid}
        ORDER BY delivery_date
    </select>
    <resultMap id="DoResultMap" type="xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseOrderDataVO">
        <id column="order_data_guid" property="orderDataGuid"/>
        <id column="source_number" property="sourceNumber"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="parent_classification_guid" property="parentClassificationGuid"/>
        <result column="order_guid" property="orderGuid"/>
        <result column="material_classification_guid" property="materialClassificationGuid"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="unit_guid" property="unitGuid"/>
        <result column="fsc_declaration_guid" property="fscDeclarationGuid"/>
        <result column="experience_production_process_guid" property="experienceProductionProcessGuid"/>
        <result column="quantity" property="quantity"/>
        <result column="gift_quantity" property="giftQuantity"/>
        <result column="spare_quantity" property="spareQuantity"/>
        <result column="using_inventory_quantity" property="usingInventoryQuantity"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="quotation_unit_price_including_tax" property="quotationUnitPriceIncludingTax"/>
        <result column="unit_price_including_tax" property="unitPriceIncludingTax"/>
        <result column="total_amount_including_tax" property="totalAmountIncludingTax"/>
        <result column="quotation_unit_price_without_tax" property="quotationUnitPriceWithoutTax"/>
        <result column="unit_price_without_tax" property="unitPriceWithoutTax"/>
        <result column="total_amount_without_tax" property="totalAmountWithoutTax"/>
        <result column="local_currency_unit_price" property="localCurrencyUnitPrice"/>
        <result column="local_currency_total_amount" property="localCurrencyTotalAmount"/>
        <result column="settlement_unit_price" property="settlementUnitPrice"/>
        <result column="settlement_total_amount" property="settlementTotalAmount"/>
        <result column="external_tracking_number" property="externalTrackingNumber"/>
        <result column="external_material_bar_code" property="externalMaterialBarCode"/>
        <result column="external_material_code" property="externalMaterialCode"/>
        <result column="external_material_name" property="externalMaterialName"/>
        <result column="delivery_date" property="deliveryDate"/>
        <result column="delivery_type_guid" property="deliveryTypeGuid"/>
        <result column="completion_status" property="completionStatus"/>
        <result column="completion_quantity" property="completionQuantity"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
        <result column="source_guid" property="sourceGuid"/>
        <result column="source_value" property="sourceValue"/>
        <result column="source_p_guid" property="sourcePGuid"/>
        <result column="administrative_area_name" property="administrativeAreaName"/>
        <result column="quotation_quantity" property="quotationQuantity"/>
        <result column="cost_scheme_guid" property="costSchemeGuid"/>
        <result column="scheme_name" property="schemeName"/>
        <result column="order_state" property="orderState"/>
        <!--行政区域 _guid-->
        <result column="administrative_area_guid" property="administrativeAreaGuid"/>
        <!--详细地址-->
        <result column="address" property="address"/>
        <!--单位名称-->
        <result column="unit_name" property="unitName"/>
        <!--申购单号-->
        <result column="workorder_number" property="purchaseWorkorderNumber"/>
        <!--申购数量-->
        <result column="workorder_quantity" property="purchaseWorkorderQuantity"/>
        <result column="quantity_per_package" property="quantityPerPackage"/>
        <result column="packaging_quantity" property="packagingQuantity"/>
        <result column="purchase_order_data_extend_guid" property="purchaseOrderDataExtendGuid"/>
        <!--物料-->
        <!--<association property="materialObj" javaType="xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO"-->
        <!--             column="{guid=material_guid}" select="xy.server.material.mapper.ErpMaterialMgtMaterialMapper.getDataByGuid"/>-->
    </resultMap>
    <select id="findListByParentGuid" resultMap="DoResultMap">
        SELECT od.*,
        u.unit_name,
        wo.workorder_number,
        pwo.workorder_guid as source_p_guid,
        pwo.quantity AS workorder_quantity,
        pod.administrative_area_guid,
        pod.address,
        ebmaa.administrative_area_name,
        od.cost_scheme_guid,
        ecs.scheme_name,
        coalesce(v.source_number,wo.workorder_number) as source_number
        FROM erp_business_mgt_order_data od
        LEFT JOIN erp_business_mgt_order_place_of_delivery pod ON od.order_data_guid = pod.order_data_guid
        LEFT JOIN erp_basic_mgt_administrative_area ebmaa
        on pod.administrative_area_guid = ebmaa.administrative_area_guid
        LEFT JOIN erp_basic_mgt_unit u ON od.unit_guid = u.unit_guid
        LEFT JOIN erp_production_mgt_workorder wo ON od.source_guid = wo.workorder_guid
        LEFT JOIN erp_production_mgt_workorder pwo ON wo.parent_classification_guid = pwo.workorder_guid
        left join erp_cost_scheme ecs on ecs.cost_scheme_guid = od.cost_scheme_guid
        LEFT JOIN view_erp_data_source v ON wo.source_guid = v.source_guid
        where
        od.deleted=false
        <if test="parentClassificationGuid != null and parentClassificationGuid !=''">
            and od.parent_classification_guid=#{parentClassificationGuid}
        </if>
        ORDER BY delivery_date
    </select>
    <select id="getSourceDataS" resultType="xy.server.purchase.entity.ErpPurchaseMgtPurchaseOrderData">
        select ebmod2.* from erp_business_mgt_order_data ebmod left join erp_business_mgt_order_data ebmod2 on ebmod.order_data_guid=ebmod2.parent_classification_guid
        where ebmod.order_guid=#{orderGuid}
    </select>

    <select id="findListByOrderGuids" resultMap="VoResultMap">
        SELECT od.*,
               u.unit_name,
               '2' as other_source_valus,
               wo.workorder_number,
               pwo.workorder_guid as source_p_guid,
               pwo.quantity       AS workorder_quantity,
               pod.administrative_area_guid,
               pod.address,
               ebmaa.administrative_area_name,
               od.cost_scheme_guid,
               ecs.scheme_name,lock_price,
        ebmpode.purchase_order_data_extend_guid
        FROM erp_business_mgt_order_data od
        left join erp_business_mgt_purchase_order_data_extend ebmpode on od.order_data_guid=ebmpode.order_data_guid
        LEFT JOIN erp_business_mgt_order_place_of_delivery pod ON od.order_data_guid = pod.order_data_guid
                 LEFT JOIN erp_basic_mgt_administrative_area ebmaa
                           on pod.administrative_area_guid = ebmaa.administrative_area_guid
                 LEFT JOIN erp_basic_mgt_unit u ON od.unit_guid = u.unit_guid
                 LEFT JOIN erp_production_mgt_workorder wo ON od.source_guid = wo.workorder_guid
                 LEFT JOIN erp_production_mgt_workorder pwo ON wo.parent_classification_guid = pwo.workorder_guid
                 left join erp_cost_scheme ecs on ecs.cost_scheme_guid = od.cost_scheme_guid
        where od.deleted = false
          and od.order_guid IN
        <foreach collection="orderGuids" item="orderGuid" open="(" separator="," close=")">
            #{orderGuid}
        </foreach>
        ORDER BY delivery_date, od.order_data_guid
    </select>

    <select id="findListByParentGuids" resultMap="DoResultMap">
        SELECT
            od.*,
            u.unit_name,
            wo.workorder_number,
            pwo.workorder_guid as source_p_guid,
            pwo.quantity AS workorder_quantity,
            pod.administrative_area_guid,
            pod.address,
            ebmaa.administrative_area_name,
            od.cost_scheme_guid,
            ecs.scheme_name,
            ebmpode.quantity_per_package,
            ebmpode.packaging_quantity,
            ebmpode.purchase_order_data_extend_guid,
            (select source_number from view_erp_data_source where source_guid = wo.source_guid) as source_number,
            business_ebmod.external_material_bar_code AS business_external_material_bar_code,
            business_ebmod.external_material_code AS business_external_material_code,
            business_ebmod.external_material_name AS business_external_material_name,
            business_ebmod.external_tracking_number AS business_external_tracking_number
        FROM erp_business_mgt_order_data od
        left join erp_business_mgt_purchase_order_data_extend ebmpode on od.order_data_guid=ebmpode.order_data_guid
        LEFT JOIN erp_business_mgt_order_place_of_delivery pod ON od.order_data_guid = pod.order_data_guid
        LEFT JOIN erp_basic_mgt_administrative_area ebmaa
        on pod.administrative_area_guid = ebmaa.administrative_area_guid
        LEFT JOIN erp_basic_mgt_unit u ON od.unit_guid = u.unit_guid
        LEFT JOIN erp_production_mgt_workorder wo ON od.source_guid = wo.workorder_guid
        LEFT JOIN erp_production_mgt_workorder pwo ON wo.parent_classification_guid = pwo.workorder_guid
        LEFT JOIN erp_business_mgt_order_data business_ebmod ON business_ebmod.order_data_guid = pwo.source_guid
        left join erp_cost_scheme ecs on ecs.cost_scheme_guid = od.cost_scheme_guid
        -- todo 来源需要优化
        -- LEFT JOIN view_erp_data_source v ON wo.source_guid = v.source_guid
        where
            od.deleted=false
            and od.parent_classification_guid IN
            <foreach collection="orderDataGuids" item="orderDataGuid" open="(" separator="," close=")">
                #{orderDataGuid}
            </foreach>
        ORDER BY delivery_date
    </select>

    <!--根据采购订单明细guids获取业务订单数据List（采购成品流程链路）-->
    <select id="selectBusinessOrderDataListByPurchaseOrderDataGuids"
            resultType="xy.server.purchase.entity.model.vo.ErpPurchaseMgtBusinessOrderDataVO">
        SELECT
            pu_ebmod.order_data_guid AS purchase_order_data_guid,
<!--            business_ebmod.order_data_guid AS business_order_data_guid,-->
<!--            business_ebmo.order_number AS business_order_number,-->
<!--            business_ebmod.external_tracking_number AS business_external_tracking_number,-->
<!--            business_ebmod.external_material_bar_code AS business_external_material_bar_code,-->
<!--            business_ebmod.external_material_code AS business_external_material_code,-->
<!--            business_ebmod.external_material_name AS business_external_material_name-->
            string_agg ( DISTINCT business_ebmod.order_data_guid, ',' ) AS business_order_data_guid,
            string_agg ( DISTINCT business_ebmo.order_number, ',' ) AS business_order_number,
            string_agg ( DISTINCT business_ebmod.external_tracking_number, ',' ) AS business_external_tracking_number,
            string_agg ( DISTINCT business_ebmod.external_material_bar_code, ',' ) AS business_external_material_bar_code,
            string_agg ( DISTINCT business_ebmod.external_material_code, ',' ) AS business_external_material_code,
            string_agg ( DISTINCT business_ebmod.external_material_name, ',' ) AS business_external_material_name
        FROM
            erp_business_mgt_order_data pu_ebmod
            LEFT JOIN erp_business_mgt_order pu_ebmo ON pu_ebmo.order_guid = pu_ebmod.order_guid
            LEFT JOIN erp_business_mgt_order_data c_pu_ebmod ON c_pu_ebmod.parent_classification_guid = pu_ebmod.order_data_guid
            LEFT JOIN erp_production_mgt_workorder pu_virtual_epmw ON pu_virtual_epmw.workorder_guid = c_pu_ebmod.source_guid
            LEFT JOIN erp_production_mgt_workorder pu_epmw ON pu_epmw.workorder_guid = pu_virtual_epmw.parent_classification_guid
            LEFT JOIN erp_business_mgt_order_data business_ebmod ON business_ebmod.order_data_guid = pu_epmw.source_guid
            LEFT JOIN erp_business_mgt_order business_ebmo ON business_ebmo.order_guid = business_ebmod.order_guid
        WHERE
            business_ebmo.order_properties = 1
            AND pu_ebmo.order_properties = 2
            AND pu_epmw.source_value = 2
            AND pu_ebmod.order_data_guid IN
            <foreach collection="purchaseOrderDataGuids" item="guid" open="(" separator="," close=")">
                #{guid}
            </foreach>
        GROUP BY
            pu_ebmod.order_data_guid
    </select>

    <select id="getPurchaseRequisitionWorkOrderData"
            resultType="xy.server.purchase.entity.model.vo.PurchaseRequisitionWorkOrderDataVO">
        SELECT epmw.workorder_guid,
               epmw2.quantity,
               -- 产出物料
               epmw19.material_guid,
               -- 产出比
               epmw19.proportion,
               epmwmu.material_loss_quantity,
               epmwmu.material_total_quantity,
               epmwmu.material_usage_quantity,

               epmwdp.output_quantity,
               epmwdp.loss_quantity,
               epmwdp.adjust_loss_quantity
        FROM
            -- 采购申请明细
            erp_production_mgt_workorder epmw
                LEFT JOIN erp_production_mgt_workorder_material_usage epmwmu
                          ON epmw.source_guid = epmwmu.workorder_material_usage_guid
                LEFT JOIN erp_production_mgt_workorder epmw2 ON epmwmu.workorder_guid = epmw2.workorder_guid
                LEFT JOIN erp_production_mgt_workorder epmw19
                          ON epmw19.parent_classification_guid = epmw2.workorder_guid
                LEFT JOIN erp_production_mgt_workorder_data_process epmwdp
                          ON epmwdp.workorder_guid = epmw2.workorder_guid
        WHERE
            epmw.workorder_guid in
        <foreach collection="workorderGuids" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        and epmw.workorder_properties = '3'
    </select>
</mapper>
