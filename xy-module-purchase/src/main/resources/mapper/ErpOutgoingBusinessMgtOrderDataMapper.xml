<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.purchase.mapper.ErpOutgoingBusinessMgtOrderDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.purchase.entity.model.vo.ErpOutgoingBusinessMgtOrderDataVO">
        <id column="order_data_guid" property="orderDataGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="order_guid" property="orderGuid"/>
        <result column="material_classification_guid" property="materialClassificationGuid"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="unit_guid" property="unitGuid"/>
        <result column="fsc_declaration_guid" property="fscDeclarationGuid"/>
        <result column="experience_production_process_guid" property="experienceProductionProcessGuid"/>
        <result column="quantity" property="quantity"/>
        <result column="gift_quantity" property="giftQuantity"/>
        <result column="spare_quantity" property="spareQuantity"/>
        <result column="using_inventory_quantity" property="usingInventoryQuantity"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="quotation_unit_price_including_tax" property="quotationUnitPriceIncludingTax"/>
        <result column="unit_price_including_tax" property="unitPriceIncludingTax"/>
        <result column="total_amount_including_tax" property="totalAmountIncludingTax"/>
        <result column="quotation_unit_price_without_tax" property="quotationUnitPriceWithoutTax"/>
        <result column="unit_price_without_tax" property="unitPriceWithoutTax"/>
        <result column="total_amount_without_tax" property="totalAmountWithoutTax"/>
        <result column="local_currency_unit_price" property="localCurrencyUnitPrice"/>
        <result column="local_currency_total_amount" property="localCurrencyTotalAmount"/>
        <result column="settlement_unit_price" property="settlementUnitPrice"/>
        <result column="settlement_total_amount" property="settlementTotalAmount"/>
        <result column="external_tracking_number" property="externalTrackingNumber"/>
        <result column="external_material_bar_code" property="externalMaterialBarCode"/>
        <result column="external_material_code" property="externalMaterialCode"/>
        <result column="external_material_name" property="externalMaterialName"/>
        <result column="delivery_date" property="deliveryDate"/>
        <result column="delivery_type_guid" property="deliveryTypeGuid"/>
        <result column="settlement_type_guid" property="settlementTypeGuid"/>
        <result column="completion_status" property="completionStatus"/>
        <result column="completion_quantity" property="completionQuantity"/>
        <result column="description" property="description"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
        <result column="to_json" property="toJson" typeHandler="com.xunyue.common.util.JsonbTypeHandler"/>
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select *
        from erp_business_mgt_order_data
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select *
        from erp_business_mgt_order_data
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select *
        from erp_business_mgt_order_data
        where order_data_guid = #{guid}
    </select>
</mapper>
