package xy.server.purchase.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.config.dataScope.DataScope;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.purchase.entity.ErpOutgoingDataProcess;
import xy.server.purchase.entity.model.qo.ErpOutgoingMgtApplicationQO;
import xy.server.purchase.entity.model.qo.ErpOutgoingQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingApplicationFormRO;
import xy.server.purchase.entity.model.vo.ErpOutgoingApplicationFormVO;
import xy.server.purchase.entity.model.vo.ErpOutgoingApplicationVO;
import xy.server.purchase.entity.model.vo.ErpOutgoingMgtApplicationVO;
import xy.server.work.entity.ErpProductionMgtWorkorder;
import xy.server.work.entity.model.dto.BaseMapContainer;
import xy.server.work.entity.model.vo.ErpProductionMgtWorkorderMaterialUsageVO;
import xy.server.work.entity.model.vo.MaterialMessageVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 工单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Mapper
public interface ErpOutgoingMgtApplicationMapper extends BaseMapper<ErpProductionMgtWorkorder> {

        /**
         * 根据GUID获取
         * @param guid
         * @return
         */
    ErpOutgoingMgtApplicationVO getDataByGuid(@Param("guid") String guid);

        /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpOutgoingMgtApplicationVO> findList(@Param("model") ErpOutgoingMgtApplicationQO qo);

        /**
       * 分页查询
       * @param page
       * @param model
       * @return
       */
    IPage<ErpOutgoingMgtApplicationVO> findPage(@Param("page") IPage<ErpOutgoingMgtApplicationVO> page, @Param("model") ErpOutgoingMgtApplicationQO model);

    List<ErpProductionMgtWorkorder> selectOutgoingData(@Param("workorderGuid") String workorderGuid);

    List<ErpProductionMgtWorkorder> selectOutgoingDataBatch(@Param("workorderGuids") List<String> workorderGuids);

    String selectMateilData(@Param("workorderGuid") String workorderGuid);

    String selectproductData(@Param("workorderGuid") String workorderGuid);

    @DataScope(component = "outgoing-application-order")
    List<String> selectOutgoing(@Param("page") IPage<ErpOutgoingApplicationFormVO> page,@Param("qo") ErpOutgoingMgtApplicationQO qo);
    IPage<ErpOutgoingApplicationFormRO> selectMessages(@Param("page") IPage<ErpOutgoingQO> page, @Param("qo") ErpOutgoingQO qo);

    List<ErpProductionMgtWorkorder> selectMosterGuid(@Param("qo") ErpOutgoingQO qo);

    List<ErpProductionMgtWorkorder> specificFindList(@Param("qo") ErpOutgoingQO qo, @Param("workorderGuid") String workorderGuid);

    /**
     * 根据外发申请id获取来源工序工单数据
     * @param workorderGuid
     * @return
     */
    ErpOutgoingDataProcess selectSourceProcess(@Param("workorderGuid") String workorderGuid);

    String selectPartName(@Param("workOrderProcessGuid") String workOrderProcessGuid);

    List<BaseMapContainer> selectPartNameBatch(@Param("workOrderProcessGuids") List<String> workOrderProcessGuid);

    String selectPartMaterialGuid(@Param("workOrderProcessGuid") String workOrderProcessGuid);

    /**
     * 根据workOrderProcessGuid批量获取部件物料Guid
     * @param workOrderProcessGuid
     * @return
     */
    List<BaseMapContainer> selectPartMaterialGuidBatch(@Param("workOrderProcessGuids") List<String> workOrderProcessGuid);

    /**
     * 根据workOrderProcessGuid批量获取部件物料Guid
     * @param workOrderProcessGuid
     * @return
     */
    List<BaseMapContainer> selectPartMaterialGuidBatchWorkorderGuid(@Param("workOrderProcessGuids") List<String> workOrderProcessGuid);

    String selectproductName(@Param("workOrderProcessGuid") String workOrderProcessGuid);

    List<BaseMapContainer> selectproductNameBatch(@Param("workOrderProcessGuids") List<String> workOrderProcessGuids);

    List<BaseMapContainer> selectproductNameBatchs(@Param("workOrderProcessGuids") List<String> workOrderProcessGuids);

    MaterialMessageVO selectProduct(@Param("workOrderProcessGuid") String workOrderProcessGuid);

    /**
     * 通过工序工单id,获取产出工单集合
     * @param workorderGuid
     * @return
     */
    List<ErpOutgoingApplicationVO> getSonList(@Param("workorderGuid") String workorderGuid, @Param("quantity") BigDecimal quantity);

    /**
     * 通过工序工单id,获取产出工单集合
     * @param workorderGuids
     * @return
     */
    List<ErpOutgoingApplicationVO> getSonListBatch(@Param("workorderGuids") List<String> workorderGuids);

    /**
     * 获取外发申请单未开订单的工序
     * @param qo
     * @return
     */
    List<ErpOutgoingApplicationFormRO> getNewFindList(@Param("qo") ErpOutgoingMgtApplicationQO qo);

    /**
     * 获取已开外发订单号列表
     * @param workorderGuid
     * @return
     */
    List<String> selectOutgoingOrderNumbers(@Param("workorderGuid") String workorderGuid);

    /**
     *  通过工序工单id,获取物料用量集合
     * @param workorderGuids
     * @return
     */
    List<ErpProductionMgtWorkorderMaterialUsageVO> getMaterialUsageListBatch(@Param("workorderGuids") List<String> workorderGuids);

    /**
     * 通过工序工单id,获取上道工序的产出
     * @param workorderGuids
     * @return
     */
    List<ErpOutgoingApplicationVO> getPreviousOutputWorkorderBatch(@Param("workorderGuids") List<String> workorderGuids);
}
