package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.purchase.entity.ErpMaterialMgtMaterialQuotationReceipt;
import xy.server.purchase.entity.model.qo.ErpMaterialMgtMaterialQuotationReceiptQO;
import xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationReceiptDetailVO;
import xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationReceiptVO;
import xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationVO;
import xy.server.purchase.entity.ErpMaterialMgtMaterialQuotationReceipt;
import xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationReceiptDetailVO;
import xy.server.purchase.entity.model.vo.ErpMaterialQuotationVO;
import xy.server.work.entity.ErpProductionMgtExperienceProductionProcessExternal;

import java.util.List;

/**
 * <p>
 * 物料报价表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Mapper
public interface ErpMaterialMgtMaterialQuotationReceiptMapper extends BaseMapper<ErpMaterialMgtMaterialQuotationReceipt> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    ErpMaterialMgtMaterialQuotationReceiptVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpMaterialMgtMaterialQuotationReceiptVO> findList(@Param("model") ErpMaterialMgtMaterialQuotationReceiptQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<ErpMaterialMgtMaterialQuotationReceiptVO> findPage(@Param("page") IPage<ErpMaterialMgtMaterialQuotationReceiptVO> page,@Param("model") ErpMaterialMgtMaterialQuotationReceiptQO model);

    /**
     * 根据客户或者供应商名称获取guid
     * @param customerOrSupplierGuid
     * @return
     */
    String getSupplierGuid(@Param("customerOrSupplierGuid") String customerOrSupplierGuid);

    String getCustomerGuid(@Param("customerOrSupplierGuid") String customerOrSupplierGuid);

    /**
     * 根据客户报价id获取报价单价以及客户id,以及产品资料额外数据id
     * @param list
     * @return
     */
    List<ErpMaterialQuotationVO> getExperienceExternal(@Param("list") List<String> list);

    /**
     * 平铺明细数据
     * @param page
     * @param model
     * @return
     */
    IPage<ErpMaterialMgtMaterialQuotationVO> findDetailPage(@Param("page") IPage<ErpMaterialMgtMaterialQuotationReceiptVO> page, @Param("model") ErpMaterialMgtMaterialQuotationReceiptQO model);

    /**
     * 获取最新的采购报价
     * @param qo
     * @return
     */
    List<ErpMaterialMgtMaterialQuotationReceiptDetailVO> getNewMoney(@Param("qo") ErpMaterialMgtMaterialQuotationReceiptQO qo);
}
