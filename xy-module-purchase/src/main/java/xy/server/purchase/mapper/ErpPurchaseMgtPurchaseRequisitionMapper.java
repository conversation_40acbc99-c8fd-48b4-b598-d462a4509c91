package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.config.dataScope.DataScope;
import com.xunyue.config.mybatisplus.annotation.IgnoreLogicDeleteInterceptor;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.basic.entity.model.vo.ViewErpDataSourceVO;
import xy.server.filter.aspect.XyViewFilter;
import xy.server.filter.aspect.XyViewFilterParam;
import xy.server.filter.enums.TableIdentificationEnum;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseRequisition;
import xy.server.purchase.entity.model.qo.ErpPurchaseMgtPurchaseRequisitionQO;
import xy.server.purchase.entity.model.vo.ErpOrderDataVO;
import xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseRequisitionVO;
import xy.server.purchase.entity.model.vo.ErpPurchaseSourceRequisitionVO;
import xy.server.work.entity.model.dto.BaseMapContainer;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2023/9/13 15:18
 * @apiNote 采购申请单mapper
 */
@Mapper
public interface ErpPurchaseMgtPurchaseRequisitionMapper extends BaseMapper<ErpPurchaseMgtPurchaseRequisition> {
    /**
     * 根据guid查询一条数据
     * @param workorderGuid
     * @return
     */
    ErpPurchaseMgtPurchaseRequisitionVO getOneById(@Param("guid") String workorderGuid);

    @DataScope(component = "purchasing-requisition")
    List<ErpPurchaseMgtPurchaseRequisitionVO> findList(@Param("model")ErpPurchaseMgtPurchaseRequisitionQO qo);

    @DataScope(component = "purchasing-requisition")
    IPage<ErpPurchaseMgtPurchaseRequisitionVO> findPage(@Param("page")IPage<ErpPurchaseMgtPurchaseRequisitionVO> page,  @Param("model")ErpPurchaseMgtPurchaseRequisitionQO model);

    /**
     * 查询采购申请单明细列表
     * @param pWorkorderGuid 父工单guid
     * @return
     */
    List<ErpPurchaseMgtPurchaseRequisitionVO> findDetailList(@Param("pGuid") String pWorkorderGuid);

    /**
     * 查询采购申请单明细列表
     * @param pWorkorderGuids 父工单guid
     * @return
     */
    List<ErpPurchaseMgtPurchaseRequisitionVO> findDetailListBatch(@Param("pGuids") List<String> pWorkorderGuids);

    /**
     * 采购申请单采购情况
     * @param workorderGuid 采购申请单guid
     * @return
     */
    String getPurchaseState(@Param("workorderGuid") String workorderGuid);

    /**
     * 批量获取采购申请单采购情况
     * @param workorderGuids
     * @return
     */
    List<BaseMapContainer> getPurchaseStateBatch(@Param("workorderGuids") List<String> workorderGuids);

    /**
     * 待开列表查询
     * @param qo
     * @return
     */
    @XyViewFilter(tableIdentificationEnum = TableIdentificationEnum.erp_purchasing_mgt_application_wait_order)
    List<ErpPurchaseMgtPurchaseRequisitionVO> findNotBilledList(@XyViewFilterParam @Param("model") ErpPurchaseMgtPurchaseRequisitionQO qo);
    List<ErpPurchaseMgtPurchaseRequisitionVO> getBilledQuantity(@Param("workorderGuids")List<String> workorderGuids);

    /**
     * 获取来源
     * @param sourceGuid
     * @return
     */
    List<ViewErpDataSourceVO> getViewErpDataSourceBySourceGuids(@Param("sourceGuids")List<String> sourceGuid);

    /**
     * 采购申请数据源查询
     * @param qo
     * @return
     */
    @IgnoreLogicDeleteInterceptor
    List<ErpPurchaseSourceRequisitionVO> waitingFindList(@Param("model") ErpPurchaseMgtPurchaseRequisitionQO qo);

    BigDecimal getWorkOrderWaitingQuantity();

    /**
     * 获取订单明细数据
     * @param sourceGuid
     * @return
     */
    ErpOrderDataVO selectOrderData(@Param("sourceGuid") String sourceGuid);

    void updateSaleOrderData(@Param("total") BigDecimal total, @Param("value") String value, @Param("saleOrderDataGuid") String saleOrderDataGuid);

    List<ErpPurchaseSourceRequisitionVO> partWaitingFindLists(@Param("model") ErpPurchaseMgtPurchaseRequisitionQO qo);

    List<ErpPurchaseSourceRequisitionVO> waitingFindLists(@Param("model") ErpPurchaseMgtPurchaseRequisitionQO qo);

    List<ErpPurchaseSourceRequisitionVO> orderDataFindList(@Param("model") ErpPurchaseMgtPurchaseRequisitionQO qo);

    List<ErpPurchaseSourceRequisitionVO> materialFindList(@Param("model") ErpPurchaseMgtPurchaseRequisitionQO qo);

    /**
     * 工单-模具来源待开数量
     * @param qo
     * @return
     */
    List<ErpPurchaseSourceRequisitionVO> mouldWaitingFindList(@Param("model") ErpPurchaseMgtPurchaseRequisitionQO qo);
    /**
     * 待开列表查询
     * @param qo
     * @return
     */
    @XyViewFilter(tableIdentificationEnum = TableIdentificationEnum.erp_purchasing_mgt_application_wait_order)
    List<ErpPurchaseMgtPurchaseRequisitionVO> findNotBilledListES(@XyViewFilterParam @Param("model") ErpPurchaseMgtPurchaseRequisitionQO qo);
}
