package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.vo.SourceDescriptionAndPictureVO;
import com.xunyue.config.dataScope.DataScope;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.filter.aspect.XyViewFilter;
import xy.server.filter.aspect.XyViewFilterParam;
import xy.server.filter.enums.TableIdentificationEnum;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseOrder;
import xy.server.purchase.entity.model.qo.ErpGetNewDataQO;
import xy.server.purchase.entity.model.qo.ErpPurchaseMgtPurchaseOrderQO;
import xy.server.purchase.entity.model.vo.ErpPurchaseInventoryWorkorderDetailVO;
import xy.server.purchase.entity.model.vo.ErpPurchaseMgtHistoricalUnitPriceVO;
import xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseOrderVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 采购订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Mapper
public interface ErpPurchaseMgtPurchaseOrderMapper extends BaseMapper<ErpPurchaseMgtPurchaseOrder> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpPurchaseMgtPurchaseOrderVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    @DataScope(component = "purchasing-order")
    @XyViewFilter(tableIdentificationEnum = TableIdentificationEnum.erp_purchasing_mgt_order)
    List<ErpPurchaseMgtPurchaseOrderVO> findList(@XyViewFilterParam @Param("model") ErpPurchaseMgtPurchaseOrderQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    @DataScope(component = "purchasing-order")
    @XyViewFilter(tableIdentificationEnum = TableIdentificationEnum.erp_purchasing_mgt_order)
    IPage<ErpPurchaseMgtPurchaseOrderVO> findPage(@Param("page") IPage<ErpPurchaseMgtPurchaseOrderVO> page,
                                                  @XyViewFilterParam @Param("model") ErpPurchaseMgtPurchaseOrderQO model);

    /**
     * 查询订单到货情况
     * @param orderGuid
     * @return
     */
    String getArrivalState(@Param("orderGuid") String orderGuid);

    /**
     * 待开列表查询
     * @param qo
     * @return
     */
    List<ErpPurchaseInventoryWorkorderDetailVO> findNotBilledList(@Param("model") ErpPurchaseMgtPurchaseOrderQO qo);

    /**
     * 待开列表查询-只查询部分数据（这里查询出全部数据，内存里面去筛选已经开完的）
     * @param qo
     * @return
     */
    @XyViewFilter(tableIdentificationEnum = TableIdentificationEnum.erp_inventory_mgt_material_arrival_fast_wait)
    List<ErpPurchaseInventoryWorkorderDetailVO> findNotBilledListSimple(@XyViewFilterParam @Param("model") ErpPurchaseMgtPurchaseOrderQO qo);

    /**
     * 待入库数量
     * @param workorderGuids
     * @return
     */
    List<ErpPurchaseInventoryWorkorderDetailVO> findNotBilledListNotBilledQuantity(@Param("workorderGuids") List<String> workorderGuids);

    /**
     * 待开列表查询 - 生产工单号
     * @param orderDataGuids
     * @return
     */
    List<ErpPurchaseInventoryWorkorderDetailVO> findNotBilledListWorkorderNumber(@Param("orderDataGuids") List<String> orderDataGuids);

    /**
     * 查询采购订单待开数量
     * @return
     */
    BigDecimal selectCountPendingList();

    /**
     * 获取顶级来源备注和图片
     * @param sourceGuids
     * @return
     */
    List<SourceDescriptionAndPictureVO> getSourceDescriptionAndPicture(@Param("sourceGuids") List<String> sourceGuids);

    /**
     * 分页查看历史单价列表
     * @param page
     * @param model
     * @return
     */
    IPage<ErpPurchaseMgtHistoricalUnitPriceVO> selectHistoricalUnitPricePage(@Param("page") IPage<ErpPurchaseMgtHistoricalUnitPriceVO> page,
                                                                             @Param("model") ErpPurchaseMgtPurchaseOrderQO model);

    /**
     * 获取最新到货单价
     * @param supplierGuid
     * @param materialGuid
     * @return
     */
    BigDecimal getLatestArrivalPrice(@Param("supplierGuid") String supplierGuid, @Param("materialGuid") String materialGuid);
    /**
     * 获取某个客户的某个物料最近的每件数量
     * @param qo
     * @return
     */
    BigDecimal getNewData(@Param("qo") ErpGetNewDataQO qo);

    /**
     * 根据采购单号查询是否还有下流程
     * @param workorderNumber
     * @return
     */
    int findNextFlow(@Param("workorderNumber") String workorderNumber);
}
