package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.vo.SourceDescriptionAndPictureVO;
import com.xunyue.config.dataScope.DataScope;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.purchase.entity.ErpOutgoingBusinessMgtOrder;
import xy.server.purchase.entity.model.qo.ErpOutgoingBusinessMgtOrderQO;
import xy.server.purchase.entity.model.qo.ErpPurchaseMgtPurchaseOrderQO;
import xy.server.purchase.entity.model.qo.ErpPurchaseProcessOutgoingOrderQO;
import xy.server.purchase.entity.model.vo.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Mapper
public interface ErpOutgoingBusinessMgtOrderMapper extends BaseMapper<ErpOutgoingBusinessMgtOrder> {

        /**
         * 根据GUID获取
         * @param guid
         * @return
         */
    ErpOutgoingBusinessMgtOrderVO getDataByGuid(@Param("guid") String guid);

    /**
     * 根据GUID List获取
     * @warning 注意：guids 不可为空
     * @param guid
     * @return
     */
    List<ErpOutgoingBusinessMgtOrderVO> findDataByGuids(@Param("guids") List<String> guid);

        /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpOutgoingBusinessMgtOrderVO> findList(@Param("model") ErpOutgoingBusinessMgtOrderQO qo);

        /**
       * 分页查询
       * @param page
       * @param model
       * @return
       */
    IPage<ErpOutgoingBusinessMgtOrderVO> findPage(@Param("page") IPage<ErpOutgoingBusinessMgtOrderVO> page, @Param("model") ErpOutgoingBusinessMgtOrderQO model);

    /**
     * 获取供应商名称
     * @param orderGuid
     * @return
     */
    String getSupplierName(@Param("orderGuid") String orderGuid);

    /**
     * 获取结算供应商名称
     * @param orderGuid
     * @return
     */
    String getSettlementSupplierName(@Param("orderGuid") String orderGuid);

    /**
     * 获取订单相关文件id集合
     * @param orderGuid
     * @return
     */
    List<FileGuidAndNameVO> getOrderFileGuid(@Param("orderGuid") String orderGuid);

    /**
     * 获取订单相关文件id集合
     * @param orderGuids
     * @return
     */
    List<FileGuidAndNameVO> getOrderFileGuids(@Param("orderGuids") List<String> orderGuids);

    /**
     * 获取订单相关订单数据集合
     * @param orderGuid
     * @return
     */
    List<ErpOutgoingOrderDataVO> getOrderDataList(@Param("orderGuid") String orderGuid);

    /**
     * 获取订单相关订单数据集合（批量）
     * @param orderGuids
     * @return
     */
    List<ErpOutgoingOrderDataVO> getOrderDataListBatch(@Param("orderGuids") List<String> orderGuids);

    @DataScope(component = "outgoing-order")
    List<String> selectOutgoing(@Param("qo") ErpOutgoingBusinessMgtOrderQO qo);

    @DataScope(component = "outgoing-order")
    IPage<String> selectOutgoing(IPage<ErpOutgoingBusinessMgtOrderVO> page, @Param("qo") ErpOutgoingBusinessMgtOrderQO qo);

    List<FileGuidAndNameVO> getFileList(@Param("orderGuid") String orderGuid);

    List<FileGuidAndNameVO> getFileListBatch(@Param("orderGuids") List<String> orderGuids);

    /**
     * 加工商预付明细列表查询
     * @param qo
     * @return
     */
    List<ErpProcessOutgoingOrderVO> processFindList(@Param("qo") ErpPurchaseProcessOutgoingOrderQO qo);

    /**
     * 外发到货单待开列表查询
     * @param qo
     * @return
     */
    List<ErpPurchaseInventoryWorkorderDetailVO> findNotBilledList(@Param("model") ErpPurchaseMgtPurchaseOrderQO qo);

    ErpOutgoingArrivalVO getOutgoingOrderVO(@Param("sourceGuid") String sourceGuid);
    List<ErpOutgoingArrivalVO> getOutgoingOrderVOBatch(@Param("sourceGuids") List<String> sourceGuids);

    BigDecimal getWaitingQuantity();

    /**
     * 批量更新或新增 其他费用
     * @param otherExpensesList
     * @return
     */
    int saveOrUpdateOtherExpensesBatch(@Param("otherExpensesList") List<ErpBusinessOtherExpensesVO> otherExpensesList);

    /**
     * 获取顶级来源备注和图片
     * @param sourceNumbers
     * @return
     */
    List<SourceDescriptionAndPictureVO> getSourceDescriptionAndPicture(@Param("sourceNumbers") List<String> sourceNumbers);

    /**
     * 获取已开外发到货单号列表
     * @param orderGuid
     * @return
     */
    List<String> selectOutgoingArrivalNumbers(@Param("orderGuid") String orderGuid);

    String selectDescriptionByOrderNumber(@Param("orderNumber") String orderNumber);
}
