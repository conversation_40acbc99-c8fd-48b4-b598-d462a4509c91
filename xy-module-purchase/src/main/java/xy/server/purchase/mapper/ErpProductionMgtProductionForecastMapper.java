package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import xy.server.purchase.entity.ErpProductionMgtProductionForecast;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseRequisition;
import xy.server.purchase.entity.model.qo.ErpProductionMgtProductionForecastQO;
import xy.server.purchase.entity.model.vo.ErpProductionMgtProductionForecastVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-10
 */
@Mapper
public interface ErpProductionMgtProductionForecastMapper extends BaseMapper<ErpProductionMgtProductionForecast> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpProductionMgtProductionForecastVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<ErpProductionMgtProductionForecastVO> findList(@Param("model") ErpProductionMgtProductionForecastQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<ErpProductionMgtProductionForecastVO> findPage(@Param("page") IPage<ErpProductionMgtProductionForecastVO> page, @Param("model") ErpProductionMgtProductionForecastQO model);

    /**
     * 查询待开采购预测的业务预测列表
     * @param qo
     * @return
     */
    List<ErpProductionMgtProductionForecastVO> findNotBilledList(@Param("model") ErpProductionMgtProductionForecastQO qo);

    /**
     * 采购预测采购情况
     * @param workorderGuid 采购申请单guid
     * @return
     */
    String getPurchaseState(@Param("workorderGuid") String workorderGuid);

    /**
     * 查询待开总数量
     * @return
     */
    BigDecimal selectCountPendingList();

    /**
     * 获取总已开采购预测数量
     * @param workorderGuid
     * @return
     */
    @Select("select COALESCE(sum(quantity), 0) from erp_production_mgt_workorder where source_guid = #{workorderGuid}" +
            " and workorder_properties = ${@com.xunyue.common.enums.WorkorderPropertiesEnum@PRODUCTION_FORECAST_SOURCE_BUSINESS_FORECASTING.getCode()}")
    BigDecimal getTotalCompletedQuantity(@Param("workorderGuid") String workorderGuid);

    ErpPurchaseMgtPurchaseRequisition getPurchaseRequisition(@Param("businessKey") String businessKey);
    List<ErpProductionMgtProductionForecastVO> selectDetailByIds(@Param("guidList") List<String> guidList);
    List<ErpProductionMgtProductionForecastVO> selectSourceDetailByGuids(@Param("guidList") List<String> guidList);
}
