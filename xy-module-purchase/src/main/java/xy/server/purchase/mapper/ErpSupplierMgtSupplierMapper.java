package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.config.dataScope.DataScope;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.purchase.entity.ErpSupplierMgtSupplier;
import xy.server.purchase.entity.model.qo.ErpSupplierMgtSupplierQO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 供应商 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Mapper
public interface ErpSupplierMgtSupplierMapper extends BaseMapper<ErpSupplierMgtSupplier> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpSupplierMgtSupplierVO getDataByGuid(@Param("guid") String guid);

    /**
     * 批量获取
     *
     * @param guids
     * @return
     */
    List<ErpSupplierMgtSupplierVO> getDataByGuids(@Param("guids") List<String> guids);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    @DataScope(component = "supplier")
    List<ErpSupplierMgtSupplierVO> findList(@Param("model") ErpSupplierMgtSupplierQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    @DataScope(component = "supplier")
    IPage<ErpSupplierMgtSupplierVO> findPage(@Param("page") IPage<ErpSupplierMgtSupplierVO> page, @Param("model") ErpSupplierMgtSupplierQO model);

    /**
     * 下拉快选择供应商
     *
     * @return
     */
    List<ErpSupplierMgtSupplier> dropDownSelect();

    /**
     * 获取发票税率Guid
     *
     * @param invoiceTypeName
     * @param taxRate
     * @return
     */
    String getInvoiceTypeTaxRateGuid(@Param("invoiceTypeName") String invoiceTypeName, @Param("taxRate") BigDecimal taxRate);
}
