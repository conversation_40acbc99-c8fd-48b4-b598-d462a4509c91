package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseOrderData;
import xy.server.purchase.entity.model.vo.ErpBusinessOtherExpensesVO;
import xy.server.purchase.entity.model.vo.ErpPurchaseMgtBusinessOrderDataVO;
import xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseOrderDataVO;
import xy.server.purchase.entity.model.vo.PurchaseRequisitionWorkOrderDataVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 订单数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Mapper
public interface ErpPurchaseMgtPurchaseOrderDataMapper extends BaseMapper<ErpPurchaseMgtPurchaseOrderData> {

    /**
     * 根据订单Guid查询订单数据列表
     * @param orderGuid
     * @return
     */
    List<ErpPurchaseMgtPurchaseOrderDataVO> findListByOrderGuid(@Param("orderGuid") String orderGuid);

    /**
     * 根据订单Guids查询订单数据列表（批量）
     * @param orderGuids
     * @return
     */
    List<ErpPurchaseMgtPurchaseOrderDataVO> findListByOrderGuids(@Param("orderGuids") List<String> orderGuids);

    /**
     * 根据父级id批量查询数据
     * @param orderDataGuids
     * @return
     */
    List<ErpPurchaseMgtPurchaseOrderDataVO> findListByParentGuids(@Param("orderDataGuids") List<String> orderDataGuids);



    /**
     * 获取总采购数量
     * @param workorderGuid
     * @return
     */
    @Select("select COALESCE(sum(quantity), 0) from erp_production_mgt_workorder where parent_classification_guid = #{workorderGuid}" +
            " and workorder_properties = ${@<EMAIL>()}")
    BigDecimal getTotalPurchaseQuantity(@Param("workorderGuid") String workorderGuid);

    List<ErpPurchaseMgtPurchaseOrderData> getSourceDataS(@Param("orderGuid") String orderGuid);

    /**
     * 删除其他费用
     * @param delDetailIds
     */
    int deleteByOtherExpensesBatch(@Param("delDetailIds") List<String> delDetailIds);

    /**
     * 批量更新或保存
     * @param otherExpensesList
     */
    boolean saveOrUpdateOtherExpensesBatch(@Param("otherExpensesList") List<ErpBusinessOtherExpensesVO> otherExpensesList);

    /**
     * 根据采购订单明细guids获取业务订单数据List（采购成品流程链路）
     *
     * @param purchaseOrderDataGuids
     * @return
     */
    List<ErpPurchaseMgtBusinessOrderDataVO> selectBusinessOrderDataListByPurchaseOrderDataGuids(@Param("purchaseOrderDataGuids") List<String> purchaseOrderDataGuids);


    /**
     * 根据采购申请来源工单数据Guid查询工序工单等信息
     * @param workorderGuids
     * @return
     */
    List<PurchaseRequisitionWorkOrderDataVO> getPurchaseRequisitionWorkOrderData(@Param("workorderGuids") List<String> workorderGuids);
}
