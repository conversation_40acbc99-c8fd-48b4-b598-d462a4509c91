package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.purchase.entity.ErpOutgoingBusinessMgtOrderPlaceOfDelivery;
import xy.server.purchase.entity.model.vo.ErpOutgoingBusinessMgtOrderPlaceOfDeliveryVO;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Mapper
public interface ErpOutgoingBusinessMgtOrderPlaceOfDeliveryMapper extends BaseMapper<ErpOutgoingBusinessMgtOrderPlaceOfDelivery> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpOutgoingBusinessMgtOrderPlaceOfDeliveryVO getDataByGuid(@Param("guid") String guid);
}
