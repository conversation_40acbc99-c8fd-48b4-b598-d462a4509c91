package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.purchase.entity.ErpSupplierMgtSupplierLevel;
import xy.server.purchase.entity.model.qo.ErpSupplierMgtSupplierLevelQO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierLevelVO;

import java.util.List;

/**
 * <p>
 * 供应商级别 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Mapper
public interface ErpSupplierMgtSupplierLevelMapper extends BaseMapper<ErpSupplierMgtSupplierLevel> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpSupplierMgtSupplierLevelVO getDataByGuid(@Param("guid") String guid);

    /**
     * 根据GUID获取
     *
     * @param guids
     * @return
     */
    List<ErpSupplierMgtSupplierLevelVO> getDataByGuids(@Param("guids") List<String> guids);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<ErpSupplierMgtSupplierLevelVO> findList(@Param("model") ErpSupplierMgtSupplierLevelQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<ErpSupplierMgtSupplierLevelVO> findPage(@Param("page") IPage<ErpSupplierMgtSupplierLevelVO> page, @Param("model") ErpSupplierMgtSupplierLevelQO model);

    /**
     * 根据父id查询所有子节点（包含该父节点本身）
     * @param parentClassificationGuid
     * @return list
     */
    List<ErpSupplierMgtSupplierLevel> getListByParentClassificationGuid(@Param("parentClassificationGuid")String parentClassificationGuid);
}
