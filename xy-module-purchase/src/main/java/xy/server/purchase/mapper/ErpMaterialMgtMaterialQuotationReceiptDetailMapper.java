package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.material.entity.model.ro.ErpMaterialMgtMaterialClassificationDetailRO;
import xy.server.purchase.entity.ErpMaterialMgtMaterialQuotationReceiptDetail;
import xy.server.purchase.entity.model.qo.ErpCustomerlQuotationReceiptQO;
import xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationReceiptDetailVO;

import java.util.List;

/**
 * <p>
 * 物料报价明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Mapper
public interface ErpMaterialMgtMaterialQuotationReceiptDetailMapper extends BaseMapper<ErpMaterialMgtMaterialQuotationReceiptDetail> {

    /**
     * 列表查询
     * @param materialQuotationReceiptGuid
     * @return
     */
    List<ErpMaterialMgtMaterialQuotationReceiptDetailVO> getList(@Param("materialQuotationReceiptGuid") String materialQuotationReceiptGuid);

    /**
     * 判断客户/供应下商报价在该生效日期内是否有其他同物料已经存在
     * @param receiptQO
     * @return
     */
    String getReceiptDetailGuid(@Param("receiptQO") ErpCustomerlQuotationReceiptQO receiptQO);

    String getMaterialClassification(@Param("searchCode") String searchCode);

    /**
     * 根据报价主表id获取明细数据
     * @param materialQuotationIds
     * @return
     */
    List<ErpMaterialMgtMaterialQuotationReceiptDetailVO> getDataList(@Param("materialQuotationIds") List<String> materialQuotationIds);

    /**
     *
     * @param materialClassifications
     * @return
     */
    List<ErpMaterialMgtMaterialClassificationDetailRO> getMaterialClassificationValues(@Param("materialClassifications") List<String> materialClassifications);
}
