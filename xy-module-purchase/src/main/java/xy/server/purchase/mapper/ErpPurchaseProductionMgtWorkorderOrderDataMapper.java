package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.purchase.entity.ErpPurchaseProductionMgtWorkorderOrderData;
import xy.server.purchase.entity.model.qo.ErpProductionMgtWorkorderOrderDataQO;
import xy.server.purchase.entity.model.vo.ErpPurchaseProductionMgtWorkorderOrderDataVO;

import java.util.List;

/**
 * <p>
 * 工单订单数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Mapper
public interface ErpPurchaseProductionMgtWorkorderOrderDataMapper extends BaseMapper<ErpPurchaseProductionMgtWorkorderOrderData> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    ErpPurchaseProductionMgtWorkorderOrderDataVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpPurchaseProductionMgtWorkorderOrderDataVO> findList(@Param("model") ErpProductionMgtWorkorderOrderDataQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<ErpPurchaseProductionMgtWorkorderOrderDataVO> findPage(@Param("page") IPage<ErpPurchaseProductionMgtWorkorderOrderDataVO> page, @Param("model") ErpProductionMgtWorkorderOrderDataQO model);
}
