package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import xy.server.purchase.entity.ErpSupplierMgtSupplierAddress;
import xy.server.purchase.entity.model.qo.ErpSupplierMgtSupplierAddressQO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierAddressVO;

import java.util.List;

/**
 * <p>
 * 供应商地址 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Mapper
public interface ErpSupplierMgtSupplierAddressMapper extends BaseMapper<ErpSupplierMgtSupplierAddress> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpSupplierMgtSupplierAddressVO getDataByGuid(@Param("guid") String guid);
    List<ErpSupplierMgtSupplierAddressVO> getDataBySupplierGuids(@Param("guids") List<String> guids);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<ErpSupplierMgtSupplierAddressVO> findList(@Param("model") ErpSupplierMgtSupplierAddressQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<ErpSupplierMgtSupplierAddressVO> findPage(@Param("page") IPage<ErpSupplierMgtSupplierAddressVO> page, @Param("model") ErpSupplierMgtSupplierAddressQO model);

    /**
     * 根据供应商编码查询供应商guid
     * @param supplierCode
     * @return
     */
    @Select("select supplier_guid from erp_supplier_mgt_supplier where supplier_code = #{supplierCode} limit 1")
    String getSupplierGuidByCode(@Param("supplierCode") String supplierCode);
}
