package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.purchase.entity.ErpOutgoingBusinessMgtOrderFile;
import xy.server.purchase.entity.model.vo.ErpOutgoingBusinessMgtOrderFileVO;

/**
 * <p>
 * 订单文件表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Mapper
public interface ErpOutgoingBusinessMgtOrderFileMapper extends BaseMapper<ErpOutgoingBusinessMgtOrderFile> {

        /**
         * 根据GUID获取
         * @param guid
         * @return
         */
    ErpOutgoingBusinessMgtOrderFileVO getDataByGuid(@Param("guid") String guid);


}
