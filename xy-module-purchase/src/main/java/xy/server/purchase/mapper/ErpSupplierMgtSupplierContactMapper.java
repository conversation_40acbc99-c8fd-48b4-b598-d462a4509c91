package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import xy.server.purchase.entity.ErpSupplierMgtSupplierContact;
import xy.server.purchase.entity.model.qo.ErpSupplierMgtSupplierContactQO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierContactVO;

import java.util.List;

/**
 * <p>
 * 供应商联系人 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Mapper
public interface ErpSupplierMgtSupplierContactMapper extends BaseMapper<ErpSupplierMgtSupplierContact> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpSupplierMgtSupplierContactVO getDataByGuid(@Param("guid") String guid);
    List<ErpSupplierMgtSupplierContactVO> getDataBySupplierGuids(@Param("guids") List<String> guids);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<ErpSupplierMgtSupplierContactVO> findList(@Param("model") ErpSupplierMgtSupplierContactQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<ErpSupplierMgtSupplierContactVO> findPage(@Param("page") IPage<ErpSupplierMgtSupplierContactVO> page, @Param("model") ErpSupplierMgtSupplierContactQO model);

    /**
     * 根据供应商编码查询供应商guid
     * @param supplierCode
     * @return
     */
    @Select("select supplier_guid from erp_supplier_mgt_supplier where supplier_code = #{supplierCode} limit 1")
    String getSupplierGuidByCode(@Param("supplierCode") String supplierCode);
}
