package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.purchase.entity.ErpOutgoingDataProcess;
import xy.server.purchase.entity.model.qo.ErpOutgoingDataProcessQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingApplicationFormRO;
import xy.server.purchase.entity.model.vo.ErpOutgoingDataProcessVO;
import xy.server.purchase.entity.model.vo.WorkOrderProcessAndOutgoingApplicationVO;

import java.util.List;

/**
 * <p>
 * 工单数据表工序工单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
@Mapper
public interface ErpOutgoingDataProcessMapper extends BaseMapper<ErpOutgoingDataProcess> {

        /**
         * 根据GUID获取
         * @param guid
         * @return
         */
    ErpOutgoingDataProcessVO getDataByGuid(@Param("guid") String guid);

        /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpOutgoingDataProcessVO> findList(@Param("model") ErpOutgoingDataProcessQO qo);

        /**
       * 分页查询
       * @param page
       * @param model
       * @return
       */
    IPage<ErpOutgoingDataProcessVO> findPage(@Param("page") IPage<ErpOutgoingDataProcessVO> page, @Param("model") ErpOutgoingDataProcessQO model);

    /**
     * 根据订单子工单id获取工序源数据
     * @param collect1
     * @return
     */
    List<WorkOrderProcessAndOutgoingApplicationVO> selectBySon(@Param("collect1") List<String> collect1);

    ErpOutgoingDataProcess getByWorkOrderGuid(@Param("sourceGuid") String sourceGuid);

    /**
     * 根据工序工单id获取上工序的产出长宽高
     * @param workorderDataProcessGuid
     * @return
     */
    ErpOutgoingApplicationFormRO getSpecifications(@Param("workorderDataProcessGuid") String workorderDataProcessGuid);

    /**
     * 根据工序工单id获取上工序的产出物料Guid
     * @param workorderDataProcessGuids
     * @return
     */
    List<ErpOutgoingApplicationFormRO> getSpecificationsBatch(@Param("workorderDataProcessGuids") List<String> workorderDataProcessGuids);
}
