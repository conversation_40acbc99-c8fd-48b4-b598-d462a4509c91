package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseOrderFile;
import xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseOrderFileVO;

import java.util.List;

/**
 * <p>
 * 订单文件表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Mapper
public interface ErpPurchaseMgtPurchaseOrderFileMapper extends BaseMapper<ErpPurchaseMgtPurchaseOrderFile> {

    /**
     * 根据订单Guid查询订单文件列表
     * @param orderGuid
     * @return
     */
    List<ErpPurchaseMgtPurchaseOrderFileVO> findListByOrderGuid(@Param("orderGuid") String orderGuid);

    /**
     * 根据订单Guids查询订单文件列表 (批量)
     * @param orderGuids
     * @return
     */
    List<ErpPurchaseMgtPurchaseOrderFileVO> findListByOrderGuids(@Param("orderGuids") List<String> orderGuids);

}
