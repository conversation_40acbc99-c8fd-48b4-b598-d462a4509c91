package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.purchase.entity.ErpOutgoingBusinessMgtOrderData;
import xy.server.purchase.entity.model.vo.ErpOutgoingBusinessMgtOrderDataVO;

/**
 * <p>
 * 订单数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Mapper
public interface ErpOutgoingBusinessMgtOrderDataMapper extends BaseMapper<ErpOutgoingBusinessMgtOrderData> {

        /**
         * 根据GUID获取
         * @param guid
         * @return
         */
    ErpOutgoingBusinessMgtOrderDataVO getDataByGuid(@Param("guid") String guid);


        }
