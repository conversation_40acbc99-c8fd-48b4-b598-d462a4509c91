package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.purchase.entity.ErpSupplierMgtSupplierClassification;
import xy.server.purchase.entity.model.qo.ErpSupplierMgtSupplierClassificationQO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierClassificationVO;

import java.util.List;

/**
 * <p>
 * 供应商分类 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Mapper
public interface ErpSupplierMgtSupplierClassificationMapper extends BaseMapper<ErpSupplierMgtSupplierClassification> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpSupplierMgtSupplierClassificationVO getDataByGuid(@Param("guid") String guid);

    List<ErpSupplierMgtSupplierClassificationVO> getDataByGuids(@Param("guids") List<String> guids);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<ErpSupplierMgtSupplierClassificationVO> findList(@Param("model") ErpSupplierMgtSupplierClassificationQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<ErpSupplierMgtSupplierClassificationVO> findPage(@Param("page") IPage<ErpSupplierMgtSupplierClassificationVO> page, @Param("model") ErpSupplierMgtSupplierClassificationQO model);

    List<ErpSupplierMgtSupplierClassificationVO> getByParentClassificationGuid(@Param("parentClassificationGuid") String parentClassificationGuid);

    /**
     * 根据父id查询所有子节点（包含该父节点本身）
     *
     * @param parentClassificationGuid
     * @return list
     */
    List<ErpSupplierMgtSupplierClassification> getListByParentClassificationGuid(@Param("parentClassificationGuid") String parentClassificationGuid);
}
