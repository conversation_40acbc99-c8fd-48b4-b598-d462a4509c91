package xy.server.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.purchase.entity.ErpSupplierMgtSupplierFile;
import xy.server.purchase.entity.model.qo.ErpSupplierMgtSupplierFileQO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierFileVO;

import java.util.List;

/**
 * <p>
 * 供应商文件 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Mapper
public interface ErpSupplierMgtSupplierFileMapper extends BaseMapper<ErpSupplierMgtSupplierFile> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpSupplierMgtSupplierFileVO getDataByGuid(@Param("guid") String guid);
    List<ErpSupplierMgtSupplierFileVO> getDataBySupplierGuids(@Param("guids") List<String> guids);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<ErpSupplierMgtSupplierFileVO> findList(@Param("model") ErpSupplierMgtSupplierFileQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<ErpSupplierMgtSupplierFileVO> findPage(@Param("page") IPage<ErpSupplierMgtSupplierFileVO> page, @Param("model") ErpSupplierMgtSupplierFileQO model);

}
