package xy.server.purchase.i18n;

import com.xunyue.common.i18n.BaseResultErrorCode;

/**
 * <AUTHOR>
 * @Date 2023-05-01
 * <p>
 * 错误代码枚举
 * </p>
 **/
public enum ResultErrorCode implements BaseResultErrorCode {
    DELETE_IS_USED("已经使用，不能【删除】", 1030000000),
    DELETE_BY_BATCH_IS_USED("已经使用，不能【批量删除】，只能【停用】", 1030000001),
    REPETITION_SUPPLIER_CLASSIFICATION_NAME("供应商分类名称不能重复", 1030000002),
    REPETITION_SUPPLIER_LEVEL_NAME("供应商级别名称不能重复", 1030000003),
    REPETITION_SUPPLIER_NAME("供应商名称不能重复", 1030000004),
    DELETE_CHILDEREN_IS_USED("存在已被使用的子级，不能删除", 1030000005),
    ALREADY_OUTGOING("已经外发不可删除或者修改", 1030000006),
    NOT_SURPLUS_APPLICATION("外发申请超额",1030000007),
    PROCESS_THAT_YIELDS_ZERO_OUTPUT("该工序加工数量为零，请检查工单是否正确",1030000008),
    STATE_NOT_UN_PURCHASE("已经采购的（采购情况），不能编辑、删除、撤审", 1030000009),
    PURCHASE_REQUISITION_QUANTITY_IS_EXCESS("采购申请数量已超出，不能保存", 1030000010),
    STATE_NOT_UN_ARRIVAL("已经到货的（到货情况），不能编辑、删除、撤审", 1030000011),
    PROCESSOR_WORK_ORDER_NOT_STATUS("该外发工序的对应工单不存在", 1030000012),
    DATA_NOT_STATUS("来源数据不存在", 1030000013),
    CANNOT_RESTART_THE_PROCESS_HAS_OUTGOING_ORDER("已开外发订单，不能重启流程！单号：${0}", 1030000014),
    ALL_PURCHASE_APPLICATIONS_HAVE_BEEN_MADE("已全部采购申请，请勿重复操作！", 1030000015),
    ALL_OUTGOING_APPLICATIONS_HAVE_BEEN_MADE("已全部外发申请，请勿重复操作！", 1030000016),
    CANNOT_RESTART_THE_PROCESS_HAS_OUTGOING_ARRIVAL("已开外发到货，不能重启流程！单号：${0}", 1030000017),
    DATA_NOT_FIND("操作数据不存在", 1030000018),
    PURCHASE_APPLY_DATA_NOT_FIND("采购申请数据不存在", 1030000019),
    MATERIAL_USAGE_DATA_NOT_FIND("物料用量数据不存在", 1030000020),
    SYNC_MATERIAL_USAGE_FAILURE("同步物料用量失败", 1030000021),
    DATA_IS_EQUAL_STATUS("在数据库中存在相同生效日期的同客户的物料数据", 1030000022),
    BOM_NOT_STATUS("该产品的产品资料不存在无法应用报价", 1030000023),
    ;
    private String msg;
    private int code;

    ResultErrorCode(String msg, int code) {
        this.msg = msg;
        this.code = code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public int getCode() {
        return code;
    }
}
