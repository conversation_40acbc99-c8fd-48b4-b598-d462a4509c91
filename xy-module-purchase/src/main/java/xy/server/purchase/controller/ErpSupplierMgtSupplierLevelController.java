package xy.server.purchase.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.tenant.history.SystemFieldHistory;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.EasyExcelUtils;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import xy.server.purchase.entity.ErpSupplierMgtSupplierLevel;
import xy.server.purchase.entity.model.qo.ErpSupplierMgtSupplierLevelQO;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierLevelRO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierLevelVO;
import xy.server.purchase.service.IErpSupplierMgtSupplierLevelService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * 供应商级别 controller
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "供应商级别")
@RestController
@RequestMapping("/purchase/erp-supplier-mgt-supplier-level")
@SystemClassLog(code = "ErpSupplierMgtSupplierLevelController")
public class ErpSupplierMgtSupplierLevelController {
    private final IErpSupplierMgtSupplierLevelService service;
    private final EasyExcelUtils easyExcelUtils;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增供应商级别")
    public Boolean createOne(@RequestBody @Validated ErpSupplierMgtSupplierLevelRO ro, HttpServletRequest request) {
        ro.setTenantGuid(request.getHeader("tenantGuid"));
        return service.create(ro);
    }

    @DeleteMapping("/delete/{supplierLevelGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除供应商级别")
    @SystemFieldHistory(targetEntity = ErpSupplierMgtSupplierLevel.class)
    public Boolean delete(@PathVariable("supplierLevelGuid") String supplierLevelGuid) {
        return service.delete(supplierLevelGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除供应商级别")
    @SystemFieldHistory(targetEntity = ErpSupplierMgtSupplierLevel.class)
    public Boolean deleteByBatch(@RequestBody List<String> ids) {
        return service.deleteByBatch(ids);
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改供应商级别")
    @SystemFieldHistory(targetEntity = ErpSupplierMgtSupplierLevel.class)
    public Boolean update(@RequestBody @Validated ErpSupplierMgtSupplierLevelRO ro) {
        return service.update(ro);
    }

    @GetMapping("/{supplierLevelGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条供应商级别")
    public ErpSupplierMgtSupplierLevelVO getOne(@PathVariable("supplierLevelGuid") String supplierLevelGuid) {
        return service.getById(supplierLevelGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询供应商级别")
    public List<ErpSupplierMgtSupplierLevelVO> findList(@RequestBody @Validated ErpSupplierMgtSupplierLevelQO qo, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        return service.findList(qo, tenantGuid);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询供应商级别")
    public IPage<ErpSupplierMgtSupplierLevelVO> findPage(@RequestBody @Validated PageParams<ErpSupplierMgtSupplierLevelQO> pageParams, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        pageParams.setUserId(userGuid);
        pageParams.setTenantGuid(tenantGuid);
        return service.findPage(pageParams);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    @SystemFieldHistory(targetEntity = ErpSupplierMgtSupplierLevel.class)
    public Boolean saveDate(@RequestBody @Validated InsertOrUpdateList<ErpSupplierMgtSupplierLevelRO> dataList, HttpServletRequest request) {
        String tenantGuid = request.getHeader("tenantGuid");
        return service.saveDate(dataList, tenantGuid);
    }

    @PostMapping("/reordered")
    @ApiOperation(value = "重新排序")
    @SystemMethodLog(type = "modify", description = "重新排序")
    public Boolean reordered(@RequestBody @Validated List<SortEntityDto> entityDtoList) {
        return service.reordered(entityDtoList);
    }

    @GetMapping("/downloadTemplate")
    @ApiOperation(value = "下载模版")
    public void downloadTemplate(HttpServletResponse response) {
        easyExcelUtils.downloadTemplate(ErpSupplierMgtSupplierLevelRO.class, response);
    }

    @PostMapping("/importExcel")
    @ApiOperation(value = "Excel导入")
    public Boolean importExcel(@RequestPart("file") MultipartFile file, @RequestParam("isErrorResume") boolean isErrorResume) {
        return easyExcelUtils.analyzeExcel(file, ErpSupplierMgtSupplierLevelRO.class, service, isErrorResume);
    }

}
