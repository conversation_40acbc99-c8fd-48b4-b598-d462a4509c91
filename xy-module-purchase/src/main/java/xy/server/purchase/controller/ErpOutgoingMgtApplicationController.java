package xy.server.purchase.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.tenant.history.SystemFieldHistory;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.trans.annotation.XyTransMethod;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.purchase.entity.model.qo.ErpOutgoingMgtApplicationQO;
import xy.server.purchase.entity.model.qo.ErpOutgoingQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingApplicationFormRO;
import xy.server.purchase.entity.model.ro.ErpOutgoingDetaiStopRO;
import xy.server.purchase.entity.model.ro.ErpOutgoingMgtApplicationRO;
import xy.server.purchase.entity.model.vo.ErpOutgoingApplicationFormVO;
import xy.server.purchase.service.IErpOutgoingMgtApplicationService;
import xy.server.work.entity.ErpProductionMgtWorkorder;

import java.util.List;
import java.util.concurrent.ExecutionException;


/**
 * <p>
 * 工单表 controller
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "外发申请模块")
@RestController
@RequestMapping("/purchase/erp-outgoing-mgt-application")
@SystemClassLog(code = "ErpOutgoingMgtApplicationController")
public class ErpOutgoingMgtApplicationController {
    private final IErpOutgoingMgtApplicationService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增工单表")
    public ErpOutgoingMgtApplicationRO createOne(@RequestBody @Validated ErpOutgoingMgtApplicationRO ro) {
        return service.create(ro);
    }

    @DeleteMapping("/delete/{workorderGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除工单表")
    @SystemFieldHistory(targetEntity = ErpProductionMgtWorkorder.class)
    public Boolean delete(@PathVariable("workorderGuid") String workorderGuid) {
        return service.delete(workorderGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "批量删除工单表")
    @SystemFieldHistory(targetEntity = ErpProductionMgtWorkorder.class)
    public Boolean deleteByBatch(@RequestBody List<String> ids) {
        return service.removeIds(ids);
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改工单表")
    @SystemFieldHistory(targetEntity = ErpProductionMgtWorkorder.class)
    public Boolean update(@RequestBody @Validated ErpOutgoingMgtApplicationRO ro) {
        return service.update(ro);
    }

    @GetMapping("/{workorderGuid}")
    @ApiOperation(value = "单条查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "查询单条工单表")
    public ErpOutgoingApplicationFormVO getOne(@PathVariable("workorderGuid") String workorderGuid) {
        return service.getById(workorderGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "列表查询工单表")
    public List<ErpOutgoingApplicationFormVO> findList(@RequestBody @Validated ErpOutgoingMgtApplicationQO qo) {
        return service.findList(qo);
    }
    @PostMapping("/newFindList")
    @ApiOperation(value = "待开列表查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "待开列表查询工单表")
    public List<ErpOutgoingApplicationFormVO> newFindList(@RequestBody @Validated ErpOutgoingMgtApplicationQO qo) throws ExecutionException, InterruptedException {
        return service.newFindList(qo);
    }


    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "分页查询工单表")
    public IPage<ErpOutgoingApplicationFormVO> findPage(@RequestBody @Validated PageParams<ErpOutgoingMgtApplicationQO> pageParams) {
        return service.findPage(pageParams);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存（新增或者更新）")
    @SystemMethodLog(type = "modify", description = "保存（新增或者更新）")
    @SystemFieldHistory(targetEntity = ErpProductionMgtWorkorder.class)
    public List<String> saveData(@RequestBody @Validated InsertOrUpdateList<ErpOutgoingMgtApplicationRO> list) {
        return service.saveData(list);
    }

    @GetMapping("/dismissal/{repairDataGuid}")
    @ApiOperation(value = "撤审")
    @SystemMethodLog(type = "update", description = "撤审外发申请单")
    public Boolean Dismissal(@PathVariable("repairDataGuid") String repairDataGuid) {
        return false;
    }

    @PostMapping("/allFindList")
    @ApiOperation(value = "所有外发的列表查询列表查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "所有外发的列表查询外发申请单")
    public IPage<ErpOutgoingApplicationFormRO> allFindList(@RequestBody @Validated PageParams<ErpOutgoingQO> qo) {
        return service.allFindList(qo);
    }
    @PostMapping("/specificFindList")
    @ApiOperation(value = "外发申请的列表查询的明细数据")
    @SystemMethodLog(type = "query", description = "外发申请的列表查询的明细数据")
    public List<ErpOutgoingApplicationFormRO> specificFindList(@RequestBody @Validated ErpOutgoingMgtApplicationQO qo) throws ExecutionException, InterruptedException {
        return service.specificFindList(qo);
    }

    @PostMapping("/stopOrRollback")
    @ApiOperation(value = "终止单据")
    @SystemMethodLog(type = "modify", description = "修改装舱表")
    public Boolean stopOrRollback(@RequestBody @Validated List<ErpOutgoingDetaiStopRO> list) {
        return service.stopOrRollback(list);
    }

    @PostMapping("/generateOutgoingApplication")
    @ApiOperation(value = "生成外发申请单")
    @SystemMethodLog(type = "modify", description = "生成外发申请单")
    public Boolean generatePurchaseRequisition(@RequestBody @Validated PageParams<ErpOutgoingQO> qo) {
        return service.generateOutgoingApplication(qo);
    }
}
