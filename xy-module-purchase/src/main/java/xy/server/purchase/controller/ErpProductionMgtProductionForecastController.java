package xy.server.purchase.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.trans.annotation.XyTransMethod;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.purchase.entity.model.qo.ErpProductionMgtProductionForecastQO;
import xy.server.purchase.entity.model.ro.ErpProductionMgtProductionForecastRO;
import xy.server.purchase.entity.model.vo.ErpProductionMgtProductionForecastVO;
import xy.server.purchase.service.IErpProductionMgtProductionForecastService;

import javax.validation.constraints.NotEmpty;
import java.util.List;


/**
 * <AUTHOR>
 * @apiNote 采购预测 controller
 * @since 2024-04-10
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "采购预测管理")
@RestController
@RequestMapping("/purchase/erp-production-mgt-production-forecast")
@SystemClassLog(code = "ErpProductionMgtProductionForecastController")
public class ErpProductionMgtProductionForecastController {
    private final IErpProductionMgtProductionForecastService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增")
    public String createOne(@RequestBody @Validated ErpProductionMgtProductionForecastRO ro) {
        return service.create(ro);
    }

    @DeleteMapping("/delete/{workorderGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除")
    public Boolean delete(@PathVariable("workorderGuid") String workorderGuid) {
        return service.delete(workorderGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除")
    public Boolean deleteByBatch(@RequestBody @NotEmpty List<String> workorderGuids) {
        return service.deleteByBatch(workorderGuids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改")
    public String update(@RequestBody @Validated ErpProductionMgtProductionForecastRO ro) {
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    public List<String> saveDate(@RequestBody @Validated InsertOrUpdateList<ErpProductionMgtProductionForecastRO> dataList) {
        return service.saveDate(dataList);
    }

    @GetMapping("/getOne/{workorderGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条")
    @XyTransMethod
    public ErpProductionMgtProductionForecastVO getOne(@PathVariable("workorderGuid") String workorderGuid) {
        return service.getDataById(workorderGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询")
    @XyTransMethod
    public List<ErpProductionMgtProductionForecastVO> findList(@RequestBody ErpProductionMgtProductionForecastQO qo) {
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询")
    @XyTransMethod
    public IPage<ErpProductionMgtProductionForecastVO> findPage(@RequestBody PageParams<ErpProductionMgtProductionForecastQO> pageParams) {
        return service.findPage(pageParams);
    }

    @PostMapping("/findNotBilledList")
    @ApiOperation(value = "查询待开采购预测的业务预测列表")
    @SystemMethodLog(type = "query", description = "查询待开采购预测的业务预测列表")
    public List<ErpProductionMgtProductionForecastVO> findNotBilledList(@RequestBody ErpProductionMgtProductionForecastQO qo) {
        return service.findNotBilledList(qo);
    }

}
