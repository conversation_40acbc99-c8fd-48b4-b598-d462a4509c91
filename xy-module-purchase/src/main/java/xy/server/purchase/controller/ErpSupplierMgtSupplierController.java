package xy.server.purchase.controller;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.tenant.history.SystemFieldHistory;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.trans.annotation.XyTransMethod;
import com.xunyue.config.util.EasyExcelUtils;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import xy.server.filter.aspect.XyViewFilter;
import xy.server.filter.aspect.XyViewFilterParam;
import xy.server.filter.enums.TableIdentificationEnum;
import xy.server.purchase.entity.ErpSupplierMgtSupplier;
import xy.server.purchase.entity.model.qo.ErpSupplierMgtSupplierQO;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierRO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierVO;
import xy.server.purchase.service.IErpSupplierMgtSupplierService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;


/**
 * @apiNote 供应商controller
 * <AUTHOR>
 * @since 2023-08-01
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "供应商")
@RestController
@RequestMapping("/purchase/erp-supplier-mgt-supplier")
@SystemClassLog(code = "ErpSupplierMgtSupplierController")
public class ErpSupplierMgtSupplierController {
    private final IErpSupplierMgtSupplierService service;
    private final EasyExcelUtils easyExcelUtils;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增供应商")
    public ErpSupplierMgtSupplier createOne(@RequestBody @Validated ErpSupplierMgtSupplierRO ro, HttpServletRequest request) {
        ro.setTenantGuid(request.getHeader("tenantGuid"));
        return service.create(ro);
    }

    @DeleteMapping("/delete/{supplierGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除供应商")
    @SystemFieldHistory(targetEntity = ErpSupplierMgtSupplier.class)
    public Boolean delete(@PathVariable("supplierGuid") String supplierGuid) {
        return service.delete(supplierGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除供应商")
    @SystemFieldHistory(targetEntity = ErpSupplierMgtSupplier.class)
    public Boolean deleteByBatch(@RequestBody List<String> ids) {
        return service.deleteByBatch(ids);
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改供应商")
    @SystemFieldHistory(targetEntity = ErpSupplierMgtSupplier.class)
    public ErpSupplierMgtSupplier update(@RequestBody @Validated ErpSupplierMgtSupplierRO ro) {
        return service.update(ro);
    }

    @GetMapping("/{supplierGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条供应商")
    @XyTransMethod
    public ErpSupplierMgtSupplierVO getOne(@PathVariable("supplierGuid") String supplierGuid) {
        return service.getById(supplierGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询供应商")
    @XyTransMethod
    @XyViewFilter(tableIdentificationEnum = TableIdentificationEnum.SUPPLIER_LIST)
    public List<ErpSupplierMgtSupplierVO> findList(@RequestBody @XyViewFilterParam ErpSupplierMgtSupplierQO qo, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        return service.findList(qo, tenantGuid);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询供应商")
    @XyTransMethod
    @XyViewFilter(tableIdentificationEnum = TableIdentificationEnum.SUPPLIER_LIST)
    public IPage<ErpSupplierMgtSupplierVO> findPage(@RequestBody @XyViewFilterParam PageParams<ErpSupplierMgtSupplierQO> pageParams, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        pageParams.setUserId(userGuid);
        pageParams.setTenantGuid(tenantGuid);
        return service.findPage(pageParams);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    @SystemFieldHistory(targetEntity = ErpSupplierMgtSupplier.class)
    public List<ErpSupplierMgtSupplier> saveDate(@RequestBody @Validated InsertOrUpdateList<ErpSupplierMgtSupplierRO> dataList, HttpServletRequest request) {
        String tenantGuid = request.getHeader("tenantGuid");
        return service.saveDate(dataList, tenantGuid);
    }

    @PostMapping("/selectList")
    @ApiOperation(value = "选择列表查询（已审核完成）")
    @SystemMethodLog(type = "query", description = "选择列表查询（已审核完成）")
    @XyTransMethod
    public List<ErpSupplierMgtSupplierVO> selectList(@RequestBody ErpSupplierMgtSupplierQO qo) {
        return service.selectList(qo);
    }

    @PostMapping("/dropDownSelect")
    @ApiOperation(value = "下拉快选择供应商")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "列表查询")
    public List<ErpSupplierMgtSupplier> dropDownSelect() {
        return service.dropDownSelect();
    }

    @GetMapping("/downloadTemplate")
    @ApiOperation(value = "下载模版")
    public void downloadTemplate(HttpServletResponse response) {
        service.downloadTemplate(response);
    }

    @PostMapping("/importExcel")
    @ApiOperation(value = "Excel导入")
    public Boolean importExcel(@RequestPart("file") MultipartFile file, @RequestParam("isErrorResume") boolean isErrorResume) {
        return service.importExcel(file, isErrorResume);
    }

    @PostMapping("/listByIds")
    @ApiOperation(value = "根据ids批量查询供应商")
    @SystemMethodLog(type = "query", description = "根据ids批量查询供应商")
    @XyTransMethod
    public List<ErpSupplierMgtSupplier> listByIds(@RequestBody List<String> supplierGuids) {
        if (CollUtil.isEmpty(supplierGuids)) {
            return new ArrayList<>();
        }
        return service.listByIds(supplierGuids);
    }
}
