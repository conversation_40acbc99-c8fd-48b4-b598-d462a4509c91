package xy.server.purchase.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.vo.SourceDescriptionAndPictureVO;
import com.xunyue.tenant.history.SystemFieldHistory;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.trans.annotation.XyTransMethod;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseOrder;
import xy.server.purchase.entity.model.qo.ErpGetNewDataQO;
import xy.server.purchase.entity.model.qo.ErpPurchaseMgtPurchaseOrderQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingDetaiStopRO;
import xy.server.purchase.entity.model.ro.ErpPurchaseMgtPurchaseOrderRO;
import xy.server.purchase.entity.model.ro.ErpUpdateToJsonRO;
import xy.server.purchase.entity.model.ro.ModifyMaterialAndSyncWorkOrderRO;
import xy.server.purchase.entity.model.vo.*;
import xy.server.purchase.service.IErpPurchaseMgtPurchaseOrderService;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;


/**
 * @apiNote 采购订单 controller
 * <AUTHOR>
 * @since 2023-09-19
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "采购管理/采购订单")
@RestController
@RequestMapping("/purchase/erp-purchase-mgt-purchase-order")
@SystemClassLog(code = "ErpPurchaseMgtPurchaseOrderController")
public class ErpPurchaseMgtPurchaseOrderController {
    private final IErpPurchaseMgtPurchaseOrderService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增采购订单")
    public ErpPurchaseMgtPurchaseOrder createOne(@RequestBody @Validated ErpPurchaseMgtPurchaseOrderRO ro) {
        return service.create(ro);
    }

    @DeleteMapping("/delete/{orderGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除采购订单")
    @SystemFieldHistory(targetEntity = ErpPurchaseMgtPurchaseOrder.class)
    public Boolean delete(@PathVariable("orderGuid") String orderGuid) {
        return service.delete(orderGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除采购订单")
    @SystemFieldHistory(targetEntity = ErpPurchaseMgtPurchaseOrder.class)
    public Boolean deleteByBatch(@RequestBody @NotEmpty(message = "ids不能为空") List<String> ids) {
        return service.deleteByBatch(ids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改采购订单")
    @SystemFieldHistory(targetEntity = ErpPurchaseMgtPurchaseOrder.class)
    public ErpPurchaseMgtPurchaseOrder update(@RequestBody @Validated ErpPurchaseMgtPurchaseOrderRO ro) {
        return service.update(ro);
    }

    @GetMapping("/getOne/{orderGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条采购订单")
    @XyTransMethod
    public ErpPurchaseMgtPurchaseOrderVO getOne(@PathVariable("orderGuid") String orderGuid) {
        return service.getById(orderGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询采购订单")
    @XyTransMethod
    public List<ErpPurchaseMgtPurchaseOrderVO> findList(@RequestBody ErpPurchaseMgtPurchaseOrderQO qo) {
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询采购订单")
    @XyTransMethod
    public IPage<ErpPurchaseMgtPurchaseOrderVO> findPage(@RequestBody PageParams<ErpPurchaseMgtPurchaseOrderQO> pageParams) {
        return service.findPage(pageParams);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    @SystemFieldHistory(targetEntity = ErpPurchaseMgtPurchaseOrder.class)
    public List<ErpPurchaseMgtPurchaseOrder> saveDate(@RequestBody @Validated InsertOrUpdateList<ErpPurchaseMgtPurchaseOrderRO> dataList) {
        return service.saveDate(dataList);
    }

    @PostMapping("/findNotBilledList")
    @ApiOperation(value = "待开列表查询")
    @SystemMethodLog(type = "query", description = "待开列表查询")
    @XyTransMethod
    public List<ErpPurchaseInventoryWorkorderDetailVO> findNotBilledList(@RequestBody ErpPurchaseMgtPurchaseOrderQO qo) {
        return service.findNotBilledList(qo);
    }

    @PostMapping("/findNotBilledTree")
    @ApiOperation(value = "待开列表查询（包含主工单）")
    @SystemMethodLog(type = "query", description = "待开列表查询（包含主工单）")
    @XyTransMethod
    public List<ErpPurchaseInventoryWorkorderVO> findNotBilledTree(@RequestBody ErpPurchaseMgtPurchaseOrderQO qo) {
        return service.findNotBilledTree(qo);
    }

    @PostMapping("/stopOrRollback")
    @ApiOperation(value = "终止单据")
    @SystemMethodLog(type = "modify", description = "修改装舱表")
    public Boolean stopOrRollback(@RequestBody @Validated List<ErpOutgoingDetaiStopRO> list) {
        return service.stopOrRollback(list);
    }

    @PostMapping("/getSourceDescriptionAndPicture")
    @ApiOperation(value = "获取顶级来源备注和图片")
    @SystemMethodLog(type = "query", description = "获取顶级来源备注和图片")
    public List<SourceDescriptionAndPictureVO> getSourceDescriptionAndPicture(@RequestBody @Validated List<String> sourceGuids) {
        return service.getSourceDescriptionAndPicture(sourceGuids);
    }

    @PostMapping("/selectHistoricalUnitPriceList")
    @ApiOperation(value = "分页查看历史单价列表")
    @SystemMethodLog(type = "query", description = "分页查看历史单价列表")
    @XyTransMethod
    public IPage<ErpPurchaseMgtHistoricalUnitPriceVO> selectHistoricalUnitPricePage(@RequestBody PageParams<ErpPurchaseMgtPurchaseOrderQO> pageParams) {
        return service.selectHistoricalUnitPricePage(pageParams);
    }

    @PostMapping("/setTheLatestArrivalPrice")
    @ApiOperation(value = "设置最近到货价格")
    @SystemMethodLog(type = "query", description = "设置最近到货价格")
    public List<ErpPurchaseMgtSetTheLatestArrivalPriceDTO> setTheLatestArrivalPrice(@RequestBody @Validated List<ErpPurchaseMgtSetTheLatestArrivalPriceDTO> dtoList) {
        return service.setTheLatestArrivalPrice(dtoList);
    }

    @PostMapping("/modifyMaterialAndSyncWorkOrder")
    @ApiOperation(value = "修改物料并同步")
    @SystemMethodLog(type = "modify", description = "修改物料并同步")
    @XyTransMethod
    public Boolean modifyMaterialAndSyncWorkOrder(@RequestBody @Validated ModifyMaterialAndSyncWorkOrderRO modifyMaterialAndSyncWorkOrderRO) {
        return service.modifyMaterialAndSyncWorkOrder(modifyMaterialAndSyncWorkOrderRO);
    }

    @PostMapping("/getPurchaseRequisitionWorkOrderData")
    @ApiOperation(value = "根据采购申请来源工单数据Guid查询工序工单等信息")
    @SystemMethodLog(type = "query", description = "根据采购申请来源工单数据Guid查询工序工单等信息")
    public List<PurchaseRequisitionWorkOrderDataVO> getPurchaseRequisitionWorkOrderData(@RequestBody List<String> workorderGuids) {
        return service.getPurchaseRequisitionWorkOrderData(workorderGuids);
    }

    @PostMapping("/getNewData")
    @ApiOperation(value = "获取某个客户的某个物料最近的每件数量")
    @SystemMethodLog(type = "modify", description = "获取某个客户的某个物料最近的每件数量")
    public BigDecimal getNewData(@RequestBody @Validated ErpGetNewDataQO qo) {
        return service.getNewData(qo);
    }
    @PostMapping("/updateToJson")
    @ApiOperation(value = "修改采购订单明细tojson")
    @SystemMethodLog(type = "modify", description = "修改采购订单明细tojson")
    @XyTransMethod
    public Boolean updateToJson(@RequestBody List<ErpUpdateToJsonRO> list) {
        return service.updateToJson(list);
    }


}
