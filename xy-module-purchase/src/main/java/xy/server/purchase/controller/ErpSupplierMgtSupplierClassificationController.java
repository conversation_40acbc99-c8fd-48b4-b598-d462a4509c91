package xy.server.purchase.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.tenant.history.SystemFieldHistory;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.trans.annotation.XyTransMethod;
import com.xunyue.config.util.EasyExcelUtils;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import xy.server.purchase.entity.ErpSupplierMgtSupplierClassification;
import xy.server.purchase.entity.model.qo.ErpSupplierMgtSupplierClassificationQO;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierClassificationRO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierClassificationVO;
import xy.server.purchase.service.IErpSupplierMgtSupplierClassificationService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 供应商分类 controller
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "供应商分类")
@RestController
@RequestMapping("/purchase/erp-supplier-mgt-supplier-classification")
@SystemClassLog(code = "ErpSupplierMgtSupplierClassificationController")
public class ErpSupplierMgtSupplierClassificationController {
    private final IErpSupplierMgtSupplierClassificationService service;
    private final EasyExcelUtils easyExcelUtils;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增供应商分类")
    @SystemFieldHistory(targetEntity = ErpSupplierMgtSupplierClassification.class)
    public Boolean createOne(@RequestBody @Validated ErpSupplierMgtSupplierClassificationRO ro, HttpServletRequest request) {
        ro.setTenantGuid(request.getHeader("tenantGuid"));
        return service.create(ro);
    }

    @DeleteMapping("/delete/{supplierClassificationGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除供应商分类")
    @SystemFieldHistory(targetEntity = ErpSupplierMgtSupplierClassification.class)
    public Boolean delete(@PathVariable("supplierClassificationGuid") String supplierClassificationGuid) {
        return service.delete(supplierClassificationGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除供应商分类")
    @SystemFieldHistory(targetEntity = ErpSupplierMgtSupplierClassification.class)
    public Boolean deleteByBatch(@RequestBody List<String> ids) {
        return service.deleteByBatch(ids);
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改供应商分类")
    @SystemFieldHistory(targetEntity = ErpSupplierMgtSupplierClassification.class)
    public Boolean update(@RequestBody @Validated ErpSupplierMgtSupplierClassificationRO ro) {
        return service.update(ro);
    }

    @GetMapping("/{supplierClassificationGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条供应商分类")
    @XyTransMethod
    public ErpSupplierMgtSupplierClassificationVO getOne(@PathVariable("supplierClassificationGuid") String supplierClassificationGuid) {
        return service.getById(supplierClassificationGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询供应商分类")
    @XyTransMethod
    public List<ErpSupplierMgtSupplierClassificationVO> findList(@RequestBody @Validated ErpSupplierMgtSupplierClassificationQO qo, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        return service.findList(qo, tenantGuid);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询供应商分类")
    @XyTransMethod
    public IPage<ErpSupplierMgtSupplierClassificationVO> findPage(@RequestBody @Validated PageParams<ErpSupplierMgtSupplierClassificationQO> pageParams, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        pageParams.setUserId(userGuid);
        pageParams.setTenantGuid(tenantGuid);
        return service.findPage(pageParams);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    @SystemFieldHistory(targetEntity = ErpSupplierMgtSupplierClassification.class)
    public Boolean saveDate(@RequestBody @Validated InsertOrUpdateList<ErpSupplierMgtSupplierClassificationRO> dataList, HttpServletRequest request) {
        String tenantGuid = request.getHeader("tenantGuid");
        return service.saveDate(dataList, tenantGuid);
    }

    @PostMapping("/reordered")
    @ApiOperation(value = "重新排序")
    @SystemMethodLog(type = "modify", description = "重新排序")
    public Boolean reordered(@RequestBody @Validated List<SortEntityDto> entityDtoList) {
        return service.reordered(entityDtoList);
    }

    @GetMapping("/downloadTemplate")
    @ApiOperation(value = "下载模版")
    public void downloadTemplate(HttpServletResponse response) {
        easyExcelUtils.downloadTemplate(ErpSupplierMgtSupplierClassificationRO.class, response);
    }

    @PostMapping("/importExcel")
    @ApiOperation(value = "Excel导入")
    public Boolean importExcel(@RequestPart("file") MultipartFile file, @RequestParam("isErrorResume") boolean isErrorResume) {
        return easyExcelUtils.analyzeExcel(file, ErpSupplierMgtSupplierClassificationRO.class, service, isErrorResume);
    }
}
