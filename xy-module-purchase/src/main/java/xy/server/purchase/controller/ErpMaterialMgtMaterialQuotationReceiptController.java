package xy.server.purchase.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.trans.annotation.XyTransMethod;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import xy.server.purchase.entity.model.qo.ErpMaterialMgtMaterialQuotationReceiptQO;
import xy.server.purchase.entity.model.ro.ErpMaterialMgtMaterialQuotationReceiptRO;
import xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationReceiptDetailVO;
import xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationReceiptVO;
import xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationVO;
import xy.server.purchase.entity.model.vo.ErpQuotationImportVO;
import xy.server.purchase.service.IErpMaterialMgtMaterialQuotationReceiptService;

import java.util.List;


/**
 * <AUTHOR>
 * @apiNote 物料报价表 controller
 * @since 2024-05-23
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "物料报价表")
@RestController
@RequestMapping("/purchase/erp-material-mgt-material-quotation-receipt")
@SystemClassLog(code = "ErpMaterialMgtMaterialQuotationReceiptController")
public class ErpMaterialMgtMaterialQuotationReceiptController {
    private final IErpMaterialMgtMaterialQuotationReceiptService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增物料报价表")
    public ErpQuotationImportVO createOne(@RequestBody @Validated ErpMaterialMgtMaterialQuotationReceiptRO ro) {
        return service.create(ro);
    }

    @DeleteMapping("/delete/{materialQuotationReceiptGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除物料报价表")
    public Boolean delete(@PathVariable("materialQuotationReceiptGuid") String materialQuotationReceiptGuid) {
        return service.delete(materialQuotationReceiptGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除物料报价表")
    public Boolean deleteByBatch(@RequestBody List<String> materialQuotationReceiptGuids) {
        return service.deleteByBatch(materialQuotationReceiptGuids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改物料报价表")
    public Boolean update(@RequestBody @Validated ErpMaterialMgtMaterialQuotationReceiptRO ro) {
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    public ErpQuotationImportVO saveDate(@RequestBody @Validated InsertOrUpdateList<ErpMaterialMgtMaterialQuotationReceiptRO> dataList) {
        return service.saveDate(dataList);
    }

    @GetMapping("/getOne/{materialQuotationReceiptGuid}")
    @ApiOperation(value = "单条查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "查询单条物料报价表")
    public ErpMaterialMgtMaterialQuotationReceiptVO getOne(@PathVariable("materialQuotationReceiptGuid") String materialQuotationReceiptGuid) {
        return service.getDataById(materialQuotationReceiptGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询物料报价表")
    @XyTransMethod
    public List<ErpMaterialMgtMaterialQuotationReceiptVO> findList(@RequestBody @Validated ErpMaterialMgtMaterialQuotationReceiptQO qo) {
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "分页查询物料报价表")
    public IPage<ErpMaterialMgtMaterialQuotationReceiptVO> findPage(@RequestBody @Validated PageParams<ErpMaterialMgtMaterialQuotationReceiptQO> pageParams) {
        return service.findPage(pageParams);
    }

    @PostMapping("/useQuotation")
    @ApiOperation(value = "应用报价")
    @SystemMethodLog(type = "update", description = "应用报价")
    public Boolean useQuotation(@RequestBody @Validated List<String> list) {
        return service.useQuotation(list);
    }

    @PostMapping("/import")
    @ApiOperation(value = "数据导入")
    public ErpQuotationImportVO importUser(@RequestPart("file") MultipartFile file) throws Exception {
        return service.importOrderDate(file);
    }

    @PostMapping("/findDetailPage")
    @ApiOperation(value = "平铺明细数据")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "平铺明细数据")
    public IPage<ErpMaterialMgtMaterialQuotationVO> findDetailPage(@RequestBody @Validated PageParams<ErpMaterialMgtMaterialQuotationReceiptQO> pageParams) {
        return service.findDetailPage(pageParams);
    }

    @PostMapping("/getNewMoney")
    @ApiOperation(value = "获取最新的采购报价")
    @SystemMethodLog(type = "query", description = "获取最新的采购报价")
    public List<ErpMaterialMgtMaterialQuotationReceiptDetailVO> getNewMoney(@RequestBody @Validated ErpMaterialMgtMaterialQuotationReceiptQO qo) {
        return service.getNewMoney(qo);
    }

}
