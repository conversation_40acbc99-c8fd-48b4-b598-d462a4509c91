package xy.server.purchase.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.tenant.history.SystemFieldHistory;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.trans.annotation.XyTransMethod;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.filter.aspect.XyViewFilterParam;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseRequisition;
import xy.server.purchase.entity.model.qo.ErpPurchaseMgtPurchaseRequisitionQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingDetaiStopRO;
import xy.server.purchase.entity.model.ro.ErpPurchaseMgtPurchaseRequisitionRO;
import xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseRequisitionVO;
import xy.server.purchase.entity.model.vo.ErpPurchaseSourceRequisitionVO;
import xy.server.purchase.service.IErpPurchaseMgtPurchaseRequisitionService;

import java.util.List;

/**
 * <AUTHOR>
 * @data 2023/9/13 10:48
 * @apiNote 采购申请单controller
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "采购申请单")
@RestController
@RequestMapping("/purchase/erp-purchase-mgt-purchase-requisition")
@SystemClassLog(code = "ErpPurchaseMgtPurchaseRequisitionController")
public class ErpPurchaseMgtPurchaseRequisitionController {
    private final IErpPurchaseMgtPurchaseRequisitionService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增采购申请单")
    public ErpPurchaseMgtPurchaseRequisitionRO create(@RequestBody ErpPurchaseMgtPurchaseRequisitionRO ro) {
        return service.create(ro);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改采购申请单")
    @SystemFieldHistory(targetEntity = ErpPurchaseMgtPurchaseRequisition.class)
    public Boolean update(@RequestBody ErpPurchaseMgtPurchaseRequisitionRO ro) {
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    @SystemFieldHistory(targetEntity = ErpPurchaseMgtPurchaseRequisition.class)
    public List<String> saveDate(@RequestBody InsertOrUpdateList<ErpPurchaseMgtPurchaseRequisitionRO> dataList) {
        return service.saveDate(dataList);
    }

    @DeleteMapping("/delete/{workorderGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除供应商")
    @SystemFieldHistory(targetEntity = ErpPurchaseMgtPurchaseRequisition.class)
    public Boolean delete(@PathVariable("workorderGuid") String workorderGuid) {
        return service.delete(workorderGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除供应商")
    @SystemFieldHistory(targetEntity = ErpPurchaseMgtPurchaseRequisition.class)
    public Boolean deleteByBatch(@RequestBody List<String> ids) {
        return service.deleteByBatch(ids);
    }

    @GetMapping("/getOne/{workorderGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条供应商")
    @XyTransMethod
    public ErpPurchaseMgtPurchaseRequisitionVO getOne(@PathVariable("workorderGuid") String workorderGuid) {
        return service.getOneById(workorderGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询供应商")
    @XyTransMethod
    public List<ErpPurchaseMgtPurchaseRequisitionVO> findList(@RequestBody ErpPurchaseMgtPurchaseRequisitionQO qo) {
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询供应商")
    @XyTransMethod
    public IPage<ErpPurchaseMgtPurchaseRequisitionVO> findPage(@RequestBody PageParams<ErpPurchaseMgtPurchaseRequisitionQO> pageParams) {
        return service.findPage(pageParams);
    }

    @PostMapping("/findNotBilledList")
    @ApiOperation(value = "待开列表查询")
    @SystemMethodLog(type = "query", description = "待开列表查询")
    @XyTransMethod
    public List<ErpPurchaseMgtPurchaseRequisitionVO> findNotBilledList(@RequestBody ErpPurchaseMgtPurchaseRequisitionQO qo) {
        return service.findNotBilledList(qo);
    }

    @PostMapping("/findNotBilledTree")
    @ApiOperation(value = "待开列表查询（包含主工单）")
    @SystemMethodLog(type = "query", description = "待开列表查询（包含主工单）")
    @XyTransMethod
    public List<ErpPurchaseMgtPurchaseRequisitionVO> findNotBilledTree(@RequestBody @XyViewFilterParam ErpPurchaseMgtPurchaseRequisitionQO qo) {
        return service.findNotBilledTree(qo);
    }
    @PostMapping("/waitingFindList")
    @ApiOperation(value = "工单，订单待开列表查询（包含主工单）")
    @SystemMethodLog(type = "query", description = "待开列表查询（包含主工单）")
    @XyTransMethod
    public List<ErpPurchaseSourceRequisitionVO> waitingFindList(@RequestBody ErpPurchaseMgtPurchaseRequisitionQO qo) {
        return service.waitingFindList(qo);
    }

    @PostMapping("/stopOrRollback")
    @ApiOperation(value = "终止单据")
    @SystemMethodLog(type = "modify", description = "修改装舱表")
    public Boolean stopOrRollback(@RequestBody @Validated List<ErpOutgoingDetaiStopRO> list) {
        return service.stopOrRollback(list);
    }

    @PostMapping("/generatePurchaseRequisition")
    @ApiOperation(value = "生成采购申请单")
    @SystemMethodLog(type = "modify", description = "生成采购申请单")
    public Boolean generatePurchaseRequisition(@RequestBody ErpPurchaseMgtPurchaseRequisitionQO qo) {
        return service.generatePurchaseRequisition(qo);
    }

    @PostMapping("/findNotBilledListES")
    @ApiOperation(value = "包含业务预测以及采购申请的数据待开列表查询")
    @SystemMethodLog(type = "query", description = "包含业务预测以及采购申请的数据待开列表查询")
    @XyTransMethod
    public List<ErpPurchaseMgtPurchaseRequisitionVO> findNotBilledListES(@RequestBody ErpPurchaseMgtPurchaseRequisitionQO qo) {
        return service.findNotBilledListES(qo);
    }

    @PostMapping("/findNotBilledTreeES")
    @ApiOperation(value = "包含业务预测以及采购申请的数据待开列表查询（包含主工单）")
    @SystemMethodLog(type = "query", description = "包含业务预测以及采购申请的数据待开列表查询（包含主工单）")
    @XyTransMethod
    public List<ErpPurchaseMgtPurchaseRequisitionVO> findNotBilledTreeES(@RequestBody @XyViewFilterParam ErpPurchaseMgtPurchaseRequisitionQO qo) {
        return service.findNotBilledTreeES(qo);
    }
}
