package xy.server.purchase.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.vo.SourceDescriptionAndPictureVO;
import com.xunyue.tenant.history.SystemFieldHistory;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.trans.annotation.XyTransMethod;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.purchase.entity.ErpOutgoingBusinessMgtOrder;
import xy.server.purchase.entity.model.qo.ErpOutgoingBusinessMgtOrderQO;
import xy.server.purchase.entity.model.qo.ErpPurchaseMgtPurchaseOrderQO;
import xy.server.purchase.entity.model.qo.ErpPurchaseProcessOutgoingOrderQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingBusinessMgtOrderRO;
import xy.server.purchase.entity.model.ro.ErpOutgoingDetaiStopRO;
import xy.server.purchase.entity.model.vo.ErpOutgoingBusinessMgtOrderVO;
import xy.server.purchase.entity.model.vo.ErpProcessOutgoingOrderVO;
import xy.server.purchase.entity.model.vo.ErpPurchaseInventoryWorkorderDetailVO;
import xy.server.purchase.entity.model.vo.ErpPurchaseInventoryWorkorderVO;
import xy.server.purchase.service.IErpOutgoingBusinessMgtOrderService;

import java.util.List;


/**
 * <p>
 * 订单表 controller
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "外发订单管理")
@RestController
@RequestMapping("/purchase/erp-business-mgt-order")
@SystemClassLog(code = "ErpOutgoingBusinessMgtOrderController")
public class ErpOutgoingBusinessMgtOrderController {
    private final IErpOutgoingBusinessMgtOrderService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增订单表")
    public String createOne(@RequestBody @Validated ErpOutgoingBusinessMgtOrderRO ro) {
        return service.create(ro);
    }
    @DeleteMapping("/delete/{orderGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除订单表")
    @SystemFieldHistory(targetEntity = ErpOutgoingBusinessMgtOrder.class)
    public Boolean delete(@PathVariable("orderGuid") String orderGuid) {
        return service.delete(orderGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "批量删除订单表")
    @SystemFieldHistory(targetEntity = ErpOutgoingBusinessMgtOrder.class)
    public Boolean deleteByBatch(@RequestBody List<String> ids) {
        return service.removeIds(ids);
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改订单表")
    @SystemFieldHistory(targetEntity = ErpOutgoingBusinessMgtOrder.class)
    public Boolean update(@RequestBody @Validated ErpOutgoingBusinessMgtOrderRO ro) {
        return service.update(ro);
    }

    @GetMapping("/{orderGuid}")
    @ApiOperation(value = "单条查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "查询单条订单表")
    public ErpOutgoingBusinessMgtOrderVO getOne(@PathVariable("orderGuid") String orderGuid) {
        return service.getById(orderGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "列表查询订单表")
    public List<ErpOutgoingBusinessMgtOrderVO> findList(@RequestBody @Validated ErpOutgoingBusinessMgtOrderQO qo) {
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "分页查询订单表")
    public IPage<ErpOutgoingBusinessMgtOrderVO> findPage(@RequestBody @Validated PageParams<ErpOutgoingBusinessMgtOrderQO> pageParams) {
        return service.findPage(pageParams);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存（新增或者更新）")
    @SystemMethodLog(type = "modify", description = "保存（新增或者更新）")
    @SystemFieldHistory(targetEntity = ErpOutgoingBusinessMgtOrder.class)
    public List<String> saveData(@RequestBody @Validated InsertOrUpdateList<ErpOutgoingBusinessMgtOrderRO> list) {
       return service.saveData(list);
    }

    @PostMapping("/processFindList")
    @ApiOperation(value = "加工商预付明细列表查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "加工商预付明细列表查询")
    public List<ErpProcessOutgoingOrderVO> processFindList(@RequestBody @Validated ErpPurchaseProcessOutgoingOrderQO qo) {
        return service.processFindList(qo);
    }

    @PostMapping("/findNotBilledList")
    @ApiOperation(value = "待开列表查询")
    @SystemMethodLog(type = "query", description = "待开列表查询")
    @XyTransMethod
    public List<ErpPurchaseInventoryWorkorderDetailVO> findNotBilledList(@RequestBody ErpPurchaseMgtPurchaseOrderQO qo) {
        return service.findNotBilledList(qo);
    }

    @PostMapping("/findNotBilledTree")
    @ApiOperation(value = "待开列表查询（包含主工单）")
    @SystemMethodLog(type = "query", description = "待开列表查询（包含主工单）")
    @XyTransMethod
    public List<ErpPurchaseInventoryWorkorderVO> findNotBilledTree(@RequestBody ErpPurchaseMgtPurchaseOrderQO qo) {
        return service.findNotBilledTree(qo);
    }

    @PostMapping("/stopOrRollback")
    @ApiOperation(value = "终止单据")
    @SystemMethodLog(type = "modify", description = "修改装舱表")
    public Boolean stopOrRollback(@RequestBody @Validated List<ErpOutgoingDetaiStopRO> list) {
        return service.stopOrRollback(list);
    }

    @PostMapping("/getSourceDescriptionAndPicture")
    @ApiOperation(value = "获取顶级来源备注和图片")
    @SystemMethodLog(type = "query", description = "获取顶级来源备注和图片")
    public List<SourceDescriptionAndPictureVO> getSourceDescriptionAndPicture(@RequestBody @Validated List<String> sourceNumbers) {
        return service.getSourceDescriptionAndPicture(sourceNumbers);
    }

}
