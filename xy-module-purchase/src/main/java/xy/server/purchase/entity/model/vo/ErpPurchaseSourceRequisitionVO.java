package xy.server.purchase.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @data 2023/9/13 11:06
 * @apiNote 采购申请单VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpPurchaseSourceRequisitionVO对象", description = "采购申请单VO")
public class ErpPurchaseSourceRequisitionVO{

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "来源")
    @XyTrans(dictionaryKey = "PURCHASING_SOURCE", dictionaryValue = "sourceValue")
    private String sourceDictValue;

    @ApiModelProperty(value = "物料guid")
    private String materialGuid;

    @ApiModelProperty(value = "来源数量")
    private BigDecimal sourceQuantity;

    @ApiModelProperty(value = "赠品数量")
    private BigDecimal giftQuantity;

    @ApiModelProperty(value = "备用数量")
    private BigDecimal spareQuantity;

    @ApiModelProperty(value = "待开数量")
    private BigDecimal waitingQuantity;

    @ApiModelProperty(value = "客户Guid")
    private String customerGuid;

    @ApiModelProperty(value = "客户简称")
    private String customerShortName;

    @ApiModelProperty(value = "单位guid")
    private String unitGuid;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

//    @ApiModelProperty(value = "规格值")
//    private String specificationValueROS;

    @ApiModelProperty(value = "物料编码，产品编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String  materialName;

    @ApiModelProperty(value = "物料Obj")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialObj;

}
