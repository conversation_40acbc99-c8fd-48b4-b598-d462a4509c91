package xy.server.purchase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 物料报价表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_material_mgt_material_quotation_receipt", autoResultMap = true)
public class ErpMaterialMgtMaterialQuotationReceipt extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("material_quotation_receipt_guid")
    private String materialQuotationReceiptGuid;
    /**
     * 报价类型(字典)
     */
    @TableField("material_quotation_receipt_type")
    private String materialQuotationReceiptType;
    /**
     * 物料报价单号
     */
    @TableField("material_quotation_receipt_numbers")
    private String materialQuotationReceiptNumbers;
    /**
     * 单据日期
     */
    @TableField("receipt_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptDate;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 客户guid or 供应商guid
     */
    @TableField("customer_or_supplier_guid")
    private String customerOrSupplierGuid;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 扩展字段
     */
    @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
    private Object toJson;
    /**
     * 流程实例ID
     */
    @TableField("process_instance_id")
    private String processInstanceId;
    /**
     * 审核状态
     */
    @TableField("audit_status")
    private String auditStatus;
    /**
     * 审核时间
     */
    @TableField("audit_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditDate;
    /**
     * 生效时间
     */
    @TableField("effective_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectiveDate;
    /**
     * 真实生效时间
     */
    @TableField("real_effective_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime realEffectiveDate;
    /**
     * 客户物料报价1，供应商物料报价2
     */
    @TableField("material_quotation_receipt_properties")
    private Integer materialQuotationReceiptProperties;
    /**
     * 生效状态
     */
    @TableField("effective_status")
    private Boolean effectiveStatus;
    /**
     * 打印次数
     */
    @TableField("print_times")
    private Integer printTimes;


}
