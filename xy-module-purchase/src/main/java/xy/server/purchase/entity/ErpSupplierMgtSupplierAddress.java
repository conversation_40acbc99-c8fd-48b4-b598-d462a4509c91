package xy.server.purchase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 供应商地址
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_supplier_mgt_supplier_address",autoResultMap = true)
public class ErpSupplierMgtSupplierAddress extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("supplier_address_guid")
    private String supplierAddressGuid;
    /**
     * erp_supplier_mgt_Supplier _guid
     */
    @TableField("supplier_guid")
    private String supplierGuid;
    /**
     * 顺序号
     */
    @TableField("serial_number")
    private Integer serialNumber;
    /**
     * ERP_BasicMGT_AdministrativeArea 行政区域_guid
     */
    @TableField("administrative_area_guid")
    private String administrativeAreaGuid;
    /**
     * 详细地址
     */
    @TableField("address")
    private String address;
    /**
     * 是否默认，只允许默认一个
     */
    @TableField("is_default")
    private Boolean isDefault;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;

    @TableField(value = "to_json",typeHandler  = JsonbTypeHandler.class)
    private Object toJson;



}
