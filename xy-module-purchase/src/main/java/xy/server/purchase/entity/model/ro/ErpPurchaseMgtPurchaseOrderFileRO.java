package xy.server.purchase.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 订单文件表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPurchaseMgtPurchaseOrderFileRO对象", description = "订单文件表")
public class ErpPurchaseMgtPurchaseOrderFileRO extends BaseEntity {

    @ApiModelProperty(value = "订单表_guid")
    private String orderGuid;

    @ApiModelProperty(value = "PK")
    private String orderFileGuid;

    @ApiModelProperty(value = "文件表_guid")
    private String fileGuid;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @NotNull(message = "文件类型(1图片，2文件，3流程图 )不能为空")
    @ApiModelProperty(value = "文件类型(1图片，2文件，3流程图 )")
    private Integer fileType;

}
