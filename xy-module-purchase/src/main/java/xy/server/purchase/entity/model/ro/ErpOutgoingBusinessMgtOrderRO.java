package xy.server.purchase.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.purchase.entity.model.vo.FileGuidAndNameVO;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 订单表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBusinessMgtOrderURO对象", description = "订单表")
public class ErpOutgoingBusinessMgtOrderRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String orderGuid;

    @NotNull(message = "外发类型不能为空")
    @ApiModelProperty(value = "外发类型")
    private String orderType;

    @NotNull(message = "联系人姓名不能为空")
    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @NotNull(message = "电话不能为空")
    @ApiModelProperty(value = "电话")
    private String mobilephone;

    @ApiModelProperty(value = "结算类型")
    private String settlementTypeGuid;

    @NotNull(message = "保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)不能为空")
    @ApiModelProperty(value = "保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)")
    private Integer decimalMethod;

    @NotNull(message = "结算单价保留小数位(默认9位，可手动变更)不能为空")
    @ApiModelProperty(value = "结算单价保留小数位(默认9位，可手动变更)")
    private Integer settlementUnitPriceKeepDecimalPlace;

    @NotNull(message = "结算金额保留小数位(默认2位，可手动变更)不能为空")
    @ApiModelProperty(value = "结算金额保留小数位(默认2位，可手动变更)")
    private Integer settlementTotalAmountKeepDecimalPlace;

    //传入订单属性：外发订单

    @ApiModelProperty(value = "税率guid")
    private String invoiceTypeTaxRateGuid;

    @ApiModelProperty(value = "币种汇率guid")
    private String currencyExchangeRateGuid;

    /*@NotNull(message = "是否加急不能为空")
    @ApiModelProperty(value = "是否加急")
    private Boolean isUrgent;

    @NotNull(message = "打印次数不能为空")
    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;*/

    @ApiModelProperty(value = "客户/供应商guid")
    private String customerOrSupplierGuid;

    @ApiModelProperty(value = "结算客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String settlementCustomerOrSupplierGuid;

    @ApiModelProperty(value = "送货客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String deliveryCustomerOrSupplierGuid;

    /*@ApiModelProperty(value = "来源guid")
    private String sourceGuid;*/

    /*@NotNull(message = "来源值不能为空")
    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;*/

    @ApiModelProperty(value = "文件Guid")
    private List<FileGuidAndNameVO> fileGuid;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "工单号")
    private String orderNumber;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "加工明细")
    private List<ErpOutgoingBusinessSpecifOrderDataRO> specificList;

}
