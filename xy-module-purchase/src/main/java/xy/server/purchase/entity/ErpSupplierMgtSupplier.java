package xy.server.purchase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 供应商
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_supplier_mgt_supplier",autoResultMap = true)
public class ErpSupplierMgtSupplier extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("supplier_guid")
    private String supplierGuid;
    /**
     * 供应商编码
     */
    @TableField("supplier_code")
    private String supplierCode;
    /**
     * 供应商简称
     */
    @TableField("supplier_short_name")
    private String supplierShortName;
    /**
     * 供应商全称
     */
    @TableField("supplier_full_name")
    private String supplierFullName;
    /**
     * 供应商分类_guid
     */
    @TableField("supplier_classification_guid")
    private String supplierClassificationGuid;
    /**
     * 供应商级别_guid
     */
    @TableField("supplier_level_guid")
    private String supplierLevelGuid;
    /**
     * 电话
     */
    @TableField("telephone")
    private String telephone;
    /**
     * 手机
     */
    @TableField("mobilephone")
    private String mobilephone;
    /**
     * 传真
     */
    @TableField("fax")
    private String fax;
    /**
     * 邮件
     */
    @TableField("email")
    private String email;
    /**
     * 开户行
     */
    @TableField("opening_bank")
    private String openingBank;
    /**
     * 银行帐号
     */
    @TableField("bank_account")
    private String bankAccount;
    /**
     * 企业税号
     */
    @TableField("enterprise_tax_identification_number")
    private String enterpriseTaxIdentificationNumber;
    /**
     * 默认币种 Currency _guid
     */
    @TableField("default_currency_guid")
    private String defaultCurrencyGuid;
    /**
     * 默认发票类型税率 InvoiceTypeTaxRate_guid
     */
    @TableField("default_invoice_type_tax_rate_guid")
    private String defaultInvoiceTypeTaxRateGuid;
    /**
     * 默认送货类型 DeliveryType_guid
     */
    @TableField("default_delivery_type_guid")
    private String defaultDeliveryTypeGuid;
    /**
     * 默认结算类型 SettlementType_guid
     */
    @TableField("default_settlement_type_guid")
    private String defaultSettlementTypeGuid;
    /**
     * 账期
     */
    @TableField("account_period")
    private Integer accountPeriod;
    /**
     * 状态(0停用，1启用)
     */
    @TableField("state")
    private Boolean state;
    /**
     * 已被使用
     */
    @TableField("is_used")
    private Boolean isUsed;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 扩展字段
     */
    @TableField(value = "to_json",typeHandler  = JsonbTypeHandler.class)
    private Object toJson;
    /**
     * 流程实例ID
     */
    @TableField("process_instance_id")
    private String processInstanceId;
    /**
     * 审核状态
     */
    @TableField("audit_status")
    private String auditStatus;
    /**
     * 审核时间
     */
    @TableField("audit_date")
    private Date auditDate;
    /**
     * 结账日 reconciliation_date
     */
    @TableField("reconciliation_date")
    private String reconciliationDate;
    /**
     * 父级供应商id
     */
    @TableField("parent_supplier_guid")
    private String parentSupplierGuid;

}
