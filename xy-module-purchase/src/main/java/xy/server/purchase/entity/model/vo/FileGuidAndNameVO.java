package xy.server.purchase.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "FileGuidAndNameVO对象", description = "文件对象")
public class FileGuidAndNameVO {
    @ApiModelProperty(value = "文件id")
    private String fileGuid;
    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;
    @ApiModelProperty(value = "文件名")
    private String fileName;
}
