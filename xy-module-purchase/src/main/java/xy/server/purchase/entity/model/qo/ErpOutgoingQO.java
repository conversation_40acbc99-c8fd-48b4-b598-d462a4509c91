package xy.server.purchase.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 单QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpOutgoingQO对象", description = "")
public class ErpOutgoingQO {

    @ApiModelProperty(value = "申请单号")
    private String applicationNumber;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "部件名称")
    private String partName;

    /**
     * 根据工序类型id获取对应的名称
     */
    @ApiModelProperty(value = "工序")
    private String processName;

    @ApiModelProperty(value = "开单开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "是否展示已开完的外发工序")
    private Boolean isAll;

    @ApiModelProperty(value = "来源id")
    private List<String> workOrderProcessGuid;

    @ApiModelProperty(value = "产品名称/编码")
    private String production;

    @ApiModelProperty(value = "审核状态")
    private List<String> auditStatus;

}
