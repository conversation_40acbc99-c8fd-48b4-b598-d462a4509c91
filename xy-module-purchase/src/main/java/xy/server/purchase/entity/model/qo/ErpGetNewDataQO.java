package xy.server.purchase.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 供应商QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpGetNewDataQO对象")
public class ErpGetNewDataQO {

    @ApiModelProperty(value = "客户或者供应商id")
    private String customerOrSupplierGuid;

    @ApiModelProperty(value = "物料guid")
    private String materialGuid;

}
