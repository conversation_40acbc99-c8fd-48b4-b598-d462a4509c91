package xy.server.purchase.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
* <p>
* 工单表RO
* </p>
*
* <AUTHOR>
* @since 2023-09-15
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpOrderDataVO对象")
public class ErpOrderDataVO {

    @ApiModelProperty(value = "订单明细扩展表id")
    private String saleOrderDataGuid;

    @ApiModelProperty(value = "订单明细数量")
    private BigDecimal orderDataQuantity;

    @ApiModelProperty(value = "已采购数量")
    private BigDecimal purchaseQuantity;
}