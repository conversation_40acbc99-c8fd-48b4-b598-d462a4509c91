package xy.server.purchase.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* <p>
* 工单表VO
* </p>
*
* <AUTHOR>
* @since 2023-09-15
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpOutgoingMgtApplicationVO对象", description = "工单表")
public class ErpOutgoingMgtApplicationVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "工单类型_guid")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "工单状态(完成、故障、未开始)")
    private String workorderState;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "工单属性(1工单，2工序工单，3采购工单，4外发工单，5车间工单，6作业工单，维修工单，保养工单)")
    private Integer workorderProperties;

    @ApiModelProperty(value = "紧急程度(非常紧急，紧急，一般)")
    private String isUrgent;

    @ApiModelProperty(value = "FSC声明_guid")
    private String fscDeclarationGuid;

    @ApiModelProperty(value = "客户")
    private String customerGuid;

    @ApiModelProperty(value = "物料分类(成品)")
    private String materialGuid;

    @ApiModelProperty(value = "工艺类型guid")
    private String productionProcessesTypeGuid;

    @ApiModelProperty(value = "占比、比例(根据工单属性:1工单占比数，2是工序工单占工单占比，工序工单产出比)")
    private BigDecimal proportion;

    @ApiModelProperty(value = "工艺描述")
    private String productionProcessesDescription;

    @ApiModelProperty(value = "数量(根据工单属性:1工单成品数，2工序投入数量，3采购申请数量，4外发申请数)")
    private BigDecimal quantity;

    @ApiModelProperty(value = "使用库存数量")
    private BigDecimal usingInventoryQuantity;

    @ApiModelProperty(value = "总数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "单位（(根据工单属性:1工单成品单位，2工序加工单位）")
    private String unitGuid;

    @ApiModelProperty(value = "规格长(根据工单属性:1工单成品规格，2工序加工规格)")
    private BigDecimal specificationsLength;

    @ApiModelProperty(value = "规格宽(根据工单属性:1工单成品规格，2工序加工规格)")
    private BigDecimal specificationsWidth;

    @ApiModelProperty(value = "规格高(根据工单属性:1工单成品规格，2工序加工规格)")
    private BigDecimal specificationsHeight;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "要求交货期")
    private LocalDateTime requiredDeliveryTime;

    @ApiModelProperty(value = "是否父工单")
    private Boolean isFatherWork;

    @ApiModelProperty(value = "产品物料id")
    private String productMaterialGuid;

    @ApiModelProperty(value = "工艺流")
    private String processFlowchart;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;
}