package xy.server.purchase.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 供应商文件QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpSupplierMgtSupplierFileQO对象", description = "供应商文件")
public class ErpSupplierMgtSupplierFileQO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String supplierFileGuid;

    @ApiModelProperty(value = "erp_supplier_mgt_Supplier 供应商_guid")
    private String supplierGuid;

    @ApiModelProperty(value = "文件类型1是图片2是文件")
    private Integer fileType;

    @ApiModelProperty(value = "文件 _guid")
    private String fileGuid;

    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "关键字列表")
    private List<String> keywords;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

}
