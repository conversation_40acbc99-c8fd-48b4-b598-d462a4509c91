package xy.server.purchase.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.purchase.entity.ErpOutgoingDataProcess;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "WorkOrderProcessAndOutgoingApplicationVO对象", description = "")
public class WorkOrderProcessAndOutgoingApplicationVO {
    @ApiModelProperty(value = "子工单对应的工单工序")
    private ErpOutgoingDataProcess workOrderProcess;
    @ApiModelProperty(value = "子工单已外发数")
    private BigDecimal sonOutgoingQuantity;
    @ApiModelProperty(value = "子工单对应的工单工序guid")
    private String workOrderProcessGuid;
}
