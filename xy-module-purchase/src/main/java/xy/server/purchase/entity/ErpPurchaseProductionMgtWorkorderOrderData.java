package xy.server.purchase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 工单订单数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_production_mgt_workorder_order_data")
public class ErpPurchaseProductionMgtWorkorderOrderData implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("workorder_order_data_guid")
    private String workorderOrderDataGuid;
    /**
     * 工单总表_guid
     */
    @TableField("workorder_guid")
    private String workorderGuid;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 订单数据表guid
     */
    @TableField("order_data_guid")
    private String orderDataGuid;


}
