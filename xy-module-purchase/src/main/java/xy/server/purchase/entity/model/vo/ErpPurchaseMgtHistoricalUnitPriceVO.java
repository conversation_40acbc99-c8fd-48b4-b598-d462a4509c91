package xy.server.purchase.entity.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @data 2024-08-19 10:17
 * @apiNote 采购历史单价VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpPurchaseMgtHistoricalUnitPriceVO", description = "采购历史单价VO")
public class ErpPurchaseMgtHistoricalUnitPriceVO {

    @ApiModelProperty(value = "物料Guid")
    private String materialGuid;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料分类Guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料分类名称")
    private String materialClassificationName;

    @ApiModelProperty(value = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "物料类型名称")
    @XyTrans(dictionaryKey = "materialType", dictionaryValue = "materialType")
    private String materialTypeName;

    @ExcelProperty("规格类型")
    private String specificationTypeStr;

    @ApiModelProperty(value = "供应商Guid")
    private String supplierGuid;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商简称")
    private String supplierShortName;

    @ApiModelProperty(value = "供应商全称")
    private String supplierFullName;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "实际到货数量")
    private BigDecimal actQuantity;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "到货日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "到货单号")
    private String workorderNumber;

    @ApiModelProperty(value = "到货单Guid")
    private String workorderGuid;

}
