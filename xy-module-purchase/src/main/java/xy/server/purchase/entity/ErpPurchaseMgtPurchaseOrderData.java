package xy.server.purchase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_business_mgt_order_data", autoResultMap = true)
public class ErpPurchaseMgtPurchaseOrderData extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 顺序号
     */
    @TableField("serial_number")
    private Integer serialNumber;
    /**
     * PK
     */
    @TableId("order_data_guid")
    private String orderDataGuid;
    /**
     * 订单guid  一对多
     */
    @TableField("order_guid")
    private String orderGuid;
    /**
     * 物料分类guid
     */
    @TableField("material_classification_guid")
    private String materialClassificationGuid;
    /**
     * 物料GUID(成品、物料)
     */
    @TableField("material_guid")
    private String materialGuid;
    /**
     * 单位guid
     */
    @TableField("unit_guid")
    private String unitGuid;
    /**
     * FSC声明_guid
     */
    @TableField("fsc_declaration_guid")
    private String fscDeclarationGuid;
    /**
     * 经验工序guid
     */
    @TableField("experience_production_process_guid")
    private String experienceProductionProcessGuid;
    /**
     * 数量
     */
    @TableField("quantity")
    private BigDecimal quantity;
    /**
     * 赠品数量
     */
    @TableField("gift_quantity")
    private BigDecimal giftQuantity;
    /**
     * 备用数量
     */
    @TableField("spare_quantity")
    private BigDecimal spareQuantity;
    /**
     * 使用库存数量
     */
    @TableField("using_inventory_quantity")
    private BigDecimal usingInventoryQuantity;
    /**
     * 总数量
     */
    @TableField("total_quantity")
    private BigDecimal totalQuantity;
    /**
     * 含税报价单价(吨价，平方价，千平方英寸)
     */
    @TableField("quotation_unit_price_including_tax")
    private BigDecimal quotationUnitPriceIncludingTax;
    /**
     * 含税单价(默认隐藏，根据计费方案转换计算得出)
     */
    @TableField("unit_price_including_tax")
    private BigDecimal unitPriceIncludingTax;
    /**
     * 含税总金额
     */
    @TableField("total_amount_including_tax")
    private BigDecimal totalAmountIncludingTax;
    /**
     * 不含税报价单价(吨价，平方价，千平方英寸)
     */
    @TableField("quotation_unit_price_without_tax")
    private BigDecimal quotationUnitPriceWithoutTax;
    /**
     * 不含税单价(默认隐藏，根据计费方案转换计算得出)
     */
    @TableField("unit_price_without_tax")
    private BigDecimal unitPriceWithoutTax;
    /**
     * 不含税总金额
     */
    @TableField("total_amount_without_tax")
    private BigDecimal totalAmountWithoutTax;
    /**
     * 本币单价(根据币种，外币才显示)，外币不含税
     */
    @TableField("local_currency_unit_price")
    private BigDecimal localCurrencyUnitPrice;
    /**
     * 本币金额(根据币种，外币才显示)，外币不含税
     */
    @TableField("local_currency_total_amount")
    private BigDecimal localCurrencyTotalAmount;
    /**
     * 结算单价（默认与含税单价相等），写入库存价格字段
     */
    @TableField("settlement_unit_price")
    private BigDecimal settlementUnitPrice;
    /**
     * 结算金额（默认与含税总金额相等），写入库存价格字段
     */
    @TableField("settlement_total_amount")
    private BigDecimal settlementTotalAmount;
    /**
     * 外部单号(例客户PO，采购送货单号)
     */
    @TableField("external_tracking_number")
    private String externalTrackingNumber;
    /**
     * 外部物料条码(BarCode)
     */
    @TableField("external_material_bar_code")
    private String externalMaterialBarCode;
    /**
     * 外部物料编码(例客户产品编码)
     */
    @TableField("external_material_code")
    private String externalMaterialCode;
    /**
     * 外部物料名称(例客户产品名称)
     */
    @TableField("external_material_name")
    private String externalMaterialName;
    /**
     * 货期
     */
    @TableField("delivery_date")
    private LocalDateTime deliveryDate;
    /**
     * 送货类型
     */
    @TableField("delivery_type_guid")
    private String deliveryTypeGuid;
    /**
     * 完成状态(0未完成，1部分完成，2已完成，3终止)
     */
    @TableField("completion_status")
    private String completionStatus;
    /**
     * 完成数量(后面流程将其更新)
     */
    @TableField("completion_quantity")
    private BigDecimal completionQuantity;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 扩展字段
     */
    @TableField(value = "to_json", typeHandler  = JsonbTypeHandler.class)
    private Object toJson;
    /**
     * 来源guid
     */
    @TableField("source_guid")
    private String sourceGuid;
    /**
     * 来源值
     */
    @TableField("source_value")
    private Integer sourceValue;
    /**
     * 报价数量
     */
    @TableField("quotation_quantity")
    private BigDecimal quotationQuantity;
    /**
     * 方案id
     */
    @TableField("cost_scheme_guid")
    private String costSchemeGuid;
    /**
     * 父节点_guid
     */
    @TableField("parent_classification_guid")
    private String parentClassificationGuid;
    /**
     * 本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)
     */
    @TableField("order_state")
    private String orderState;
    /**
     * 预计交期(可变更的)
     */
    @TableField("expected_receipt_date")
    private LocalDateTime expectedReceiptDate;

    @TableField(exist = false)
    private ErpBusinessMgtPurchaseOrderDataExtend orderDataExtend;
}
