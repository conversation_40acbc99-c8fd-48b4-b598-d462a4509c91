package xy.server.purchase.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.purchase.entity.model.ro.ErpOutgoingApplicationFormRO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 外发申请单VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpOutgoingApplicationFormVO对象", description = "外发申请")
public class ErpOutgoingApplicationFormVO {

    @ApiModelProperty(value = "外发状态(0未外发，1部分外发，2全部外发，3终止外发)")
    private String workorderState;

    @XyTrans(dictionaryKey = "OUTGOINGSTATUS", dictionaryValue = "workorderState")
    @ApiModelProperty(value = "外发状态(0未外发，1部分外发，2全部外发，3终止外发)")
    private String workorderStateName;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @ApiModelProperty(value = "工单表_guid")
    private String workorderGuid;

    @ApiModelProperty(value = "申请类型")
    private String workorderTypeGuid;

    @XyTrans(dictionaryKey = "OUTGOINGAPPLICATIONTYPE", dictionaryValue = "workorderTypeGuid")
    @ApiModelProperty(value = "申请类型")
    private String workorderTypeName;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态字典值")
    @XyTrans(dictionaryKey = "AUDIT_STATUS", dictionaryValue = "auditStatus")
    private String auditStatusDictValue;

    @XyTransCycle
    private List<ErpOutgoingApplicationFormRO> outgoingApplicationList;
}
