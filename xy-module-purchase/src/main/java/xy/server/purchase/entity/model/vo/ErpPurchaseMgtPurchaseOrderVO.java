package xy.server.purchase.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 采购订单VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpPurchaseMgtPurchaseOrderVO对象", description = "采购订单")
public class ErpPurchaseMgtPurchaseOrderVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String orderGuid;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "订单需求描述")
    private String orderDemandGuid;

    @ApiModelProperty(value = "单据日期(可变更的)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "采购类型")
    private String orderType;

    @ApiModelProperty(value = "采购类型字典值")
    @XyTrans(dictionaryKey = "PURCHASE_TYPE", dictionaryValue = "orderType")
    private String orderTypeDictValue;

    @ApiModelProperty(value = "总费用(含税)")
    private BigDecimal totalExpensesIncludingTax;

    @ApiModelProperty(value = "总费用(不含税)")
    private BigDecimal totalExpensesWithoutTax;

    @ApiModelProperty(value = "本币总费用(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalExpenses;

    @ApiModelProperty(value = "结算费用（默认与含税总金额相等)")
    private BigDecimal settlementTotalExpenses;

    @ApiModelProperty(value = "总体积")
    private BigDecimal totalVolume;

    @ApiModelProperty(value = "总重量")
    private BigDecimal totalWeight;

    @ApiModelProperty(value = "保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)")
    private Integer decimalMethod;

    @ApiModelProperty(value = "结算单价保留小数位(默认9位，可手动变更)")
    private Integer settlementUnitPriceKeepDecimalPlace;

    @ApiModelProperty(value = "结算金额保留小数位(默认2位，可手动变更)")
    private Integer settlementTotalAmountKeepDecimalPlace;

    //@ApiModelProperty(value = "订单属性(1业务合同，2采购订单，3外发订单)")
    //private Integer orderProperties;

    @ApiModelProperty(value = "税率guid")
    private String invoiceTypeTaxRateGuid;

    @ApiModelProperty(value = "税率名称")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "汇率guid")
    private String currencyExchangeRateGuid;

    @ApiModelProperty(value = "汇率名称")
    private String exchangeRate;

    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    @ApiModelProperty(value = "是否本币")
    private Boolean isLocalCurrency;

    @ApiModelProperty(value = "是否加急")
    private Boolean isUrgent;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @ApiModelProperty(value = "客户/供应商guid")
    private String customerOrSupplierGuid;

    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @ApiModelProperty(value = "电话")
    private String mobilephone;

    @ApiModelProperty(value = "结算客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String settlementCustomerOrSupplierGuid;

    @ApiModelProperty(value = "送货客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String deliveryCustomerOrSupplierGuid;

    @ApiModelProperty(value = "送货类型")
    private String deliveryTypeGuid;

    @ApiModelProperty(value = "结算类型")
    private String settlementTypeGuid;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核状态字典值")
    @XyTrans(dictionaryKey = "AUDIT_STATUS", dictionaryValue = "auditStatus")
    private String auditStatusDictValue;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditDate;


    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "到货情况")
    private String arrivalState;

    @ApiModelProperty(value = "到货情况字典值")
    @XyTrans(dictionaryKey = "ARRIVAL_STATE", dictionaryValue = "arrivalState")
    private String arrivalStateDictValue;

    @ApiModelProperty(value = "供应商Obj")
    @XyTransCycle
    private ErpSupplierMgtSupplierVO supplierObj;

    @ApiModelProperty(value = "结算供应商Obj")
    @XyTransCycle
    private ErpSupplierMgtSupplierVO settlementSupplierObj;

    @ApiModelProperty(value = "采购明细列表")
    @XyTransCycle
    private List<ErpPurchaseMgtPurchaseOrderDataVO> detailList;

    @ApiModelProperty(value = "采购订单文件列表")
    private List<ErpPurchaseMgtPurchaseOrderFileVO> fileList;

}
