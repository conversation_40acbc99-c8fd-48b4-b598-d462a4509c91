package xy.server.purchase.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.basic.entity.model.vo.ErpBusinessMgtOtherExpensesVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单数据异常处理表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpBusinessMgtPurchaseOrderDataExtendVO对象", description = "订单数据异常处理表")
public class ErpReconciliationFindVO {

    @ApiModelProperty(value = "到货id")
    private String workOrderGuid;
    @ApiModelProperty(value = "实际到货数")
    private BigDecimal actQuantity;
    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRate;
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;
    @ApiModelProperty(value = "供应商编码简称")
    private String supplierShortName;
    @ApiModelProperty(value = "供应商编码全称")
    private String supplierFullName;

    @ApiModelProperty(value = "对账情况")
    private String statementSituation;

    @XyTrans(dictionaryKey = "RECONCILIATION",dictionaryValue = "statementSituation")
    @ApiModelProperty(value = "对账情况名称")
    private String statementStatusName;

    @ApiModelProperty(value = "到货单号")
    private String workorderNumber;

    @ApiModelProperty(value = "到货状态")
    private String completionStatus;
    @ApiModelProperty(value = "到货状态")
    private String completionStatusName;

    @ApiModelProperty(value = "单据状态")
    private String currentStatus;
    @ApiModelProperty(value = "单据状态 (0-新建，1-正常处理，2-正常结束，3-异常结束)\")")
    @XyTrans(dictionaryKey = "DOCUMENT_STATUS",dictionaryValue = "currentStatus")
    private String currentStatusName;

    @ApiModelProperty(value = "外发单号")
    private String orderNumber;
    @ApiModelProperty(value = "订单|工单")
    private String orderDataNumber;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "交货日期")
    private LocalDateTime receiptDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "返回日期")
    private LocalDateTime returnDate;
    @ApiModelProperty(value = "业务员名称")
    private String salesmanName;
    @ApiModelProperty(value = "开单日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;
    @ApiModelProperty(value = "产品编码")
    private String productionCode;
    @ApiModelProperty(value = "产品名称")
    private String productionName;
    @ApiModelProperty(value = "加工规格")
    private String specifications;
    @ApiModelProperty(value = "外发总数量")
    private BigDecimal totalQuantity;
    @ApiModelProperty(value = "返回数量")
    private BigDecimal returnQuantity;
    @ApiModelProperty(value = "单位")
    private String unitGuid;
    @ApiModelProperty("制单人")
    public String creatorName;
    @ApiModelProperty(value = "备注或描述")
    private String description;
    @ApiModelProperty("交期天数")
    private Integer expectedReceiptDay;
    @ApiModelProperty("实际天数")
    private Integer actualDay;
    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;
    @ApiModelProperty(value = "金额")
    private BigDecimal totalAmount;
    @ApiModelProperty(value = "其他费用")
    List<ErpBusinessMgtOtherExpensesVO> otherExpensesList;
    @ApiModelProperty(value = "其他费用")
    private BigDecimal otherExpenses = new BigDecimal(0);
    @ApiModelProperty(value = "结算金额")
    private BigDecimal settlementTotalAmount = new BigDecimal(0);
    @ApiModelProperty("本币单价")
    private BigDecimal localCurrencyUnitPrice;
    @ApiModelProperty("本币金额")
    private BigDecimal localCurrencyTotalAmount;
    @ApiModelProperty(value = "本币结算金额")
    private BigDecimal localSettlementTotalAmount;

    @ApiModelProperty(value = "订单明细Guid")
    private String orderDataGuid;

    @ApiModelProperty(value = "到货明细id")
    private String  workOrderDateGuid;

    @ApiModelProperty(value = "订单创建人")
    private String orderCreatorName;

}
