package xy.server.purchase.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xunyue.config.mybatisplus.BaseEntity;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2023/9/13 11:06
 * @apiNote 采购申请单VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpPurchaseRequisitionVO对象", description = "采购申请单VO")
public class ErpPurchaseMgtPurchaseRequisitionVO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "申请类型")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "申请类型（字典值）")
    @XyTrans(dictionaryKey = "APPLICATION_TYPE", dictionaryValue = "workorderTypeGuid")
    private String workorderTypeDictValue;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "来源")
    @XyTrans(dictionaryKey = "PURCHASING_SOURCE", dictionaryValue = "sourceValue")
    private String sourceDictValue;

    @ApiModelProperty(value = "工单状态(采购情况)")
    private String workorderState;

    @ApiModelProperty(value = "工单状态(采购情况)")
    @XyTrans(dictionaryKey = "PURCHASE_STATE", dictionaryValue = "workorderState")
    private String workorderStateDictValue;

    //@ApiModelProperty(value = "工单属性")
    //private Integer workorderProperties;

    @ApiModelProperty(value = "紧急程度(非常紧急，紧急，一般)")
    private String isUrgent;

    @ApiModelProperty(value = "物料guid")
    private String materialGuid;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "总数量数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "单位guid")
    private String unitGuid;

    @ApiModelProperty(value = "要求交货期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime requiredDeliveryTime;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核状态字典值")
    @XyTrans(dictionaryKey = "AUDIT_STATUS", dictionaryValue = "auditStatus")
    private String auditStatusDictValue;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    /**
     * 关联表数据
     */

    @ApiModelProperty(value = "待采购数")
    private BigDecimal notBilledQuantity;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(value = "来源数量")
    private BigDecimal sourceQuantity;

    @ApiModelProperty(value = "物料Obj")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "采购申请单明细")
    @XyTransCycle
    private List<ErpPurchaseMgtPurchaseRequisitionVO> detailList;

    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String currentStatus;

    @ApiModelProperty(value = "客户guid")
    private String customerGuid;

    @ApiModelProperty(value = "客户简称")
    private String customerShortName;

    @ApiModelProperty(value = "产品名")
    private String productName;

    @XyTrans(dictionaryKey = "FINANCE_MGT_STATE",dictionaryValue = "currentStatus")
    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String currentStatusName;

    @ApiModelProperty(value = "外部单号(例客户PO，采购送货单号)")
    private String externalTrackingNumber;

    @ApiModelProperty(value = "外部物料条码(BarCode)")
    private String externalMaterialBarCode;

    @ApiModelProperty(value = "外部物料编码(例客户产品编码)")
    private String externalMaterialCode;

    @ApiModelProperty(value = "外部物料名称(例客户产品名称)")
    private String externalMaterialName;

    // 临时用，不返回前端
    @JsonIgnore
    private BigDecimal billedQuantity;
}
