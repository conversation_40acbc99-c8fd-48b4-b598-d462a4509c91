package xy.server.purchase.entity.model.ro;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xunyue.tenant.history.SystemFieldHistoryCycle;
import com.xunyue.config.excel.annotation.XyExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelTemplate;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.purchase.entity.ErpSupplierMgtSupplierContact;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 供应商RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
@XyExcelTemplate("供应商资料模板")
@ApiModel(value = "ErpSupplierMgtSupplierURO对象", description = "供应商")
public class ErpSupplierMgtSupplierRO extends BaseEntity {

    @ApiModelProperty(value = "PK ")
    private String supplierGuid;

    @ExcelProperty("供应商编码")
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ExcelProperty("供应商简称")
    @NotBlank(message = "供应商简称不能为空")
    @ApiModelProperty(value = "供应商简称")
    private String supplierShortName;

    @ExcelProperty("供应商全称")
    @ApiModelProperty(value = "供应商全称")
    private String supplierFullName;

    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("供应商分类名称")
    @ApiModelProperty(value = "供应商分类_guid")
    private String supplierClassificationGuid;


    @NotBlank(message = "供应商级别不能为空")
    @ExcelProperty("供应商级别名称")
    @ApiModelProperty(value = "供应商级别_guid")
    private String supplierLevelGuid;

    @NotBlank(message = "默认币种不能为空")
    @ExcelProperty("币种")
    @ApiModelProperty(value = "默认币种 Currency _guid ")
    private String defaultCurrencyGuid;

    @ExcelProperty("发票类型")
    @ApiModelProperty(value = "默认发票类型")
    private String defaultInvoiceTypeName;

    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("发票税率")
    @ApiModelProperty(value = "默认发票类型税率 InvoiceTypeTaxRate_guid")
    private String defaultInvoiceTypeTaxRateGuid;

    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("送货类型")
    @ApiModelProperty(value = "默认送货类型 DeliveryType_guid")
    private String defaultDeliveryTypeGuid;

    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("结算类型")
    @ApiModelProperty(value = "默认结算类型 SettlementType_guid")
    private String defaultSettlementTypeGuid;

//    @NotNull(message = "电话不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("电话")
    @ApiModelProperty(value = "电话")
    private String telephone;

//    @NotNull(message = "手机不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("手机")
    @ApiModelProperty(value = "手机")
    private String mobilephone;

//    @NotNull(message = "传真不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("传真")
    @ApiModelProperty(value = "传真")
    private String fax;

    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("邮件")
    @ApiModelProperty(value = "邮件")
    private String email;

//    @NotNull(message = "开户行不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("开户行")
    @ApiModelProperty(value = "开户行")
    private String openingBank;

//    @NotNull(message = "银行帐号不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("银行帐号")
    @ApiModelProperty(value = "银行帐号")
    private String bankAccount;

//    @NotNull(message = "企业税号不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("企业税号")
    @ApiModelProperty(value = "企业税号")
    private String enterpriseTaxIdentificationNumber;

    @NotNull(message = "账期不能为空")
    @XyExcelProperty(isUseDefaultValue = true, intDefaultValue = 1)
    @ExcelProperty("结账天数")
    @ApiModelProperty(value = "账期")
    private Integer accountPeriod;

    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("结账日")
    @ApiModelProperty(value = "结账日")
    private String reconciliationDate;

    @ExcelProperty("父级供应商编码")
    @ApiModelProperty(value = "父级供应商id")
    private String parentSupplierGuid;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "已被使用")
    private Boolean isUsed;

    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("备注")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "是否自动编码")
    private Boolean isAutomaticCode;

    @ApiModelProperty(value = "供应商文件列表")
    @Valid
    private List<ErpSupplierMgtSupplierFileRO> supplierFileROList;
    @ApiModelProperty(value = "删除的供应商文件guid列表")
    private List<String> delSupplierFileGuids;

    @ApiModelProperty(value = "供应商地址列表")
    @Valid
    private List<ErpSupplierMgtSupplierAddressRO> supplierAddressROList;
    @ApiModelProperty(value = "删除的供应商地址guid列表")
    private List<String> delSupplierAddressGuids;

    @ApiModelProperty(value = "供应商联系人列表")
    @Valid
    @SystemFieldHistoryCycle(targetEntity = ErpSupplierMgtSupplierContact.class)
    private List<ErpSupplierMgtSupplierContactRO> supplierContactROList;
    @ApiModelProperty(value = "删除的供应商联系人guid列表")
    private List<String> delSupplierContactGuids;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核时间")
    private Date auditDate;

}
