package xy.server.purchase.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.basic.entity.model.vo.ErpBasicMgtCurrencyVO;
import xy.server.basic.entity.model.vo.ErpBasicMgtDeliveryTypeVO;
import xy.server.basic.entity.model.vo.ErpBasicMgtInvoiceTypeTaxRateVO;
import xy.server.basic.entity.model.vo.ErpBasicMgtSettlementTypeVO;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 供应商VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpSupplierMgtSupplierVO对象", description = "供应商")
public class ErpSupplierMgtSupplierVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String supplierGuid;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商简称")
    private String supplierShortName;

    @ApiModelProperty(value = "供应商全称")
    private String supplierFullName;

    @ApiModelProperty(value = "供应商分类_guid")
    private String supplierClassificationGuid;

    @ApiModelProperty(value = "供应商级别_guid")
    private String supplierLevelGuid;

    @ApiModelProperty(value = "电话")
    private String telephone;

    @ApiModelProperty(value = "手机")
    private String mobilephone;

    @ApiModelProperty(value = "传真")
    private String fax;

    @ApiModelProperty(value = "邮件")
    private String email;

    @ApiModelProperty(value = "开户行")
    private String openingBank;

    @ApiModelProperty(value = "银行帐号")
    private String bankAccount;

    @ApiModelProperty(value = "企业税号")
    private String enterpriseTaxIdentificationNumber;

    @ApiModelProperty(value = "默认币种 Currency _guid ")
    private String defaultCurrencyGuid;

    @ApiModelProperty(value = "默认发票类型税率 InvoiceTypeTaxRate_guid")
    private String defaultInvoiceTypeTaxRateGuid;

    @ApiModelProperty(value = "默认送货类型 DeliveryType_guid")
    private String defaultDeliveryTypeGuid;

    @ApiModelProperty(value = "默认结算类型 SettlementType_guid")
    private String defaultSettlementTypeGuid;

    @ApiModelProperty(value = "账期")
    private Integer accountPeriod;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "已被使用")
    private Boolean isUsed;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "供应商分类")
    private ErpSupplierMgtSupplierClassificationVO supplierClassificationVO;

    @ApiModelProperty(value = "供应商级别")
    private ErpSupplierMgtSupplierLevelVO supplierLevelVO;

    @ApiModelProperty(value = "默认币种")
    private ErpBasicMgtCurrencyVO defaultCurrencyVO;

    @ApiModelProperty(value = "默认发票类型税率")
    private ErpBasicMgtInvoiceTypeTaxRateVO defaultInvoiceTypeTaxRateVO;

    @ApiModelProperty(value = "默认送货类型")
    private ErpBasicMgtDeliveryTypeVO defaultDeliveryTypeVO;

    @ApiModelProperty(value = "默认结算类型")
    private ErpBasicMgtSettlementTypeVO defaultSettlementTypeVO;


    @ApiModelProperty(value = "供应商文件列表")
    private List<ErpSupplierMgtSupplierFileVO> supplierFileVOList;

    @ApiModelProperty(value = "供应商地址列表")
    private List<ErpSupplierMgtSupplierAddressVO> supplierAddressVOList;

    @ApiModelProperty(value = "供应商联系人列表")
    private List<ErpSupplierMgtSupplierContactVO> supplierContactVOList;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核状态字典值")
    @XyTrans(dictionaryKey = "AUDIT_STATUS", dictionaryValue = "auditStatus")
    private String auditStatusDictValue;

    @ApiModelProperty(value = "审核时间")
    private Date auditDate;

    @ApiModelProperty(value = "结账日")
    private String reconciliationDate;

    @XyTrans(dictionaryKey = "ACCOUNT_DAY", dictionaryValue = "reconciliationDate")
    @ApiModelProperty(value = "结账日")
    private String ReconciliationDateName;

    @ApiModelProperty(value = "父级供应商id")
    private String parentSupplierGuid;

}
