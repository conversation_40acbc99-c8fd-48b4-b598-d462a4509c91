package xy.server.purchase.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 外发申请单QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpProcessOutgoingOrderQO对象", description = "")
public class ErpPurchaseProcessOutgoingOrderQO {

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "部件名称")
    private String partName;

    /**
     * 根据工序类型id获取对应的名称
     */
    @ApiModelProperty(value = "工序")
    private String processName;

    @ApiModelProperty(value = "开单开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDateTime endTime;

}
