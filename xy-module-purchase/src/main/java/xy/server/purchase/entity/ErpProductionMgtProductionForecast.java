package xy.server.purchase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 采购预测
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-10
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_production_mgt_workorder", autoResultMap = true)
public class ErpProductionMgtProductionForecast extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("workorder_guid")
    private String workorderGuid;
    /**
     * 工单号
     */
    @TableField("workorder_number")
    private String workorderNumber;
    /**
     * 顺序号(每一个层级有对应的顺序号)
     */
    @TableField("serial_number")
    private Integer serialNumber;
    /**
     * 父节点_guid
     */
    @TableField("parent_classification_guid")
    private String parentClassificationGuid;
    /**
     * 打印次数
     */
    @TableField("print_times")
    private Integer printTimes;
    /**
     * 单据日期
     */
    @TableField("receipt_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptDate;
    /**
     * 工单类型_guid
     */
    @TableField("workorder_type_guid")
    private String workorderTypeGuid;
    /**
     * 来源guid
     */
    @TableField("source_guid")
    private String sourceGuid;
    /**
     * 来源值
     */
    @TableField("source_value")
    private Integer sourceValue;
    /**
     * 工单状态(完成、故障、未开始)
     */
    @TableField("workorder_state")
    private String workorderState;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 流程实例id
     */
    @TableField("process_instance_id")
    private String processInstanceId;
    /**
     * 审核状态
     */
    @TableField("audit_status")
    private String auditStatus;
    /**
     * 审核时间
     */
    @TableField("audit_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditDate;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 工单属性(1工单，2工序工单，3采购工单，4外发工单，5车间工单，6作业工单，维修工单，保养工单)
     */
    @TableField("workorder_properties")
    private Integer workorderProperties;
    /**
     * 客户/供应商guid
     */
    @TableField("customer_guid")
    private String customerGuid;
    /**
     * 物料分类(成品)
     */
    @TableField("material_guid")
    private String materialGuid;
    /**
     * 数量(根据工单属性:1工单成品数，2工序投入数量，3采购申请数量，4外发申请数)
     */
    @TableField("quantity")
    private BigDecimal quantity;
    /**
     * 使用库存数量
     */
    @TableField("using_inventory_quantity")
    private BigDecimal usingInventoryQuantity;
    /**
     * 总数量
     */
    @TableField("total_quantity")
    private BigDecimal totalQuantity;
    /**
     * 要求交货期
     */
    @TableField("required_delivery_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime requiredDeliveryTime;
    /**
     * 扩展字段
     */
    @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
    private Object toJson;

}
