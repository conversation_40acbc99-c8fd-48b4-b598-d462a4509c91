package xy.server.purchase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 供应商文件
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_supplier_mgt_supplier_file",autoResultMap = true)
public class ErpSupplierMgtSupplierFile extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("supplier_file_guid")
    private String supplierFileGuid;
    /**
     * erp_supplier_mgt_Supplier 供应商_guid
     */
    @TableField("supplier_guid")
    private String supplierGuid;
    /**
     * 文件类型1是图片2是文件
     */
    @TableField("file_type")
    private Integer fileType;
    /**
     * 文件 _guid
     */
    @TableField("file_guid")
    private String fileGuid;
    /**
     * 顺序号
     */
    @TableField("serial_number")
    private Integer serialNumber;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;

    @TableField(value = "to_json",typeHandler  = JsonbTypeHandler.class)
    private Object toJson;


}
