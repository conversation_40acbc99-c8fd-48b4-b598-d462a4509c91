package xy.server.purchase.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 外发申请单RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpOutgoingApplicationFormRO对象", description = "外发申请")
public class ErpOutgoingMgtApplicationRO extends BaseEntity {

    @ApiModelProperty(value = "工单表_guid")
    private String workorderGuid;

    @NotBlank(message = "申请类型不能为空")
    @ApiModelProperty(value = "申请类型")
    private String workorderTypeGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

     private List<ErpOutgoingApplicationFormRO> outgoingApplicationList;

}
