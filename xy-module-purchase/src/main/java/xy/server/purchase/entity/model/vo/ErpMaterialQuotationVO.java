package xy.server.purchase.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.xunyue.config.mybatisplus.BaseEntity;

import java.math.BigDecimal;


@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpMaterialQuotationVO")
public class ErpMaterialQuotationVO  {

    @ApiModelProperty(value = "")
    private String experienceProductionProcessExternalGuid;

    @ApiModelProperty(value = "产品bom物料Guid")
    private String materialBomGuid;

    @ApiModelProperty(value = "物料Guid")
    private String materialGuid;

    @ApiModelProperty(value = "含税报价单价")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "物料name")
    private String materialName;
    @ApiModelProperty(value = "物料Code")
    private String materialCode;

}
