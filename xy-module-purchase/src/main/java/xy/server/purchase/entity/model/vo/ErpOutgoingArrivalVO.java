package xy.server.purchase.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
* <p>
* 工单表RO
* </p>
*
* <AUTHOR>
* @since 2023-09-15
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpOutgoingArrivalVO对象", description = "工单表")
public class ErpOutgoingArrivalVO {

    @ApiModelProperty(value = "外发订单的工单id")
    private String workorderGuid;

    @ApiModelProperty(value = "外发到货单主表id")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "外发到货单数据表id")
    private String sonWorkOrderGuid;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "外发单号")
    private String orderNumber;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "来源的工序单号")
    private String sourceProcessNumber;

    @ApiModelProperty(value = "工序工单的工单guid")
    private String workOrderProcessGuid;

    @ApiModelProperty(value = "外发订单数id")
    private String orderDataGuid;
    /**
     * 根据物料id查询获取到的印件名称，
     * 以及部件名称，
     * 部件名称是当前id对于的物料名称，印件名称是他的上级物料id获得的
     */
    @ApiModelProperty(value = "物料分类(成品)")
    private String materialGuid;
    /**
     * 是物料分类对应物料的名称
     */
    @ApiModelProperty(value = "部件名称")
    private String partName;

    @ApiModelProperty(value = "产品物料id")
    public String productMaterialGuid;

    @ApiModelProperty(value = "产品名称")
    private String productName;
    /**
     * 根据工序类型id获取到，工序这个字段的名称
     * 根据工序id能够一对多获取到工序项的名称，根据工序项的id获取到工序参数的值然后拼接起来，得到工序参数字段
     */
    @ApiModelProperty(value = "工序类型guid")
    private String productionProcessesTypeGuid;
    /**
     * 根据工序类型id获取对应的名称
     */
    @ApiModelProperty(value = "工序")
    private String processName;
    /**
     * 根据工序类型id查询对应的参数项集合，然后组合相关的参数值组合起来
     */
    @ApiModelProperty(value = "工序参数")
    private String processParameters;
    /**
     * 收货数量
     */
    @ApiModelProperty(value = "到货数量")
    private BigDecimal quantity;

    /**
     * 要求交期
     */
    @ApiModelProperty(value = "外发订单的要求交期")
    private LocalDateTime requiredDeliveryTime;


    @ApiModelProperty(value = "外发订单描述")
    private String description;

    @ApiModelProperty(value = "来源数量")
    private BigDecimal sourceQuantity;

    @ApiModelProperty(value = "实际产品物料id")
    public String actMaterialGuid;

    @ApiModelProperty(value = "实际产品名称")
    private String actMaterialName;

    @XyTransCycle
    @ApiModelProperty(value = "产出工单数据")
    private List<ErpOutgoingApplicationVO> sonList;
}
