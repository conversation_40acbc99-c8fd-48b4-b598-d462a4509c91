package xy.server.purchase.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 采购预测 VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpProductionMgtProductionForecastVO", description = "采购预测 VO")
public class ErpProductionMgtProductionForecastVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "工单类型_guid")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "工单状态(完成、故障、未开始)")
    private String workorderState;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核状态字典值")
    @XyTrans(dictionaryKey = "AUDIT_STATUS", dictionaryValue = "auditStatus")
    private String auditStatusDictValue;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "工单属性(1工单，2工序工单，3采购工单，4外发工单，5车间工单，6作业工单，维修工单，保养工单)")
    private Integer workorderProperties;

    @ApiModelProperty(value = "物料Obj")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "客户/供应商guid")
    private String customerGuid;

    @ApiModelProperty(value = "物料分类(成品)")
    private String materialGuid;

    @ApiModelProperty(value = "数量(根据工单属性:1工单成品数，2工序投入数量，3采购申请数量，4外发申请数)")
    private BigDecimal quantity;

    @ApiModelProperty(value = "总数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "当前物库存数")
    private BigDecimal inventoryQuantity;

    @ApiModelProperty(value = "当前类型全部库存数")
    private BigDecimal inventoryTotalQuantity;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "要求交货期")
    private LocalDateTime requiredDeliveryTime;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    /**
     * 关联数据
     */
    @ApiModelProperty(value = "客户名称")
    private String customerShortName;

    @ApiModelProperty(value = "产品系列id")
    private String materialSeriesGuid;

    @ApiModelProperty(value = "产品系列name")
    private String materialSeriesName;

    @ApiModelProperty(value = "产品资料guid")
    private String experienceProductionProcessGuid;

    @ApiModelProperty(value = "产品规格")
    private String productSpecification;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料规格")
    private String materialSpecification;

    @ApiModelProperty(value = "搜索码")
    private String searchCode;

    @ApiModelProperty(name = "产品数量")
    private BigDecimal productQuantity;

    @ApiModelProperty(name = "物料使用量")
    private BigDecimal materialUsageQuantity;

    @ApiModelProperty(value = "产品单位")
    private String productUnitName;

    @ApiModelProperty(value = "物料单位")
    private String materialUnitName;

    @ApiModelProperty(value = "明细列表")
    private List<ErpProductionMgtProductionForecastVO> detailList;

    @ApiModelProperty(value = "来源明细列表")
    private List<ErpProductionMgtProductionForecastVO> sourceDetailList;

}
