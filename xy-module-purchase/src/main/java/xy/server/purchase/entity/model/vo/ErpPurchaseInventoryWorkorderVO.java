package xy.server.purchase.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2023/10/10 14:52
 * @apiNote 采购-到货工单
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpPurchaseInventoryWorkorderVO对象", description = "采购-到货工单VO")
public class ErpPurchaseInventoryWorkorderVO {
    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "客户/供应商guid")
    private String customerGuid;

    @ApiModelProperty(value = "客户/供应商guid")
    private String customerOrSupplierName;

    @ApiModelProperty(value = "结算客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String settlementCustomerOrSupplierGuid;

    @ApiModelProperty(value = "结算客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String settlementCustomerOrSupplierName;

    @ApiModelProperty(value = "要求交货期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime requiredDeliveryTime;

    @ApiModelProperty(value = "采购-到货工单明细List")
    @XyTransCycle
    private List<ErpPurchaseInventoryWorkorderDetailVO> detailList;
}
