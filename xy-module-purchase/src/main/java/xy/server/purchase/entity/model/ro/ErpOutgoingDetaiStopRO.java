package xy.server.purchase.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 装舱明细表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpOutgoingDetaiStopRO对象", description = "")
public class ErpOutgoingDetaiStopRO {

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "状态")
    private String currentStatus;

    @ApiModelProperty(value = "PK")
    private String orderDataGuid;

}