package xy.server.purchase.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* <p>
* 物料报价明细表VO
* </p>
*
* <AUTHOR>
* @since 2024-05-23
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpMaterialMgtMaterialQuotationVO对象", description = "物料报价明细表")
public class ErpMaterialMgtMaterialQuotationVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String materialQuotationReceiptDetailGuid;

    @ApiModelProperty(value = "报价guid")
    private String materialQuotationReceiptGuid;

    @ApiModelProperty(value = "物料分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料分类")
    private String materialClassificationName;

    @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

    @ApiModelProperty(value = "最小数量")
    private BigDecimal minQuantity;

    @ApiModelProperty(value = "最大数量")
    private BigDecimal maxQuantity;

    @ApiModelProperty(value = "含税报价单价")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "物料Obj")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "客户或者供应商货号")
    private String externalMaterialCode;

    @ApiModelProperty(value = "调整幅度")
    private BigDecimal adjustmentMagnitude;

    @ApiModelProperty(value = "原含税报价单价")
    private BigDecimal oldQuotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "报价类型(字典)")
    private String materialQuotationReceiptType;

    @XyTrans(dictionaryKey = "QUOTATION_TYPE",dictionaryValue = "materialQuotationReceiptType")
    @ApiModelProperty(value = "报价类型(字典)")
    private String materialQuotationReceiptTypeName;

    @ApiModelProperty(value = "物料报价单号")
    private String materialQuotationReceiptNumbers;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "客户guid or 供应商guid")
    private String customerOrSupplierGuid;

    @ApiModelProperty(value = "客户or供应商name")
    private String customerOrSupplierName;

    @ApiModelProperty(value = "客户or供应商code")
    private String customerOrSupplierCode;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生效时间")
    private LocalDateTime effectiveDate;

    @ApiModelProperty(value = "客户物料报价1，供应商物料报价2")
    private Integer materialQuotationReceiptProperties;

    @ApiModelProperty(value = "生效状态")
    private Boolean effectiveStatus;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    /**
     * 真实生效时间
     */
    @ApiModelProperty(value = "真实生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime realEffectiveDate;

}
