package xy.server.purchase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 工单数据表工序工单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("erp_production_mgt_workorder_data_process")
public class ErpOutgoingDataProcess extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("workorder_data_process_guid")
    private String workorderDataProcessGuid;
    /**x
     * 工单表_guid
     */
    @TableField("workorder_guid")
    private String workorderGuid;
    /**
     * 损耗数量(根据工单属性:2是工序工单损耗数)
     */
    @TableField("loss_quantity")
    private BigDecimal lossQuantity;
    /**
     * 调整损耗数量(根据工单属性:2是工序工单调整损耗数)
     */
    @TableField("adjust_loss_quantity")
    private BigDecimal adjustLossQuantity;
    /**
     * 产出数量
     */
    @TableField("output_quantity")
    private BigDecimal outputQuantity;
    /**
     * 产出单位
     */
    @TableField("output_unit_guid")
    private String outputUnitGuid;
    /**
     * 产出规格长(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @TableField("output_specifications_length")
    private BigDecimal outputSpecificationsLength;
    /**
     * 产出规格宽(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @TableField("output_specifications_width")
    private BigDecimal outputSpecificationsWidth;
    /**
     * 产出规格高(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @TableField("output_specifications_height")
    private BigDecimal outputSpecificationsHeight;
    /**
     * 是否排程(根据工序是否默认)
     */
    @TableField("is_production_scheduling")
    private Boolean isProductionScheduling;
    /**
     * 排程状态(0未排程，1部分排程，2全部排程，3终止排程)
     */
    @TableField("production_scheduling_state")
    private String productionSchedulingState;
    /**
     * 已排程数量
     */
    @TableField("production_scheduled_quantity")
    private BigDecimal productionScheduledQuantity;
    /**
     * 是否外发(根据工序是否默认)
     */
    @TableField("is_outsource")
    private Boolean isOutsource;
    /**
     * 外发申请状态(0未外发申请，1部分外发申请，2全部外发申请，3终止外发申请)
     */
    @TableField("outsource_requisition_state")
    private String outsourceRequisitionState;
    /**
     * 外发状态(0未外发，1部分外发，2全部外发，3终止外发)
     */
    @TableField("outsource_state")
    private String outsourceState;
    /**
     * 生产状态(0未生产，1部分生产，2全部生产，3暂停)
     */
    @TableField("produced_state")
    private String producedState;
    /**
     * 已生产数量
     */
    @TableField("produced_quantity")
    private BigDecimal producedQuantity;
    /**
     * 已外发申请数量
     */
    @TableField("outsourced_requisition_quantity")
    private BigDecimal outsourcedRequisitionQuantity;
    /**
     * 已外发数量
     */
    @TableField("outsourced_quantity")
    private BigDecimal outsourcedQuantity;
    /**
     * 设备型号guid
     */
    @TableField("equipment_model_guid")
    private String equipmentModelGuid;
    /**
     * 模具型号guid
     */
    @TableField("mold_model_guid")
    private String moldModelGuid;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 来源guid
     */
    @TableField("source_guid")
    private String sourceGuid;
    /**
     * 来源值
     */
    @TableField("source_value")
    private Integer sourceValue;


}
