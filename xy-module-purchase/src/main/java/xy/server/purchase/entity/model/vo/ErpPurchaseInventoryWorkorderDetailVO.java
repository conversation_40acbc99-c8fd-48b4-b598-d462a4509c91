package xy.server.purchase.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.basic.entity.model.vo.ErpBusinessMgtOtherExpensesVO;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseOrderData;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2023/10/10 13:49
 * @apiNote 采购-到货工单明细
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpPurchaseInventoryWorkorderDetailVO对象", description = "采购-到货工单明细VO")
public class ErpPurchaseInventoryWorkorderDetailVO {

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "来源字典值")
    @XyTrans(dictionaryKey = "MATERIAL_RECEIPT_SOURCE_TYPE", dictionaryValue = "sourceValue")
    private String sourceDictValue;

    @ApiModelProperty(value = "物料guid")
    private String materialGuid;

    @ApiModelProperty(value = "物料Name")
    private String materialName;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "总数量数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "单位guid")
    private String unitGuid;

    @ApiModelProperty(value = "要求交货期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime requiredDeliveryTime;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "客户/供应商guid")
    private String customerGuid;

    @ApiModelProperty(value = "客户/供应商guid")
    private String parentCustomerGuid;

    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "待采购数")
    private BigDecimal notBilledQuantity;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "物料Obj")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "来源Obj")
    private ErpPurchaseMgtPurchaseOrderData sourceObj;

    @XyTransCycle
    @ApiModelProperty(value = "外发订单相关数据")
    private ErpOutgoingArrivalVO outgoingOrderVO;

    @ApiModelProperty(value = "结算客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String settlementCustomerOrSupplierGuid;

    @ApiModelProperty(value = "组装的结算供应商以及下单供应商的名称")
    private String supplierName;

    @ApiModelProperty(value = "客户/供应商guid")
    private String customerOrSupplierName;

    @ApiModelProperty(value = "结算客户/供应商")
    private String settlementCustomerOrSupplierName;

    @ApiModelProperty(value = "税率guid")
    private String invoiceTypeTaxRateGuid;

    @ApiModelProperty(value = "税率名称")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "汇率guid")
    private String currencyExchangeRateGuid;

    @ApiModelProperty(value = "汇率名称")
    private String exchangeRate;

    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    @ApiModelProperty(value = "是否本币")
    private Boolean isLocalCurrency;

    @ApiModelProperty(value = "计费方案id")
    private String costSchemeGuid;

    @ApiModelProperty(value = "计费方案名称")
    private String schemeName;

    @ApiModelProperty(value = "币种id")
    private String currencyGuid;

    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty(value = "本币单价(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyUnitPrice;

    @ApiModelProperty(value = "本币金额(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalAmount;

    @ApiModelProperty(value = "结算单价（默认与含税单价相等），写入库存价格字段")
    private BigDecimal settlementUnitPrice;

    @ApiModelProperty(value = "结算金额（默认与含税总金额相等），写入库存价格字段")
    private BigDecimal settlementTotalAmount;

    /**
     * 规格长(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @ApiModelProperty(value = "规格长")
    private BigDecimal specificationsLength;
    /**
     * 规格宽(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @ApiModelProperty(value = "规格宽")
    private BigDecimal specificationsWidth;
    /**
     * 规格高(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @ApiModelProperty(value = "规格高")
    private BigDecimal specificationsHeight;

    @ApiModelProperty(value = "客户")
    private String customerOrSupplierNameAndSCustomerOrSupplierName;

    @ApiModelProperty(value = "其他费用")
    List<ErpBusinessMgtOtherExpensesVO> otherExpensesList;

    @ApiModelProperty(value = "已删除文件")
    List<String> isdelectFile;

    @ApiModelProperty(value = "生产工单号")
    private String sourceProductOrderNumber;

    @ApiModelProperty(value = "待入件数")
    private BigDecimal packagingQuantity;

    @ApiModelProperty(value = "来源件数")
    private BigDecimal packagingOrderQuantity;

    @JsonIgnore
    private String orderDataGuid;
}
