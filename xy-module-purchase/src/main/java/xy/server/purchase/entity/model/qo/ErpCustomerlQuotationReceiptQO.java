package xy.server.purchase.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
* <p>
* 物料报价表VO
* </p>
*
* <AUTHOR>
* @since 2024-05-23
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpCustomerlQuotationReceiptQO对象", description = "物料报价表")
public class ErpCustomerlQuotationReceiptQO {

    @ApiModelProperty(value = "客户guid or 供应商guid")
    private String customerOrSupplierGuid;

    @ApiModelProperty(value = "物料分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料guid")
    private String materialGuid;

    @ApiModelProperty(value = "产品报价明细guid")
    private String materialQuotationReceiptDetailGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生效时间")
    private LocalDateTime effectiveDate;
}