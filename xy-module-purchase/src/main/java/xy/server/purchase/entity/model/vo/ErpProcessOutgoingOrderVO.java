package xy.server.purchase.entity.model.vo;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 工单表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpProcessOutgoingOrderVO对象", description = "工单表")
public class ErpProcessOutgoingOrderVO extends BaseEntity {

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "工序工单的工单guid")
    private String workOrderProcessGuid;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workorderNumber;
    /**
     * 根据物料id查询获取到的印件名称，
     * 以及部件名称，
     * 部件名称是当前id对于的物料名称，印件名称是他的上级物料id获得的
     */
    @ApiModelProperty(value = "物料分类(成品)")
    private String materialGuid;


    @ApiModelProperty(value = "产品物料id")
    public String productMaterialGuid;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 是物料分类对应物料的名称
     */
    @ApiModelProperty(value = "部件名称")
    private String partName;

    /**
     * 根据工序类型id获取到，工序这个字段的名称
     * 根据工序id能够一对多获取到工序项的名称，根据工序项的id获取到工序参数的值然后拼接起来，得到工序参数字段
     */
    @ApiModelProperty(value = "工序类型guid")
    private String productionProcessesTypeGuid;
    /**
     * 根据工序类型id获取对应的名称
     */
    @ApiModelProperty(value = "工序")
    private String processName;
    /**
     * 根据工序类型id查询对应的参数项集合，然后组合相关的参数值组合起来
     */
    @ApiModelProperty(value = "工序参数")
    private String processParameters;
    /**
     * 加工数=数量
     * 损耗数=损耗数
     * 外发总数=数量+损耗数=总数
     * 交货数量=产出数量
     */
    @ApiModelProperty(value = "加工数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "损耗数量")
    private BigDecimal lossQuantity;
    /**
     * 收货数量
     */
    @ApiModelProperty(value = "产出数量=交货数量")
    private BigDecimal outputQuantity;

    /**
     * 要求交期
     */
    @ApiModelProperty(value = "要求交期")
    private LocalDateTime requiredDeliveryTime;

    /**
     * 总数量
     */
    @ApiModelProperty(value = "总数量")
    private BigDecimal totalQuantity;

    @NotNull(message = "含税报价单价(吨价，平方价，千平方英寸)不能为空")
    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @NotNull(message = "含税总金额不能为空")
    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @NotNull(message = "不含税报价单价(吨价，平方价，千平方英寸)不能为空")
    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @NotNull(message = "不含税总金额不能为空")
    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @NotNull(message = "联系人姓名不能为空")
    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @NotNull(message = "电话不能为空")
    @ApiModelProperty(value = "电话")
    private String mobilephone;

    @ApiModelProperty(value = "PK")
    private String advancePaymentDetailGuid;
}
