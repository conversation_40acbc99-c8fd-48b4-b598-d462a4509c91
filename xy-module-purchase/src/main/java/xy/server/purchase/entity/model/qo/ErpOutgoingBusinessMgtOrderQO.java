package xy.server.purchase.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
* <p>
* 订单表QO
* </p>
*
* <AUTHOR>
* @since 2023-09-19
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBusinessMgtOrderQO对象", description = "订单表")
public class ErpOutgoingBusinessMgtOrderQO {

    @ApiModelProperty(value = "开单开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "申请类型")
    private List<String> workorderTypeGuid;

    @ApiModelProperty(value = "申请单状态")
    private List<String> workorderState;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    private List<String> keywords;

    @ApiModelProperty(value = "审核状态")
    private List<String> auditStatus;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "外发申请单号")
    private String outgoingApplyOrderNumber;

    @ApiModelProperty(value = "生产工单号")
    private String productionWorkOrderNumber;


    @ApiModelProperty(value = "产品名称/编码")
    private String production;

    @ApiModelProperty(value = "加工商编码/简称/全称")
    private String processSupplier;

    @ApiModelProperty(value = "结算加工商编码/简称/全称")
    private String settlementSupplier;

    @ApiModelProperty(value = "订单Guids")
    private List<String> businessKeys;
}

