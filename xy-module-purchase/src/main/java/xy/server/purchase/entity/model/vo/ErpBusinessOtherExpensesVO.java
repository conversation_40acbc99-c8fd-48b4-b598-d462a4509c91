package xy.server.purchase.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBusinessMgtOtherExpensesVO对象", description = "其他费用表")
public class ErpBusinessOtherExpensesVO {

    @ApiModelProperty(value = "租户guid")
    private String tenantGuid;

    @ApiModelProperty(value = "guid")
    private String otherExpensesGuid;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值（业务订单，采购订单，外发订单）")
    private String sourceValus;

    @ApiModelProperty(value = "费用类型")
    private String otherExpensesType;

    @ApiModelProperty(value = "费用名称")
    private String otherExpensesName;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "其他费用含税")
    private BigDecimal otherExpensesIncludingTax;

    @ApiModelProperty(value = "其他费用不含税")
    private BigDecimal otherExpensesWithoutTax;

    @ApiModelProperty(value = "其他费用本币(不含税)")
    private BigDecimal localCurrencyOtherExpenses;

    @ApiModelProperty(value = "创建人GUID(取用户表UserGUID)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人GUID(取用户表UserGUID)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

}
