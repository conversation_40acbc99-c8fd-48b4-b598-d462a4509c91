package xy.server.purchase.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 工单数据表工序工单QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpProductionMgtWorkorderDataProcessQO对象", description = "工单数据表工序工单")
public class ErpOutgoingDataProcessQO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String workorderDataProcessGuid;

    @ApiModelProperty(value = "工单表_guid")
    private String workorderGuid;

    @ApiModelProperty(value = "模数((一个加工数量上面投入生产部件个数，例一个印面放两个产品)) 投入模数，产出模数")
    private BigDecimal modulus;

    @ApiModelProperty(value = "损耗数量(根据工单属性:2是工序工单损耗数)")
    private BigDecimal lossQuantity;

    @ApiModelProperty(value = "调整损耗数量(根据工单属性:2是工序工单调整损耗数)")
    private BigDecimal adjustLossQuantity;

    @ApiModelProperty(value = "产出数量")
    private BigDecimal outputQuantity;

    @ApiModelProperty(value = "产出单位")
    private String outputUnitGuid;

    @ApiModelProperty(value = "产出规格长(根据工单属性:1工单成品规格，2工序加工规格)")
    private BigDecimal outputSpecificationsLength;

    @ApiModelProperty(value = "产出规格宽(根据工单属性:1工单成品规格，2工序加工规格)")
    private BigDecimal outputSpecificationsWidth;

    @ApiModelProperty(value = "产出规格高(根据工单属性:1工单成品规格，2工序加工规格)")
    private BigDecimal outputSpecificationsHeight;

    @ApiModelProperty(value = "是否排程(根据工序是否默认)")
    private Boolean isProductionScheduling;

    @ApiModelProperty(value = "排程状态(0未排程，1部分排程，2全部排程，3终止排程)")
    private String productionSchedulingState;

    @ApiModelProperty(value = "已排程数量")
    private BigDecimal productionScheduledQuantity;

    @ApiModelProperty(value = "是否外发(根据工序是否默认)")
    private Boolean isOutsource;

    @ApiModelProperty(value = "外发申请状态(0未外发申请，1部分外发申请，2全部外发申请，3终止外发申请)")
    private String outsourceRequisitionState;

    @ApiModelProperty(value = "外发状态(0未外发，1部分外发，2全部外发，3终止外发)")
    private String outsourceState;

    @ApiModelProperty(value = "生产状态(0未生产，1部分生产，2全部生产，3暂停)")
    private String producedState;

    @ApiModelProperty(value = "已生产数量")
    private BigDecimal producedQuantity;

    @ApiModelProperty(value = "已外发申请数量")
    private BigDecimal outsourcedRequisitionQuantity;

    @ApiModelProperty(value = "已外发数量")
    private BigDecimal outsourcedQuantity;

    @ApiModelProperty(value = "设备型号guid")
    private String equipmentModelGuid;

    @ApiModelProperty(value = "模具型号guid")
    private String moldModelGuid;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

}