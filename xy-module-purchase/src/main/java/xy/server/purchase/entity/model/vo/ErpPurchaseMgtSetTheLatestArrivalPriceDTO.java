package xy.server.purchase.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @data 2024-08-19 16:52
 * @apiNote 采购订单设置最近到货单价DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpPurchaseMgtSetTheLatestArrivalPriceDTO", description = "采购订单设置最近到货单价DTO")
public class ErpPurchaseMgtSetTheLatestArrivalPriceDTO {

    @ApiModelProperty(value = "下标")
    private Integer index;

    @NotBlank
    @ApiModelProperty(value = "物料Guid")
    private String materialGuid;

    @NotBlank
    @ApiModelProperty(value = "供应商Guid")
    private String supplierGuid;

    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;
}
