package xy.server.purchase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 订单文件表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_business_mgt_order_file", autoResultMap = true)
public class ErpPurchaseMgtPurchaseOrderFile extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 订单表_guid
     */
    @TableField("order_guid")
    private String orderGuid;
    /**
     * PK
     */
    @TableId("order_file_guid")
    private String orderFileGuid;
    /**
     * 文件表_guid
     */
    @TableField("file_guid")
    private String fileGuid;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 扩展字段
     */
    @TableField(value = "to_json", typeHandler  = JsonbTypeHandler.class)
    private Object toJson;
    /**
     * 文件类型(1图片，2文件，3流程图 )
     */
    @TableField("file_type")
    private Integer fileType;


}
