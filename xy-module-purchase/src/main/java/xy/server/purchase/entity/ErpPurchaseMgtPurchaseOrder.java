package xy.server.purchase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 采购订单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_business_mgt_order", autoResultMap = true)
public class ErpPurchaseMgtPurchaseOrder extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("order_guid")
    private String orderGuid;
    /**
     * 订单号
     */
    @TableField("order_number")
    private String orderNumber;
    /**
     * 订单需求描述
     */
    @TableField("order_demand_guid")
    private String orderDemandGuid;
    /**
     * 单据日期(可变更的)
     */
    @TableField("receipt_date")
    private LocalDateTime receiptDate;
    /**
     * 采购类型
     */
    @TableField("order_type")
    private String orderType;
    /**
     * 总费用(含税)
     */
    @TableField("total_expenses_including_tax")
    private BigDecimal totalExpensesIncludingTax;
    /**
     * 总费用(不含税)
     */
    @TableField("total_expenses_without_tax")
    private BigDecimal totalExpensesWithoutTax;
    /**
     * 本币总费用(根据币种，外币才显示)，外币不含税
     */
    @TableField("local_currency_total_expenses")
    private BigDecimal localCurrencyTotalExpenses;
    /**
     * 结算费用（默认与含税总金额相等)
     */
    @TableField("settlement_total_expenses")
    private BigDecimal settlementTotalExpenses;
    /**
     * 总体积
     */
    @TableField("total_volume")
    private BigDecimal totalVolume;
    /**
     * 总重量
     */
    @TableField("total_weight")
    private BigDecimal totalWeight;
    /**
     * 保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)
     */
    @TableField("decimal_method")
    private Integer decimalMethod;
    /**
     * 结算单价保留小数位(默认9位，可手动变更)
     */
    @TableField("settlement_unit_price_keep_decimal_place")
    private Integer settlementUnitPriceKeepDecimalPlace;
    /**
     * 结算金额保留小数位(默认2位，可手动变更)
     */
    @TableField("settlement_total_amount_keep_decimal_place")
    private Integer settlementTotalAmountKeepDecimalPlace;
    /**
     * 订单属性(1业务合同，2采购订单，3外发订单)
     */
    @TableField("order_properties")
    private Integer orderProperties;
    /**
     * 税率guid
     */
    @TableField("invoice_type_tax_rate_guid")
    private String invoiceTypeTaxRateGuid;
    /**
     * 汇率guid
     */
    @TableField("currency_exchange_rate_guid")
    private String currencyExchangeRateGuid;
    /**
     * 是否加急
     */
    @TableField("is_urgent")
    private Boolean isUrgent;
    /**
     * 打印次数
     */
    @TableField("print_times")
    private Integer printTimes;
    /**
     * 客户/供应商guid
     */
    @TableField("customer_or_supplier_guid")
    private String customerOrSupplierGuid;
    /**
     * 联系人姓名
     */
    @TableField("contact_name")
    private String contactName;
    /**
     * 电话
     */
    @TableField("mobilephone")
    private String mobilephone;
    /**
     * 结算客户/供应商guid(如A客户落单，送B客户，结算找C客户)
     */
    @TableField("settlement_customer_or_supplier_guid")
    private String settlementCustomerOrSupplierGuid;
    /**
     * 送货客户/供应商guid(如A客户落单，送B客户，结算找C客户)
     */
    @TableField("delivery_customer_or_supplier_guid")
    private String deliveryCustomerOrSupplierGuid;
    /**
     * 送货类型
     */
    @TableField("delivery_type_guid")
    private String deliveryTypeGuid;
    /**
     * 结算类型
     */
    @TableField("settlement_type_guid")
    private String settlementTypeGuid;
    /**
     * 来源guid
     */
    @TableField("source_guid")
    private String sourceGuid;
    /**
     * 来源值
     */
    @TableField("source_value")
    private Integer sourceValue;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 扩展字段
     */
    @TableField(value = "to_json", typeHandler  = JsonbTypeHandler.class)
    private Object toJson;
    /**
     * 流程实例ID
     */
    @TableField("process_instance_id")
    private String processInstanceId;
    /**
     * 审核状态
     */
    @TableField("audit_status")
    private String auditStatus;
    /**
     * 审核时间
     */
    @TableField("audit_date")
    private LocalDateTime auditDate;

}
