package xy.server.purchase.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.purchase.entity.model.vo.ErpBusinessOtherExpensesVO;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 工单表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpOutgoingBusinessSpecifOrderDataRO对象", description = "工单表")
public class ErpOutgoingBusinessSpecifOrderDataRO extends BaseEntity {

    @ApiModelProperty(value = "工单表_guid")
    private String workorderGuid;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "外发申请单号")
    private String applicationNumber;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workorderNumber;
    /**
     * 收货数量
     */
    @ApiModelProperty(value = "收货数量")
    private BigDecimal outputQuantity;
    /**
     * 实际收货数量
     */
    @ApiModelProperty(value = "收货数量")
    private BigDecimal quantity;
    /**
     * 要求交期
     */
    @ApiModelProperty(value = "要求交期")
    private LocalDateTime requiredDeliveryTime;
    /**
     * 总数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "行政区域 _guid")
    private String administrativeAreaGuid;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "工序类型guid")
    private String productionProcessesTypeGuid;

    /**
     * 是物料分类对应物料的名称
     */
    @ApiModelProperty(value = "部件名称")
    private String partName;


    @ApiModelProperty(value = "产品名称")
    private String productName;
    /**
     * 根据工序类型id获取对应的名称
     */
    @ApiModelProperty(value = "工序")
    private String processName;
    /**
     * 根据工序类型id查询对应的参数项集合，然后组合相关的参数值组合起来
     */
    @ApiModelProperty(value = "工序参数")
    private String processParameters;
    /**
     * 工序的备注
     */
    @ApiModelProperty(value = "工序描述")
    private String productionProcessesDescription;
    /**
     * 要求交期
     */
    @ApiModelProperty(value = "要求交期")
    private LocalDateTime deliveryDate;

    @ApiModelProperty(value = "产出单位")
    private String unitGuid;

    @ApiModelProperty(value = "产出规格长(根据工单属性:1工单成品规格，2工序加工规格)")
    private BigDecimal specificationsLength;

    @ApiModelProperty(value = "产出规格宽(根据工单属性:1工单成品规格，2工序加工规格)")
    private BigDecimal specificationsWidth;

    @ApiModelProperty(value = "产出规格高(根据工单属性:1工单成品规格，2工序加工规格)")
    private BigDecimal specificationsHeight;

    @ApiModelProperty(value = "入库状况")
    private String completionStatus;

    @ApiModelProperty(value = "工序工单的工单guid")
    private String workOrderProcessGuid;

    @ApiModelProperty(value = "计费方案id")
    private String costSchemeGuid;

    @ApiModelProperty(value = "物料(成品)")
    private String materialGuid;

    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "本币单价(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyUnitPrice;

    @ApiModelProperty(value = "本币金额(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalAmount;

    @ApiModelProperty(value = "结算单价（默认与含税单价相等），写入库存价格字段")
    private BigDecimal settlementUnitPrice;

    @ApiModelProperty(value = "结算金额（默认与含税总金额相等），写入库存价格字段")
    private BigDecimal settlementTotalAmount;

    @NotNull(message = "含税报价单价(吨价，平方价，千平方英寸)不能为空")
    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @NotNull(message = "含税总金额不能为空")
    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @NotNull(message = "不含税报价单价(吨价，平方价，千平方英寸)不能为空")
    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @NotNull(message = "不含税总金额不能为空")
    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String orderState;

    @ApiModelProperty(value = "其他费用")
    private List<ErpBusinessOtherExpensesVO> otherExpensesList;

    @ApiModelProperty(value = "删除")
    List<String> isdelectList;

    @ApiModelProperty(value = "工序工单的扩展表id")
    private String workorderDataProcessGuid;

}
