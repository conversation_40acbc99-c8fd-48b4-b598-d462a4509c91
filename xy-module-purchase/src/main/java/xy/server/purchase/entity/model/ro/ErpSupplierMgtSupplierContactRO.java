package xy.server.purchase.entity.model.ro;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelTemplate;
import com.xunyue.config.excel.converter.XyExcelConverter;
import com.xunyue.config.excel.enums.ExcelPropertyTypeEnum;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 供应商联系人RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
@XyExcelTemplate("供应商联系人模板")
@ApiModel(value = "ErpSupplierMgtSupplierContactURO对象", description = "供应商联系人")
public class ErpSupplierMgtSupplierContactRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String supplierContactGuid;

    @ExcelProperty("供应商编码")
    @ApiModelProperty(value = "erp_supplier_mgt_Supplier 供应商_guid")
    private String supplierGuid;

    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @NotNull(message = "联系人姓名不能为空")
    @ExcelProperty("联系人姓名")
    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

//    @NotNull(message = "电话不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("电话")
    @ApiModelProperty(value = "电话")
    private String telephone;

//    @NotNull(message = "手机不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("手机")
    @ApiModelProperty(value = "手机")
    private String mobilephone;

//    @NotNull(message = "传真不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("传真")
    @ApiModelProperty(value = "传真")
    private String fax;

//    @NotNull(message = "邮件不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("邮件")
    @ApiModelProperty(value = "邮件")
    private String email;

//    @NotNull(message = "QQ号码不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("QQ号码")
    @ApiModelProperty(value = "QQ号码")
    private String qqNumber;

//    @NotNull(message = "微信号码不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("微信号码")
    @ApiModelProperty(value = "微信号码")
    private String wechatNumber;

//    @NotNull(message = "是否默认不能为空")
    @XyExcelProperty(type = ExcelPropertyTypeEnum.DICTIONARY, dictKey = "YES_OR_NO", isUseDefaultValue = true, boolDefaultValue = false)
    @ExcelProperty(value = "是否默认", converter = XyExcelConverter.class)
    @ApiModelProperty(value = "是否默认")
    private Boolean isDefault;

//    @NotNull(message = "备注或描述不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("备注")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

}
