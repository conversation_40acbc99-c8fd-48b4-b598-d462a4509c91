package xy.server.purchase.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @data 2023/9/13 15:05
 * @apiNote 采购申请单QO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPurchaseMgtPurchaseRequisitionQO", description = "采购申请单QO")
public class ErpPurchaseMgtPurchaseRequisitionQO {

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "申请类型")
    private List<String> workorderTypeGuids;

    @ApiModelProperty(value = "开始日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "审核状态")
    private List<String> auditStatus;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "物料类型")
    private List<String> materialTypes;

    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;

    @ApiModelProperty(value = "物料名称/编码")
    private String materialName;

    @ApiModelProperty("来源id")
    private List<String> sourceGuid;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(value = "创建人/工号")
    private String creator;

    @ApiModelProperty(value = "是否忽略数量")
    private Boolean isIgnore;

    @ApiModelProperty(value = "页面大小（默认50）")
    private long size = 50;

    @ApiModelProperty(value = "搜索码")
    private String searchCode;

    @ApiModelProperty(value = "门幅")
    private String widthOfFabric;
}
