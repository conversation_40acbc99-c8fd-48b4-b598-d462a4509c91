package xy.server.purchase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 供应商联系人
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_supplier_mgt_supplier_contact",autoResultMap = true)
public class ErpSupplierMgtSupplierContact extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("supplier_contact_guid")
    private String supplierContactGuid;
    /**
     * erp_supplier_mgt_Supplier 供应商_guid
     */
    @TableField("supplier_guid")
    private String supplierGuid;
    /**
     * 顺序号
     */
    @TableField("serial_number")
    private Integer serialNumber;
    /**
     * 联系人姓名
     */
    @TableField("contact_name")
    private String contactName;
    /**
     * 电话
     */
    @TableField("telephone")
    private String telephone;
    /**
     * 手机
     */
    @TableField("mobilephone")
    private String mobilephone;
    /**
     * 传真
     */
    @TableField("fax")
    private String fax;
    /**
     * 邮件
     */
    @TableField("email")
    private String email;
    /**
     * QQ号码
     */
    @TableField("qq_number")
    private String qqNumber;
    /**
     * 微信号码
     */
    @TableField("wechat_number")
    private String wechatNumber;
    /**
     * 是否默认
     */
    @TableField("is_default")
    private Boolean isDefault;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;

    @TableField(value = "to_json",typeHandler  = JsonbTypeHandler.class)
    private Object toJson;



}
