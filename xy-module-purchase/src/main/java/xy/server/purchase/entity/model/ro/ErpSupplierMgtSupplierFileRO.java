package xy.server.purchase.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 供应商文件RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpSupplierMgtSupplierFileURO对象", description = "供应商文件")
public class ErpSupplierMgtSupplierFileRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String supplierFileGuid;

    @ApiModelProperty(value = "erp_supplier_mgt_Supplier 供应商_guid")
    private String supplierGuid;

    @NotNull(message = "文件类型1是图片2是文件不能为空")
    @ApiModelProperty(value = "文件类型1是图片2是文件")
    private Integer fileType;

    @ApiModelProperty(value = "文件 _guid")
    private String fileGuid;

    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;


}
