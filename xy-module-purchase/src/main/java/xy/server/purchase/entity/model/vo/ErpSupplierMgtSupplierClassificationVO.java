package xy.server.purchase.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 供应商分类VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpSupplierMgtSupplierClassificationVO对象", description = "供应商分类")
public class ErpSupplierMgtSupplierClassificationVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String supplierClassificationGuid;

    @ApiModelProperty(value = "供应商分类名称")
    private String supplierClassificationName;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "供应商分类属性值（来源字典表）")
    private String supplierClassificationAttributeValue;

    @ApiModelProperty(value = "供应商分类属性值（来源字典表）")
    @XyTrans(dictionaryKey = "SUPPLY_ATTRIBUTE", dictionaryValue = "supplierClassificationAttributeValue")
    private String supplierClassificationAttribute;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean type;

    @ApiModelProperty(value = "已被使用")
    private Boolean isUsed;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "子类")
    private List<ErpSupplierMgtSupplierClassificationVO> children;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

}
