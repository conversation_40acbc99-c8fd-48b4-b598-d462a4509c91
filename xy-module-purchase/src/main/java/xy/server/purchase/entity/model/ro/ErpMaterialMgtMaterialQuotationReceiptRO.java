package xy.server.purchase.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 物料报价表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMaterialMgtMaterialQuotationReceiptRO对象", description = "物料报价表")
public class ErpMaterialMgtMaterialQuotationReceiptRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String materialQuotationReceiptGuid;

    @ApiModelProperty(value = "报价类型(字典)")
    private String materialQuotationReceiptType;

    @ApiModelProperty(value = "物料报价单号")
    private String materialQuotationReceiptNumbers;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "客户guid or 供应商guid")
    private String customerOrSupplierGuid;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生效时间")
    private LocalDateTime effectiveDate;

    @NotNull(message = "客户物料报价1，供应商物料报价2不能为空")
    @ApiModelProperty(value = "客户物料报价1，供应商物料报价2")
    private Integer materialQuotationReceiptProperties;

    @ApiModelProperty(value = "生效状态")
    private Boolean effectiveStatus;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @ApiModelProperty(value = "是否是导入")
    private Boolean importTent;

    /**
     * 真实生效时间
     */
    @ApiModelProperty(value = "真实生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime realEffectiveDate;

    @ApiModelProperty(value = "报价明细")
    private List<ErpMaterialMgtMaterialQuotationReceiptDetailRO> detailList;

}
