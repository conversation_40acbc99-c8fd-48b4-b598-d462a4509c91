package xy.server.purchase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 订单数据异常处理表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_business_mgt_purchase_order_data_extend", autoResultMap = true)
public class ErpBusinessMgtPurchaseOrderDataExtend extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("purchase_order_data_extend_guid")
    private String purchaseOrderDataExtendGuid;
    /**
     * 订单数据guid
     */
    @TableField("order_data_guid")
    private String orderDataGuid;
    /**
     * 是否锁价
     */
    @TableField("lock_price")
    private Boolean lockPrice;
    /**
     * 每件数量
     */
    @TableField("quantity_per_package")
    private BigDecimal quantityPerPackage;
    /**
     * 件数
     */
    @TableField("packaging_quantity")
    private BigDecimal packagingQuantity;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 扩展字段
     */
    @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
    private Object toJson;


}
