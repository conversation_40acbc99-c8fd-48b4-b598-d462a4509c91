package xy.server.purchase.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 采购订单QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPurchaseMgtPurchaseOrderQO对象", description = "采购订单")
public class ErpPurchaseMgtPurchaseOrderQO {

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "订单号集合")
    private List<String> orderNumberList;

    @ApiModelProperty(value = "来源生产单号")
    private String sourceProductOrderNumber;

    @ApiModelProperty(value = "采购类型")
    private List<String> orderTypes;



    @ApiModelProperty(value = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "开始日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "审核状态")
    private List<String> auditStatus;

    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;

    @ApiModelProperty(value = "供应商简称/全称/编码")
    private String supplierName;

    @ApiModelProperty(value = "物料名称/编码")
    private String materialName;

    @ApiModelProperty(value = "申购单号")
    private String purchaseWorkorderNumber;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(name = "制单人/工号")
    private String creator;

    @ApiModelProperty(value = "物料Guid")
    private String materialGuid;

    @ApiModelProperty(value = "物料Guid（多选）")
    private List<String> materialGuids;

    @ApiModelProperty(value = "供应商Guid")
    private String supplierGuid;

    @ApiModelProperty(value = "是否忽略待开数量")
    private Boolean isIgnore;

    @ApiModelProperty(value = "是否忽略待开件数")
    private Boolean isPackagingQuantity;

    @ApiModelProperty(value = "到货明细")
    private List<String> workorderGuids;

    @ApiModelProperty(value = "采购订单明细")
    private List<String> orderDataGuid;

}
