package xy.server.purchase.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
        /**
* <p>
* 物料报价明细表QO
* </p>
*
* <AUTHOR>
* @since 2024-05-23
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMaterialMgtMaterialQuotationReceiptDetailQO对象", description = "物料报价明细表")
public class ErpMaterialMgtMaterialQuotationReceiptDetailQO{

            @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

            @ApiModelProperty(value = "PK")
    private String materialQuotationReceiptDetailGuid;

            @ApiModelProperty(value = "报价guid")
    private String materialQuotationReceiptGuid;

            @ApiModelProperty(value = "物料分类guid")
    private String materialClassificationGuid;

            @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

                @ApiModelProperty(value = "最小数量")
    private BigDecimal minQuantity;

                @ApiModelProperty(value = "最大数量")
    private BigDecimal maxQuantity;

                @ApiModelProperty(value = "含税报价单价")
    private BigDecimal quotationUnitPriceIncludingTax;

                @ApiModelProperty(value = "备注或描述")
    private String description;

            @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

                @ApiModelProperty(value = "创建人")
    private String creator;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

            @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

                @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

            @ApiModelProperty(value = "扩展字段")
    private Object toJson;

}