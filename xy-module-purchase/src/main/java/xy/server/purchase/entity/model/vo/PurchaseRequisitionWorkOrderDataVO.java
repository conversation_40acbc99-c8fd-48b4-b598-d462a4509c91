package xy.server.purchase.entity.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024-11-21 14:41
 * <p>
 *     采购申请来源工单数据
 * </p>
 **/
@Data
public class PurchaseRequisitionWorkOrderDataVO {

    @ApiModelProperty("采购申请明细Guid")
    private String workorderGuid;

    @ApiModelProperty("投入数量")
    private BigDecimal quantity;

    @ApiModelProperty("物料使用数量")
    private BigDecimal materialUsageQuantity;

    @ApiModelProperty("物料损耗数量")
    private BigDecimal materialLossQuantity;

    @ApiModelProperty("物料总数量")
    private BigDecimal materialTotalQuantity;

    @ApiModelProperty("产出物料")
    private String materialGuid;

    @ApiModelProperty("产出物料Obj")
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty("产出数量")
    private BigDecimal outputQuantity;

    @ApiModelProperty("损耗数量")
    private BigDecimal lossQuantity;

    @ApiModelProperty("调整数量")
    private BigDecimal adjustLossQuantity;

    @ApiModelProperty("产出比")
    private BigDecimal proportion;
}
