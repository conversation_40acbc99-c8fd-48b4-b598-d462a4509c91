package xy.server.purchase.entity.model.ro;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 订单表ExceRO
 * </p>
 *
 * <AUTHOR>
 *
 * @since 2023-09-28
 */
@Data
@ApiModel(value = "ErpBusinessMgtOrderURO对象", description = "订单表")
public class ErpMaterialMgtQuotationExceRO {

    @ApiModelProperty(value = "客户编码")
    @ExcelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "客户简称")
    @ExcelProperty(value = "客户简称")
    private String customerName;

    @ApiModelProperty(value = "供应商简称")
    @ExcelProperty(value = "供应商简称")
    private String supplierName;

    @ApiModelProperty(value = "产品名称")
    @ExcelProperty(value = "产品名称")
    private String materialName;

    @ApiModelProperty(value = "产品编码")
    @ExcelProperty(value = "产品编码")
    private String materialCode;

    @ExcelProperty(value = "最小数量")
    @ApiModelProperty(value = "最小数量")
    private BigDecimal minQuantity;

    @ExcelProperty(value = "最大数量")
    @ApiModelProperty(value = "最大数量")
    private BigDecimal maxQuantity;

    @ExcelProperty(value = "含税报价单价")
    @ApiModelProperty(value = "含税报价单价")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ExcelProperty(value = "备注")
    @ApiModelProperty(value = "备注")
    private String description;

    @ExcelProperty(value = "生效时间")
    @ApiModelProperty(value = "生效时间")
    private String effectiveDate;

    @ExcelProperty(value = "供方货号")
    @ApiModelProperty(value = "供方货号")
    private String externalMaterialCode;

    @ExcelProperty(value = "搜索码")
    @ApiModelProperty(value = "搜索码")
    private String searchCode;



}
