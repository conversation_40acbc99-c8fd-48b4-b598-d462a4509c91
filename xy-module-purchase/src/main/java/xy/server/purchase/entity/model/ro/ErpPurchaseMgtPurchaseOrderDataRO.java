package xy.server.purchase.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.purchase.entity.model.vo.ErpBusinessOtherExpensesVO;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单数据表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPurchaseMgtPurchaseOrderDataRO对象", description = "订单数据表")
public class ErpPurchaseMgtPurchaseOrderDataRO extends BaseEntity {

    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "PK")
    private String orderDataGuid;

    @ApiModelProperty(value = "订单guid  一对多")
    private String orderGuid;

    @ApiModelProperty(value = "物料分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料GUID(成品、物料)")
    @NotBlank(message = "物料GUID不能为空")
    private String materialGuid;

    @ApiModelProperty(value = "单位guid")
    private String unitGuid;

    @ApiModelProperty(value = "FSC声明_guid")
    private String fscDeclarationGuid;

    @ApiModelProperty(value = "经验工序guid")
    private String experienceProductionProcessGuid;

    @NotNull(message = "数量不能为空")
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "赠品数量")
    private BigDecimal giftQuantity;

    @ApiModelProperty(value = "备用数量")
    private BigDecimal spareQuantity;

    @ApiModelProperty(value = "使用库存数量")
    private BigDecimal usingInventoryQuantity;

    @ApiModelProperty(value = "总数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty(value = "本币单价(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyUnitPrice;

    @ApiModelProperty(value = "本币金额(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalAmount;

    @ApiModelProperty(value = "结算单价（默认与含税单价相等），写入库存价格字段")
    private BigDecimal settlementUnitPrice;

    @ApiModelProperty(value = "结算金额（默认与含税总金额相等），写入库存价格字段")
    private BigDecimal settlementTotalAmount;

    @ApiModelProperty(value = "外部单号(例客户PO，采购送货单号)")
    private String externalTrackingNumber;

    @ApiModelProperty(value = "外部物料条码(BarCode)")
    private String externalMaterialBarCode;

    @ApiModelProperty(value = "外部物料编码(例客户产品编码)")
    private String externalMaterialCode;

    @ApiModelProperty(value = "外部物料名称(例客户产品名称)")
    private String externalMaterialName;

    @ApiModelProperty(value = "货期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deliveryDate;

    @ApiModelProperty(value = "送货类型")
    private String deliveryTypeGuid;

    @ApiModelProperty(value = "完成状态(0未完成，1部分完成，2已完成，3终止)")
    private String completionStatus;

    @ApiModelProperty(value = "完成数量(后面流程将其更新)")
    private BigDecimal completionQuantity;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "交货地址-行政区域 _guid")
    private String administrativeAreaGuid;

    @ApiModelProperty(value = "交货地址-详细地址")
    private String address;

    @ApiModelProperty(value = "报价数量")
    private BigDecimal quotationQuantity;

    @ApiModelProperty(value = "计费方案id")
    private String costSchemeGuid;

    /**
     * 关联表数据
     */

    @ApiModelProperty(value = "采购明细列表")
    @Valid
    private List<ErpPurchaseMgtPurchaseOrderDataRO> detailList;

    @ApiModelProperty(value = "其他费用")
    private List<ErpBusinessOtherExpensesVO> otherExpensesList;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String orderState;

    @ApiModelProperty(value = "预计交期")
    private LocalDateTime expectedReceiptDate;

    @ApiModelProperty(value = "删除ids")
    private List<String> isdelectList;

    @ApiModelProperty(value = "是否锁价")
    private Boolean lockPrice;

    @ApiModelProperty(value = "每件数量")
    private BigDecimal quantityPerPackage;

    @ApiModelProperty(value = "件数")
    private BigDecimal packagingQuantity;

    @ApiModelProperty(value = "订单扩展数据")
    private String purchaseOrderDataExtendGuid;

}
