package xy.server.purchase.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xunyue.config.mybatisplus.BaseEntity;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.basic.entity.model.vo.ErpWorkorderDataProcessParameterVO;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;
import xy.server.purchase.entity.model.vo.ErpOutgoingApplicationVO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 工单表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpOutgoingMgtApplicationRO对象", description = "工单表")
public class ErpOutgoingApplicationFormRO extends BaseEntity {

    @ApiModelProperty(value = "工单表_guid")
    private String workorderGuid;

    @ApiModelProperty(value = "申请类型")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "工序工单的工单guid")
    private String workOrderProcessGuid;

    @ApiModelProperty(value = "外发申请单号")
    private String applicationNumber;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workorderNumber;
    /**
     * 根据物料id查询获取到的印件名称，
     * 以及部件名称，
     * 部件名称是当前id对于的物料名称，印件名称是他的上级物料id获得的
     */
    @ApiModelProperty(value = "物料(成品)")
    private String materialGuid;
    /**
     * 是物料分类对应物料的名称
     */
    @ApiModelProperty(value = "部件名称")
    private String partName;

    @ApiModelProperty(value = "产品物料id")
    public String productMaterialGuid;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品编码")
    private String materialCode;

    @ApiModelProperty(value = "实际产品物料id")
    public String actProductMaterialGuid;

    @ApiModelProperty(value = "实际产品名称")
    private String actProductName;

    @ApiModelProperty(value = "实际产出数量")
    private BigDecimal actQuantity;

    @ApiModelProperty(value = "加工与损耗的比例")
    private BigDecimal proportion;


    /**
     * 根据工序类型id获取到，工序这个字段的名称
     * 根据工序id能够一对多获取到工序项的名称，根据工序项的id获取到工序参数的值然后拼接起来，得到工序参数字段
     */
    @ApiModelProperty(value = "工序类型guid")
    private String productionProcessesTypeGuid;
    /**
     * 根据工序类型id获取对应的名称
     */
    @ApiModelProperty(value = "工序")
    private String processName;
    /**
     * 根据工序类型id查询对应的参数项集合，然后组合相关的参数值组合起来
     */
    @ApiModelProperty(value = "工序参数")
    private String processParameters;

    @ApiModelProperty(value = "工序参数集合")
    private List<ErpWorkorderDataProcessParameterVO> processParameterVOS;
    /**
     * 工序的备注
     */
    @ApiModelProperty(value = "工序描述")
    private String productionProcessesDescription;
    /**
     * 加工数=数量
     * 损耗数=损耗数
     * 外发总数=数量+损耗数=总数
     * 交货数量=产出数量
     */
    @ApiModelProperty(value = "加工数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "损耗数量")
    private BigDecimal lossQuantity;

    /**
     * 收货数量
     */
    @ApiModelProperty(value = "产出数量=交货数量")
    private BigDecimal outputQuantity;

    /**
     * 总数量
     */
    @ApiModelProperty(value = "外发总数量,加工总数")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "工序数量")
    private BigDecimal processorQuantity;

    /**
     * 要求交期
     */
    @NotBlank(message = "要求交期(不能为空)")
    @ApiModelProperty(value = "要求交期")
    private LocalDateTime requiredDeliveryTime;

    /**
     * 待发总数量
     */
    @ApiModelProperty(value = "待发总数量")
    private BigDecimal waitOutgoingQuantity;

    @ApiModelProperty(value = "产出单位")
    private String unitGuid;

    @ApiModelProperty(value = "行政区域 _guid")
    private String administrativeAreaGuid;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @NotNull(message = "含税报价单价(吨价，平方价，千平方英寸)不能为空")
    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @NotNull(message = "含税总金额不能为空")
    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @NotNull(message = "不含税报价单价(吨价，平方价，千平方英寸)不能为空")
    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @NotNull(message = "不含税总金额不能为空")
    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @XyTransCycle
    @ApiModelProperty(value = "产出工单数据")
    private List<ErpOutgoingApplicationVO> sonList;

    @ApiModelProperty(value = "外发状态(0未外发，1部分外发，2全部外发，3终止外发)")
    private String workorderState;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String currentStatus;

    @XyTrans(dictionaryKey = "FINANCE_MGT_STATE",dictionaryValue = "currentStatus")
    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String currentStatusName;

    @ApiModelProperty(value = "物料Obj")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialObj;

    /**
     * 规格长(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @ApiModelProperty(value = "规格长")
    private BigDecimal specificationsLength;
    /**
     * 规格宽(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @ApiModelProperty(value = "规格宽")
    private BigDecimal specificationsWidth;
    /**
     * 规格高(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @ApiModelProperty(value = "规格高")
    private BigDecimal specificationsHeight;

    @ApiModelProperty(value = "工序工单的扩展表id")
    private String workorderDataProcessGuid;

    @ApiModelProperty(value = "已外发申请数量")
    private BigDecimal outsourcedRequisitionQuantity;

    @ApiModelProperty(value = "tmpGuid")
    @JsonIgnore
    private String tmpGuid;

    @ApiModelProperty(value = "上工序")
    private String onProcessName;

}
