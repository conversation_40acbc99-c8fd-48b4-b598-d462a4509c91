package xy.server.purchase.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
* <p>
* 工单表RO
* </p>
*
* <AUTHOR>
* @since 2023-09-15
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpQuotationImportVO对象")
public class ErpQuotationImportVO {

    @ApiModelProperty(value = "描述")
    private List<String> message;

    @ApiModelProperty(value = "导入数量")
    private int importQuantity;

    @ApiModelProperty(value = "未导入数量")
    private int importNoQuantity;
}