package xy.server.purchase.entity.model.ro;

import com.xunyue.config.excel.annotation.XyExcelTemplate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;


/**
 * 修改物料并同步
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@XyExcelTemplate("修改物料并同步")
@ApiModel(value = "ModifyMaterialAndSyncWorkOrderRO对象", description = "修改物料并同步")
public class ModifyMaterialAndSyncWorkOrderRO {

    @NotEmpty(message = "采购单明细guid不能为空")
    @ApiModelProperty(value = "采购单明细guid; level-3")
    private String orderDatatGuid;

    @NotBlank(message = "需要修改的物料guid不能为空")
    @ApiModelProperty(value = "需要修改的物料id")
    private String modifyMaterialGuid;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "投入物料用量")
    private BigDecimal materialUsageQuantity;

    @ApiModelProperty(value = "产出比")
    private BigDecimal proportion;

    @ApiModelProperty(value = "加工数")
    private BigDecimal machiningNumber;
}
