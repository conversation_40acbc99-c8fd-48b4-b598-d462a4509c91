package xy.server.purchase.entity.model.vo;

import com.xunyue.config.mybatisplus.BaseEntity;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.basic.entity.model.vo.ErpBusinessMgtOtherExpensesVO;
import xy.server.basic.entity.model.vo.ErpWorkorderDataProcessParameterVO;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;
import xy.server.work.entity.model.vo.ErpProductionMgtWorkorderMaterialUsageVO;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
* <p>
* 工单表RO
* </p>
*
* <AUTHOR>
* @since 2023-09-15
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpOutgoingOrderDataVO对象", description = "工单表")
public class ErpOutgoingOrderDataVO extends BaseEntity {

    @ApiModelProperty(value = "工单表_guid")
    private String workorderGuid;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "外发申请单号")
    private String applicationNumber;

    @ApiModelProperty(value = "工序工单的工单关联guid")
    private String workOrderProcessGuid;

    @ApiModelProperty(value = "工序工单的工单guid")
    private String processWorkOrderGuid;
    /**
     * 外发申请单号
     */
    @ApiModelProperty(value = "外发申请单号")
    private String workorderNumber;
    /**
     * 生产工单号
     */
    @ApiModelProperty(value = "生产工单号")
    private String productionWorkOrderNumber;
    /**
     * 收货数量
     */
    @ApiModelProperty(value = "收货数量")
    private BigDecimal outputQuantity;
    /**
     * 实际收货数量
     */
    @ApiModelProperty(value = "收货数量")
    private BigDecimal quantity;
    /**
     * 工单要求交期
     */
    @ApiModelProperty(value = "要求交期")
    private LocalDateTime requiredDeliveryTime;
    /**
     * 总数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "行政区域 _guid")
    private String administrativeAreaGuid;

    @ApiModelProperty(value = "行政区域 _guid")
    private String administrativeAreaName;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @NotNull(message = "含税报价单价(吨价，平方价，千平方英寸)不能为空")
    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @NotNull(message = "含税总金额不能为空")
    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @NotNull(message = "不含税报价单价(吨价，平方价，千平方英寸)不能为空")
    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @NotNull(message = "不含税总金额不能为空")
    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "本币单价(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyUnitPrice;

    @ApiModelProperty(value = "本币金额(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalAmount;

    @ApiModelProperty(value = "结算单价（默认与含税单价相等），写入库存价格字段")
    private BigDecimal settlementUnitPrice;

    @ApiModelProperty(value = "结算金额（默认与含税总金额相等），写入库存价格字段")
    private BigDecimal settlementTotalAmount;

    @ApiModelProperty(value = "工序类型guid")
    private String productionProcessesTypeGuid;

    /**
     * 是物料分类对应物料的名称
     */
    @ApiModelProperty(value = "部件名称")
    private String partName;


    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品编码")
    private String productCode;
    /**
     * 根据工序类型id获取对应的名称
     */
    @ApiModelProperty(value = "工序")
    private String processName;
    /**
     * 根据工序类型id查询对应的参数项集合，然后组合相关的参数值组合起来
     */
    @ApiModelProperty(value = "工序参数")
    private String processParameters;

    @ApiModelProperty(value = "工序参数集合")
    private List<ErpWorkorderDataProcessParameterVO> processParameterVOS;
    /**
     * 工序的备注
     */
    @ApiModelProperty(value = "工序描述")
    private String productionProcessesDescription;
    /**
     * 订单要求交期
     */
    @ApiModelProperty(value = "要求交期")
    private LocalDateTime deliveryDate;

    @ApiModelProperty(value = "入库状况")
    private String completionStatus;

    @ApiModelProperty(value = "计费方案id")
    private String costSchemeGuid;

    @ApiModelProperty(value = "计费方案name")
    private String schemeName;

    @XyTransCycle
    @ApiModelProperty(value = "产出工单数据")
    private List<ErpOutgoingApplicationVO> sonList;

    @XyTransCycle
    @ApiModelProperty(value = "上工序产出工单数据")
    private List<ErpOutgoingApplicationVO> previousOutputWorkorderList;


    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String orderState;

    @XyTrans(dictionaryKey = "FINANCE_MGT_STATE",dictionaryValue = "orderState")
    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String orderStateName;

    /**
     * 规格长(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @ApiModelProperty(value = "规格长")
    private BigDecimal specificationsLength;
    /**
     * 规格宽(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @ApiModelProperty(value = "规格宽")
    private BigDecimal specificationsWidth;
    /**
     * 规格高(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @ApiModelProperty(value = "规格高")
    private BigDecimal specificationsHeight;

    @ApiModelProperty(value = "物料Obj")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "物料(成品)")
    private String materialGuid;

    @ApiModelProperty(value = "orderGuid")
    private String orderGuid;

    @ApiModelProperty(value = "orderDataGuid")
    private String orderDataGuid;

    @ApiModelProperty(value = "工序用量列表")
    private List<ErpProductionMgtWorkorderMaterialUsageVO> workorderMaterialUsageList;

    @ApiModelProperty(value = "其他费用")
    private List<ErpBusinessMgtOtherExpensesVO> otherExpensesList;

    @ApiModelProperty(value = "加工数量")
    private BigDecimal processCount;

    @ApiModelProperty(value = "损耗数")
    private BigDecimal lossCount;

    @ApiModelProperty(value = "总数")
    private BigDecimal totality;

}
