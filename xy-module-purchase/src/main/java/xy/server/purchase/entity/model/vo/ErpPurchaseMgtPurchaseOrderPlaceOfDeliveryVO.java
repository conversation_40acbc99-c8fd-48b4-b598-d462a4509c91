package xy.server.purchase.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 订单交货地点管理VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPurchaseMgtPurchaseOrderPlaceOfDeliveryVO对象", description = "订单交货地点管理")
public class ErpPurchaseMgtPurchaseOrderPlaceOfDeliveryVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "订单表guid")
    private String orderGuid;

    @ApiModelProperty(value = "订单数据表guid")
    private String orderDataGuid;

    @ApiModelProperty(value = "PK")
    private String orderPlaceOfDeliveryGuid;

    @ApiModelProperty(value = "行政区域 _guid")
    private String administrativeAreaGuid;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @ApiModelProperty(value = "电话")
    private String telephone;

    @ApiModelProperty(value = "手机")
    private String mobilePhone;

    @ApiModelProperty(value = "传真")
    private String fax;

    @ApiModelProperty(value = "邮件")
    private String email;

    @ApiModelProperty(value = "QQ号码")
    private String qqNumber;

    @ApiModelProperty(value = "微信号码")
    private String wechatNumber;

    @ApiModelProperty(value = "是否默认")
    private Boolean isDefault;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

}