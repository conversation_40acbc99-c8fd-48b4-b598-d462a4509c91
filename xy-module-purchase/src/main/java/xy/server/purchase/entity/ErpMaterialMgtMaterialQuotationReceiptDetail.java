package xy.server.purchase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 物料报价明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_material_mgt_material_quotation_receipt_detail", autoResultMap = true)
public class ErpMaterialMgtMaterialQuotationReceiptDetail extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("material_quotation_receipt_detail_guid")
    private String materialQuotationReceiptDetailGuid;
    /**
     * 报价guid
     */
    @TableField("material_quotation_receipt_guid")
    private String materialQuotationReceiptGuid;
    /**
     * 物料分类guid
     */
    @TableField("material_classification_guid")
    private String materialClassificationGuid;
    /**
     * 物料_guid
     */
    @TableField("material_guid")
    private String materialGuid;
    /**
     * 最小数量
     */
    @TableField("min_quantity")
    private BigDecimal minQuantity;
    /**
     * 最大数量
     */
    @TableField("max_quantity")
    private BigDecimal maxQuantity;
    /**
     * 含税报价单价
     */
    @TableField("quotation_unit_price_including_tax")
    private BigDecimal quotationUnitPriceIncludingTax;
    /**
     *调整幅度
     */
    @TableField("adjustment_magnitude")
    private BigDecimal adjustmentMagnitude;
    /**
     * 原含税报价单价
     */
    @TableField("old_quotation_unit_price_including_tax")
    private BigDecimal oldQuotationUnitPriceIncludingTax;
    /**
     * 客户或者供应商货号
     */
    @TableField("external_material_code")
    private String externalMaterialCode;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 扩展字段
     */
    @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
    private Object toJson;


}
