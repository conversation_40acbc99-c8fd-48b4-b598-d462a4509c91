package xy.server.purchase.entity.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @data 2024-11-06 10:29
 * @apiNote 采购订单关联业务订单数据VO
 */
@Data
public class ErpPurchaseMgtBusinessOrderDataVO {

    @ApiModelProperty("采购订单明细guid")
    private String purchaseOrderDataGuid;

    @ApiModelProperty(value = "业务订单明细guid")
    private String businessOrderDataGuid;

    @ApiModelProperty(value = "业务订单号")
    private String businessOrderNumber;

    @ApiModelProperty(value = "业务订单外部单号(例客户PO，采购送货单号)")
    private String businessExternalTrackingNumber;

    @ApiModelProperty(value = "业务订单外部物料条码(BarCode)")
    private String businessExternalMaterialBarCode;

    @ApiModelProperty(value = "业务订单外部物料编码(例客户产品编码)")
    private String businessExternalMaterialCode;

    @ApiModelProperty(value = "业务订单外部物料名称(例客户产品名称)")
    private String businessExternalMaterialName;
}
