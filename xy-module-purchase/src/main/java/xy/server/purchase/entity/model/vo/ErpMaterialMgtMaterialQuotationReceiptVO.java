package xy.server.purchase.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
* <p>
* 物料报价表VO
* </p>
*
* <AUTHOR>
* @since 2024-05-23
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpMaterialMgtMaterialQuotationReceiptVO对象", description = "物料报价表")
public class ErpMaterialMgtMaterialQuotationReceiptVO {

    @ApiModelProperty(value = "PK")
    private String materialQuotationReceiptGuid;

    @ApiModelProperty(value = "报价类型(字典)")
    private String materialQuotationReceiptType;

    @XyTrans(dictionaryKey = "QUOTATION_TYPE",dictionaryValue = "materialQuotationReceiptType")
    @ApiModelProperty(value = "报价类型(字典)")
    private String materialQuotationReceiptTypeName;

    @ApiModelProperty(value = "物料报价单号")
    private String materialQuotationReceiptNumbers;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "客户guid or 供应商guid")
    private String customerOrSupplierGuid;

    @ApiModelProperty(value = "客户or供应商name")
    private String customerOrSupplierName;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生效时间")
    private LocalDateTime effectiveDate;

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "客户物料报价1，供应商物料报价2")
    private Integer materialQuotationReceiptProperties;

    @ApiModelProperty(value = "生效状态")
    private Boolean effectiveStatus;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    /**
     * 真实生效时间
     */
    @ApiModelProperty(value = "真实生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime realEffectiveDate;

    @ApiModelProperty(value = "报价明细")
    @XyTransCycle
    private List<ErpMaterialMgtMaterialQuotationReceiptDetailVO> detailList;

}
