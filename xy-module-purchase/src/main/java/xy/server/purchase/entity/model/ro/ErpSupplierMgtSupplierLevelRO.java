package xy.server.purchase.entity.model.ro;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelTemplate;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 供应商级别RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
@XyExcelTemplate("供应商级别模板")
@ApiModel(value = "ErpSupplierMgtSupplierLevelURO对象", description = "供应商级别")
public class ErpSupplierMgtSupplierLevelRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String supplierLevelGuid;

    @ExcelProperty("供应商级别名称")
    @NotBlank(message = "供应商级别名称不能为空")
    @ApiModelProperty(value = "供应商级别名称")
    private String supplierLevelName;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("上级供应商级别名称（可选填）")
    @NotNull
    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "已被使用")
    private Boolean isUsed;

    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("备注")
    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

}
