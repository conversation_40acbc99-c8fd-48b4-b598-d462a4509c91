package xy.server.purchase.entity.model.ro;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelTemplate;
import com.xunyue.config.excel.converter.XyExcelConverter;
import com.xunyue.config.excel.enums.ExcelPropertyTypeEnum;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 供应商地址RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
@XyExcelTemplate("供应商地址模板")
@ApiModel(value = "ErpSupplierMgtSupplierAddressURO对象", description = "供应商地址")
public class ErpSupplierMgtSupplierAddressRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String supplierAddressGuid;

    @ExcelProperty("供应商编码")
    @ApiModelProperty(value = "erp_supplier_mgt_Supplier _guid")
    private String supplierGuid;

    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @NotNull(message = "行政区域不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ApiModelProperty(value = "ERP_BasicMGT_AdministrativeArea 行政区域_guid")
    private String administrativeAreaGuid;

    @NotNull(message = "详细地址不能为空")
    @ExcelProperty("详细地址")
    @ApiModelProperty(value = "详细地址")
    private String address;

    @NotNull(message = "是否默认，只允许默认一个不能为空")
    @XyExcelProperty(type = ExcelPropertyTypeEnum.DICTIONARY, dictKey = "YES_OR_NO", isUseDefaultValue = true, boolDefaultValue = false)
    @ExcelProperty(value = "是否默认", converter = XyExcelConverter.class)
    @ApiModelProperty(value = "是否默认，只允许默认一个")
    private Boolean isDefault;

    @NotNull(message = "备注或描述不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("备注")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

}
