package xy.server.purchase.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 物料报价表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMaterialMgtMaterialQuotationReceiptQO对象", description = "物料报价表")
public class ErpMaterialMgtMaterialQuotationReceiptQO {

    @ApiModelProperty(value = "客户简称")
    private String customerName;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "报价单号")
    private String materialQuotationReceiptNumbers;

    @ApiModelProperty(value = "产品名称")
    private String materialName;

    @ApiModelProperty(value = "产品编码")
    private String materialCode;

    @ApiModelProperty(value = "产品编码")
    private List<String> materialCodeS;

    @ApiModelProperty(value = "开单开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDateTime endTime;

    @NotNull(message = "报价属性不能为空")
    @ApiModelProperty(value = "客户物料报价1，供应商物料报价2")
    private Integer materialQuotationReceiptProperties;

    @ApiModelProperty(value = "物料分类guid")
    private List<String> materialClassificationGuidS;

    @ApiModelProperty(value = "客户guid or 供应商guid")
    private String customerOrSupplierGuid;

    @ApiModelProperty(value = "物料分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "客户或者供应商货号")
    private String externalMaterialCode;
}