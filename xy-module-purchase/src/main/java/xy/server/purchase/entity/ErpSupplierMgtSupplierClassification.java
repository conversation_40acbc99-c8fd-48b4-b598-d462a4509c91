package xy.server.purchase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 供应商分类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_supplier_mgt_supplier_classification",autoResultMap = true)
public class ErpSupplierMgtSupplierClassification extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("supplier_classification_guid")
    private String supplierClassificationGuid;
    /**
     * 供应商分类名称
     */
    @TableField("supplier_classification_name")
    private String supplierClassificationName;
    /**
     * 顺序号(每一个层级有对应的顺序号)
     */
    @TableField("serial_number")
    private Integer serialNumber;
    /**
     * 父节点_guid
     */
    @TableField("parent_classification_guid")
    private String parentClassificationGuid;
    /**
     * 供应商分类属性值_guid
     */
    @TableField("supplier_classification_attribute_value")
    private String supplierClassificationAttributeValue;
    /**
     * 状态(0停用，1启用)
     */
    @TableField("state")
    private Boolean state;
    /**
     * 已被使用
     */
    @TableField("is_used")
    private Boolean isUsed;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;

    @TableField(value = "to_json",typeHandler  = JsonbTypeHandler.class)
    private Object toJson;



}
