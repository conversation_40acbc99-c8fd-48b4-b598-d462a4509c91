package xy.server.purchase.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.material.entity.model.vo.ErpMaterialSpecificationVO;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 物料报价明细表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpMaterialMgtMaterialQuotationReceiptDetailRO对象", description = "物料报价明细表")
public class ErpMaterialMgtMaterialQuotationReceiptDetailRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String materialQuotationReceiptDetailGuid;

    @ApiModelProperty(value = "报价guid")
    private String materialQuotationReceiptGuid;

    @ApiModelProperty(value = "物料分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

    @NotNull(message = "最小数量不能为空")
    @ApiModelProperty(value = "最小数量")
    private BigDecimal minQuantity;

    @NotNull(message = "最大数量不能为空")
    @ApiModelProperty(value = "最大数量")
    private BigDecimal maxQuantity;

    @NotNull(message = "含税报价单价不能为空")
    @ApiModelProperty(value = "含税报价单价")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;
    /**
     * 用于数据导入的数据检查
     */
    @ApiModelProperty(value = "规格类型值列表")
    private List<ErpMaterialSpecificationVO> specificationValues;

    @ApiModelProperty(value = "物料名")
    private String materialName;

    @ApiModelProperty(value = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "单位 _guid")
    private String unitGuid;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "是否自动编码")
    private Boolean isCode;

    @ApiModelProperty(value = "搜索码")
    private String searchCode;

    @ApiModelProperty(value = "调整幅度")
    private BigDecimal adjustmentMagnitude;

    @ApiModelProperty(value = "原含税报价单价")
    private BigDecimal oldQuotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "客户或者供应商货号")
    private String externalMaterialCode;

}
