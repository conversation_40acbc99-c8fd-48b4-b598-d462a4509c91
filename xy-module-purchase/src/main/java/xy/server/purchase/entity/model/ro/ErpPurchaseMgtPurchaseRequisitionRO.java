package xy.server.purchase.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2023/9/13 11:06
 * @apiNote 采购申请单RO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPurchaseMgtPurchaseRequisitionRO对象", description = "采购申请单RO")
public class ErpPurchaseMgtPurchaseRequisitionRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "申请类型")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "工单状态(采购情况)")
    private String workorderState;

    @ApiModelProperty(value = "工单属性")
    private Integer workorderProperties;

    @ApiModelProperty(value = "紧急程度(非常紧急，紧急，一般)")
    private String isUrgent;

    @ApiModelProperty(value = "物料guid")
    @NotBlank(message = "物料不能为空")
    private String materialGuid;

    @ApiModelProperty(value = "待采购数")
    private BigDecimal quantity;

    @ApiModelProperty(value = "数量")
    @NotNull(message = "数量不能为空")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "单位")
    @NotBlank(message = "单位不能为空")
    private String unitGuid;

    @ApiModelProperty(value = "要求交货期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime requiredDeliveryTime;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "采购申请单明细")
    @NotEmpty(message = "采购申请单明细不能为空")
    @Valid
    private List<ErpPurchaseMgtPurchaseRequisitionRO> detailList;

    @ApiModelProperty(value = "采购申请单明细删除ids")
    private List<String> delDetailIds;

    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String currentStatus;

    @ApiModelProperty(value = "客户guid")
    private String customerGuid;
}
