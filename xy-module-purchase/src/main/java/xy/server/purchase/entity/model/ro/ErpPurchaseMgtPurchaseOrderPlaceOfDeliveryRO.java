package xy.server.purchase.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 订单交货地点管理RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPurchaseMgtPurchaseOrderPlaceOfDeliveryRO对象", description = "订单交货地点管理")
public class ErpPurchaseMgtPurchaseOrderPlaceOfDeliveryRO extends BaseEntity {

    @ApiModelProperty(value = "订单表guid")
    private String orderGuid;

    @ApiModelProperty(value = "订单数据表guid")
    private String orderDataGuid;

    @ApiModelProperty(value = "PK")
    private String orderPlaceOfDeliveryGuid;

    @ApiModelProperty(value = "行政区域 _guid")
    @NotBlank(message = "行政区域不能为空")
    private String administrativeAreaGuid;

    @NotBlank(message = "详细地址不能为空")
    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @ApiModelProperty(value = "电话")
    private String telephone;

    @ApiModelProperty(value = "手机")
    private String mobilePhone;

    @ApiModelProperty(value = "传真")
    private String fax;

    @ApiModelProperty(value = "邮件")
    private String email;

    @ApiModelProperty(value = "QQ号码")
    private String qqNumber;

    @ApiModelProperty(value = "微信号码")
    private String wechatNumber;

    @ApiModelProperty(value = "是否默认")
    private Boolean isDefault;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

}
