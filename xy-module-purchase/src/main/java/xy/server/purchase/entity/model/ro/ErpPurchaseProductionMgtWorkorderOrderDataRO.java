package xy.server.purchase.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* <p>
* 工单订单数据表RO
* </p>
*
* <AUTHOR>
* @since 2023-11-08
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpProductionMgtWorkorderOrderDataRO对象", description = "工单订单数据表")
public class ErpPurchaseProductionMgtWorkorderOrderDataRO extends BaseEntity {

        @ApiModelProperty(value = "PK")
    private String workorderOrderDataGuid;

        @ApiModelProperty(value = "工单总表_guid")
    private String workorderGuid;

        @ApiModelProperty(value = "订单数据表guid")
    private String orderDataGuid;

}
