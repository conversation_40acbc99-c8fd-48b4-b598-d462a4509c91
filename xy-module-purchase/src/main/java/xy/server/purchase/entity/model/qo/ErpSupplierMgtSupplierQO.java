package xy.server.purchase.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 供应商QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpSupplierMgtSupplierQO对象", description = "供应商")
public class ErpSupplierMgtSupplierQO {

    @ApiModelProperty(value = "PK")
    private String supplierGuid;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商简称/全称")
    private String supplierName;

    @ApiModelProperty(value = "供应商简称")
    private String supplierShortName;

    @ApiModelProperty(value = "供应商全称")
    private String supplierFullName;

    @ApiModelProperty(value = "供应商分类属性")
    private List<String> supplierClassificationAttributeValue;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "状态列表(0停用，1启用)")
    private List<Boolean> states;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean type;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "关键字列表")
    private List<String> keywords;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "审核状态")
    private List<String> auditStatus;

    @ApiModelProperty(value = "审核时间")
    private Date auditDate;

    @ApiModelProperty(value = "供应商级别Guid")
    private String supplierLevelGuid;

    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;

    @ApiModelProperty("供应商级别Guids")
    private List<String> supplierLevelGuids;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("联系人名称")
    private String contactName;

    @ApiModelProperty("联系人手机/电话")
    private String contactPhone;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "供应商分类Guids")
    private List<String> supplierClassificationGuids;

    @ApiModelProperty(value = "PK")
    private List<String> supplierGuids;
}
