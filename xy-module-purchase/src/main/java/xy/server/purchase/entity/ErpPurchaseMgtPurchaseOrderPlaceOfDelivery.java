package xy.server.purchase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 订单交货地点管理
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_business_mgt_order_place_of_delivery", autoResultMap = true)
public class ErpPurchaseMgtPurchaseOrderPlaceOfDelivery extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 订单表guid
     */
    @TableField("order_guid")
    private String orderGuid;
    /**
     * 订单数据表guid
     */
    @TableField("order_data_guid")
    private String orderDataGuid;
    /**
     * PK
     */
    @TableId("order_place_of_delivery_guid")
    private String orderPlaceOfDeliveryGuid;
    /**
     * 行政区域 _guid
     */
    @TableField("administrative_area_guid")
    private String administrativeAreaGuid;
    /**
     * 详细地址
     */
    @TableField("address")
    private String address;
    /**
     * 联系人姓名
     */
    @TableField("contact_name")
    private String contactName;
    /**
     * 电话
     */
    @TableField("telephone")
    private String telephone;
    /**
     * 手机
     */
    @TableField("mobile_phone")
    private String mobilePhone;
    /**
     * 传真
     */
    @TableField("fax")
    private String fax;
    /**
     * 邮件
     */
    @TableField("email")
    private String email;
    /**
     * QQ号码
     */
    @TableField("qq_number")
    private String qqNumber;
    /**
     * 微信号码
     */
    @TableField("wechat_number")
    private String wechatNumber;
    /**
     * 是否默认
     */
    @TableField("is_default")
    private Boolean isDefault;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 扩展字段
     */
    @TableField(value = "to_json", typeHandler  = JsonbTypeHandler.class)
    private Object toJson;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;


}
