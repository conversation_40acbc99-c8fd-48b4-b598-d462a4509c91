package xy.server.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.enums.CompletionStatusEnum;
import com.xunyue.common.enums.FormSourceEnum;
import com.xunyue.common.enums.PurchaseStateEnum;
import com.xunyue.common.enums.WorkorderPropertiesEnum;
import com.xunyue.common.i18n.BaseResultErrorCodeImpl;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import com.xunyue.config.util.SpringBeanUtil;
import com.xunyue.exteriororder.entity.model.dto.PurFrtDTO;
import com.xunyue.exteriororder.entity.model.dto.PurRequDTO;
import com.xunyue.exteriororder.service.IPurFrtService;
import com.xunyue.exteriororder.service.IPurRequService;
import com.xunyue.node.common.enums.ClazzEnum;
import com.xunyue.node.common.enums.TypeEnum;
import com.xunyue.node.entity.Node;
import com.xunyue.node.entity.model.dto.LinkDTO;
import com.xunyue.node.service.ILinkService;
import com.xunyue.node.service.INodeService;
import com.xunyue.proof.entity.model.dto.ProofUnitQuantityDTO;
import com.xunyue.proof.service.IProofUnitQuantityService;
import com.xy.util.BaseContext;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import xy.server.activiti.common.enums.BusinessStatusEnum;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.entity.model.ro.ActAuditProcessRO;
import xy.server.activiti.service.IErpFormPendingNumberService;
import xy.server.activiti.service.IProcessInstanceService;
import xy.server.activiti.utils.actEvent.ActEventEntity;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.activiti.utils.formPendingEvent.FormPendingEventSource;
import xy.server.filter.enums.TableIdentificationEnum;
import xy.server.material.entity.ErpMaterialMgtMaterial;
import xy.server.material.service.IErpMaterialMgtMaterialService;
import xy.server.purchase.entity.ErpProductionMgtProductionForecast;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseRequisition;
import xy.server.purchase.entity.model.qo.ErpProductionMgtProductionForecastQO;
import xy.server.purchase.entity.model.ro.ErpProductionMgtProductionForecastRO;
import xy.server.purchase.entity.model.ro.ErpPurchaseMgtPurchaseRequisitionRO;
import xy.server.purchase.entity.model.vo.ErpProductionMgtProductionForecastVO;
import xy.server.purchase.i18n.ResultErrorCode;
import xy.server.purchase.mapper.ErpProductionMgtProductionForecastMapper;
import xy.server.purchase.service.IErpProductionMgtProductionForecastService;
import xy.server.purchase.service.IErpPurchaseMgtPurchaseRequisitionService;
import xy.server.work.entity.ErpProductionMgtWorkorder;
import xy.server.work.service.IErpProductionMgtWorkorderService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-10
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service("WORK_ORDER.PRODUCTION_FORECAST")
public class ErpProductionMgtProductionForecastServiceImpl extends ServiceImpl<ErpProductionMgtProductionForecastMapper, ErpProductionMgtProductionForecast>
        implements IErpProductionMgtProductionForecastService, ActEventStrategyService {

    private final IErpSystemMgtOrderSerialNumberService iErpSystemMgtOrderSerialNumberService;
    private final IErpProductionMgtWorkorderService iErpProductionMgtWorkorderService;
    private final IErpFormPendingNumberService iErpFormPendingNumberService;
    private final IErpMaterialMgtMaterialService materialService;
    private final IPurFrtService purchaseForecastingService;
    private final IProofUnitQuantityService proofUnitQuantityService;
    private final ILinkService linkService;
    private final IErpPurchaseMgtPurchaseRequisitionService requisitionService;
    private final IPurRequService purchaseRequisitionService;
    private final IProcessInstanceService processInstanceService;
    private final INodeService nodeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(ErpProductionMgtProductionForecastRO ro) {
        String creator = BaseContext.getCreator();
        String staffShortName = BaseContext.getStaffShortName();
        String creatorGuid = BaseContext.getCreatorGuid();
        String tenantGuid = BaseContext.getTenantGuid();
        String staffGuid = BaseContext.getStaffGuid();
        ErpProductionMgtProductionForecast entity = new ErpProductionMgtProductionForecast();
        BeanUtil.copyProperties(ro, entity);
        entity.setWorkorderNumber(iErpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.PRODUCTION_FORECAST));
        if (entity.getReceiptDate() == null) {
            entity.setReceiptDate(LocalDateTime.now());
        }
        entity.setWorkorderProperties(WorkorderPropertiesEnum.PRODUCTION_FORECAST.getCode());

        if (super.save(entity)) {
            // 保存明细列表
            List<ErpProductionMgtProductionForecast> productionForecastList = this.saveDetailList(entity, ro.getDetailList(), ro.getDelDetailGuids());
            // 更新待开数量
            iErpFormPendingNumberService.updatePendingNumber(WorkflowKeyEnum.ORDER_PURCHASE, WorkflowKeyEnum.WORK_ORDER_PURCHASE,
                    baseMapper.selectCountPendingList());
            if (com.xunyue.common.util.StringUtils.isEmpty(ro.getAuditStatus()) || !ro.getAuditStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
                // 启动审核流程
                iProcessInstanceService.start(WorkflowKeyEnum.WORK_ORDER_PRODUCTION_FORECAST, entity.getWorkorderGuid());
            }
            //双写业务预测数据到proof
            ThreadUtil.execAsync(() -> {
                try {
                    BaseContext.setCreator(creator);
                    BaseContext.setCreatorGuid(creatorGuid);
                    BaseContext.setTenantGuid(tenantGuid);
                    BaseContext.setStaffGuid(staffGuid);
                    BaseContext.setStaffShortName(staffShortName);
                    IErpProductionMgtProductionForecastService productionForecastService = SpringBeanUtil.getBean(IErpProductionMgtProductionForecastService.class);
                    productionForecastService.sycForecastingToProof(entity, productionForecastList, true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            return entity. getWorkorderGuid();
        }
        return entity.getWorkorderGuid();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String workorderGuid) {
//        this.operationCheck(workorderGuid, false);
        super.removeById(workorderGuid);
        List<String> detailGuids = baseMapper.selectList(Wrappers.<ErpProductionMgtProductionForecast>lambdaQuery()
                        .eq(ErpProductionMgtProductionForecast::getParentClassificationGuid, workorderGuid))
                .stream().map(ErpProductionMgtProductionForecast::getWorkorderGuid).collect(Collectors.toList());
        this.deleteDetailList(detailGuids, true);
        // 更新待开数量
        iErpFormPendingNumberService.updatePendingNumber(WorkflowKeyEnum.ORDER_PURCHASE, WorkflowKeyEnum.WORK_ORDER_PURCHASE,
                baseMapper.selectCountPendingList());
        // 删除流程相关数据
        iProcessInstanceService.deleteProcessAndHisInst(workorderGuid, WorkflowKeyEnum.WORK_ORDER_PRODUCTION_FORECAST);
        return true;
    }

    /**
     * 待审核
     *
     * @param processVO
     */
    @Override
    public void waitingCall(ActEventEntity processVO) {
        ActEventStrategyService.super.waitingCall(processVO);
        //获取采购申请的主表数据
        ErpPurchaseMgtPurchaseRequisition requisitionRO = baseMapper.getPurchaseRequisition(processVO.getBusinessKey());
        if (Objects.nonNull(requisitionRO) && BusinessStatusEnum.DRAFT.getStatus().equals(requisitionRO.getAuditStatus())) {
            ActAuditProcessRO eventEntity = new ActAuditProcessRO();
            eventEntity.setBusinessKey(requisitionRO.getWorkorderGuid());
            eventEntity.setFormSourceKey(WorkflowKeyEnum.WORK_ORDER_PURCHASE.getFormSourceKey());
            eventEntity.setFormSourceTypeValue(WorkflowKeyEnum.WORK_ORDER_PURCHASE.getFormSourceTypeValue());
            eventEntity.setProcessInstanceId(requisitionRO.getProcessInstanceId());
            eventEntity.setComment("同步提交");
            processInstanceService.commitAudit(eventEntity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> workorderGuids) {
        workorderGuids.forEach(this::delete);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String update(ErpProductionMgtProductionForecastRO ro) {
//        this.operationCheck(ro.getWorkorderGuid(), false);
        String creator = BaseContext.getCreator();
        String staffShortName = BaseContext.getStaffShortName();
        String creatorGuid = BaseContext.getCreatorGuid();
        String tenantGuid = BaseContext.getTenantGuid();
        String staffGuid = BaseContext.getStaffGuid();
        ErpProductionMgtProductionForecast entity = new ErpProductionMgtProductionForecast();
        BeanUtil.copyProperties(ro, entity);
        if (entity.getReceiptDate() == null) {
            entity.setReceiptDate(LocalDateTime.now());
        }
        if (super.updateById(entity)) {
            // 保存明细列表
            List<ErpProductionMgtProductionForecast> productionForecastList = this.saveDetailList(entity, ro.getDetailList(), ro.getDelDetailGuids());
            // 更新待开数量
            iErpFormPendingNumberService.updatePendingNumber(WorkflowKeyEnum.ORDER_PURCHASE, WorkflowKeyEnum.WORK_ORDER_PURCHASE,
                    baseMapper.selectCountPendingList());
            //双写业务预测数据到proof
            ThreadUtil.execAsync(() -> {
                try {
                    BaseContext.setCreator(creator);
                    BaseContext.setCreatorGuid(creatorGuid);
                    BaseContext.setTenantGuid(tenantGuid);
                    BaseContext.setStaffGuid(staffGuid);
                    BaseContext.setStaffShortName(staffShortName);
                    IErpProductionMgtProductionForecastService productionForecastService = SpringBeanUtil.getBean(IErpProductionMgtProductionForecastService.class);
                    productionForecastService.sycForecastingToProof(entity, productionForecastList, false);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            return entity.getWorkorderGuid();
        }
        return entity.getWorkorderGuid();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveDate(InsertOrUpdateList<ErpProductionMgtProductionForecastRO> dataList) {
        List<String> workorderGuids = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(s->{
                String id = this.create(s);
                workorderGuids.add(id);
            });
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(s->{
                String id = this.update(s);
                workorderGuids.add(id);
            });
        }
        return workorderGuids;
    }

    @Override
    public ErpProductionMgtProductionForecastVO getDataById(String workorderGuid) {
        ErpProductionMgtProductionForecastVO forecastVO = baseMapper.getDataByGuid(workorderGuid);
        detailData(Arrays.asList(forecastVO));
        return forecastVO;
    }

    @Override
    public List<ErpProductionMgtProductionForecastVO> findList(ErpProductionMgtProductionForecastQO qo) {
        List<ErpProductionMgtProductionForecastVO> list = baseMapper.findList(qo);
        detailData(list);
        return list;
    }

    private void detailData(List<ErpProductionMgtProductionForecastVO> list) {
        if (CollectionUtil.isNotEmpty(list)) {
            //批量获取明细数据
            List<String> collect = list.stream().map(ErpProductionMgtProductionForecastVO::getWorkorderGuid).collect(Collectors.toList());
            List<ErpProductionMgtProductionForecastVO> detailsList = baseMapper.selectDetailByIds(collect);
            if (!CollectionUtil.isEmpty(detailsList)){
                materialService.loadMaterialObj(detailsList, ErpProductionMgtProductionForecastVO::getMaterialGuid, ErpProductionMgtProductionForecastVO::setMaterialObj, null);
                //批量获取明细来源数据
                List<String> workGuids = detailsList.stream().map(ErpProductionMgtProductionForecastVO::getWorkorderGuid).collect(Collectors.toList());
                List<ErpProductionMgtProductionForecastVO> sourceList = baseMapper.selectSourceDetailByGuids(workGuids);
                if (CollectionUtil.isNotEmpty(sourceList)){
                    materialService.loadMaterialObj(sourceList, ErpProductionMgtProductionForecastVO::getMaterialGuid, ErpProductionMgtProductionForecastVO::setMaterialObj, null);
                    Map<String, List<ErpProductionMgtProductionForecastVO>> map = sourceList.stream().collect(Collectors.groupingBy(ErpProductionMgtProductionForecastVO::getParentClassificationGuid));
                    detailsList.forEach(s->{
                        List<ErpProductionMgtProductionForecastVO> source = map.get(s.getWorkorderGuid());
                        if (CollectionUtil.isNotEmpty(source)) {
                            s.setSourceDetailList(source);
                        }
                    });
                }
                Map<String, List<ErpProductionMgtProductionForecastVO>> map = detailsList.stream().collect(Collectors.groupingBy(ErpProductionMgtProductionForecastVO::getParentClassificationGuid));
                list.forEach(s -> {
                    List<ErpProductionMgtProductionForecastVO> vos = map.get(s.getWorkorderGuid());
                    if (!CollectionUtil.isEmpty(vos)) {
                        s.setDetailList(vos);
                    }
                });
            }
        }
    }

    @Override
    public IPage<ErpProductionMgtProductionForecastVO> findPage(PageParams<ErpProductionMgtProductionForecastQO> pageParams) {
        IPage<ErpProductionMgtProductionForecastVO> page = pageParams.buildPage();
        ErpProductionMgtProductionForecastQO model = pageParams.getModel();
        IPage<ErpProductionMgtProductionForecastVO> page1 = baseMapper.findPage(page, model);
        List<ErpProductionMgtProductionForecastVO> list = page1.getRecords();
        detailData(list);
        page1.setRecords(list);
        return page1;
    }

    /**
     * 查询待开采购预测的业务预测列表
     *
     * @param qo
     * @return
     */
    @Override
    public List<ErpProductionMgtProductionForecastVO> findNotBilledList(ErpProductionMgtProductionForecastQO qo) {
        List<ErpProductionMgtProductionForecastVO> list = baseMapper.findNotBilledList(qo);
        materialService.loadMaterialObj(list, ErpProductionMgtProductionForecastVO::getMaterialGuid, ErpProductionMgtProductionForecastVO::setMaterialObj, null);
        return list;
    }

    @Override
    public void sycForecastingToProof(ErpProductionMgtProductionForecast entity,
                                      List<ErpProductionMgtProductionForecast> productionForecastList, Boolean isSave) {
        if (CollectionUtil.isEmpty(productionForecastList)) {
            return;
        }
        //根据采购预测的物料id获取单位数据
        List<ErpProductionMgtProductionForecast> productionForecasts = new ArrayList<>();
        List<ErpMaterialMgtMaterial> materialList = materialService.listByIds(productionForecastList.stream().
                map(ErpProductionMgtProductionForecast::getMaterialGuid).distinct().collect(Collectors.toList()));
        Map<String, ErpMaterialMgtMaterial> materialMap = materialList.stream().collect(Collectors.toMap(ErpMaterialMgtMaterial::getMaterialGuid, material -> material));
        //批量删除单位数量关联表
        List<String> parentIds = productionForecastList.stream().map(ErpProductionMgtProductionForecast::getWorkorderGuid).distinct().collect(Collectors.toList());
        ProofUnitQuantityDTO quantityDTO = new ProofUnitQuantityDTO();
        quantityDTO.setProofUnitQuantityParentIds(parentIds);
        List<ProofUnitQuantityDTO> quantityDTOList = proofUnitQuantityService.findList(quantityDTO);
        if (CollectionUtil.isNotEmpty(quantityDTOList)) {
            proofUnitQuantityService.removeByIds(quantityDTOList.stream().map(ProofUnitQuantityDTO::getProofUnitQuantityId).collect(Collectors.toList()));
        }
        //同步采购预测数据到proof
        List<PurFrtDTO> purchaseForecastingList = new ArrayList<>();
        List<ProofUnitQuantityDTO> proofUnitQuantityList = new ArrayList<>();
        List<LinkDTO> linkDTOS = new ArrayList<>();
        List<String> workorderGuids = new ArrayList<>();
        PurFrtDTO purchaseForecasting = new PurFrtDTO();
        purchaseForecasting.setPurFrtUserCode(entity.getWorkorderNumber());
        purchaseForecasting.setPurFrtReceiptDate(Date.from(entity.getReceiptDate().atZone(ZoneId.systemDefault()).toInstant()));
        purchaseForecasting.setPurFrtId(entity.getWorkorderGuid());
        purchaseForecasting.setPurFrtTenantId(entity.getTenantGuid());
        purchaseForecasting.setPurFrtClazz(ClazzEnum.PURCHASE_FORECASTING.name());
        purchaseForecastingService.saveOrUpdate(purchaseForecasting);
        productionForecastList.forEach(productionForecast -> {
            if (entity.getWorkorderGuid().equals(productionForecast.getParentClassificationGuid())) {
                PurFrtDTO purchaseForecastingDTO = new PurFrtDTO();
                purchaseForecastingDTO.setPurFrtMaterialId(productionForecast.getMaterialGuid());
                purchaseForecastingDTO.setPurFrtQuantity(productionForecast.getQuantity());
                purchaseForecastingDTO.setPurFrtId(productionForecast.getWorkorderGuid());
                purchaseForecastingDTO.setPurFrtParentId(productionForecast.getParentClassificationGuid());
                purchaseForecastingDTO.setPurFrtTenantId(entity.getTenantGuid());
                purchaseForecastingDTO.setPurFrtPurchaseRequisitionQuantity(productionForecast.getQuantity());
                purchaseForecastingDTO.setPurFrtPurchaseRequisitionState(Integer.valueOf(PurchaseStateEnum.FULL.getValue()));
                if (Objects.nonNull(productionForecast.getToJson())) {
                    purchaseForecastingDTO.setPurFrtExtension(productionForecast.getToJson().toString());
                }
                purchaseForecastingDTO.setPurFrtClazz(ClazzEnum.PURCHASE_FORECASTING_DET.name());
                purchaseForecastingDTO.setPurFrtPurchaseRequisitionQuantity(productionForecast.getQuantity());
                purchaseForecastingDTO.setPurFrtPurchaseRequisitionState(Integer.valueOf(PurchaseStateEnum.FULL.getValue()));
                List<ProofUnitQuantityDTO> proofUnitQuantityDTOS = new ArrayList<>();
                //根据物料id获取单位id
                ProofUnitQuantityDTO unitQuantityDTO = new ProofUnitQuantityDTO();
                purchaseForecastingDTO.setPurFrtUnitCode(materialMap.get(productionForecast.getMaterialGuid()).getUnitGuid());
                unitQuantityDTO.setProofUnitQuantityQuantity(productionForecast.getQuantity());
                unitQuantityDTO.setProofUnitQuantityId(IdWorker.getIdStr());
                unitQuantityDTO.setProofUnitQuantityUnitId(purchaseForecastingDTO.getPurFrtUnitCode());
                unitQuantityDTO.setProofUnitQuantityParentId(productionForecast.getWorkorderGuid());
                unitQuantityDTO.setProofUnitQuantityClazz(ClazzEnum.PROOF_UNIT_QUANTITY.name());
                proofUnitQuantityDTOS.add(unitQuantityDTO);
                if (Objects.nonNull(productionForecast.getToJson())) {
                    String jsonStr = JSON.toJSONString(productionForecast.getToJson());
                    JSONObject jsonObject = JSONObject.parseObject(jsonStr);
                    if (Objects.nonNull(jsonObject) && jsonObject.containsKey("quantityPerItem")) {
                        ProofUnitQuantityDTO unitQuantityDTO1 = new ProofUnitQuantityDTO();
                        unitQuantityDTO1.setProofUnitQuantityQuantity(new BigDecimal(String.valueOf(jsonObject.get("quantityPerItem"))));
                        unitQuantityDTO1.setProofUnitQuantityUnitId("7caebe79177d9e07ee51ffdd35013b4a");
                        unitQuantityDTO1.setProofUnitQuantityParentId(productionForecast.getWorkorderGuid());
                        unitQuantityDTO1.setProofUnitQuantityId(IdWorker.getIdStr());
                        unitQuantityDTO1.setProofUnitQuantityClazz(ClazzEnum.PROOF_UNIT_QUANTITY.name());
                        proofUnitQuantityDTOS.add(unitQuantityDTO1);
                    }
                }
                proofUnitQuantityList.addAll(proofUnitQuantityDTOS);
                productionForecasts.add(productionForecast);
                purchaseForecastingList.add(purchaseForecastingDTO);
                //获取当前明细的明细id数据
                List<ErpProductionMgtProductionForecast> forecastChildList = productionForecastList.stream().
                        filter(p -> p.getParentClassificationGuid().equals(productionForecast.
                        getWorkorderGuid())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(forecastChildList)) {
                    workorderGuids.add(productionForecast.getWorkorderGuid());
                    forecastChildList.forEach(forecastChild -> {
                        LinkDTO linkDTO = new LinkDTO();
                        //处理来源link连接
                        linkDTO.setLinkTarget(productionForecast.getWorkorderGuid());
                        linkDTO.setLinkId(IdWorker.getIdStr());
                        linkDTO.setLinkSource(forecastChild.getSourceGuid());
                        linkDTO.setLinkTypeCode(TypeEnum.SOURCE.getCode());
                        linkDTO.setLinkClazz(ClazzEnum.MARKET_FORECASTING_DET.name()+ StringPool.DASH+ClazzEnum.PURCHASE_FORECASTING_DET.name());
                        linkDTO.setLinkIsolation(entity.getTenantGuid());
                        linkDTOS.add(linkDTO);
                    });
                }
            }
        });
        purchaseForecastingService.saveOrUpdateBatch(purchaseForecastingList);
        if (CollectionUtil.isNotEmpty(workorderGuids)){
            linkService.removeBatchBySourceAndClazz( ClazzEnum.MARKET_FORECASTING_DET.name()+ StringPool.DASH+ClazzEnum.PURCHASE_FORECASTING_DET.name(),workorderGuids,entity.getTenantGuid());
        }
        linkService.saveOrUpdateBatch(linkDTOS);
        proofUnitQuantityService.saveOrUpdateBatch(proofUnitQuantityList);
        purchaseForecasting.setPurFrtVersion(2);
        if (isSave) {
            purchaseForecastingService.update(purchaseForecasting);
        }
        IErpProductionMgtProductionForecastService productionForecastService = SpringBeanUtil.getBean(IErpProductionMgtProductionForecastService.class);
        productionForecastService.sycPurchaseRequistion(entity, productionForecasts);
    }

    @Override
    public void sycPurchaseRequistion(ErpProductionMgtProductionForecast entity, List<ErpProductionMgtProductionForecast> productionForecastList) {
        //组装采购申请保存的数据
        ErpPurchaseMgtPurchaseRequisitionRO purchaseRequisitionRO = new ErpPurchaseMgtPurchaseRequisitionRO();
        purchaseRequisitionRO.setReceiptDate(entity.getReceiptDate());
        purchaseRequisitionRO.setSourceValue(4);
        purchaseRequisitionRO.setWorkorderTypeGuid("3");
        purchaseRequisitionRO.setDescription(entity.getDescription());
        purchaseRequisitionRO.setWorkorderNumber(entity.getWorkorderNumber());
        List<ErpPurchaseMgtPurchaseRequisitionRO> purchaseRequisitionList = new ArrayList<>();
        productionForecastList.forEach(productionForecast -> {
            ErpPurchaseMgtPurchaseRequisitionRO mgtPurchaseRequisitionRO = new ErpPurchaseMgtPurchaseRequisitionRO();
            mgtPurchaseRequisitionRO.setSourceGuid(productionForecast.getWorkorderGuid());
            mgtPurchaseRequisitionRO.setMaterialGuid(productionForecast.getMaterialGuid());
            mgtPurchaseRequisitionRO.setQuantity(productionForecast.getQuantity());
            mgtPurchaseRequisitionRO.setTotalQuantity(productionForecast.getTotalQuantity());
            mgtPurchaseRequisitionRO.setDescription(productionForecast.getDescription());
            mgtPurchaseRequisitionRO.setSerialNumber(productionForecast.getSerialNumber());
            mgtPurchaseRequisitionRO.setToJson(productionForecast.getToJson());
            mgtPurchaseRequisitionRO.setWorkorderNumber(entity.getWorkorderNumber());
            purchaseRequisitionList.add(mgtPurchaseRequisitionRO);
        });
        purchaseRequisitionRO.setDetailList(purchaseRequisitionList);
        List<String> purchaseForecastIds = productionForecastList.stream().map(ErpProductionMgtProductionForecast::getWorkorderGuid).distinct().collect(Collectors.toList());
        LinkDTO linkDTO1 = new LinkDTO();
        linkDTO1.setLinkSourceIds(purchaseForecastIds);
        linkDTO1.setLinkTypeCode(TypeEnum.SOURCE.getCode());
        List<LinkDTO> linkDTOList = linkService.findList(linkDTO1);
        if (CollectionUtil.isNotEmpty(linkDTOList)) {
            Map<String, List<LinkDTO>> linkMap = linkDTOList.stream().collect(Collectors.groupingBy(LinkDTO::getLinkSource));
            List<String> purchaseRequisitionIds1 = linkDTOList.stream().map(LinkDTO::getLinkTarget).distinct().collect(Collectors.toList());
            //根据采购预测明细id获取主表的ids
            List<PurRequDTO> requisitionDTOList = purchaseRequisitionService.getByIds(purchaseRequisitionIds1);
            if (CollectionUtil.isNotEmpty(requisitionDTOList)) {
                List<String> requisitionIds = requisitionDTOList.stream().map(PurRequDTO::getPurRequParentId).collect(Collectors.toList());
                List<PurRequDTO> requisitionParentDTOList = purchaseRequisitionService.getByIds(requisitionIds);
                //组装数据
                purchaseRequisitionRO.setWorkorderGuid(requisitionParentDTOList.get(0).getPurRequId());
                purchaseRequisitionRO.setWorkorderNumber(requisitionParentDTOList.get(0).getPurRequUserCode());
                purchaseRequisitionRO.getDetailList().forEach(detail -> {
                    //根据来源id获取采购申请的数据
                    List<LinkDTO> linkDTOS = linkMap.get(detail.getSourceGuid());
                    if (CollectionUtil.isNotEmpty(linkDTOS)) {
                        detail.setWorkorderGuid(linkDTOS.get(0).getLinkTarget());
                    }
                });
            }
        } else {
            purchaseRequisitionRO.setAuditStatus(BusinessStatusEnum.FINISH.getStatus());
        }
        if (StringUtils.isEmpty(purchaseRequisitionRO.getWorkorderGuid())) {
            requisitionService.create(purchaseRequisitionRO);
        } else {
            requisitionService.updateNotCheck(purchaseRequisitionRO);
        }
    }

    @Override
    public Boolean sycDeleteForecastingToProof(List<String> delDetailGuids, Boolean isDeleteAll) {
        if (CollectionUtil.isEmpty(delDetailGuids)) {
            return Boolean.FALSE;
        }
        //删除link关联数据
        linkService.removeByTargets(delDetailGuids, TypeEnum.SOURCE);
        //删除采购预测数据
        purchaseForecastingService.removeBatch(delDetailGuids);
        //删除proof单位数据
        ProofUnitQuantityDTO quantityDTO = new ProofUnitQuantityDTO();
        quantityDTO.setProofUnitQuantityParentIds(delDetailGuids);
        quantityDTO.setProofUnitQuantityClazz(ClazzEnum.PROOF_UNIT_QUANTITY.name());
        List<ProofUnitQuantityDTO> quantityDTOList = proofUnitQuantityService.findList(quantityDTO);
        if (CollectionUtil.isNotEmpty(quantityDTOList)) {
            List<String> quantityIds = quantityDTOList.stream().map(ProofUnitQuantityDTO::getProofUnitQuantityId).collect(Collectors.toList());
            proofUnitQuantityService.removeBatch(quantityIds);

        }
        LinkDTO linkDTO1 = new LinkDTO();
        linkDTO1.setLinkSourceIds(delDetailGuids);
        linkDTO1.setLinkTypeCode(TypeEnum.SOURCE.getCode());
        List<LinkDTO> linkDTOList = linkService.findList(linkDTO1);
        if (CollectionUtil.isNotEmpty(linkDTOList)) {
            List<String> purchaseRequisitionIds1 = linkDTOList.stream().map(LinkDTO::getLinkSource).distinct().collect(Collectors.toList());
            //根据采购预测明细id获取主表的ids
            List<PurRequDTO> requisitionDTOList = purchaseRequisitionService.getByIds(purchaseRequisitionIds1);
            if (CollectionUtil.isNotEmpty(requisitionDTOList)) {
                List<String> requisitionIds = requisitionDTOList.stream().map(PurRequDTO::getPurRequParentId).collect(Collectors.toList());
                if (isDeleteAll) {
                    requisitionService.delete(requisitionIds.get(0));
                    return true;
                }
                requisitionService.removeByIds(purchaseRequisitionIds1);
                purchaseRequisitionService.removeBatch(requisitionIds);
                linkService.removeBatch(linkDTOList.stream().map(LinkDTO::getLinkId).collect(Collectors.toList()));
                //删除proof单位数据
                ProofUnitQuantityDTO quantityDTO1 = new ProofUnitQuantityDTO();
                quantityDTO1.setProofUnitQuantityParentIds(purchaseRequisitionIds1);
                quantityDTO1.setProofUnitQuantityClazz(ClazzEnum.PROOF_UNIT_QUANTITY.name());
                List<ProofUnitQuantityDTO> quantityDTOList1 = proofUnitQuantityService.findList(quantityDTO);
                if (CollectionUtil.isNotEmpty(quantityDTOList1)) {
                    List<String> quantityIds = quantityDTOList.stream().map(ProofUnitQuantityDTO::getProofUnitQuantityId).collect(Collectors.toList());
                    proofUnitQuantityService.removeBatch(quantityIds);
                }
                return true;
            }
        }
        return false;
    }


    /**
     * 重启流程时事件
     *
     * @param processVO
     */
    @Override
    public void restartCall(ActEventEntity processVO) {
        //更新采购预测主表审核状态
        Node node = nodeService.getById(processVO.getBusinessKey());
        if (BeanUtil.isNotEmpty(node)) {
            node.setAuditDate(new Date());
            node.setAuditStatus(BusinessStatusEnum.DRAFT.getStatus());
            nodeService.updateById(node);
        }
//        this.operationCheck(processVO.getBusinessKey(), true);
        // 重启流程后，下流程（采购订单）待开数量-1
        iErpFormPendingNumberService.minusOne(WorkflowKeyEnum.ORDER_PURCHASE, WorkflowKeyEnum.WORK_ORDER_PRODUCTION_FORECAST,
                TableIdentificationEnum.erp_purchasing_mgt_application_wait_order);
        //获取采购申请的主表数据
        ErpPurchaseMgtPurchaseRequisition requisitionRO = baseMapper.getPurchaseRequisition(processVO.getBusinessKey());
        if (Objects.nonNull(requisitionRO)) {
            ActAuditProcessRO eventEntity = new ActAuditProcessRO();
            eventEntity.setBusinessKey(requisitionRO.getWorkorderGuid());
            eventEntity.setProcessInstanceId(requisitionRO.getProcessInstanceId());
            eventEntity.setFormSourceKey(WorkflowKeyEnum.WORK_ORDER_PURCHASE.getFormSourceKey());
            eventEntity.setFormSourceTypeValue(WorkflowKeyEnum.WORK_ORDER_PURCHASE.getFormSourceTypeValue());
            eventEntity.setComment("同步重启流程");
            processInstanceService.restartProcess(eventEntity);
        }
    }

    @Override
    public void finishCall(ActEventEntity processVO) {
        //更新采购预测主表审核状态
        Node node = nodeService.getById(processVO.getBusinessKey());
        if (BeanUtil.isNotEmpty(node)) {
            node.setAuditDate(new Date());
            node.setAuditStatus(BusinessStatusEnum.FINISH.getStatus());
            nodeService.updateById(node);
        }
        // 审核完成后，下流程（采购订单）待开数量+1
        iErpFormPendingNumberService.plusOne(WorkflowKeyEnum.ORDER_PURCHASE, WorkflowKeyEnum.WORK_ORDER_PRODUCTION_FORECAST,
                TableIdentificationEnum.erp_purchasing_mgt_application_wait_order);
        //获取采购申请的主表数据
        ErpPurchaseMgtPurchaseRequisition requisitionRO = baseMapper.getPurchaseRequisition(processVO.getBusinessKey());
        if (Objects.nonNull(requisitionRO)) {
            ActAuditProcessRO eventEntity = new ActAuditProcessRO();
            eventEntity.setBusinessKey(requisitionRO.getWorkorderGuid());
            eventEntity.setProcessInstanceId(requisitionRO.getProcessInstanceId());
            eventEntity.setFormSourceKey(WorkflowKeyEnum.WORK_ORDER_PURCHASE.getFormSourceKey());
            eventEntity.setFormSourceTypeValue(WorkflowKeyEnum.WORK_ORDER_PURCHASE.getFormSourceTypeValue());
            eventEntity.setComment("同步审核流程");
            processInstanceService.audit(true, eventEntity);
        }
    }

    /**
     * 已撤销（上一节点撤销到下一节点）
     *
     * @param processVO
     */
    @Override
    public void cancelCall(ActEventEntity processVO) {
        ActEventStrategyService.super.cancelCall(processVO);
        //获取采购申请的主表数据
        ErpPurchaseMgtPurchaseRequisition requisitionRO = baseMapper.getPurchaseRequisition(processVO.getBusinessKey());
        if (Objects.nonNull(requisitionRO)) {
            ActAuditProcessRO eventEntity = new ActAuditProcessRO();
            eventEntity.setBusinessKey(requisitionRO.getWorkorderGuid());
            eventEntity.setProcessInstanceId(requisitionRO.getProcessInstanceId());
            eventEntity.setFormSourceKey(WorkflowKeyEnum.WORK_ORDER_PURCHASE.getFormSourceKey());
            eventEntity.setFormSourceTypeValue(WorkflowKeyEnum.WORK_ORDER_PURCHASE.getFormSourceTypeValue());
            eventEntity.setComment("同步撤销提交");
            processInstanceService.cancelCommitAudit(eventEntity);
        }
    }

    /**
     * 更新待开数量事件监听
     *
     * @param formPendingEventSource
     */
    @EventListener(value = FormPendingEventSource.class)
    public void formPendingEventListener(FormPendingEventSource formPendingEventSource) {
        if (WorkflowKeyEnum.WORK_ORDER_PRODUCTION_FORECAST.equals(formPendingEventSource.getWorkflowKeyEnum())) {
            iErpFormPendingNumberService.updatePendingNumber(formPendingEventSource.getWorkflowKeyEnum(),
                    formPendingEventSource.getSourceWorkflowKeyEnum(), baseMapper.selectCountPendingList());
        }
    }

    /**
     * 编辑删除操作前检查
     *
     * @param id
     * @param isFlow 是否为流程操作
     */
    private void operationCheck(String id, boolean isFlow) {
        ErpProductionMgtProductionForecast entity = baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new FlowException(BaseResultErrorCodeImpl.NO_REPETITION_DATA);
        }
        if (!baseMapper.getPurchaseState(id).equals(PurchaseStateEnum.UN.getValue())) {
            throw new FlowException(ResultErrorCode.STATE_NOT_UN_PURCHASE);
        }
        if (!isFlow && !entity.getAuditStatus().equals(BusinessStatusEnum.DRAFT.getStatus())) {
            throw new FlowException(BaseResultErrorCodeImpl.AUDIT_STATE_NOT_DRAFT);
        }
    }

    /**
     * 保存明细列表
     *
     * @param pEntity
     * @param detailList
     * @param delDetailGuids
     */
    private List<ErpProductionMgtProductionForecast> saveDetailList(ErpProductionMgtProductionForecast pEntity, List<ErpProductionMgtProductionForecastRO> detailList, List<String> delDetailGuids) {
        // 先执行删除旧数据
        this.deleteDetailList(delDetailGuids, false);
        // 在保存最新明细数据
        if (CollectionUtil.isNotEmpty(detailList)) {
            List<ErpProductionMgtProductionForecast> detailEntityList = new ArrayList<>();
            Set<String> sourceGuids = new HashSet<>();
            detailList.forEach(detail -> {
                ErpProductionMgtProductionForecast detailEntity = BeanUtil.toBean(detail, ErpProductionMgtProductionForecast.class);
                // 如果guid不存在 则先设置guid
                if (StringUtils.isEmpty(detailEntity.getWorkorderGuid())) {
                    detailEntity.setWorkorderGuid(IdUtil.fastSimpleUUID());
                }
                // 新增时设置采购情况为未采购
                detailEntity.setWorkorderState(PurchaseStateEnum.UN.getValue());
                // 冗余父工单数据
                detailEntity.setParentClassificationGuid(pEntity.getWorkorderGuid());
                detailEntity.setWorkorderNumber(pEntity.getWorkorderNumber());
                detailEntity.setWorkorderProperties(pEntity.getWorkorderProperties());
                detailEntityList.add(detailEntity);

                // 处理来源数据列表
                // 先处理要删除的来源数据
                if (CollectionUtil.isNotEmpty(detail.getDelSourceDetailGuids())) {
                    List<ErpProductionMgtProductionForecast> sourceDetailList = baseMapper.selectBatchIds(detail.getDelSourceDetailGuids());
                    baseMapper.deleteBatchIds(detail.getDelSourceDetailGuids());
                    sourceGuids.addAll(sourceDetailList.stream().map(ErpProductionMgtProductionForecast::getSourceGuid).collect(Collectors.toSet()));
                }
                // 在保存最新数据
                if (CollectionUtil.isNotEmpty(detail.getSourceDetailList())) {
                    detail.getSourceDetailList().forEach(sourceDetail -> {
                        ErpProductionMgtProductionForecast sourceDetailEntity = new ErpProductionMgtProductionForecast();
                        // 组装第三层数据（记录来源）
                        sourceDetailEntity.setWorkorderGuid(sourceDetail.getWorkorderGuid());
                        sourceDetailEntity.setSourceGuid(sourceDetail.getSourceGuid());
                        sourceDetailEntity.setQuantity(sourceDetail.getQuantity());
                        sourceDetailEntity.setDescription(sourceDetail.getDescription());
                        sourceDetailEntity.setParentClassificationGuid(detailEntity.getWorkorderGuid());
                        sourceDetailEntity.setWorkorderNumber(detailEntity.getWorkorderNumber());
                        sourceDetailEntity.setWorkorderProperties(WorkorderPropertiesEnum.PRODUCTION_FORECAST_SOURCE_BUSINESS_FORECASTING.getCode());
                        detailEntityList.add(sourceDetailEntity);
                        sourceGuids.add(sourceDetailEntity.getSourceGuid());
                    });
                }
            });
            // 保存或更新明细数据（包含第三层的来源明细数据）
            super.saveOrUpdateBatch(detailEntityList);
            // 更新来源数据
            this.updateSourceData(sourceGuids);
            return detailEntityList;
        }
        return Collections.emptyList();
    }

    /**
     * 删除明细列表
     *
     * @param delDetailGuids
     */
    private void deleteDetailList(List<String> delDetailGuids, Boolean isDeleteAll) {
        String creator = BaseContext.getCreator();
        String staffShortName = BaseContext.getStaffShortName();
        String creatorGuid = BaseContext.getCreatorGuid();
        String tenantGuid = BaseContext.getTenantGuid();
        String staffGuid = BaseContext.getStaffGuid();
        if (CollectionUtil.isNotEmpty(delDetailGuids)) {
            List<ErpProductionMgtProductionForecast> sourceDetailList = baseMapper.selectList(Wrappers.<ErpProductionMgtProductionForecast>lambdaQuery()
                    .eq(ErpProductionMgtProductionForecast::getWorkorderProperties,
                            WorkorderPropertiesEnum.PRODUCTION_FORECAST_SOURCE_BUSINESS_FORECASTING.getCode())
                    .in(ErpProductionMgtProductionForecast::getParentClassificationGuid, delDetailGuids));
            Set<String> sourceGuids = new HashSet<>();
            if (CollectionUtil.isNotEmpty(sourceDetailList)) {
                sourceDetailList.forEach(sourceDetail -> {
                    delDetailGuids.add(sourceDetail.getWorkorderGuid());
                    sourceGuids.add(sourceDetail.getSourceGuid());
                });
            }
            // 删除明细
            baseMapper.deleteBatchIds(delDetailGuids);
            // 更新来源数据（完成状态）
            this.updateSourceData(sourceGuids);
            //双写业务预测数据到proof
            ThreadUtil.execAsync(() -> {
                BaseContext.setCreator(creator);
                BaseContext.setCreatorGuid(creatorGuid);
                BaseContext.setTenantGuid(tenantGuid);
                BaseContext.setStaffGuid(staffGuid);
                BaseContext.setStaffShortName(staffShortName);
                IErpProductionMgtProductionForecastService productionForecastService = SpringBeanUtil.getBean(IErpProductionMgtProductionForecastService.class);
                productionForecastService.sycDeleteForecastingToProof(delDetailGuids, isDeleteAll);
            });
        }
    }

    /**
     * 更新来源数据
     *
     * @param sourceGuids
     */
    private void updateSourceData(Set<String> sourceGuids) {
        if (CollectionUtil.isNotEmpty(sourceGuids)) {
            // 查询来源工单（业务预测）
            List<ErpProductionMgtWorkorder> workorderEntityList = iErpProductionMgtWorkorderService.getBaseMapper().selectBatchIds(sourceGuids);
            workorderEntityList.forEach(workorderEntity -> {
                // 查询总已开采购预测数量
                BigDecimal totalCompletedQuantity = baseMapper.getTotalCompletedQuantity(workorderEntity.getWorkorderGuid());
                // 比较业务预测数量和总已开采购预测数量
                CompletionStatusEnum completionStatusEnum = null;
                if (totalCompletedQuantity.compareTo(BigDecimal.ZERO) == 0) {
                    completionStatusEnum = CompletionStatusEnum.NOT_FINISH;
                } else if (totalCompletedQuantity.compareTo(workorderEntity.getQuantity()) == 0) {
                    completionStatusEnum = CompletionStatusEnum.ALL_FINISH;
                } else if (totalCompletedQuantity.compareTo(workorderEntity.getQuantity()) == 1) {
                    completionStatusEnum = CompletionStatusEnum.ALL_FINISH;
                } else {
                    completionStatusEnum = CompletionStatusEnum.PARTIALLY_FINISH;
                }
                // 设置申购单采购情况
                workorderEntity.setWorkorderState(completionStatusEnum.getCode());
            });
            iErpProductionMgtWorkorderService.updateBatchById(workorderEntityList);
        }
    }

}
