package xy.server.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.enums.FormSourceEnum;
import com.xunyue.common.enums.MaterialQuotationEnum;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.common.util.StringUtils;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.service.IProcessInstanceService;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.purchase.entity.ErpMaterialMgtMaterialQuotationReceipt;
import xy.server.purchase.entity.ErpMaterialMgtMaterialQuotationReceiptDetail;
import xy.server.purchase.entity.model.qo.ErpMaterialMgtMaterialQuotationReceiptQO;
import xy.server.purchase.entity.model.ro.ErpMaterialMgtMaterialQuotationReceiptDetailRO;
import xy.server.purchase.entity.model.ro.ErpMaterialMgtMaterialQuotationReceiptRO;
import xy.server.purchase.entity.model.ro.ErpMaterialMgtQuotationExceRO;
import xy.server.purchase.entity.model.vo.*;
import xy.server.purchase.i18n.ResultErrorCode;
import xy.server.purchase.mapper.ErpMaterialMgtMaterialQuotationReceiptMapper;
import xy.server.purchase.service.IErpMaterialMgtMaterialQuotationReceiptDetailService;
import xy.server.purchase.service.IErpMaterialMgtMaterialQuotationReceiptService;
import xy.server.work.entity.ErpProductionMgtExperienceProductionProcessExternal;
import xy.server.work.service.IErpProductionMgtExperienceProductionProcessExternalService;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 物料报价表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Service("MATERIAL_QUOTATION")
public class ErpMaterialMgtMaterialQuotationReceiptServiceImpl extends ServiceImpl<ErpMaterialMgtMaterialQuotationReceiptMapper, ErpMaterialMgtMaterialQuotationReceipt> implements IErpMaterialMgtMaterialQuotationReceiptService, ActEventStrategyService {

    @Autowired
    private IErpMaterialMgtMaterialQuotationReceiptDetailService detailService;
    @Autowired
    private IErpSystemMgtOrderSerialNumberService numberService;
    @Autowired
    private IProcessInstanceService iProcessInstanceService;
    @Autowired
    private IErpProductionMgtExperienceProductionProcessExternalService externalService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ErpQuotationImportVO create(ErpMaterialMgtMaterialQuotationReceiptRO ro) {
        //处理数据导入的情况，增加导入结果阐述
        ErpQuotationImportVO vo = new ErpQuotationImportVO();
        int importNoQuantity = 0;
        int importQuantity = 0;
        List<String> list = new ArrayList<>();
        ErpMaterialMgtMaterialQuotationReceipt entity = new ErpMaterialMgtMaterialQuotationReceipt();
        if (Objects.nonNull(ro.getImportTent())&&ro.getImportTent()) {
            if (ro.getMaterialQuotationReceiptProperties().equals(MaterialQuotationEnum.CUSTOMER_QUOTATION.getKey())) {
                //如果是导入的数据，需要处理客户或者供应商guid
                String customerGuid = baseMapper.getCustomerGuid(ro.getCustomerOrSupplierGuid());
                if (StringUtils.isNotEmpty(customerGuid)) {
                    ro.setCustomerOrSupplierGuid(customerGuid);
                } else {
                    importNoQuantity += ro.getDetailList().size();
                    String message = ro.getCustomerOrSupplierGuid() + "在数据库中不存在，导入失败";
                    list.add(message);
                    vo.setImportNoQuantity(importNoQuantity);
                    vo.setImportQuantity(importQuantity);
                    vo.setMessage(list);
                    return vo;
                }
            } else {
                //如果是导入的数据，需要处理客户或者供应商guid
                String supplierGuid = baseMapper.getSupplierGuid(ro.getCustomerOrSupplierGuid());
                if (StringUtils.isNotEmpty(supplierGuid)) {
                    ro.setCustomerOrSupplierGuid(supplierGuid);
                } else {
                    importNoQuantity += ro.getDetailList().size();
                    String message = ro.getCustomerOrSupplierGuid() + "在数据库中不存在，导入失败";
                    list.add(message);
                    vo.setImportNoQuantity(importNoQuantity);
                    vo.setImportQuantity(importQuantity);
                    vo.setMessage(list);
                    return vo;
                }
            }
        }
        BeanUtil.copyProperties(ro, entity);
        if (ro.getMaterialQuotationReceiptProperties().equals(MaterialQuotationEnum.CUSTOMER_QUOTATION.getKey())) {
            entity.setMaterialQuotationReceiptNumbers(numberService.generateOrderNumber(FormSourceEnum.CUSTOMER_QUOTATION));
        } else {
            entity.setMaterialQuotationReceiptNumbers(numberService.generateOrderNumber(FormSourceEnum.SUPPLIER_QUOTATION));
        }
        if (Objects.isNull(entity.getReceiptDate())) {
            entity.setReceiptDate(LocalDateTime.now());
        }
        if (super.save(entity)) {
            ro.setMaterialQuotationReceiptGuid(entity.getMaterialQuotationReceiptGuid());
            //插入明细数据
            ErpQuotationImportVO vo1 = detailService.saveData(ro);
            //把导入数据的情况汇总
            if (Objects.nonNull(vo1)) {
                importQuantity += vo1.getImportQuantity();
                importNoQuantity += vo1.getImportNoQuantity();
                if (CollectionUtils.isNotEmpty(vo1.getMessage())) {
                    list.addAll(vo1.getMessage());
                }
            }
            //启动审核
            if (ro.getMaterialQuotationReceiptProperties().equals(MaterialQuotationEnum.CUSTOMER_QUOTATION.getKey())) {
                iProcessInstanceService.start(WorkflowKeyEnum.CUSTOMER_QUOTATION, entity.getMaterialQuotationReceiptGuid());
            } else {
                iProcessInstanceService.start(WorkflowKeyEnum.SUPPLIER_QUOTATION, entity.getMaterialQuotationReceiptGuid());
            }
        }
        vo.setImportNoQuantity(importNoQuantity);
        vo.setImportQuantity(importQuantity);
        if (CollectionUtils.isNotEmpty(list)) {
            vo.setMessage(list);
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String materialQuotationReceiptGuid) {
        ErpMaterialMgtMaterialQuotationReceipt receipt = baseMapper.selectById(materialQuotationReceiptGuid);
        //删除报价明细数据
        detailService.deleteMessage(materialQuotationReceiptGuid);
        //删除审核
        if (receipt.getMaterialQuotationReceiptProperties().equals(MaterialQuotationEnum.CUSTOMER_QUOTATION.getKey())) {
            iProcessInstanceService.deleteProcessAndHisInst(receipt.getMaterialQuotationReceiptGuid(), WorkflowKeyEnum.CUSTOMER_QUOTATION);
        } else {
            iProcessInstanceService.deleteProcessAndHisInst(receipt.getMaterialQuotationReceiptGuid(), WorkflowKeyEnum.SUPPLIER_QUOTATION);
        }
        return super.removeById(materialQuotationReceiptGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> materialQuotationReceiptGuids) {
        for (String materialQuotationReceiptGuid : materialQuotationReceiptGuids) {
            super.removeById(materialQuotationReceiptGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpMaterialMgtMaterialQuotationReceiptRO ro) {
        ErpMaterialMgtMaterialQuotationReceipt entity = new ErpMaterialMgtMaterialQuotationReceipt();
        BeanUtil.copyProperties(ro, entity);
        if (super.updateById(entity)) {
            detailService.deleteMessage(entity.getMaterialQuotationReceiptGuid());
            detailService.saveData(ro);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ErpQuotationImportVO saveDate(InsertOrUpdateList<ErpMaterialMgtMaterialQuotationReceiptRO> dataList) {
        ErpQuotationImportVO vo = new ErpQuotationImportVO();
        List<String> list = new ArrayList<>();
        AtomicInteger importNoQuantity = new AtomicInteger();
        AtomicInteger importQuantity = new AtomicInteger();
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(s -> {
                ErpQuotationImportVO vo1 = this.create(s);
                if (Objects.nonNull(vo1)) {
                    importQuantity.addAndGet(vo1.getImportQuantity());
                    importNoQuantity.addAndGet(vo1.getImportNoQuantity());
                    if (CollectionUtils.isNotEmpty(vo1.getMessage())) {
                        list.addAll(vo1.getMessage());
                    }
                }
            });
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        vo.setImportNoQuantity(importNoQuantity.get());
        vo.setImportQuantity(importQuantity.get());
        vo.setMessage(list.stream().distinct().collect(Collectors.toList()));
        return vo;
    }

    @Override
    public ErpMaterialMgtMaterialQuotationReceiptVO getDataById(String materialQuotationReceiptGuid) {
        ErpMaterialMgtMaterialQuotationReceiptVO quotationReceiptVO = baseMapper.getDataByGuid(materialQuotationReceiptGuid);
        if (ObjectUtil.isNotNull(quotationReceiptVO)) {
            List<ErpMaterialMgtMaterialQuotationReceiptDetailVO> detailList = detailService.getDataList(Arrays.asList(materialQuotationReceiptGuid), quotationReceiptVO.getMaterialQuotationReceiptProperties());
            quotationReceiptVO.setDetailList(detailList);
        }
        return quotationReceiptVO;
    }

    @Override
    public List<ErpMaterialMgtMaterialQuotationReceiptVO> findList(ErpMaterialMgtMaterialQuotationReceiptQO qo) {
        List<ErpMaterialMgtMaterialQuotationReceiptVO> list = new ArrayList<>(baseMapper.findList(qo));
        Set<String> guidSet = new HashSet<>();
        List<ErpMaterialMgtMaterialQuotationReceiptVO> distinctList = list.stream()
                .filter(vo -> guidSet.add(vo.getMaterialQuotationReceiptGuid()))
                .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(distinctList)){
            List<String> materialQuotationIds = distinctList.stream().map(ErpMaterialMgtMaterialQuotationReceiptVO::getMaterialQuotationReceiptGuid).collect(Collectors.toList());
            //获取明细数据
            List<ErpMaterialMgtMaterialQuotationReceiptDetailVO> detailList = detailService.getDataList(materialQuotationIds,qo.getMaterialQuotationReceiptProperties());
            if (CollectionUtils.isNotEmpty(detailList)){
                Map<String, List<ErpMaterialMgtMaterialQuotationReceiptDetailVO>> map = detailList.stream().collect(Collectors.groupingBy(ErpMaterialMgtMaterialQuotationReceiptDetailVO::getMaterialQuotationReceiptGuid));
                distinctList.forEach(item->{
                    List<ErpMaterialMgtMaterialQuotationReceiptDetailVO> detailVOS = map.get(item.getMaterialQuotationReceiptGuid());
                    if (CollectionUtils.isNotEmpty(detailVOS)) {
                        item.setDetailList(detailVOS);
                    }
                });
            }
        }
        return distinctList;
    }

    @Override
    public IPage<ErpMaterialMgtMaterialQuotationReceiptVO> findPage(PageParams<ErpMaterialMgtMaterialQuotationReceiptQO> pageParams) {
        IPage<ErpMaterialMgtMaterialQuotationReceiptVO> page = pageParams.buildPage();
        ErpMaterialMgtMaterialQuotationReceiptQO model = pageParams.getModel();
        IPage<ErpMaterialMgtMaterialQuotationReceiptVO> page1 = baseMapper.findPage(page, model);
        List<ErpMaterialMgtMaterialQuotationReceiptVO> list = page1.getRecords().stream().distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(list)){
            List<String> materialQuotationIds = list.stream().map(ErpMaterialMgtMaterialQuotationReceiptVO::getMaterialQuotationReceiptGuid).collect(Collectors.toList());
            //获取明细数据
            List<ErpMaterialMgtMaterialQuotationReceiptDetailVO> detailList = detailService.getDataList(materialQuotationIds,model.getMaterialQuotationReceiptProperties());
            if (CollectionUtils.isNotEmpty(detailList)){
                Map<String, List<ErpMaterialMgtMaterialQuotationReceiptDetailVO>> map = detailList.stream().collect(Collectors.groupingBy(ErpMaterialMgtMaterialQuotationReceiptDetailVO::getMaterialQuotationReceiptGuid));
                list.forEach(item->{
                    List<ErpMaterialMgtMaterialQuotationReceiptDetailVO> detailVOS = map.get(item.getMaterialQuotationReceiptGuid());
                    if (CollectionUtils.isNotEmpty(detailVOS)) {
                        item.setDetailList(detailVOS);
                    }
                });
            }
        }
        page1.setRecords(list);
        return page1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean useQuotation(List<String> list) {
        LambdaUpdateWrapper<ErpMaterialMgtMaterialQuotationReceipt> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ErpMaterialMgtMaterialQuotationReceipt::getMaterialQuotationReceiptGuid, list)
                .set(ErpMaterialMgtMaterialQuotationReceipt::getEffectiveStatus, true);
        baseMapper.update(null, wrapper);
        //根据客户报价id获取报价单价以及客户id,以及产品资料额外数据id
        List<ErpMaterialQuotationVO> list1 = baseMapper.getExperienceExternal(list);
        String collect = list1.stream().filter(s ->
                        StringUtils.isBlank(s.getMaterialBomGuid())).map(s -> StringUtils
                        .defaultString(s.getMaterialName(), "") + "+" +
                        StringUtils.defaultString(s.getMaterialCode(), "") + ";")
                .collect(Collectors.joining());
        if (StringUtils.isNotBlank(collect)){
            throw new FlowException(ResultErrorCode.BOM_NOT_STATUS,collect);
        }
        if (CollectionUtils.isNotEmpty(list1)) {
            List<String> collect1 = list1.stream().map(ErpMaterialQuotationVO::getExperienceProductionProcessExternalGuid).distinct().collect(Collectors.toList());
            List<ErpProductionMgtExperienceProductionProcessExternal> externalList = externalService.listByIds(collect1);
            externalList.forEach(s->{
                ErpMaterialQuotationVO vo = list1.stream().filter(k -> s.getExperienceProductionProcessExternalGuid()
                        .equals(k.getExperienceProductionProcessExternalGuid())).collect(Collectors.toList()).get(0);
                s.setQuotationUnitPriceIncludingTax(vo.getQuotationUnitPriceIncludingTax());
            });
            externalService.updateBatchById(externalList);
        }
        return true;
    }

    /**
     * 数据导入
     *
     * @param file
     * @return
     */
    @Override
    public ErpQuotationImportVO importOrderDate(MultipartFile file) throws IOException {
        InputStream inputStream = file.getInputStream();
        List<ErpMaterialMgtQuotationExceRO> list = EasyExcel.read(inputStream) //调用read方法
                //注册自定义监听器，字段校验可以在监听器内实现
                .head(ErpMaterialMgtQuotationExceRO.class) //对应导入的实体类
                .sheet(0) //导入数据的sheet页编号，0代表第一个sheet页，如果不填，则会导入所有sheet页的数据
                .headRowNumber(1) //列表头行数，1代表列表头有1行，第二行开始为数据行
                .doReadSync(); //开始读Excel，返回一个List<T>集合，继续后续入库操作
        ErpQuotationImportVO vo = new ErpQuotationImportVO();
        if (CollectionUtils.isNotEmpty(list)) {
            List<ErpMaterialMgtMaterialQuotationReceiptRO> list1 = new ArrayList<>();
            //组装数据
            //1-判断该文件是供应商报价还是客户报价数据
            long count = list.stream().filter(s -> Objects.nonNull(s.getSupplierCode())).count();
            //发现是供应商
            if (count > 0) {
                Map<String, List<ErpMaterialMgtQuotationExceRO>> collect = list.stream()
                        .collect(Collectors.groupingBy(ErpMaterialMgtQuotationExceRO::getSupplierCode));
                // 使用 forEach() 方法
                collect.forEach((key, value) -> {
                    ErpMaterialMgtMaterialQuotationReceiptRO ro = new ErpMaterialMgtMaterialQuotationReceiptRO();
                    ro.setReceiptDate(LocalDateTime.now());
                    ro.setMaterialQuotationReceiptProperties(2);
                    ro.setCustomerOrSupplierGuid(key);
                    ro.setMaterialQuotationReceiptType("1");
                    ro.setImportTent(true);
                    String date = value.stream().filter(s -> Objects.nonNull(s.getEffectiveDate()))
                            .map(ErpMaterialMgtQuotationExceRO::getEffectiveDate).findFirst()
                            .orElse("");
                    if (StringUtils.isNotBlank(date)) {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/M/d");
                        LocalDate dateTime = LocalDate.parse(date, formatter);
                        ro.setEffectiveDate(dateTime.atStartOfDay());
                    }else {
                        ro.setEffectiveDate(LocalDateTime.now());
                    }
                    if (CollectionUtils.isNotEmpty(value)) {
                        List<ErpMaterialMgtMaterialQuotationReceiptDetailRO> detailList = new ArrayList<>();
                        value.forEach(l -> {
                            ErpMaterialMgtMaterialQuotationReceiptDetailRO ro1 = new ErpMaterialMgtMaterialQuotationReceiptDetailRO();
                            BeanUtil.copyProperties(l, ro1);
                            detailList.add(ro1);
                        });
                        ro.setDetailList(detailList);
                        list1.add(ro);
                    }
                });
            } else {
                Map<String, List<ErpMaterialMgtQuotationExceRO>> collect = list.stream()
                        .collect(Collectors.groupingBy(ErpMaterialMgtQuotationExceRO::getCustomerCode));
                // 使用 forEach() 方法
                collect.forEach((key, value) -> {
                    ErpMaterialMgtMaterialQuotationReceiptRO ro = new ErpMaterialMgtMaterialQuotationReceiptRO();
                    ro.setReceiptDate(LocalDateTime.now());
                    ro.setMaterialQuotationReceiptProperties(1);
                    ro.setCustomerOrSupplierGuid(key);
                    ro.setMaterialQuotationReceiptType("1");
                    ro.setImportTent(true);
                    String date = value.stream().filter(s -> Objects.nonNull(s.getEffectiveDate()))
                            .map(ErpMaterialMgtQuotationExceRO::getEffectiveDate).findFirst()
                            .orElse("");
                    if (StringUtils.isNotBlank(date)) {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
                        LocalDate dateTime = LocalDate.parse(date, formatter);
                        ro.setEffectiveDate(dateTime.atStartOfDay());
                    }else {
                        ro.setEffectiveDate(LocalDateTime.now());
                    }
                    if (CollectionUtils.isNotEmpty(value)) {
                        List<ErpMaterialMgtMaterialQuotationReceiptDetailRO> detailList = new ArrayList<>();
                        value.forEach(l -> {
                            ErpMaterialMgtMaterialQuotationReceiptDetailRO ro1 = new ErpMaterialMgtMaterialQuotationReceiptDetailRO();
                            BeanUtil.copyProperties(l, ro1);
                            detailList.add(ro1);
                        });
                        ro.setDetailList(detailList);
                        list1.add(ro);
                    }
                });
            }
            InsertOrUpdateList<ErpMaterialMgtMaterialQuotationReceiptRO> list2 = new InsertOrUpdateList<>();
            if (CollectionUtils.isNotEmpty(list1)){
            list2.setInsertList(list1);
               return this.saveDate(list2);
            }
        } else {
            List<String> list1=new ArrayList<>();
            list1.add("文件为空");
            vo.setMessage(list1);
            return vo;
        }
        List<String> list1=new ArrayList<>();
        list1.add("文件为空");
        vo.setMessage(list1);
        return vo;
    }

    /**
     * 平铺明细数据
     *
     * @param pageParams
     * @return
     */
    @Override
    public IPage<ErpMaterialMgtMaterialQuotationVO> findDetailPage(PageParams<ErpMaterialMgtMaterialQuotationReceiptQO> pageParams) {
        IPage<ErpMaterialMgtMaterialQuotationReceiptVO> page = pageParams.buildPage();
        ErpMaterialMgtMaterialQuotationReceiptQO model = pageParams.getModel();
        return baseMapper.findDetailPage(page, model);
    }

    /**
     * 获取最新的采购报价
     *
     * @param qo
     * @return
     */
    @Override
    public List<ErpMaterialMgtMaterialQuotationReceiptDetailVO> getNewMoney(ErpMaterialMgtMaterialQuotationReceiptQO qo) {
        return baseMapper.getNewMoney(qo);
    }
}
