package xy.server.purchase.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.vo.SourceDescriptionAndPictureVO;
import com.xunyue.config.util.PageParams;
import xy.server.purchase.entity.ErpOutgoingBusinessMgtOrder;
import xy.server.purchase.entity.ErpOutgoingBusinessMgtOrderData;
import xy.server.purchase.entity.model.qo.ErpOutgoingBusinessMgtOrderQO;
import xy.server.purchase.entity.model.qo.ErpPurchaseMgtPurchaseOrderQO;
import xy.server.purchase.entity.model.qo.ErpPurchaseProcessOutgoingOrderQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingBusinessMgtOrderRO;
import xy.server.purchase.entity.model.ro.ErpOutgoingDetaiStopRO;
import xy.server.purchase.entity.model.vo.*;

import java.util.List;

/**
 * <p>
 * 订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
public interface IErpOutgoingBusinessMgtOrderService extends IService<ErpOutgoingBusinessMgtOrder> {
    String create(ErpOutgoingBusinessMgtOrderRO ro);

    boolean delete(String orderGuid);

    void deleteSonData(QueryWrapper<ErpOutgoingBusinessMgtOrderData> wrapper, List<ErpOutgoingBusinessMgtOrderData> orderDataList);

    boolean update(ErpOutgoingBusinessMgtOrderRO ro);

    ErpOutgoingBusinessMgtOrderVO getById(String orderGuid);

    List<ErpOutgoingBusinessMgtOrderVO> findList(ErpOutgoingBusinessMgtOrderQO qo);

    IPage<ErpOutgoingBusinessMgtOrderVO> findPage(PageParams<ErpOutgoingBusinessMgtOrderQO> pageParams);

    Boolean removeIds(List<String> ids);

    List<String> saveData(InsertOrUpdateList<ErpOutgoingBusinessMgtOrderRO> list);

    /**
     * 加工商预付明细列表查询
     * @param qo
     * @return
     */
    List<ErpProcessOutgoingOrderVO> processFindList(ErpPurchaseProcessOutgoingOrderQO qo);

    /**
     * 外发到货单待开列表查询
     * @param qo
     * @return
     */
    List<ErpPurchaseInventoryWorkorderDetailVO> findNotBilledList(ErpPurchaseMgtPurchaseOrderQO qo);

    /**
     * 外发到货单待开列表查询
     * @param qo
     * @return
     */
    List<ErpPurchaseInventoryWorkorderVO> findNotBilledTree(ErpPurchaseMgtPurchaseOrderQO qo);

    /**
     * 终止单据
     * @param list
     * @return
     */
    Boolean stopOrRollback(List<ErpOutgoingDetaiStopRO> list);

    /**
     * 获取顶级来源备注和图片
     * @param sourceNumbers
     * @return
     */
    List<SourceDescriptionAndPictureVO> getSourceDescriptionAndPicture(List<String> sourceNumbers);

    /**
     *
     * @param sourceGuids
     * @return
     */
    List<ErpOutgoingArrivalVO> getOutgoingOrderVOBatch(List<String> sourceGuids);
}
