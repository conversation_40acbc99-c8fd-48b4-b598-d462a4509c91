package xy.server.purchase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseOrderPlaceOfDelivery;
import xy.server.purchase.entity.model.ro.ErpPurchaseMgtPurchaseOrderDataRO;

import java.util.List;

/**
 * <p>
 * 订单交货地点管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
public interface IErpPurchaseMgtPurchaseOrderPlaceOfDeliveryService extends IService<ErpPurchaseMgtPurchaseOrderPlaceOfDelivery> {
    /**
     * 保存数据
     * @param orderGuid 订单guid
     * @param orderDataList 订单数据表RO
     */
    void saveDate(String orderGuid, List<ErpPurchaseMgtPurchaseOrderDataRO> orderDataList);

    /**
     * 根据订单guid删除数据
     * @param orderGuid
     * @param delOrderDataGuids
     */
    void delByOrderGuid(String orderGuid, List<String> delOrderDataGuids);
}
