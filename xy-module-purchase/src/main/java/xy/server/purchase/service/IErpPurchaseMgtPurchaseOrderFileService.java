package xy.server.purchase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseOrderFile;
import xy.server.purchase.entity.model.ro.ErpPurchaseMgtPurchaseOrderFileRO;
import xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseOrderFileVO;

import java.util.List;

/**
 * <p>
 * 订单文件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
public interface IErpPurchaseMgtPurchaseOrderFileService extends IService<ErpPurchaseMgtPurchaseOrderFile> {
    /**
     * 保存数据
     * @param orderGuid 订单guid
     * @param fileList 文件列表
     * @param delFileIds 删除ids
     */
    void saveDate(String orderGuid, List<ErpPurchaseMgtPurchaseOrderFileRO> fileList, List<String> delFileIds);

    /**
     * 根据订单guid删除数据
     * @param orderGuid
     */
    void delByOrderGuid(String orderGuid);

    /**
     * 批量查询
     * @param orderGuids
     * @return
     */
    List<ErpPurchaseMgtPurchaseOrderFileVO> getDataByOrderGuids(List<String> orderGuids);
}
