package xy.server.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.basic.entity.OrganizationAddress;
import com.xunyue.basic.entity.OrganizationContact;
import com.xunyue.basic.entity.OrganizationFile;
import com.xunyue.basic.entity.OrganizationFollowUpStaff;
import com.xunyue.basic.entity.model.dto.SupplierDTO;
import com.xunyue.basic.service.*;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.enums.FormSourceEnum;
import com.xunyue.common.i18n.BaseResultErrorCodeImpl;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.common.util.LoadDataUtils;
import com.xunyue.common.util.UsedUtils;
import com.xunyue.common.util.XyBeanUtil;
import com.xunyue.config.excel.EasyExcelDataHandleService;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.EasyExcelUtils;
import com.xunyue.config.util.PageParams;
import com.xunyue.node.common.enums.ClazzEnum;
import com.xunyue.tenant.sign.UserApi;
import com.xy.util.BaseContext;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import xy.server.activiti.common.enums.BusinessStatusEnum;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.basic.entity.ErpBasicMgtCurrency;
import xy.server.basic.entity.ErpBasicMgtDeliveryType;
import xy.server.basic.entity.ErpBasicMgtSettlementType;
import xy.server.basic.entity.model.vo.ErpBasicMgtCurrencyVO;
import xy.server.basic.entity.model.vo.ErpBasicMgtDeliveryTypeVO;
import xy.server.basic.entity.model.vo.ErpBasicMgtInvoiceTypeTaxRateVO;
import xy.server.basic.entity.model.vo.ErpBasicMgtSettlementTypeVO;
import xy.server.basic.service.IErpBasicMgtCurrencyService;
import xy.server.basic.service.IErpBasicMgtDeliveryTypeService;
import xy.server.basic.service.IErpBasicMgtInvoiceTypeTaxRateService;
import xy.server.basic.service.IErpBasicMgtSettlementTypeService;
import xy.server.dto.XyMemberDto;
import xy.server.purchase.entity.ErpSupplierMgtSupplier;
import xy.server.purchase.entity.ErpSupplierMgtSupplierClassification;
import xy.server.purchase.entity.ErpSupplierMgtSupplierLevel;
import xy.server.purchase.entity.model.qo.ErpSupplierMgtSupplierQO;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierAddressRO;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierContactRO;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierRO;
import xy.server.purchase.entity.model.vo.*;
import xy.server.purchase.i18n.ResultErrorCode;
import xy.server.purchase.mapper.ErpSupplierMgtSupplierMapper;
import xy.server.purchase.service.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service("SUPPLIER.SUPPLIER_AUDIT")
public class ErpSupplierMgtSupplierServiceImpl extends ServiceImpl<ErpSupplierMgtSupplierMapper, ErpSupplierMgtSupplier> implements IErpSupplierMgtSupplierService, ActEventStrategyService {
    private final IErpSystemMgtOrderSerialNumberService iErpSystemMgtOrderSerialNumberService;
    private final IErpSupplierMgtSupplierClassificationService iErpSupplierMgtSupplierClassificationService;
    private final IErpSupplierMgtSupplierLevelService iErpSupplierMgtSupplierLevelService;
    private final IErpSupplierMgtSupplierAddressService supplierAddressService;
    private final IErpSupplierMgtSupplierFileService supplierFileService;
    private final IErpSupplierMgtSupplierContactService supplierContactService;
    private final IErpBasicMgtCurrencyService iErpBasicMgtCurrencyService;
    private final IErpBasicMgtInvoiceTypeTaxRateService iErpBasicMgtInvoiceTypeTaxRateService;
    private final IErpBasicMgtSettlementTypeService iErpBasicMgtSettlementTypeService;
    private final IErpBasicMgtDeliveryTypeService iErpBasicMgtDeliveryTypeService;
    private final UsedUtils usedUtils;
    private final EasyExcelUtils easyExcelUtils;
    private final ISupplierService supplierService;

    private final UserApi userApi;

    private final IOrganizationAddressService organizationAddressService;
    private final IOrganizationContactService organizationContactService;
    private final IOrganizationFollowUpStaffService organizationFollowUpStaffService;
    private final IOrganizationFileService organizationFileService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ErpSupplierMgtSupplier create(ErpSupplierMgtSupplierRO ro) {
        // 数据校验
        this.saveOrUpdateVerify(ro);

        // 自动编码
        if ((null != ro.getIsAutomaticCode() && ro.getIsAutomaticCode()) || StringUtils.isEmpty(ro.getSupplierCode())) {
            ro.setSupplierCode(iErpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.SUPPLIER_AUDIT));
        }
        // 全称为空时，设置默认等于简称
        if (StringUtils.isEmpty(ro.getSupplierFullName())) {
            ro.setSupplierFullName(ro.getSupplierShortName());
        }
        // 保存供应商主表
        ErpSupplierMgtSupplier entity = new ErpSupplierMgtSupplier();
        BeanUtil.copyProperties(ro, entity);
        if (super.save(entity)) {
            // 保存相关关联表
            this.saveAssociatedData(entity.getSupplierGuid(), ro);
            // 处理相关表已被使用字段
            this.changeIsUsed(entity, null, false);
            if (!BusinessStatusEnum.FINISH.getStatus().equals(ro.getAuditStatus())) {
                // 启动审核流程
                iProcessInstanceService.start(WorkflowKeyEnum.SUPPLIER_AUDIT, entity.getSupplierGuid());
            }
            XyMemberDto xyMemberDto = userApi.getThreadLocalXyMemberDto();
            ThreadUtil.execAsync(() -> {
                userApi.setThreadLocal(xyMemberDto);
                this.writeCustomer(entity);
            });
            return entity;
        }
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String supplierGuid) {
        ErpSupplierMgtSupplier entity = this.operationCheck(supplierGuid);

        // 删除关联表数据
        this.removeAssociatedData(supplierGuid);

        if (super.removeById(supplierGuid)) {
            supplierService.removeById(supplierGuid);
            // 处理相关表已被使用字段
            this.changeIsUsed(entity, null, true);
            // 删除流程相关数据
            iProcessInstanceService.deleteProcessAndHisInst(entity.getSupplierGuid(), WorkflowKeyEnum.SUPPLIER_AUDIT);
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> ids) {
        boolean flag = true;
        for (String id : ids) {
            flag = false;
            ErpSupplierMgtSupplier entity = this.operationCheck(id);
            // 删除关联表数据
            this.removeAssociatedData(id);

            if (super.removeById(id)) {
                // 处理相关表已被使用字段
                this.changeIsUsed(entity, null, true);
                // 删除流程相关数据
                iProcessInstanceService.deleteProcessAndHisInst(entity.getSupplierGuid(), WorkflowKeyEnum.SUPPLIER_AUDIT);
                flag = true;
            }

        }
        supplierService.removeBatch(ids);
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ErpSupplierMgtSupplier update(ErpSupplierMgtSupplierRO ro) {
        ErpSupplierMgtSupplier oldEntity = this.operationCheck(ro.getSupplierGuid());

        // 数据校验
        this.saveOrUpdateVerify(ro);

        // 保存供应商主表
        ErpSupplierMgtSupplier entity = new ErpSupplierMgtSupplier();
        BeanUtil.copyProperties(ro, entity);
        if (super.updateById(entity)) {
            // 保存相关关联表
            this.saveAssociatedData(entity.getSupplierGuid(), ro);
            // 处理相关表已被使用字段
            this.changeIsUsed(entity, oldEntity, false);
            XyMemberDto xyMemberDto = userApi.getThreadLocalXyMemberDto();
            ThreadUtil.execAsync(() -> {
                userApi.setThreadLocal(xyMemberDto);
                this.writeCustomer(entity);
            });
            return entity;
        }
        return entity;
    }

    @Override
    public ErpSupplierMgtSupplierVO getById(String supplierGuid) {
        ErpSupplierMgtSupplierVO dataByGuid = baseMapper.getDataByGuid(supplierGuid);
        if (Objects.isNull(dataByGuid)) {
            return null;
        }
        List<ErpSupplierMgtSupplierVO> supplierVOS = Arrays.asList(dataByGuid);
        return setDetail(supplierVOS).get(0);
    }

    @Override
    public List<ErpSupplierMgtSupplierVO> findList(ErpSupplierMgtSupplierQO qo, String tenantGuid) {
        if (!StringUtils.isEmpty(qo.getKeyword())) {
            qo.setKeywords(Arrays.asList(qo.getKeyword().split(" ")));
        }
        return setDetail(baseMapper.findList(qo));
    }

    @Override
    public IPage<ErpSupplierMgtSupplierVO> findPage(PageParams<ErpSupplierMgtSupplierQO> pageParams) {
        String staffGuid = BaseContext.getStaffGuid();
        IPage<ErpSupplierMgtSupplierVO> page = pageParams.buildPage();
        ErpSupplierMgtSupplierQO model = pageParams.getModel();
        if (!StringUtils.isEmpty(model.getKeyword())) {
            model.setKeywords(Arrays.asList(model.getKeyword().split(" ")));
        }
        IPage<ErpSupplierMgtSupplierVO> baseMapperPage = baseMapper.findPage(page, model);
        return baseMapperPage.setRecords(setDetail(baseMapperPage.getRecords()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ErpSupplierMgtSupplier> saveDate(InsertOrUpdateList<ErpSupplierMgtSupplierRO> dataList, String tenantGuid) {
        List<ErpSupplierMgtSupplier> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList.getInsertList())) {
            List<ErpSupplierMgtSupplier> insertList = dataList.getInsertList().stream().map(data -> {
                data.setTenantGuid(tenantGuid);
                return this.create(data);
            }).collect(Collectors.toList());
            resultList.addAll(insertList);
        }

        if (!CollectionUtils.isEmpty(dataList.getUpdateList())) {
            List<ErpSupplierMgtSupplier> updateList = dataList.getUpdateList().stream().map(this::update).collect(Collectors.toList());
            resultList.addAll(updateList);
        }
        return resultList;
    }

    @Override
    public List<ErpSupplierMgtSupplierVO> selectList(ErpSupplierMgtSupplierQO qo) {
        List<String> auditStatus = new ArrayList<>();
        auditStatus.add(BusinessStatusEnum.FINISH.getStatus());
        qo.setAuditStatus(auditStatus);
        if (!StringUtils.isEmpty(qo.getKeyword())) {
            qo.setKeywords(Arrays.asList(qo.getKeyword().split(" ")));
        }
        return setDetail(baseMapper.findList(qo));
    }

    /**
     * 下拉快选择供应商
     *
     * @return
     */
    @Override
    public List<ErpSupplierMgtSupplier> dropDownSelect() {
        return baseMapper.dropDownSelect();
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        Class[] classes = new Class[]{
                ErpSupplierMgtSupplierRO.class, ErpSupplierMgtSupplierAddressRO.class, ErpSupplierMgtSupplierContactRO.class
        };
        easyExcelUtils.downloadTemplate(classes, response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean importExcel(MultipartFile file, boolean isErrorResume) {
        Class[] classes = new Class[]{
                ErpSupplierMgtSupplierRO.class, ErpSupplierMgtSupplierAddressRO.class, ErpSupplierMgtSupplierContactRO.class
        };
        EasyExcelDataHandleService[] easyExcelDataHandleServices = new EasyExcelDataHandleService[]{
                this, supplierAddressService, supplierContactService
        };
        return easyExcelUtils.analyzeExcel(file, classes, easyExcelDataHandleServices, isErrorResume);
    }

    /**
     * 批量获取供应商数据
     *
     * @param supplierGuids
     * @return
     */
    @Override
    public List<ErpSupplierMgtSupplierVO> getDataByIds(List<String> supplierGuids) {
        if (CollectionUtils.isEmpty(supplierGuids)) {
            return Collections.emptyList();
        }
        List<ErpSupplierMgtSupplierVO> entityList = baseMapper.getDataByGuids(supplierGuids);
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        return setDetail(entityList);
    }

    @Override
    public Map<String, ErpSupplierMgtSupplierVO> getDataByIdsMap(List<String> supplierGuids) {
        return CollStreamUtil.toIdentityMap(getDataByIds(supplierGuids), ErpSupplierMgtSupplierVO::getSupplierGuid);
    }

    @Override
    public CompletableFuture<Map<String, ErpSupplierMgtSupplierVO>> getDataByIdsMapAsync(List<String> supplierGuids, XyMemberDto xyMemberDto) {
        return CompletableFuture.supplyAsync(() -> {
            userApi.setThreadLocal(xyMemberDto);
            return getDataByIdsMap(supplierGuids);
        });
    }

    /**
     * 设置明细数据
     *
     * @param entityList
     * @return
     */
    @SneakyThrows
    public List<ErpSupplierMgtSupplierVO> setDetail(List<ErpSupplierMgtSupplierVO> entityList) {

        if (CollectionUtils.isEmpty(entityList)) {
            return new ArrayList<>();
        }
        List<String> supplierClassificationGuids = entityList.stream()
                .map(ErpSupplierMgtSupplierVO::getSupplierClassificationGuid)
                .collect(Collectors.toList());
        List<String> supplierLevelGuids = entityList.stream()
                .map(ErpSupplierMgtSupplierVO::getSupplierLevelGuid)
                .collect(Collectors.toList());
        List<String> defaultCurrencyGuids = entityList.stream()
                .map(ErpSupplierMgtSupplierVO::getDefaultCurrencyGuid)
                .collect(Collectors.toList());
        List<String> defaultInvoiceTypeTaxRateGuids = entityList.stream()
                .map(ErpSupplierMgtSupplierVO::getDefaultInvoiceTypeTaxRateGuid)
                .collect(Collectors.toList());
        List<String> defaultDeliveryTypeGuids = entityList.stream()
                .map(ErpSupplierMgtSupplierVO::getDefaultDeliveryTypeGuid)
                .collect(Collectors.toList());
        List<String> defaultSettlementTypeGuids = entityList.stream()
                .map(ErpSupplierMgtSupplierVO::getDefaultSettlementTypeGuid)
                .collect(Collectors.toList());
        List<String> supplierGuids = entityList.stream()
                .map(ErpSupplierMgtSupplierVO::getSupplierGuid)
                .collect(Collectors.toList());


        XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();
        // 供应商分类
        CompletableFuture<List<ErpSupplierMgtSupplierClassificationVO>> supplierClassificationTask = CompletableFuture.supplyAsync(() -> {
            userApi.setThreadLocal(memberDto);
            return LoadDataUtils.batchLoadDataAsync(iErpSupplierMgtSupplierClassificationService::getDataByIds, supplierClassificationGuids, memberDto, null);
        });
        // 供应商等级
        CompletableFuture<List<ErpSupplierMgtSupplierLevelVO>> supplierLevelTask = CompletableFuture.supplyAsync(() -> {
            userApi.setThreadLocal(memberDto);
            return LoadDataUtils.batchLoadDataAsync(iErpSupplierMgtSupplierLevelService::getDataByIds, supplierLevelGuids, memberDto, null);
        });
        // 默认币种
        CompletableFuture<List<ErpBasicMgtCurrencyVO>> defaultCurrencyTask = CompletableFuture.supplyAsync(() -> {
            userApi.setThreadLocal(memberDto);
            return LoadDataUtils.batchLoadDataAsync(iErpBasicMgtCurrencyService::getDataByIds, defaultCurrencyGuids, memberDto, null);
        });
        // 发票税率
        CompletableFuture<List<ErpBasicMgtInvoiceTypeTaxRateVO>> defaultInvoiceTypeTaxRateTask = CompletableFuture.supplyAsync(() -> {
            userApi.setThreadLocal(memberDto);
            return LoadDataUtils.batchLoadDataAsync(iErpBasicMgtInvoiceTypeTaxRateService::getDataByIds, defaultInvoiceTypeTaxRateGuids, memberDto, null);
        });
        // 默认送货类型
        CompletableFuture<List<ErpBasicMgtDeliveryTypeVO>> defaultDeliveryTypeTask = CompletableFuture.supplyAsync(() -> {
            userApi.setThreadLocal(memberDto);
            return LoadDataUtils.batchLoadDataAsync(iErpBasicMgtDeliveryTypeService::getDataByIds, defaultDeliveryTypeGuids, memberDto, null);
        });
        // 默认结算类型
        CompletableFuture<List<ErpBasicMgtSettlementTypeVO>> defaultSettlementTypeTask = CompletableFuture.supplyAsync(() -> {
            userApi.setThreadLocal(memberDto);
            return LoadDataUtils.batchLoadDataAsync(iErpBasicMgtSettlementTypeService::getDataByIds, defaultSettlementTypeGuids, memberDto, null);
        });
        // 供应商文件列表
        CompletableFuture<List<ErpSupplierMgtSupplierFileVO>> supplierFileTask = CompletableFuture.supplyAsync(() -> {
            userApi.setThreadLocal(memberDto);
            return LoadDataUtils.batchLoadDataAsync(supplierFileService::getDataByIds, supplierGuids, memberDto, null);
        });
        // 供应商地址列表
        CompletableFuture<List<ErpSupplierMgtSupplierAddressVO>> supplierAddressTask = CompletableFuture.supplyAsync(() -> {
            userApi.setThreadLocal(memberDto);
            return LoadDataUtils.batchLoadDataAsync(supplierAddressService::getDataByIds, supplierGuids, memberDto, null);
        });
        // 供应商联系人列表
        CompletableFuture<List<ErpSupplierMgtSupplierContactVO>> supplierContactTask = CompletableFuture.supplyAsync(() -> {
            userApi.setThreadLocal(memberDto);
            return LoadDataUtils.batchLoadDataAsync(supplierContactService::getDataByIds, supplierGuids, memberDto, null);
        });

        CompletableFuture.allOf(supplierClassificationTask,
                supplierLevelTask,
                defaultCurrencyTask,
                defaultInvoiceTypeTaxRateTask,
                defaultDeliveryTypeTask,
                defaultSettlementTypeTask,
                supplierFileTask,
                supplierAddressTask,
                supplierContactTask).join();

        List<ErpSupplierMgtSupplierClassificationVO> supplierClassificationList = supplierClassificationTask.get();
        List<ErpSupplierMgtSupplierLevelVO> supplierLevelList = supplierLevelTask.get();
        List<ErpBasicMgtCurrencyVO> defaultCurrencyList = defaultCurrencyTask.get();
        List<ErpBasicMgtInvoiceTypeTaxRateVO> defaultInvoiceTypeTaxRateList = defaultInvoiceTypeTaxRateTask.get();
        List<ErpBasicMgtDeliveryTypeVO> defaultDeliveryTypeList = defaultDeliveryTypeTask.get();
        List<ErpBasicMgtSettlementTypeVO> defaultSettlementTypeList = defaultSettlementTypeTask.get();
        List<ErpSupplierMgtSupplierFileVO> supplierFileList = supplierFileTask.get();
        List<ErpSupplierMgtSupplierAddressVO> supplierAddressList = supplierAddressTask.get();
        List<ErpSupplierMgtSupplierContactVO> supplierContactList = supplierContactTask.get();

        // 供应商分类
        LoadDataUtils.loadDataObjTool(entityList,
                supplierClassificationList,
                ErpSupplierMgtSupplierClassificationVO::getSupplierClassificationGuid,
                ErpSupplierMgtSupplierVO::getSupplierClassificationGuid,
                ErpSupplierMgtSupplierVO::setSupplierClassificationVO);

        // 供应商等级
        LoadDataUtils.loadDataObjTool(entityList,
                supplierLevelList,
                ErpSupplierMgtSupplierLevelVO::getSupplierLevelGuid,
                ErpSupplierMgtSupplierVO::getSupplierLevelGuid,
                ErpSupplierMgtSupplierVO::setSupplierLevelVO);

        // 默认币种
        LoadDataUtils.loadDataObjTool(entityList,
                defaultCurrencyList,
                ErpBasicMgtCurrencyVO::getCurrencyGuid,
                ErpSupplierMgtSupplierVO::getDefaultCurrencyGuid,
                ErpSupplierMgtSupplierVO::setDefaultCurrencyVO);

        // 发票税率
        LoadDataUtils.loadDataObjTool(entityList,
                defaultInvoiceTypeTaxRateList,
                ErpBasicMgtInvoiceTypeTaxRateVO::getInvoiceTypeTaxRateGuid,
                ErpSupplierMgtSupplierVO::getDefaultInvoiceTypeTaxRateGuid,
                ErpSupplierMgtSupplierVO::setDefaultInvoiceTypeTaxRateVO);

        // 默认送货类型
        LoadDataUtils.loadDataObjTool(entityList,
                defaultDeliveryTypeList,
                ErpBasicMgtDeliveryTypeVO::getDeliveryTypeGuid,
                ErpSupplierMgtSupplierVO::getDefaultDeliveryTypeGuid,
                ErpSupplierMgtSupplierVO::setDefaultDeliveryTypeVO);

        // 默认结算类型
        LoadDataUtils.loadDataObjTool(entityList,
                defaultSettlementTypeList,
                ErpBasicMgtSettlementTypeVO::getSettlementTypeGuid,
                ErpSupplierMgtSupplierVO::getDefaultSettlementTypeGuid,
                ErpSupplierMgtSupplierVO::setDefaultSettlementTypeVO);

        // 供应商文件列表
        LoadDataUtils.loadDataListTool(entityList,
                supplierFileList,
                ErpSupplierMgtSupplierFileVO::getSupplierGuid,
                ErpSupplierMgtSupplierVO::getSupplierGuid,
                ErpSupplierMgtSupplierVO::setSupplierFileVOList);

        // 供应商地址列表
        LoadDataUtils.loadDataListTool(entityList,
                supplierAddressList,
                ErpSupplierMgtSupplierAddressVO::getSupplierGuid,
                ErpSupplierMgtSupplierVO::getSupplierGuid,
                ErpSupplierMgtSupplierVO::setSupplierAddressVOList);

        // 供应商联系人列表
        LoadDataUtils.loadDataListTool(entityList,
                supplierContactList,
                ErpSupplierMgtSupplierContactVO::getSupplierGuid,
                ErpSupplierMgtSupplierVO::getSupplierGuid,
                ErpSupplierMgtSupplierVO::setSupplierContactVOList);

        return entityList;
    }

    /**
     * 处理导入Excel数据
     *
     * @param ro
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcelDataHandle(ErpSupplierMgtSupplierRO ro) {
        // 处理供应商分类
        if (!StringUtils.isEmpty(ro.getSupplierClassificationGuid())) {
            ErpSupplierMgtSupplierClassification supplierClassification = iErpSupplierMgtSupplierClassificationService.getOne(Wrappers
                    .<ErpSupplierMgtSupplierClassification>lambdaQuery()
                    .select(ErpSupplierMgtSupplierClassification::getSupplierClassificationGuid)
                    .eq(ErpSupplierMgtSupplierClassification::getSupplierClassificationName, ro.getSupplierClassificationGuid()));
            if (ObjectUtil.isNull(supplierClassification) || StringUtils.isEmpty(supplierClassification.getSupplierClassificationGuid())) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "供应商分类【" + ro.getSupplierClassificationGuid() + "】不存在！");
            }
            ro.setSupplierClassificationGuid(supplierClassification.getSupplierClassificationGuid());
        }
        // 处理供应商级别
        if (!StringUtils.isEmpty(ro.getSupplierLevelGuid())) {
            ErpSupplierMgtSupplierLevel supplierLevel = iErpSupplierMgtSupplierLevelService.getOne(Wrappers
                    .<ErpSupplierMgtSupplierLevel>lambdaQuery()
                    .select(ErpSupplierMgtSupplierLevel::getSupplierLevelGuid)
                    .eq(ErpSupplierMgtSupplierLevel::getSupplierLevelName, ro.getSupplierLevelGuid()));
            if (ObjectUtil.isNull(supplierLevel) || StringUtils.isEmpty(supplierLevel.getSupplierLevelGuid())) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "供应商级别【" + ro.getSupplierLevelGuid() + "】不存在！");
            }
            ro.setSupplierLevelGuid(supplierLevel.getSupplierLevelGuid());
        }
        // 处理父级供应商
        if (!StringUtils.isEmpty(ro.getParentSupplierGuid())) {
            ErpSupplierMgtSupplier parentSupplier = this.getOne(Wrappers.<ErpSupplierMgtSupplier>lambdaQuery()
                    .select(ErpSupplierMgtSupplier::getSupplierGuid)
                    .eq(ErpSupplierMgtSupplier::getSupplierCode, ro.getParentSupplierGuid()));
            if (ObjectUtil.isNull(parentSupplier) || StringUtils.isEmpty(parentSupplier.getSupplierGuid())) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "父级供应商【" + ro.getParentSupplierGuid() + "】不存在！");
            }
            ro.setParentSupplierGuid(parentSupplier.getSupplierGuid());
        }
        // 处理币种
        if (!StringUtils.isEmpty(ro.getDefaultCurrencyGuid())) {
            ErpBasicMgtCurrency currency = iErpBasicMgtCurrencyService.getOne(Wrappers
                    .<ErpBasicMgtCurrency>lambdaQuery()
                    .select(ErpBasicMgtCurrency::getCurrencyGuid)
                    .eq(ErpBasicMgtCurrency::getCurrencyName, ro.getDefaultCurrencyGuid()));
            if (ObjectUtil.isNull(currency) || StringUtils.isEmpty(currency.getCurrencyGuid())) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "币种【" + ro.getDefaultCurrencyGuid() + "】不存在！");
            }
            ro.setDefaultCurrencyGuid(currency.getCurrencyGuid());
        }
        // 处理发票税率
        if (!StringUtils.isEmpty(ro.getDefaultInvoiceTypeName()) && !StringUtils.isEmpty(ro.getDefaultInvoiceTypeTaxRateGuid())) {
            String invoiceTypeTaxRateGuid = baseMapper.getInvoiceTypeTaxRateGuid(ro.getDefaultInvoiceTypeName(), new BigDecimal(ro.getDefaultInvoiceTypeTaxRateGuid()));
            if (StringUtils.isEmpty(invoiceTypeTaxRateGuid)) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "发票类型税率【" + ro.getDefaultInvoiceTypeName() + "=>" + ro.getDefaultInvoiceTypeTaxRateGuid() + "】不存在！");
            }
            ro.setDefaultInvoiceTypeTaxRateGuid(invoiceTypeTaxRateGuid);
        }
        // 处理送货类型
        if (!StringUtils.isEmpty(ro.getDefaultDeliveryTypeGuid())) {
            ErpBasicMgtDeliveryType deliveryType = iErpBasicMgtDeliveryTypeService.getOne(Wrappers
                    .<ErpBasicMgtDeliveryType>lambdaQuery()
                    .select(ErpBasicMgtDeliveryType::getDeliveryTypeGuid)
                    .eq(ErpBasicMgtDeliveryType::getDeliveryTypeName, ro.getDefaultDeliveryTypeGuid()));
            if (ObjectUtil.isNull(deliveryType) || StringUtils.isEmpty(deliveryType.getDeliveryTypeGuid())) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "送货类型【" + ro.getDefaultDeliveryTypeGuid() + "】不存在！");
            }
            ro.setDefaultDeliveryTypeGuid(deliveryType.getDeliveryTypeGuid());
        }
        // 处理结算类型
        if (!StringUtils.isEmpty(ro.getDefaultSettlementTypeGuid())) {
            ErpBasicMgtSettlementType settlementType = iErpBasicMgtSettlementTypeService.getOne(Wrappers
                    .<ErpBasicMgtSettlementType>lambdaQuery()
                    .select(ErpBasicMgtSettlementType::getSettlementTypeGuid)
                    .eq(ErpBasicMgtSettlementType::getSettlementTypeName, ro.getDefaultSettlementTypeGuid()));
            if (ObjectUtil.isNull(settlementType) || StringUtils.isEmpty(settlementType.getSettlementTypeGuid())) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "结算类型【" + ro.getDefaultSettlementTypeGuid() + "】不存在！");
            }
            ro.setDefaultSettlementTypeGuid(settlementType.getSettlementTypeGuid());
        }
        // 设置审核状态为已完成
        ro.setAuditStatus(BusinessStatusEnum.FINISH.getStatus());
        // 设置审核时间为当前时间
        ro.setAuditDate(new Date());
        // 保存数据到数据库
        this.create(ro);
    }

    /**
     * 新增和修改时字段校验
     *
     * @param ro
     */
    private void saveOrUpdateVerify(ErpSupplierMgtSupplierRO ro) {
        // 判断名称是否重复
        LambdaQueryWrapper<ErpSupplierMgtSupplier> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ErpSupplierMgtSupplier::getSupplierShortName, ro.getSupplierShortName())
                .ne(ro.getSupplierGuid() != null, ErpSupplierMgtSupplier::getSupplierGuid, ro.getSupplierGuid());
        if (baseMapper.exists(queryWrapper)) {
            throw new FlowException(ResultErrorCode.REPETITION_SUPPLIER_NAME);
        }

        // 如果【供应商全称】为空，默认取【供应商简称】
        if (StringUtils.isEmpty(ro.getSupplierFullName())) {
            ro.setSupplierFullName(ro.getSupplierShortName());
        }
    }

    /**
     * 删除关联表数据
     *
     * @param supplierGuid
     */
    public void removeAssociatedData(String supplierGuid) {
        // 供应商地址
        supplierAddressService.delBySupplierGuid(supplierGuid);
        // 供应商文件
        supplierFileService.delBySupplierGuid(supplierGuid);
        // 供应商联系人
        supplierContactService.delBySupplierGuid(supplierGuid);

        // 双写删除
        QueryWrapper<OrganizationAddress> organizationAddressQueryWrapper = new QueryWrapper<>();
        organizationAddressQueryWrapper.eq("organization_id", supplierGuid);
        organizationAddressService.remove(organizationAddressQueryWrapper);

        QueryWrapper<OrganizationContact> organizationContactQueryWrapper = new QueryWrapper<>();
        organizationContactQueryWrapper.eq("organization_id", supplierGuid);
        organizationContactService.remove(organizationContactQueryWrapper);

        QueryWrapper<OrganizationFile> organizationFileQueryWrapper = new QueryWrapper<>();
        organizationFileQueryWrapper.eq("organization_id", supplierGuid);
        organizationFileService.remove(organizationFileQueryWrapper);

    }

    /**
     * 保存关联表数据
     *
     * @param supplierGuid
     * @param ro
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAssociatedData(String supplierGuid, ErpSupplierMgtSupplierRO ro) {
        // 供应商地址
        supplierAddressService.saveData(ro.getSupplierAddressROList(), ro.getDelSupplierAddressGuids(), supplierGuid);
        // 供应商文件
        supplierFileService.saveData(ro.getSupplierFileROList(), ro.getDelSupplierFileGuids(), supplierGuid);
        // 供应商联系人
        supplierContactService.saveData(ro.getSupplierContactROList(), ro.getDelSupplierContactGuids(), supplierGuid);
    }

    /**
     * 处理相关表已被删除数据
     *
     * @param entity
     * @param oldEntity
     * @param isDel
     */
    public void changeIsUsed(ErpSupplierMgtSupplier entity, ErpSupplierMgtSupplier oldEntity, boolean isDel) {
        if (isDel) {
            usedUtils.isUsed("erp_supplier_mgt_supplier_classification", entity.getSupplierClassificationGuid(), "supplier_classification_guid");
            usedUtils.isUsed("erp_supplier_mgt_supplier_level", entity.getSupplierLevelGuid(), "supplier_level_guid");
            usedUtils.isUsed("erp_basic_mgt_currency", entity.getDefaultCurrencyGuid(), "currency_guid");
            usedUtils.isUsed("erp_basic_mgt_invoice_type_tax_rate", entity.getDefaultInvoiceTypeTaxRateGuid(), "invoice_type_tax_rate_guid");
            usedUtils.isUsed("erp_basic_mgt_delivery_type", entity.getDefaultDeliveryTypeGuid(), "delivery_type_guid");
            usedUtils.isUsed("erp_basic_mgt_settlement_type", entity.getDefaultSettlementTypeGuid(), "settlement_type_guid");
        } else {
            if (oldEntity != null) {
                usedUtils.isUsed("erp_supplier_mgt_supplier_classification", entity.getSupplierClassificationGuid(), oldEntity.getSupplierClassificationGuid(), "supplier_classification_guid");
                usedUtils.isUsed("erp_supplier_mgt_supplier_level", entity.getSupplierLevelGuid(), oldEntity.getSupplierLevelGuid(), "supplier_level_guid");
                usedUtils.isUsed("erp_basic_mgt_currency", entity.getDefaultCurrencyGuid(), oldEntity.getDefaultCurrencyGuid(), "currency_guid");
                usedUtils.isUsed("erp_basic_mgt_invoice_type_tax_rate", entity.getDefaultInvoiceTypeTaxRateGuid(), oldEntity.getDefaultInvoiceTypeTaxRateGuid(), "invoice_type_tax_rate_guid");
                usedUtils.isUsed("erp_basic_mgt_delivery_type", entity.getDefaultDeliveryTypeGuid(), oldEntity.getDefaultDeliveryTypeGuid(), "delivery_type_guid");
                usedUtils.isUsed("erp_basic_mgt_settlement_type", entity.getDefaultSettlementTypeGuid(), oldEntity.getDefaultSettlementTypeGuid(), "settlement_type_guid");
            } else {
                usedUtils.isUsed("erp_supplier_mgt_supplier_classification", entity.getSupplierClassificationGuid(), null, "supplier_classification_guid");
                usedUtils.isUsed("erp_supplier_mgt_supplier_level", entity.getSupplierLevelGuid(), null, "supplier_level_guid");
                usedUtils.isUsed("erp_basic_mgt_currency", entity.getDefaultCurrencyGuid(), null, "currency_guid");
                usedUtils.isUsed("erp_basic_mgt_invoice_type_tax_rate", entity.getDefaultInvoiceTypeTaxRateGuid(), null, "invoice_type_tax_rate_guid");
                usedUtils.isUsed("erp_basic_mgt_delivery_type", entity.getDefaultDeliveryTypeGuid(), null, "delivery_type_guid");
                usedUtils.isUsed("erp_basic_mgt_settlement_type", entity.getDefaultSettlementTypeGuid(), null, "settlement_type_guid");
            }
        }
    }

    /**
     * 编辑删除操作前检查
     *
     * @param id
     */
    private ErpSupplierMgtSupplier operationCheck(String id) {
        ErpSupplierMgtSupplier entity = baseMapper.selectById(id);
//        if (entity.getIsUsed() != null && entity.getIsUsed()) {
//            throw new FlowException(BaseResultErrorCodeImpl.IS_USED_NOT_DELETE_OR_EDIT);
//        }
        if (Objects.isNull(entity)) {
            throw new FlowException(BaseResultErrorCodeImpl.NO_REPETITION_DATA);
        }
        if (!entity.getAuditStatus().equals(BusinessStatusEnum.DRAFT.getStatus())) {
            throw new FlowException(BaseResultErrorCodeImpl.AUDIT_STATE_NOT_DRAFT);
        }
        return entity;
    }

    /**
     * 双写客户数据
     */
    private void writeCustomer(ErpSupplierMgtSupplier entity) {
        SupplierDTO supplierDTO = XyBeanUtil.copyPropertiesByGst(entity, SupplierDTO.class);
        supplierDTO.setSupplierName(entity.getSupplierFullName());
        supplierDTO.setSupplierUserCode(entity.getSupplierCode());
        supplierDTO.setSupplierId(entity.getSupplierGuid());
        supplierDTO.setSupplierClassification(entity.getSupplierClassificationGuid());
        supplierDTO.setSupplierLevel(entity.getSupplierLevelGuid());
        if (ObjectUtil.isNotNull(entity.getDefaultInvoiceTypeTaxRateGuid())) {
            ErpBasicMgtInvoiceTypeTaxRateVO result = iErpBasicMgtInvoiceTypeTaxRateService.getById(entity.getDefaultInvoiceTypeTaxRateGuid());
            supplierDTO.setSupplierDefaultInvoiceTypeTaxRate(null != result ? result.getTaxRate() : null);
        }
        supplierDTO.setSupplierCurrencyId(entity.getDefaultCurrencyGuid());
        supplierDTO.setSupplierDefaultDeliveryType(entity.getDefaultDeliveryTypeGuid());
        supplierDTO.setSupplierDefaultSettlementType(entity.getDefaultSettlementTypeGuid());
        supplierDTO.setSupplierAccountPeriod(entity.getAccountPeriod());
        supplierDTO.setSupplierEnterpriseTaxIdentificationNumber(entity.getEnterpriseTaxIdentificationNumber());
        supplierDTO.setSupplierClazz(ClazzEnum.XY_USERCENTER_ORGANIZATION.name());
        supplierDTO.setSupplierShortName(entity.getSupplierShortName());
        supplierDTO.setSupplierMobile(entity.getMobilephone());
        supplierDTO.setSupplierTelephone(entity.getTelephone());
        supplierDTO.setSupplierEmail(entity.getEmail());
        supplierDTO.setSupplierFaxNo(entity.getFax());
        supplierDTO.setSupplierBankAccountId(entity.getBankAccount());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(entity.getDefaultCurrencyGuid())) {
            //获取全部的币种信息
            ErpBasicMgtCurrencyVO currencyVO = iErpBasicMgtCurrencyService.getById(entity.getDefaultCurrencyGuid());
            if (Objects.nonNull(currencyVO)) {
                supplierDTO.setSupplierCurrencyId(currencyVO.getCurrencyName());
            }
        }
        supplierService.saveOrUpdate(supplierDTO);
    }
}
