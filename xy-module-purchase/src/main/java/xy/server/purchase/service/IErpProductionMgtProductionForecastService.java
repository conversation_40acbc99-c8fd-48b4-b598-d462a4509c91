package xy.server.purchase.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.purchase.entity.ErpProductionMgtProductionForecast;
import xy.server.purchase.entity.model.qo.ErpProductionMgtProductionForecastQO;
import xy.server.purchase.entity.model.ro.ErpProductionMgtProductionForecastRO;
import xy.server.purchase.entity.model.vo.ErpProductionMgtProductionForecastVO;

import java.util.List;

/**
 * <AUTHOR>
 * @apiNote 服务类
 * @since 2024-04-10
 */
public interface IErpProductionMgtProductionForecastService extends IService<ErpProductionMgtProductionForecast> {
    String create(ErpProductionMgtProductionForecastRO ro);

    boolean delete(String workorderGuid);

    boolean deleteByBatch(List<String> workorderGuids);

    String update(ErpProductionMgtProductionForecastRO ro);

    List<String> saveDate(InsertOrUpdateList<ErpProductionMgtProductionForecastRO> dataList);

    ErpProductionMgtProductionForecastVO getDataById(String workorderGuid);

    List<ErpProductionMgtProductionForecastVO> findList(ErpProductionMgtProductionForecastQO qo);

    IPage<ErpProductionMgtProductionForecastVO> findPage(PageParams<ErpProductionMgtProductionForecastQO> pageParams);

    /**
     * 查询待开采购预测的业务预测列表
     * @param qo
     * @return
     */
    List<ErpProductionMgtProductionForecastVO> findNotBilledList(ErpProductionMgtProductionForecastQO qo);

    void sycForecastingToProof(ErpProductionMgtProductionForecast entity, List<ErpProductionMgtProductionForecast> productionForecastList,Boolean isSave);

    Boolean sycDeleteForecastingToProof(List<String> delDetailGuids,Boolean isDeleteAll);

    void sycPurchaseRequistion(ErpProductionMgtProductionForecast entity, List<ErpProductionMgtProductionForecast> productionForecastList);
}
