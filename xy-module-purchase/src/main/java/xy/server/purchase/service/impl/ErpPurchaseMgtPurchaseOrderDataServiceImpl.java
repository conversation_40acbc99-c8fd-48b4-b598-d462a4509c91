package xy.server.purchase.service.impl;

import cn.easyes.common.utils.CollectionUtils;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.enums.*;
import com.xunyue.common.util.XyBeanUtil;
import com.xunyue.config.exception.FlowException;
import com.xunyue.exteriororder.entity.model.dto.ProcOrderDetDTO;
import com.xunyue.exteriororder.service.IProcOrderDetService;
import com.xunyue.node.common.enums.ClazzEnum;
import com.xunyue.node.common.enums.TypeEnum;
import com.xunyue.node.entity.model.dto.LinkDTO;
import com.xunyue.node.service.ILinkService;
import com.xunyue.proof.entity.model.dto.ProofUnitQuantityDTO;
import com.xunyue.proof.service.IProofUnitQuantityService;
import com.xunyue.tenant.sign.UserApi;
import com.xy.util.BaseContext;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import xy.server.basic.service.IErpBusinessMgtOtherExpensesService;
import xy.server.basic.util.UuidUtils;
import xy.server.dto.XyMemberDto;
import xy.server.material.entity.ErpMaterialMgtMaterial;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;
import xy.server.material.service.IErpMaterialMgtMaterialService;
import xy.server.purchase.entity.ErpBusinessMgtPurchaseOrderDataExtend;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseOrderData;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseRequisition;
import xy.server.purchase.entity.model.ro.ErpPurchaseMgtPurchaseOrderDataRO;
import xy.server.purchase.entity.model.vo.ErpPurchaseMgtBusinessOrderDataVO;
import xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseOrderDataVO;
import xy.server.purchase.i18n.ResultErrorCode;
import xy.server.purchase.mapper.ErpPurchaseMgtPurchaseOrderDataMapper;
import xy.server.purchase.service.IErpBusinessMgtPurchaseOrderDataExtendService;
import xy.server.purchase.service.IErpPurchaseMgtPurchaseOrderDataService;
import xy.server.purchase.service.IErpPurchaseMgtPurchaseOrderPlaceOfDeliveryService;
import xy.server.purchase.service.IErpPurchaseMgtPurchaseRequisitionService;
import xy.server.work.entity.ErpProductionMgtWorkorderMaterialUsage;
import xy.server.work.service.IErpProductionMgtWorkorderMaterialUsageService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service
public class ErpPurchaseMgtPurchaseOrderDataServiceImpl extends ServiceImpl<ErpPurchaseMgtPurchaseOrderDataMapper, ErpPurchaseMgtPurchaseOrderData> implements IErpPurchaseMgtPurchaseOrderDataService {

    private final IErpPurchaseMgtPurchaseRequisitionService iErpPurchaseMgtPurchaseRequisitionService;
    private final IErpProductionMgtWorkorderMaterialUsageService iErpProductionMgtWorkorderMaterialUsageService;
    private final IErpPurchaseMgtPurchaseOrderPlaceOfDeliveryService iErpPurchaseMgtPurchaseOrderPlaceOfDeliveryService;
    private final IErpMaterialMgtMaterialService materialService;
    private final IErpBusinessMgtOtherExpensesService otherExpensesService;
    private final IErpBusinessMgtPurchaseOrderDataExtendService dataExtendService;
    private final UserApi userApi;
    private final IProcOrderDetService procOrderDetService;
    private final ILinkService linkService;
    private final IProofUnitQuantityService proofUnitQuantityService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDate(String orderGuid, List<ErpPurchaseMgtPurchaseOrderDataRO> detailList, List<String> delDetailIds) {
        // 1.更新数据来源采购相关数据
        this.updateWorkorderMaterialUsage(orderGuid, true);

        if (CollectionUtil.isNotEmpty(delDetailIds)) {
            List<ErpPurchaseMgtPurchaseOrderData> orderDataList = baseMapper.selectList(Wrappers.<ErpPurchaseMgtPurchaseOrderData>lambdaQuery()
                    .in(ErpPurchaseMgtPurchaseOrderData::getOrderDataGuid, delDetailIds));
            if (CollectionUtil.isNotEmpty(orderDataList)) {
                // 需要删除的采购订单明细来源工单guids（虚拟工单）
                List<String> delSourceGuids = new ArrayList<>();
                // 需要更新的采购订单明细来源工单guids（真实工单）
                List<String> updateSourceGuids = new ArrayList<>();
                for (ErpPurchaseMgtPurchaseOrderData detail : orderDataList) {
                    if (!StringUtils.isEmpty(detail.getSourceGuid())) {
                        // 采购订单绑定的采购工单（虚拟工单）
                        ErpPurchaseMgtPurchaseRequisition sourceWorkorder = iErpPurchaseMgtPurchaseRequisitionService.getById(detail.getSourceGuid());
                        assert sourceWorkorder != null;
                        delSourceGuids.add(sourceWorkorder.getWorkorderGuid());
                        updateSourceGuids.add(sourceWorkorder.getParentClassificationGuid());
                    }
                }
                // 2.删除采购订单明细
                super.removeBatchByIds(delDetailIds);
                procOrderDetService.removeBatch(delDetailIds);
                // 3.删除交货地址数据
                iErpPurchaseMgtPurchaseOrderPlaceOfDeliveryService.delByOrderGuid(orderGuid, delDetailIds);
                if (CollectionUtil.isNotEmpty(delSourceGuids)) {
                    // 4.删除采购订单明细对应的采购工单明细
                    iErpPurchaseMgtPurchaseRequisitionService.removeBatchByIds(delSourceGuids);
                }
                // 5.批量更新申购单采购情况
                this.updatePurchaseState(updateSourceGuids);
            }
        }

        List<ErpPurchaseMgtPurchaseOrderData> entityList = new ArrayList<>();
        List<String> sourceGuids = new ArrayList<>();
        // 保存订单数据表
        List<ErpPurchaseMgtPurchaseOrderData> nodeOrderData = new ArrayList<>();
        List<ErpPurchaseMgtPurchaseOrderData> linkOrderDataS = new ArrayList<>();
        detailList.forEach(s -> {
            ErpPurchaseMgtPurchaseOrderData orderData = new ErpPurchaseMgtPurchaseOrderData();
            BeanUtils.copyProperties(s, orderData);
            orderData.setOrderGuid(orderGuid);
            // 默认未完成状态
            orderData.setCompletionStatus(CompletionStatusEnum.NOT_FINISH.getCode());
            orderData.setSourceValue(null);
            super.saveOrUpdate(orderData);
            //插入扩展数据
            ErpBusinessMgtPurchaseOrderDataExtend extend = new ErpBusinessMgtPurchaseOrderDataExtend();
            BeanUtils.copyProperties(s, extend);
            extend.setOrderDataGuid(orderData.getOrderDataGuid());
            dataExtendService.saveOrUpdate(extend);
            orderData.setOrderDataExtend(extend);
            nodeOrderData.add(orderData);
            // 其他费用findList
            if (CollUtil.isNotEmpty(s.getOtherExpensesList())) {
                s.getOtherExpensesList().forEach(other -> {
                    if (com.xunyue.common.util.StringUtils.isBlank(other.getOtherExpensesGuid())) {
                        other.setOtherExpensesGuid(UuidUtils.generateUUID());
                    }
                    other.setSourceGuid(orderData.getOrderDataGuid());
                    other.setSourceValus(OtherExpensesEnum.PURCHASE_ORDER.getCode());
                    other.setTenantGuid(BaseContext.getTenantGuid());
                    other.setCreator(BaseContext.getCreator());
                    other.setCreatorGuid(BaseContext.getCreatorGuid());
                    other.setCreateDate(LocalDateTime.now());
                    other.setLastUpdater(BaseContext.getCreator());
                    other.setLastUpdaterGuid(BaseContext.getCreator());
                    other.setLastUpdateDate(LocalDateTime.now());
                });
                baseMapper.saveOrUpdateOtherExpensesBatch(s.getOtherExpensesList());
            }
            if (CollectionUtil.isNotEmpty(s.getIsdelectList())) {
                // 6.删除其他费用
                baseMapper.deleteByOtherExpensesBatch(s.getIsdelectList());
            }
            s.setOrderDataGuid(orderData.getOrderDataGuid());
            if (CollectionUtils.isNotEmpty(s.getDetailList())) {
                s.getDetailList().forEach(detail -> {
                    if (!StringUtils.isEmpty(detail.getSourceGuid())) {
                        // 申购单
                        ErpPurchaseMgtPurchaseRequisition sourceWorkorder = iErpPurchaseMgtPurchaseRequisitionService.getById(detail.getSourceGuid());
                        assert sourceWorkorder != null;
                        if (Objects.nonNull(sourceWorkorder)){
                            ErpPurchaseMgtPurchaseOrderData linkOrderData = new ErpPurchaseMgtPurchaseOrderData();
                            linkOrderData.setOrderDataGuid(s.getOrderDataGuid());
                            linkOrderData.setSourceGuid(sourceWorkorder.getWorkorderGuid());
                            linkOrderDataS.add(linkOrderData);
                        }
                        // 先处理采购工单明细数据
                        if (StringUtils.isEmpty(detail.getOrderDataGuid())) {
                            // 新增-->创建一条采购工单明细数据与之一一对应
                            ErpPurchaseMgtPurchaseRequisition workorderEntity = new ErpPurchaseMgtPurchaseRequisition();
                            //workorderEntity
                            BeanUtils.copyProperties(sourceWorkorder, workorderEntity, "workorderGuid");
                            workorderEntity.setParentClassificationGuid(sourceWorkorder.getWorkorderGuid());
                            workorderEntity.setWorkorderState(PurchaseStateEnum.FULL.getValue());
                            workorderEntity.setQuantity(detail.getQuantity());
                            workorderEntity.setTotalQuantity(workorderEntity.getQuantity());
                            workorderEntity.setDescription("系统生成虚拟数据，为实现订单明细与工单明细一对一");
                            iErpPurchaseMgtPurchaseRequisitionService.save(workorderEntity);
                            // 获取新的采购工单明细guid作为采购订单明细的来源guid
                            detail.setSourceGuid(workorderEntity.getWorkorderGuid());
                            sourceGuids.add(workorderEntity.getParentClassificationGuid());
                        } else {
                            // 编辑-->修改申购数
                            sourceWorkorder.setQuantity(detail.getQuantity());
                            sourceWorkorder.setTotalQuantity(sourceWorkorder.getQuantity());
                            iErpPurchaseMgtPurchaseRequisitionService.updateById(sourceWorkorder);
                            sourceGuids.add(sourceWorkorder.getParentClassificationGuid());
                        }
                    }
                    if (StringUtils.isEmpty(detail.getOrderDataGuid())) {
                        // 如果为新增则先设置guid，用于跟交货地址想关联
                        detail.setOrderDataGuid(IdUtil.fastSimpleUUID());
                    }
                    ErpPurchaseMgtPurchaseOrderData entity = new ErpPurchaseMgtPurchaseOrderData();
                    BeanUtils.copyProperties(detail, entity);
                    entity.setParentClassificationGuid(orderData.getOrderDataGuid());
                    // 默认未完成状态
                    entity.setCompletionStatus(CompletionStatusEnum.NOT_FINISH.getCode());
                    entity.setTotalQuantity(entity.getQuantity());
                    entity.setSourceValue(null);
                    entityList.add(entity);
                    //插入扩展数据
                    ErpBusinessMgtPurchaseOrderDataExtend extend1 = new ErpBusinessMgtPurchaseOrderDataExtend();
                    BeanUtils.copyProperties(detail, extend1);
                    dataExtendService.saveOrUpdate(extend1);
                });
            }
        });
        super.saveOrUpdateBatch(entityList);

        XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();
        ThreadUtil.execAsync(() -> {
            userApi.setThreadLocal(memberDto);
            this.createOrderDetail(nodeOrderData, orderGuid,linkOrderDataS);
        });
        // 更新交货地址数据
        iErpPurchaseMgtPurchaseOrderPlaceOfDeliveryService.saveDate(orderGuid, detailList);
        // 批量更新申购单采购情况
        this.updatePurchaseState(sourceGuids);
        // 3.最后更新数据来源采购相关数据
        this.updateWorkorderMaterialUsage(orderGuid, false);
    }

    /**
     * 双写采购单数据
     * @param entityList
     * @param orderGuid
     */
    public void createOrderDetail(List<ErpPurchaseMgtPurchaseOrderData> entityList, String orderGuid,List<ErpPurchaseMgtPurchaseOrderData> linkOrderDataS) {
        if (CollectionUtil.isNotEmpty(entityList)) {
            //根据采购预测的物料id获取单位数据
            List<ErpMaterialMgtMaterial> materialList = materialService.listByIds(entityList.stream().
                    map(ErpPurchaseMgtPurchaseOrderData::getMaterialGuid).distinct().collect(Collectors.toList()));
            //批量删除单位数量关联表
            List<String> parentIds = entityList.stream().map(ErpPurchaseMgtPurchaseOrderData::getOrderDataGuid).distinct().collect(Collectors.toList());
            ProofUnitQuantityDTO quantityDTO = new ProofUnitQuantityDTO();
            quantityDTO.setProofUnitQuantityParentIds(parentIds);
            List<ProofUnitQuantityDTO> quantityDTOList = proofUnitQuantityService.findList(quantityDTO);
            if (CollectionUtil.isNotEmpty(quantityDTOList)) {
                proofUnitQuantityService.removeByIds(quantityDTOList.stream().map(ProofUnitQuantityDTO::getProofUnitQuantityId).collect(Collectors.toList()));
            }
            Map<String, ErpMaterialMgtMaterial> materialMap = materialList.stream().collect(Collectors.toMap(ErpMaterialMgtMaterial::getMaterialGuid, material -> material));
            List<ProofUnitQuantityDTO> proofUnitQuantityList = new ArrayList<>();
            List<LinkDTO> linkDTOS = new ArrayList<>();
            List<ProcOrderDetDTO> detailDTOList = new ArrayList<>();
            List<String> orderDataGuids = new ArrayList<>();
            String tenantId = "";
            for (ErpPurchaseMgtPurchaseOrderData orderData : entityList) {
                ProcOrderDetDTO orderDetailDTO = XyBeanUtil.copyPropertiesByGst(orderData, ProcOrderDetDTO.class);
                //入库实例
                orderDetailDTO.setProcOrderDetQuantity(orderData.getQuantity());
                orderDetailDTO.setProcOrderDetTenantId(orderData.getTenantGuid());
                tenantId=orderData.getTenantGuid();
                orderDetailDTO.setProcOrderDetParentId(orderGuid);
                orderDetailDTO.setProcOrderDetId(orderData.getOrderDataGuid());
                orderDataGuids.add(orderData.getOrderDataGuid());
                orderDetailDTO.setProcOrderDetClazz(ClazzEnum.PROCUREMENT_ORDER_DETAIL.name());
                orderDetailDTO.setProcOrderDetMaterialId(orderData.getMaterialGuid());
                orderDetailDTO.setProcOrderDetDeliveryDate(ObjectUtil.isNotEmpty(orderData.getDeliveryDate())?Date.from(orderData.getDeliveryDate().atZone(ZoneId.systemDefault()).toInstant()):null);
                orderDetailDTO.setProcOrderDetExpProcessId(orderData.getExperienceProductionProcessGuid());
                orderDetailDTO.setProcOrderDetGiftQty(orderData.getGiftQuantity());
                orderDetailDTO.setProcOrderDetSpareQty(orderData.getSpareQuantity());
                orderDetailDTO.setProcOrderDetUsedQty(orderData.getUsingInventoryQuantity());
                orderDetailDTO.setProcOrderDetTaxInclPrice(orderData.getQuotationUnitPriceIncludingTax());
                orderDetailDTO.setProcOrderDetUnitPriceTax(orderData.getUnitPriceIncludingTax());
                orderDetailDTO.setProcOrderDetTaxInclTotal(orderData.getTotalAmountIncludingTax());
                orderDetailDTO.setProcOrderDetTaxExclPrice(orderData.getQuotationUnitPriceWithoutTax());
                orderDetailDTO.setProcOrderDetTaxExclUnitPrice(orderData.getUnitPriceWithoutTax());
                orderDetailDTO.setProcOrderDetTaxExclTotal(orderData.getTotalAmountWithoutTax());
                orderDetailDTO.setProcOrderDetLocUnitPrice(orderData.getLocalCurrencyUnitPrice());
                orderDetailDTO.setProcOrderDetLocalAmount(orderData.getLocalCurrencyTotalAmount());
                orderDetailDTO.setProcOrderDetSettlePrice(orderData.getSettlementUnitPrice());
                orderDetailDTO.setProcOrderDetSettleAmount(orderData.getSettlementTotalAmount());

                orderDetailDTO.setProcOrderDetFscType(orderData.getFscDeclarationGuid());
                orderDetailDTO.setProcOrderDetMatCat(orderData.getMaterialClassificationGuid());
                orderDetailDTO.setProcOrderDetSumQty(orderData.getTotalQuantity());
//                orderDetailDTO.setProcOrderDetQuantity(orderData.getTotalQuantity());
                orderDetailDTO.setProcOrderDetExternalNo(orderData.getExternalTrackingNumber());
                orderDetailDTO.setProcOrderDetExpectedDate(orderData.getExpectedReceiptDate());
                orderDetailDTO.setProcOrderDetSource(orderData.getSourceGuid());
                orderDetailDTO.setProcOrderDetDescription(orderData.getDescription());
                orderDetailDTO.setProcOrderDetCompleteQuantity(orderData.getCompletionQuantity());
                orderDetailDTO.setProcOrderDetCompleteStatus(orderData.getCompletionStatus());
                orderDetailDTO.setProcOrderDetDeliveryTypeId(orderData.getDeliveryTypeGuid());
                orderDetailDTO.setProcOrderDetExtMatName(orderData.getExternalMaterialName());
                orderDetailDTO.setProcOrderDetExtMatCode(orderData.getExternalMaterialCode());
                orderDetailDTO.setProcOrderDetExtMatBarcode(orderData.getExternalMaterialBarCode());

                //根据物料id获取单位id
                //保存单据单位数量关联表
                ProofUnitQuantityDTO unitQuantityDTO = new ProofUnitQuantityDTO();
                orderDetailDTO.setProcOrderDetUnitCode(materialMap.get(orderData.getMaterialGuid()).getUnitGuid());
                unitQuantityDTO.setProofUnitQuantityQuantity(orderData.getQuantity());
                unitQuantityDTO.setProofUnitQuantityId(IdWorker.getIdStr());
                unitQuantityDTO.setProofUnitQuantityUnitId(orderDetailDTO.getProcOrderDetUnitCode());
                unitQuantityDTO.setProofUnitQuantityParentId(orderData.getOrderDataGuid());
                unitQuantityDTO.setProofUnitQuantityClazz(ClazzEnum.PROOF_UNIT_QUANTITY.name());
                proofUnitQuantityList.add(unitQuantityDTO);
                if (Objects.nonNull(orderData.getOrderDataExtend())){
                    ProofUnitQuantityDTO unitQuantityDTO1 = new ProofUnitQuantityDTO();
                    unitQuantityDTO1.setProofUnitQuantityQuantity(orderData.getOrderDataExtend().getPackagingQuantity());
                    unitQuantityDTO1.setProofUnitQuantityUnitId("7caebe79177d9e07ee51ffdd35013b4a");
                    unitQuantityDTO1.setProofUnitQuantityParentId(orderData.getOrderDataGuid());
                    unitQuantityDTO1.setProofUnitQuantityId(IdWorker.getIdStr());
                    unitQuantityDTO1.setProofUnitQuantityClazz(ClazzEnum.PROOF_UNIT_QUANTITY.name());
                    proofUnitQuantityList.add(unitQuantityDTO1);
                }
                detailDTOList.add(orderDetailDTO);
                /*LinkDTO linkDTO = new LinkDTO();
                linkDTO.setLinkId(orderData.getOrderDataGuid());
                linkDTO.setLinkClazz(ClazzEnum.CONTRACT.name());
                linkDTO.setLinkSource(orderData.getSourceGuid());
                linkDTOS.add(linkDTO);
                */
            }
            //采购申请关联采购订单
            if (CollectionUtils.isNotEmpty(linkOrderDataS)){
                //删除应存在的link
                linkService.removeBatchByTargetAndClazz(ClazzEnum.PURCHASE_REQUISITION_DETAIL.name()+ StringPool.DASH+ClazzEnum.PROCUREMENT_ORDER_DETAIL.name(),orderDataGuids,tenantId);
                linkOrderDataS.forEach(orderData->{
                    LinkDTO linkDTO = new LinkDTO();
                    linkDTO.setLinkTarget(orderData.getOrderDataGuid());
                    linkDTO.setLinkSource(orderData.getSourceGuid());
                    linkDTO.setLinkTypeCode(TypeEnum.SOURCE.getCode());
                    linkDTO.setLinkId(IdWorker.getIdStr());
                    linkDTO.setLinkClazz(ClazzEnum.PURCHASE_REQUISITION_DETAIL.name()+ StringPool.DASH+ClazzEnum.PROCUREMENT_ORDER_DETAIL.name());
                    linkDTO.setLinkIsolation(orderData.getTenantGuid());
                    linkDTOS.add(linkDTO);
                });
            }
            //无来源不进行保存link关系
            if(CollUtil.isNotEmpty(linkDTOS)){
                linkService.saveOrUpdateBatch(linkDTOS);
            }
            procOrderDetService.saveOrUpdateBatch(detailDTOList);
            proofUnitQuantityService.saveOrUpdateBatch(proofUnitQuantityList);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByOrderGuid(String orderGuid) {
        // 1.更新数据来源采购相关数据
        this.updateWorkorderMaterialUsage(orderGuid, true);

        List<ErpPurchaseMgtPurchaseOrderData> orderDataList = baseMapper.selectList(Wrappers.<ErpPurchaseMgtPurchaseOrderData>lambdaQuery()
                .eq(ErpPurchaseMgtPurchaseOrderData::getOrderGuid, orderGuid));
        if (CollectionUtil.isNotEmpty(orderDataList)) {
            // 需要删除的采购订单明细guids
            List<String> delDetailIds = new ArrayList<>();
            // 需要删除的采购订单明细来源工单guids（虚拟工单）
            List<String> delSourceGuids = new ArrayList<>();
            // 需要更新的采购订单明细来源工单guids（真实工单）
            List<String> updateSourceGuids = new ArrayList<>();
            for (ErpPurchaseMgtPurchaseOrderData detail : orderDataList) {
                delDetailIds.add(detail.getOrderDataGuid());
                LambdaQueryWrapper<ErpPurchaseMgtPurchaseOrderData> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ErpPurchaseMgtPurchaseOrderData::getParentClassificationGuid, detail.getOrderDataGuid());
                List<ErpPurchaseMgtPurchaseOrderData> list = baseMapper.selectList(wrapper);
                if (CollectionUtils.isNotEmpty(list)) {
                    delDetailIds.addAll(list.stream().map(ErpPurchaseMgtPurchaseOrderData::getOrderDataGuid).collect(Collectors.toList()));
                }
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(s -> {
                        if (!StringUtils.isEmpty(s.getSourceGuid())) {
                            // 采购订单绑定的采购工单（虚拟工单）
                            ErpPurchaseMgtPurchaseRequisition sourceWorkorder = iErpPurchaseMgtPurchaseRequisitionService.getById(s.getSourceGuid());
                            assert sourceWorkorder != null;
                            delSourceGuids.add(sourceWorkorder.getWorkorderGuid());
                            updateSourceGuids.add(sourceWorkorder.getParentClassificationGuid());
                        }
                    });
                }
            }
            if (CollectionUtil.isNotEmpty(delDetailIds)) {
                // 2.删除采购订单明细
                super.removeBatchByIds(delDetailIds);
                procOrderDetService.removeBatch(delDetailIds);
                // 3.删除交货地址数据
                iErpPurchaseMgtPurchaseOrderPlaceOfDeliveryService.delByOrderGuid(orderGuid, delDetailIds);
            }
            if (CollectionUtil.isNotEmpty(delSourceGuids)) {
                // 4.删除采购订单明细对应的采购工单明细
                iErpPurchaseMgtPurchaseRequisitionService.removeBatchByIds(delSourceGuids);
            }
            // 5.批量更新申购单采购情况
            this.updatePurchaseState(updateSourceGuids);
        }
    }

    @Override
    public List<ErpPurchaseMgtPurchaseOrderDataVO> getDataByOrderGuids(List<String> orderGuids) {
        List<ErpPurchaseMgtPurchaseOrderDataVO> resultList = baseMapper.findListByOrderGuids(orderGuids);
        if (CollectionUtil.isNotEmpty(resultList)) {

            List<String> orderDataGuids = resultList.stream().map(ErpPurchaseMgtPurchaseOrderDataVO::getOrderDataGuid).distinct().collect(Collectors.toList());
            List<ErpPurchaseMgtPurchaseOrderDataVO> listByParentGuids = baseMapper.findListByParentGuids(orderDataGuids);

            // 获取物料Guid
            List<String> materialGuids = resultList
                    .stream()
                    .map(ErpPurchaseMgtPurchaseOrderDataVO::getMaterialGuid)
                    .distinct()
                    .collect(Collectors.toList());
            List<String> materialGuids2 = listByParentGuids
                    .stream()
                    .map(ErpPurchaseMgtPurchaseOrderDataVO::getMaterialGuid)
                    .distinct()
                    .collect(Collectors.toList());
            materialGuids.addAll(materialGuids2);

            Map<String, ErpMaterialMgtMaterialVO> materialVOMap = materialService.getMapByIds(materialGuids);

            Map<String, List<ErpPurchaseMgtPurchaseOrderDataVO>> childDetailGroupMap = listByParentGuids.stream()
                    .collect(Collectors.groupingBy(ErpPurchaseMgtPurchaseOrderDataVO::getParentClassificationGuid));

            resultList.forEach(item -> {
                List<ErpPurchaseMgtPurchaseOrderDataVO> childDetailList = childDetailGroupMap.get(item.getOrderDataGuid());
                if (CollUtil.isNotEmpty(childDetailList)) {
                    childDetailList.forEach(childDetail -> childDetail.setMaterialObj(childDetail.getMaterialObj()));
                }
                item.setDetailList(childDetailList);
                item.setMaterialObj(materialVOMap.get(item.getMaterialGuid()));
            });

            listByParentGuids.forEach(item -> {
                item.setMaterialObj(materialVOMap.get(item.getMaterialGuid()));
            });

            // 加载其他费用数据
            otherExpensesService.loadBusinessMgtOtherExpenses(OtherExpensesEnum.PURCHASE_ORDER, resultList
                    , ErpPurchaseMgtPurchaseOrderDataVO::getOrderDataGuid, ErpPurchaseMgtPurchaseOrderDataVO::setOtherExpensesList, null);
        }
        return resultList;
    }

    /**
     * 根据采购订单明细guids获取业务订单数据Map（采购成品流程链路）
     *
     * @param purchaseOrderDataGuids
     * @return
     */
    @Override
    public Map<String, ErpPurchaseMgtBusinessOrderDataVO> getBusinessOrderDataMapByPurchaseOrderDataGuids(List<String> purchaseOrderDataGuids) {
        if (CollectionUtil.isEmpty(purchaseOrderDataGuids)) {
            return MapUtil.empty();
        }
        List<ErpPurchaseMgtBusinessOrderDataVO> resultList = baseMapper.selectBusinessOrderDataListByPurchaseOrderDataGuids(purchaseOrderDataGuids);
        return CollStreamUtil.toMap(resultList, ErpPurchaseMgtBusinessOrderDataVO::getPurchaseOrderDataGuid, Function.identity());
    }

    /**
     * 批量更新申购单采购情况
     *
     * @param sourceGuids
     */
    private synchronized void updatePurchaseState(List<String> sourceGuids) {
        if (CollectionUtil.isNotEmpty(sourceGuids)) {
            List<ErpPurchaseMgtPurchaseRequisition> workorderList = new ArrayList<>();
            for (String sourceGuid : sourceGuids) {
                // 真实的采购工单（采购申请单明细）
                ErpPurchaseMgtPurchaseRequisition workorder = iErpPurchaseMgtPurchaseRequisitionService.getById(sourceGuid);
                assert workorder != null;
                // 获取总采购数量
                BigDecimal totalPurchaseQuantity = baseMapper.getTotalPurchaseQuantity(workorder.getWorkorderGuid());
                // 比较采购申请数量和总采购数量
                PurchaseStateEnum purchaseStateEnum = null;
                if (totalPurchaseQuantity.compareTo(BigDecimal.ZERO) == 0) {
                    purchaseStateEnum = PurchaseStateEnum.UN;
                } else if (totalPurchaseQuantity.compareTo(workorder.getQuantity()) == 0) {
                    purchaseStateEnum = PurchaseStateEnum.FULL;
                } else if (totalPurchaseQuantity.compareTo(workorder.getQuantity()) == 1) {
                    purchaseStateEnum = PurchaseStateEnum.EXCESS;
                } else {
                    purchaseStateEnum = PurchaseStateEnum.PARTIAL;
                }
                // 设置申购单采购情况
                workorder.setWorkorderState(purchaseStateEnum.getValue());
                workorderList.add(workorder);
            }
            iErpPurchaseMgtPurchaseRequisitionService.updateBatchById(workorderList);
        }
    }

    /**
     * 更新数据来源采购申请相关数据
     *
     * @param orderGuid
     * @param isDelete
     */
    private synchronized void updateWorkorderMaterialUsage(String orderGuid, boolean isDelete) {
        List<ErpPurchaseMgtPurchaseOrderData> detailList = baseMapper.getSourceDataS(orderGuid);
        /*List<ErpPurchaseMgtPurchaseOrderData> detailList = baseMapper.selectList(Wrappers.<ErpPurchaseMgtPurchaseOrderData>lambdaQuery()
                .eq(ErpPurchaseMgtPurchaseOrderData::getOrderGuid, orderGuid));*/
        for (ErpPurchaseMgtPurchaseOrderData detail : detailList) {
            if (!StringUtils.isEmpty(detail.getSourceGuid())) {
                // 采购订单绑定的采购工单（虚拟工单）
                ErpPurchaseMgtPurchaseRequisition sourceWorkorder = iErpPurchaseMgtPurchaseRequisitionService.getById(detail.getSourceGuid());
                // 数据来源为【工单】，更新工单材料用量表
                if (sourceWorkorder.getWorkorderProperties().equals(WorkorderPropertiesEnum.PURCHASE.getCode()) && sourceWorkorder.getSourceValue().equals(PurchaseSourceEnum.WORK_ORDER.getKey())) {
                    // 查询工单材料用量表数据
                    ErpProductionMgtWorkorderMaterialUsage workorderMaterialUsage = iErpProductionMgtWorkorderMaterialUsageService.getUsage(detail.getSourceGuid());
                    if (Objects.isNull(workorderMaterialUsage)) {
                        throw new FlowException(ResultErrorCode.DATA_NOT_STATUS);
                    }
                    BigDecimal purchasedQuantity = workorderMaterialUsage.getPurchasedQuantity();
                    // 计算采购总数量
                    BigDecimal total = isDelete ? purchasedQuantity.subtract(detail.getQuantity()) : purchasedQuantity.add(detail.getQuantity());
                    // 比较采购总数量与物料总数量，设置采购状态
                    if (total.compareTo(BigDecimal.ZERO) <= 0) {
                        workorderMaterialUsage.setPurchaseState(PurchaseStateEnum.UN.getValue());
                        total = BigDecimal.ZERO;
                    } else if (total.compareTo(workorderMaterialUsage.getMaterialUsageQuantity().add(workorderMaterialUsage.getMaterialLossQuantity())) == 0) {
                        workorderMaterialUsage.setPurchaseState(PurchaseStateEnum.FULL.getValue());
                    } else if (total.compareTo(workorderMaterialUsage.getMaterialUsageQuantity().add(workorderMaterialUsage.getMaterialLossQuantity())) == -1) {
                        workorderMaterialUsage.setPurchaseState(PurchaseStateEnum.PARTIAL.getValue());
                    } else {
                        workorderMaterialUsage.setPurchaseState(PurchaseStateEnum.EXCESS.getValue());
                    }
                    workorderMaterialUsage.setPurchasedQuantity(total);
                    // 更新工单材料用量表数据
                    iErpProductionMgtWorkorderMaterialUsageService.updateById(workorderMaterialUsage);
                }
            }
        }
    }
}
