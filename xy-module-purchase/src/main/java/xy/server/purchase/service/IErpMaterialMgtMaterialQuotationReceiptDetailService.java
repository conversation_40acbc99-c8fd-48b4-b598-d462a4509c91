package xy.server.purchase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import xy.server.purchase.entity.ErpMaterialMgtMaterialQuotationReceiptDetail;
import xy.server.purchase.entity.model.ro.ErpMaterialMgtMaterialQuotationReceiptRO;
import xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationReceiptDetailVO;
import xy.server.purchase.entity.model.vo.ErpQuotationImportVO;

import java.util.List;

/**
 * @apiNote 物料报价明细表 服务类
 * <AUTHOR>
 * @since 2024-05-23
 */
public interface IErpMaterialMgtMaterialQuotationReceiptDetailService extends IService<ErpMaterialMgtMaterialQuotationReceiptDetail> {
    /**
     * 插入报价明细数据
     * @param ro
     */
    ErpQuotationImportVO saveData(ErpMaterialMgtMaterialQuotationReceiptRO ro);

    /**
     * 删除报价明细数据
     * @param materialQuotationReceiptGuid
     */
    void deleteMessage(String materialQuotationReceiptGuid);

    /**
     * 根据报价主表id获取明细数据
     * @param materialQuotationIds
     * @return
     */
    List<ErpMaterialMgtMaterialQuotationReceiptDetailVO> getDataList(List<String> materialQuotationIds,Integer materialQuotationReceiptProperties);
}
