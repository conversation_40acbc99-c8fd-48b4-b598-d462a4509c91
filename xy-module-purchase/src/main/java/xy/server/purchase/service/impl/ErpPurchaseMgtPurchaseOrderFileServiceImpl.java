package xy.server.purchase.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseOrderFile;
import xy.server.purchase.entity.model.ro.ErpPurchaseMgtPurchaseOrderFileRO;
import xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseOrderFileVO;
import xy.server.purchase.mapper.ErpPurchaseMgtPurchaseOrderFileMapper;
import xy.server.purchase.service.IErpPurchaseMgtPurchaseOrderFileService;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 订单文件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Service
public class ErpPurchaseMgtPurchaseOrderFileServiceImpl extends ServiceImpl<ErpPurchaseMgtPurchaseOrderFileMapper, ErpPurchaseMgtPurchaseOrderFile> implements IErpPurchaseMgtPurchaseOrderFileService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDate(String orderGuid, List<ErpPurchaseMgtPurchaseOrderFileRO> fileList, List<String> delFileIds) {
        if (!CollectionUtils.isEmpty(delFileIds)) {
            super.removeBatchByIds(delFileIds);
        }
        if (!CollectionUtils.isEmpty(fileList)) {
            List<ErpPurchaseMgtPurchaseOrderFile> entityList = new ArrayList<>();
            fileList.forEach(file -> {
                ErpPurchaseMgtPurchaseOrderFile entity = new ErpPurchaseMgtPurchaseOrderFile();
                BeanUtils.copyProperties(file, entity);
                entity.setOrderGuid(orderGuid);
                entityList.add(entity);
            });
            super.saveOrUpdateBatch(entityList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByOrderGuid(String orderGuid) {
        if (!StringUtils.isEmpty(orderGuid)) {
            super.remove(Wrappers.<ErpPurchaseMgtPurchaseOrderFile>lambdaQuery()
                    .eq(ErpPurchaseMgtPurchaseOrderFile::getOrderGuid, orderGuid));
        }
    }

    @Override
    public List<ErpPurchaseMgtPurchaseOrderFileVO> getDataByOrderGuids(List<String> orderGuids) {
        return baseMapper.findListByOrderGuids(orderGuids);
    }
}
