package xy.server.purchase.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.config.excel.EasyExcelDataHandleService;
import com.xunyue.config.util.PageParams;
import xy.server.purchase.entity.ErpSupplierMgtSupplierClassification;
import xy.server.purchase.entity.model.qo.ErpSupplierMgtSupplierClassificationQO;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierClassificationRO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierClassificationVO;

import java.util.List;

/**
 * <p>
 * 供应商分类 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
public interface IErpSupplierMgtSupplierClassificationService extends IService<ErpSupplierMgtSupplierClassification>, EasyExcelDataHandleService<ErpSupplierMgtSupplierClassificationRO> {
    boolean create(ErpSupplierMgtSupplierClassificationRO ro);

    boolean delete(String supplierClassificationGuid);

    boolean deleteByBatch(List<String> ids);

    boolean update(ErpSupplierMgtSupplierClassificationRO ro);

    ErpSupplierMgtSupplierClassificationVO getById(String supplierClassificationGuid);

    List<ErpSupplierMgtSupplierClassificationVO> findList(ErpSupplierMgtSupplierClassificationQO qo, String tenantGuid);

    IPage<ErpSupplierMgtSupplierClassificationVO> findPage(PageParams<ErpSupplierMgtSupplierClassificationQO> pageParams);

    Boolean reordered(List<SortEntityDto> entityDtoList);

    Boolean saveDate(InsertOrUpdateList<ErpSupplierMgtSupplierClassificationRO> dataList, String tenantGuid);

    /**
     * 批量获取数据
     * @param ids
     * @return
     */
    List<ErpSupplierMgtSupplierClassificationVO> getDataByIds(List<String> ids);

}
