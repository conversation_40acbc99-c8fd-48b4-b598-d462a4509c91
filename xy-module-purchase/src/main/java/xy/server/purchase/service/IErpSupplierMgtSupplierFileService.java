package xy.server.purchase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import xy.server.purchase.entity.ErpSupplierMgtSupplierFile;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierFileRO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierFileVO;

import java.util.List;

/**
 * <p>
 * 供应商文件 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
public interface IErpSupplierMgtSupplierFileService extends IService<ErpSupplierMgtSupplierFile> {

    /**
     * 保存数据
     * @param roList ro列表
     * @param delGuids 删除的Guids
     * @param supplierGuid 供应商Guid
     * @return
     */
    void saveData(List<ErpSupplierMgtSupplierFileRO> roList, List<String> delGuids, String supplierGuid);

    /**
     * 根据供应商Guid删除数据
     * @param supplierGuid
     * @return
     */
    void delBySupplierGuid(String supplierGuid);

    List<ErpSupplierMgtSupplierFileVO> getDataByIds(List<String> supplierGuids);
}
