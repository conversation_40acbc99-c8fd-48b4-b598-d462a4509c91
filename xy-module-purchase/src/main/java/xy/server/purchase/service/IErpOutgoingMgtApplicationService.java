package xy.server.purchase.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.purchase.entity.model.qo.ErpOutgoingMgtApplicationQO;
import xy.server.purchase.entity.model.qo.ErpOutgoingQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingApplicationFormRO;
import xy.server.purchase.entity.model.ro.ErpOutgoingDetaiStopRO;
import xy.server.purchase.entity.model.ro.ErpOutgoingMgtApplicationRO;
import xy.server.purchase.entity.model.vo.ErpOutgoingApplicationFormVO;
import xy.server.purchase.entity.model.vo.ErpOutgoingApplicationVO;
import xy.server.work.entity.ErpProductionMgtWorkorder;
import xy.server.work.entity.model.dto.BaseMapContainer;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * <p>
 * 工单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
public interface IErpOutgoingMgtApplicationService extends IService<ErpProductionMgtWorkorder> {
    ErpOutgoingMgtApplicationRO create(ErpOutgoingMgtApplicationRO ro);

    boolean delete(String workorderGuid);

    boolean update(ErpOutgoingMgtApplicationRO ro);

    ErpOutgoingApplicationFormVO getById(String workorderGuid);

    List<ErpOutgoingApplicationFormVO> findList(ErpOutgoingMgtApplicationQO qo);

    IPage<ErpOutgoingApplicationFormVO> findPage(PageParams<ErpOutgoingMgtApplicationQO> pageParams);

    Boolean removeIds(List<String> ids);

    List<String> saveData(InsertOrUpdateList<ErpOutgoingMgtApplicationRO> list);

    IPage<ErpOutgoingApplicationFormRO> allFindList(PageParams<ErpOutgoingQO> qo);

    List<ErpOutgoingApplicationFormRO> specificFindList(ErpOutgoingMgtApplicationQO qo) throws ExecutionException, InterruptedException;

    /**
     * 待开列表查询
     * @param qo
     * @return
     */
    List<ErpOutgoingApplicationFormVO> newFindList(ErpOutgoingMgtApplicationQO qo) throws ExecutionException, InterruptedException;

    /**
     * 终止单据
     * @param list
     * @return
     */
    Boolean stopOrRollback(List<ErpOutgoingDetaiStopRO> list);

    /**
     * 生成外发申请单
     * @param qo
     * @return
     */
    Boolean generateOutgoingApplication(PageParams<ErpOutgoingQO> qo);

    /**
     * 根据工序工单Guid批量获取部件名称
     * @param workOrderProcessGuid
     * @return
     */
    Map<String,String> selectPartNameBatch(List<String> workOrderProcessGuid);

    /**
     * 根据工序工单Guid批量获取产品名称
     * @param workOrderProcessGuids
     * @return
     */
    Map<String,String> selectproductNameBatch(List<String> workOrderProcessGuids);

    /**
     * 根据工序工单Guid批量获取产品
     * @param workOrderProcessGuids
     * @return
     */
    Map<String, BaseMapContainer> selectproductNameBatchs(List<String> workOrderProcessGuids);

    /**
     * 通过工序工单id,获取产出工单集合
     * @param workorderGuids
     * @return
     */
    List<ErpOutgoingApplicationVO> getSonListBatch(List<String> workorderGuids);
}
