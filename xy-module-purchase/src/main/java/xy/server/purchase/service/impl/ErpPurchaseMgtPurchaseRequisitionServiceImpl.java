package xy.server.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.enums.*;
import com.xunyue.common.i18n.BaseResultErrorCodeImpl;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import com.xunyue.config.util.SpringBeanUtil;
import com.xunyue.exteriororder.service.IPurRequService;
import com.xunyue.node.common.enums.ClazzEnum;
import com.xunyue.node.common.enums.TypeEnum;
import com.xunyue.node.entity.Node;
import com.xunyue.node.entity.model.dto.LinkDTO;
import com.xunyue.node.service.ILinkService;
import com.xunyue.exteriororder.entity.model.dto.PurRequDTO;
import com.xunyue.node.service.INodeService;
import com.xunyue.proof.entity.model.dto.ProofUnitQuantityDTO;
import com.xunyue.proof.service.IProofUnitQuantityService;
import com.xy.util.BaseContext;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import xy.server.activiti.common.enums.BusinessStatusEnum;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.service.IErpFormPendingNumberService;
import xy.server.activiti.utils.actEvent.ActEventEntity;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.basic.entity.model.vo.ViewErpDataSourceVO;
import xy.server.filter.enums.TableIdentificationEnum;
import xy.server.material.entity.ErpMaterialMgtMaterial;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;
import xy.server.material.service.IErpMaterialMgtMaterialService;
import xy.server.purchase.entity.ErpProductionMgtProductionForecast;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseRequisition;
import xy.server.purchase.entity.model.qo.ErpPurchaseMgtPurchaseRequisitionQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingDetaiStopRO;
import xy.server.purchase.entity.model.ro.ErpPurchaseMgtPurchaseRequisitionRO;
import xy.server.purchase.entity.model.vo.ErpOrderDataVO;
import xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseRequisitionVO;
import xy.server.purchase.entity.model.vo.ErpPurchaseSourceRequisitionVO;
import xy.server.purchase.i18n.ResultErrorCode;
import xy.server.purchase.mapper.ErpPurchaseMgtPurchaseRequisitionMapper;
import xy.server.purchase.service.IErpProductionMgtProductionForecastService;
import xy.server.purchase.service.IErpPurchaseMgtPurchaseRequisitionService;
import xy.server.work.entity.ErpProductionMgtWorkorderMaterialUsage;
import xy.server.work.entity.model.dto.BaseMapContainer;
import xy.server.work.service.IErpProductionMgtWorkorderMaterialUsageService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @data 2023/9/13 10:55
 * @apiNote 采购申请单service实现
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service("WORK_ORDER.PURCHASE")
public class ErpPurchaseMgtPurchaseRequisitionServiceImpl extends ServiceImpl<ErpPurchaseMgtPurchaseRequisitionMapper, ErpPurchaseMgtPurchaseRequisition> implements IErpPurchaseMgtPurchaseRequisitionService, ActEventStrategyService {

    private final IErpSystemMgtOrderSerialNumberService iErpSystemMgtOrderSerialNumberService;
    private final IErpProductionMgtWorkorderMaterialUsageService iErpProductionMgtWorkorderMaterialUsageService;
    private final IErpMaterialMgtMaterialService materialService;
    private final IErpFormPendingNumberService iErpFormPendingNumberService;
    private final IPurRequService purchaseRequisitionService;
    private final IProofUnitQuantityService proofUnitQuantityService;
    private final ILinkService linkService;
    private final INodeService nodeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ErpPurchaseMgtPurchaseRequisitionRO create(ErpPurchaseMgtPurchaseRequisitionRO ro) {
        String creator = BaseContext.getCreator();
        String staffShortName = BaseContext.getStaffShortName();
        String creatorGuid = BaseContext.getCreatorGuid();
        String tenantGuid = BaseContext.getTenantGuid();
        String staffGuid = BaseContext.getStaffGuid();
        // 保存采购申请单
        ErpPurchaseMgtPurchaseRequisition entity = new ErpPurchaseMgtPurchaseRequisition();
        BeanUtil.copyProperties(ro, entity);
        if (StringUtils.isBlank(entity.getWorkorderNumber())) {
            entity.setWorkorderNumber(iErpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.WORK_ORDER_PURCHASE));
        }
        if (entity.getReceiptDate() == null) {
            entity.setReceiptDate(LocalDateTime.now());
        }
        entity.setWorkorderProperties(WorkorderPropertiesEnum.PURCHASE.getCode());

        if (super.save(entity)) {
            // 保存采购申请单明细
            List<ErpPurchaseMgtPurchaseRequisition> purchaseRequisitionList = this.saveDetailData(entity, ro);
            if (!BusinessStatusEnum.FINISH.getStatus().equals(ro.getAuditStatus())) {
                // 启动审核流程
                iProcessInstanceService.start(WorkflowKeyEnum.WORK_ORDER_PURCHASE, entity.getWorkorderGuid());
            }
            //双写采购申请到proof
            ThreadUtil.execAsync(() -> {
                BaseContext.setCreator(creator);
                BaseContext.setCreatorGuid(creatorGuid);
                BaseContext.setTenantGuid(tenantGuid);
                BaseContext.setStaffGuid(staffGuid);
                BaseContext.setStaffShortName(staffShortName);
                IErpPurchaseMgtPurchaseRequisitionService purchaseMgtPurchaseRequisitionService = SpringBeanUtil.getBean(IErpPurchaseMgtPurchaseRequisitionService.class);
                purchaseMgtPurchaseRequisitionService.syncRequistionToProof(entity, purchaseRequisitionList,true);
            });
            ro.setWorkorderGuid(entity.getWorkorderGuid());
            return ro;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpPurchaseMgtPurchaseRequisitionRO ro) {
        String creator = BaseContext.getCreator();
        String staffShortName = BaseContext.getStaffShortName();
        String creatorGuid = BaseContext.getCreatorGuid();
        String tenantGuid = BaseContext.getTenantGuid();
        String staffGuid = BaseContext.getStaffGuid();
        this.operationCheck(ro.getWorkorderGuid(), false);
        // 保存采购申请单
        ErpPurchaseMgtPurchaseRequisition entity = new ErpPurchaseMgtPurchaseRequisition();
        BeanUtil.copyProperties(ro, entity);
        if (entity.getReceiptDate() == null) {
            entity.setReceiptDate(LocalDateTime.now());
        }
        if (super.updateById(entity)) {
            // 保存采购申请单明细
            List<ErpPurchaseMgtPurchaseRequisition> purchaseRequisitionList = this.saveDetailData(entity, ro);
            //双写采购申请到proof
            ThreadUtil.execAsync(() -> {
                BaseContext.setCreator(creator);
                BaseContext.setCreatorGuid(creatorGuid);
                BaseContext.setTenantGuid(tenantGuid);
                BaseContext.setStaffGuid(staffGuid);
                BaseContext.setStaffShortName(staffShortName);
                IErpPurchaseMgtPurchaseRequisitionService purchaseMgtPurchaseRequisitionService = SpringBeanUtil.getBean(IErpPurchaseMgtPurchaseRequisitionService.class);
                purchaseMgtPurchaseRequisitionService.syncRequistionToProof(entity, purchaseRequisitionList,false);
            });
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveDate(InsertOrUpdateList<ErpPurchaseMgtPurchaseRequisitionRO> dataList) {
        List<String> ids = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(item->{
                ErpPurchaseMgtPurchaseRequisitionRO erpPurchaseMgtPurchaseRequisitionRO = this.create(item);
                ids.add(erpPurchaseMgtPurchaseRequisitionRO.getWorkorderGuid());
            });
        }

        if (!CollectionUtils.isEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(item->{
                if (StringUtils.isNotBlank(item.getWorkorderGuid())) {
                    this.update(item);
                    ids.add(item.getWorkorderGuid());
                }
            });
        }
        return ids;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String workorderGuid) {
        String creator = BaseContext.getCreator();
        String staffShortName = BaseContext.getStaffShortName();
        String creatorGuid = BaseContext.getCreatorGuid();
        String tenantGuid = BaseContext.getTenantGuid();
        String staffGuid = BaseContext.getStaffGuid();
        this.operationCheck(workorderGuid, false);
        //获取数据
        ErpPurchaseMgtPurchaseRequisitionVO vo = baseMapper.getOneById(workorderGuid);
        if (super.removeById(workorderGuid)) {
            // 删除采购申请单明细
            this.deleteDetailData(workorderGuid);
            /*vo.getDetailList().forEach(s -> {
                if (s.getSourceValue().equals(PurchaseSourceEnum.WORK_ORDER.getKey())) {
                    iErpFormPendingNumberService.updatePendingNumber(WorkflowKeyEnum.WORK_ORDER_PURCHASE, WorkflowKeyEnum.BUSINESS_MGT_ORDER
                            , baseMapper.getWorkOrderWaitingQuantity());
                }
            });*/
            // 删除流程相关数据
            iProcessInstanceService.deleteProcessAndHisInst(workorderGuid, WorkflowKeyEnum.WORK_ORDER_PURCHASE);
            //双写采购申请到proof
            ThreadUtil.execAsync(() -> {
                BaseContext.setCreator(creator);
                BaseContext.setCreatorGuid(creatorGuid);
                BaseContext.setTenantGuid(tenantGuid);
                BaseContext.setStaffGuid(staffGuid);
                BaseContext.setStaffShortName(staffShortName);
                IErpPurchaseMgtPurchaseRequisitionService purchaseMgtPurchaseRequisitionService = SpringBeanUtil.getBean(IErpPurchaseMgtPurchaseRequisitionService.class);
                purchaseMgtPurchaseRequisitionService.syncDeleteRequistionToProof(Arrays.asList(workorderGuid));
            });
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> ids) {
        String creator = BaseContext.getCreator();
        String staffShortName = BaseContext.getStaffShortName();
        String creatorGuid = BaseContext.getCreatorGuid();
        String tenantGuid = BaseContext.getTenantGuid();
        String staffGuid = BaseContext.getStaffGuid();
        for (String id : ids) {
            this.operationCheck(id, false);
            //获取数据
            ErpPurchaseMgtPurchaseRequisitionVO vo = baseMapper.getOneById(id);
            if (super.removeById(id)) {
                // 删除采购申请单明细
                this.deleteDetailData(id);
                // 删除流程相关数据
                iProcessInstanceService.deleteProcessAndHisInst(id, WorkflowKeyEnum.WORK_ORDER_PURCHASE);
                /*vo.getDetailList().forEach(s -> {
                    if (s.getSourceValue().equals(PurchaseSourceEnum.WORK_ORDER.getKey())) {
                        iErpFormPendingNumberService.updatePendingNumber(WorkflowKeyEnum.WORK_ORDER_PURCHASE, WorkflowKeyEnum.BUSINESS_MGT_ORDER
                                , baseMapper.getWorkOrderWaitingQuantity());
                    }
                });*/
            }
        }
        //双写采购申请到proof
        ThreadUtil.execAsync(() -> {
            BaseContext.setCreator(creator);
            BaseContext.setCreatorGuid(creatorGuid);
            BaseContext.setTenantGuid(tenantGuid);
            BaseContext.setStaffGuid(staffGuid);
            BaseContext.setStaffShortName(staffShortName);
            IErpPurchaseMgtPurchaseRequisitionService purchaseMgtPurchaseRequisitionService = SpringBeanUtil.getBean(IErpPurchaseMgtPurchaseRequisitionService.class);
            purchaseMgtPurchaseRequisitionService.syncDeleteRequistionToProof(ids);
        });
        return true;
    }

    @Override
    public ErpPurchaseMgtPurchaseRequisitionVO getOneById(String workorderGuid) {
        return baseMapper.getOneById(workorderGuid);
    }

    @Override
    public List<ErpPurchaseMgtPurchaseRequisitionVO> findList(ErpPurchaseMgtPurchaseRequisitionQO qo) {
        return setDetailData(baseMapper.findList(qo));
    }

    @Override
    public IPage<ErpPurchaseMgtPurchaseRequisitionVO> findPage(PageParams<ErpPurchaseMgtPurchaseRequisitionQO> pageParams) {
        IPage<ErpPurchaseMgtPurchaseRequisitionVO> page = pageParams.buildPage();
        ErpPurchaseMgtPurchaseRequisitionQO model = pageParams.getModel();
        IPage<ErpPurchaseMgtPurchaseRequisitionVO> page1 = baseMapper.findPage(page, model);
        if (CollUtil.isNotEmpty(page1.getRecords())) {
            page1.setRecords(setDetailData(page1.getRecords()));
        }
        return page1;
    }

    /**
     * 组装明细数据
     *
     * @param detail
     * @return
     */
    public List<ErpPurchaseMgtPurchaseRequisitionVO> setDetailData(List<ErpPurchaseMgtPurchaseRequisitionVO> detail) {
        List<String> workorderGuids = detail.stream()
                .map(ErpPurchaseMgtPurchaseRequisitionVO::getWorkorderGuid)
                .distinct()
                .collect(Collectors.toList());

        // 获取明细数据
        List<ErpPurchaseMgtPurchaseRequisitionVO> detailListBatch = baseMapper.findDetailListBatch(workorderGuids);
        // 获取采购申请状态
        List<BaseMapContainer> purchaseStateBatch = baseMapper.getPurchaseStateBatch(workorderGuids);
        Map<String, String> purchaseStateBatchMap = new HashMap<>();
        if(CollUtil.isNotEmpty(purchaseStateBatch)){
            purchaseStateBatchMap = purchaseStateBatch.stream()
                    .collect(Collectors.toMap(BaseMapContainer::getName, BaseMapContainer::getValue, (e, n) -> e));
        }

        // 获取物料数据
        if (CollUtil.isNotEmpty(detailListBatch)) {
            List<String> materialGuids = detailListBatch.stream()
                    .map(ErpPurchaseMgtPurchaseRequisitionVO::getMaterialGuid)
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, ErpMaterialMgtMaterialVO> mapByIds = materialService.getMapByIds(materialGuids);
            detailListBatch.forEach(v -> v.setMaterialObj(mapByIds.get(v.getMaterialGuid())));
        }

        Map<String, String> finalPurchaseStateBatchMap = purchaseStateBatchMap;
        detail.forEach(v -> {
            List<ErpPurchaseMgtPurchaseRequisitionVO> collect = detailListBatch.stream()
                    .filter(u -> u.getParentClassificationGuid().equals(v.getWorkorderGuid()))
                    .collect(Collectors.toList());
            v.setDetailList(collect);
            v.setWorkorderState(finalPurchaseStateBatchMap.get(v.getWorkorderGuid()));
        });

        return detail;
    }

    /**
     * 待开列表查询
     *
     * @param qo
     * @return
     */
    @Override
    public List<ErpPurchaseMgtPurchaseRequisitionVO> findNotBilledList(ErpPurchaseMgtPurchaseRequisitionQO qo) {

        List<ErpPurchaseMgtPurchaseRequisitionVO> notBilledList = baseMapper.findNotBilledList(qo);
        if (!CollectionUtils.isEmpty(notBilledList)) {
            List<String> workorderGuids = notBilledList.stream()
                    .map(ErpPurchaseMgtPurchaseRequisitionVO::getWorkorderGuid)
                    .collect(Collectors.toList());
            List<String> sourceGuids = notBilledList.stream()
                    .map(ErpPurchaseMgtPurchaseRequisitionVO::getSourceGuid)
                    .collect(Collectors.toList());

            List<ErpPurchaseMgtPurchaseRequisitionVO> billedQuantity = baseMapper.getBilledQuantity(workorderGuids);
            List<ViewErpDataSourceVO> viewErpDataSourceVOS = baseMapper.getViewErpDataSourceBySourceGuids(sourceGuids);
            Map<String, ErpPurchaseMgtPurchaseRequisitionVO> billedQuantityMap = billedQuantity.stream()
                    .collect(Collectors.toMap(ErpPurchaseMgtPurchaseRequisitionVO::getParentClassificationGuid, u -> u));
            Map<String, ViewErpDataSourceVO> dataSourceVOMap = viewErpDataSourceVOS.stream().collect(Collectors.toMap(ViewErpDataSourceVO::getSourceGuid, u -> u, (o, n) -> o));

            notBilledList.forEach(result -> {
                ErpPurchaseMgtPurchaseRequisitionVO requisitionVO = billedQuantityMap.get(result.getWorkorderGuid());
                if (Objects.nonNull(requisitionVO)) {
                    if (Objects.isNull(result.getQuantity())) {
                        result.setQuantity(BigDecimal.ZERO);
                    }
                    result.setNotBilledQuantity(result.getQuantity().subtract(requisitionVO.getBilledQuantity()));
                } else {
                    result.setNotBilledQuantity(result.getQuantity());
                }

                ViewErpDataSourceVO erpDataSourceVO = dataSourceVOMap.get(result.getSourceGuid());
                if (Objects.nonNull(erpDataSourceVO)) {
                    result.setSourceNumber(erpDataSourceVO.getSourceNumber());
                    result.setSourceQuantity(erpDataSourceVO.getSourceQuantity());
                }
            });
            // 加载采购申请单明细物料信息
            materialService.loadMaterialObj(notBilledList, ErpPurchaseMgtPurchaseRequisitionVO::getMaterialGuid, ErpPurchaseMgtPurchaseRequisitionVO::setMaterialObj, null);
        }
        return notBilledList;
    }

    /**
     * 待开列表查询（包含主工单）
     *
     * @param qo
     * @return
     */
    @Override
    public List<ErpPurchaseMgtPurchaseRequisitionVO> findNotBilledTree(ErpPurchaseMgtPurchaseRequisitionQO qo) {
        List<ErpPurchaseMgtPurchaseRequisitionVO> resultList = new ArrayList<>();
        // 待开列表（采购申请单明细）
        List<ErpPurchaseMgtPurchaseRequisitionVO> notBilledList = baseMapper.findNotBilledList(qo);
        // 获取父guid列表
        if (!CollectionUtils.isEmpty(notBilledList)) {
            List<String> workorderGuids = notBilledList.stream()
                    .map(ErpPurchaseMgtPurchaseRequisitionVO::getWorkorderGuid)
                    .collect(Collectors.toList());
            List<String> sourceGuids = notBilledList.stream()
                    .map(ErpPurchaseMgtPurchaseRequisitionVO::getSourceGuid)
                    .collect(Collectors.toList());

            List<ErpPurchaseMgtPurchaseRequisitionVO> billedQuantity = baseMapper.getBilledQuantity(workorderGuids);
            List<ViewErpDataSourceVO> viewErpDataSourceVOS = baseMapper.getViewErpDataSourceBySourceGuids(sourceGuids);
            Map<String, ErpPurchaseMgtPurchaseRequisitionVO> billedQuantityMap = billedQuantity.stream()
                    .collect(Collectors.toMap(ErpPurchaseMgtPurchaseRequisitionVO::getParentClassificationGuid, u -> u));
            Map<String, ViewErpDataSourceVO> dataSourceVOMap = viewErpDataSourceVOS.stream().collect(Collectors.toMap(ViewErpDataSourceVO::getSourceGuid, u -> u, (o, n) -> o));

            notBilledList.forEach(result -> {
                ErpPurchaseMgtPurchaseRequisitionVO requisitionVO = billedQuantityMap.get(result.getWorkorderGuid());
                if (Objects.nonNull(requisitionVO)) {
                    if (Objects.isNull(result.getQuantity())) {
                        result.setQuantity(BigDecimal.ZERO);
                    }
                    result.setNotBilledQuantity(result.getQuantity().subtract(requisitionVO.getBilledQuantity()));
                } else {
                    result.setNotBilledQuantity(result.getQuantity());
                }

                ViewErpDataSourceVO erpDataSourceVO = dataSourceVOMap.get(result.getSourceGuid());
                if (Objects.nonNull(erpDataSourceVO)) {
                    result.setSourceNumber(erpDataSourceVO.getSourceNumber());
                    result.setSourceQuantity(erpDataSourceVO.getSourceQuantity());
                }
            });


            // 加载采购申请单明细物料信息
            materialService.loadMaterialObj(notBilledList,ErpPurchaseMgtPurchaseRequisitionVO::getMaterialGuid, ErpPurchaseMgtPurchaseRequisitionVO::setMaterialObj, null);

            Set<String> pGuids = notBilledList.stream().map(ErpPurchaseMgtPurchaseRequisitionVO::getParentClassificationGuid).collect(Collectors.toSet());
            List<ErpPurchaseMgtPurchaseRequisition> pEntityList = baseMapper.selectList(Wrappers.<ErpPurchaseMgtPurchaseRequisition>lambdaQuery()
                    .in(ErpPurchaseMgtPurchaseRequisition::getWorkorderGuid, pGuids));
            resultList = BeanUtil.copyToList(pEntityList, ErpPurchaseMgtPurchaseRequisitionVO.class);
            // 组装数据
            for (ErpPurchaseMgtPurchaseRequisitionVO result : resultList) {
                result.setQuantity(null);
                result.setDetailList(notBilledList.stream().filter(e -> e.getParentClassificationGuid().equals(result.getWorkorderGuid())).collect(Collectors.toList()));
            }
        }
        return resultList;
    }


    /**
     * 采购申请数据源查询
     *
     * @param qo
     * @return
     */
    @Override
    public List<ErpPurchaseSourceRequisitionVO> waitingFindList(ErpPurchaseMgtPurchaseRequisitionQO qo) {
        List<ErpPurchaseSourceRequisitionVO> list = new ArrayList<>();
        if (ObjUtil.isEmpty(qo.getAuditStatus())) {
            qo.setAuditStatus(Lists.newArrayList(BusinessStatusEnum.FINISH.getStatus()));
        }
        //处理工单来源报错问题
        if (1 == qo.getSourceValue()) {
            list = baseMapper.waitingFindLists(qo);
        } else if (2 == qo.getSourceValue()) {
            //订单
            list = baseMapper.orderDataFindList(qo);
        } else if (3 == qo.getSourceValue()) {
            //物料
            list = baseMapper.materialFindList(qo);
        } else if (PurchaseSourceEnum.WORK_ORDER_PART.getKey() == qo.getSourceValue()) {
            list = baseMapper.partWaitingFindLists(qo);
        } else if (PurchaseSourceEnum.WORK_ORDER_MOULD.getKey() == qo.getSourceValue()) {
            // 工单-模具
            list = baseMapper.mouldWaitingFindList(qo);
            materialService.loadMaterialObj(list, ErpPurchaseSourceRequisitionVO::getMaterialGuid,
                    ErpPurchaseSourceRequisitionVO::setMaterialObj, null);
        }
        return list;
    }

    /**
     * 终止单据
     *
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean stopOrRollback(List<ErpOutgoingDetaiStopRO> list) {
        List<ErpPurchaseMgtPurchaseRequisition> list1 = new ArrayList<>();
        list.forEach(s -> {
            ErpPurchaseMgtPurchaseRequisition detail = baseMapper.selectById(s.getWorkorderGuid());
            detail.setCurrentStatus(s.getCurrentStatus());
            list1.add(detail);
        });
        super.updateBatchById(list1);
        return true;
    }

    /**
     * 生成采购申请单
     *
     * @param qo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generatePurchaseRequisition(ErpPurchaseMgtPurchaseRequisitionQO qo) {
        List<ErpPurchaseSourceRequisitionVO> toBeOpenedList = this.waitingFindList(qo);
        if (CollectionUtil.isEmpty(toBeOpenedList)) {
            throw new FlowException(ResultErrorCode.ALL_PURCHASE_APPLICATIONS_HAVE_BEEN_MADE);
        }
        ErpPurchaseMgtPurchaseRequisitionRO purchaseRequisitionRO = new ErpPurchaseMgtPurchaseRequisitionRO();
        purchaseRequisitionRO.setSourceValue(qo.getSourceValue());
        // 申请类型默认为“生产用料”
        purchaseRequisitionRO.setWorkorderTypeGuid("1");
        purchaseRequisitionRO.setAuditStatus(BusinessStatusEnum.FINISH.getStatus());
        purchaseRequisitionRO.setAuditDate(LocalDateTime.now());

        List<ErpPurchaseMgtPurchaseRequisitionRO> detailList = new ArrayList<>(toBeOpenedList.size());
        toBeOpenedList.forEach(item -> {
            ErpPurchaseMgtPurchaseRequisitionRO detailRO = BeanUtil.toBean(item, ErpPurchaseMgtPurchaseRequisitionRO.class);
            detailRO.setQuantity(item.getWaitingQuantity());
            detailList.add(detailRO);
        });
        purchaseRequisitionRO.setDetailList(detailList);
        return null != this.create(purchaseRequisitionRO);
    }

    /**
     * 包含业务预测以及采购申请的数据
     *
     * @param qo
     * @return
     */
    @Override
    public List<ErpPurchaseMgtPurchaseRequisitionVO> findNotBilledListES(ErpPurchaseMgtPurchaseRequisitionQO qo) {
        List<ErpPurchaseMgtPurchaseRequisitionVO> notBilledList = baseMapper.findNotBilledListES(qo);
        materialService.loadMaterialObj(notBilledList, ErpPurchaseMgtPurchaseRequisitionVO::getMaterialGuid, ErpPurchaseMgtPurchaseRequisitionVO::setMaterialObj, null);
        return notBilledList;
    }

    /**
     * 包含业务预测以及采购申请的数据待开列表查询（包含主工单）
     *
     * @param qo
     * @return
     */
    @Override
    public List<ErpPurchaseMgtPurchaseRequisitionVO> findNotBilledTreeES(ErpPurchaseMgtPurchaseRequisitionQO qo) {
        List<ErpPurchaseMgtPurchaseRequisitionVO> resultList = new ArrayList<>();
        // 待开列表（采购申请单明细）
        List<ErpPurchaseMgtPurchaseRequisitionVO> notBilledList = baseMapper.findNotBilledListES(qo);
        materialService.loadMaterialObj(notBilledList, ErpPurchaseMgtPurchaseRequisitionVO::getMaterialGuid, ErpPurchaseMgtPurchaseRequisitionVO::setMaterialObj, null);
        // 获取父guid列表
        if (!CollectionUtils.isEmpty(notBilledList)) {
            Set<String> pGuids = notBilledList.stream().map(ErpPurchaseMgtPurchaseRequisitionVO::getParentClassificationGuid).collect(Collectors.toSet());
            List<ErpPurchaseMgtPurchaseRequisition> pEntityList = baseMapper.selectList(Wrappers.<ErpPurchaseMgtPurchaseRequisition>lambdaQuery()
                    .in(ErpPurchaseMgtPurchaseRequisition::getWorkorderGuid, pGuids));
            resultList = BeanUtil.copyToList(pEntityList, ErpPurchaseMgtPurchaseRequisitionVO.class);
            // 组装数据
            for (ErpPurchaseMgtPurchaseRequisitionVO result : resultList) {
                result.setQuantity(null);
                result.setDetailList(notBilledList.stream().filter(e -> e.getParentClassificationGuid().equals(result.getWorkorderGuid())).collect(Collectors.toList()));
            }
        }
        return resultList;
    }

    @Override
    public void syncRequistionToProof(ErpPurchaseMgtPurchaseRequisition entity, List<ErpPurchaseMgtPurchaseRequisition> purchaseRequisitionList
    ,Boolean isSave) {
        if (CollectionUtil.isEmpty(purchaseRequisitionList)) {
            return;
        }
        Map<String,String> linkSourceMap=new HashMap<>();
        //处理来源是工单时link的连接问题
        if (Objects.nonNull(entity.getSourceValue())&&entity.getSourceValue()==1){
            //根据工单用料扩展id获取工序工单id
            List<String> sourceList = purchaseRequisitionList.stream().filter(s -> StringUtils.isNotBlank(s.getSourceGuid()))
                    .map(ErpPurchaseMgtPurchaseRequisition::getSourceGuid).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(sourceList)) {
                List<ErpProductionMgtWorkorderMaterialUsage> workorderMaterialUsageList = iErpProductionMgtWorkorderMaterialUsageService.listByIds(sourceList);
                if (CollectionUtil.isNotEmpty(workorderMaterialUsageList)) {
                    workorderMaterialUsageList.forEach(p->{
                        linkSourceMap.put(p.getWorkorderMaterialUsageGuid(),p.getWorkorderGuid());
                    });
                }
            }
        }
        //根据采购预测的物料id获取单位数据
        List<ErpMaterialMgtMaterial> materialList = materialService.listByIds(purchaseRequisitionList.stream().
                map(ErpPurchaseMgtPurchaseRequisition::getMaterialGuid).distinct().collect(Collectors.toList()));
        //批量删除单位数量关联表
        List<String> parentIds = purchaseRequisitionList.stream().map(ErpPurchaseMgtPurchaseRequisition::getWorkorderGuid).distinct().collect(Collectors.toList());
        ProofUnitQuantityDTO quantityDTO = new ProofUnitQuantityDTO();
        quantityDTO.setProofUnitQuantityParentIds(parentIds);
        List<ProofUnitQuantityDTO> quantityDTOList = proofUnitQuantityService.findList(quantityDTO);
        if (CollectionUtil.isNotEmpty(quantityDTOList)) {
            proofUnitQuantityService.removeByIds(quantityDTOList.stream().map(ProofUnitQuantityDTO::getProofUnitQuantityId).collect(Collectors.toList()));
        }
        Map<String, ErpMaterialMgtMaterial> materialMap = materialList.stream().collect(Collectors.toMap(ErpMaterialMgtMaterial::getMaterialGuid, material -> material));
        List<PurRequDTO> requisitionDTOS = new ArrayList<>();
        List<ProofUnitQuantityDTO> proofUnitQuantityList = new ArrayList<>();
        List<LinkDTO> linkDTOS = new ArrayList<>();
        PurRequDTO requisitionDTO = new PurRequDTO();
        requisitionDTO.setPurRequId(entity.getWorkorderGuid());
        requisitionDTO.setPurRequUserCode(entity.getWorkorderNumber());
        requisitionDTO.setPurRequReceiptDate(Date.from(entity.getReceiptDate().atZone(ZoneId.systemDefault()).toInstant()));
        requisitionDTO.setPurRequClazz(ClazzEnum.PURCHASE_REQUISITION.name());
        requisitionDTO.setPurRequSourceType(String.valueOf(entity.getSourceValue()));
        requisitionDTO.setPurRequTenantId(entity.getTenantGuid());
        requisitionDTO.setPurRequIsolation(entity.getTenantGuid());
        purchaseRequisitionService.saveOrUpdateBatch(Arrays.asList(requisitionDTO));
        purchaseRequisitionList.forEach(purchaseRequisition->{
            PurRequDTO requisitionDTO1 = new PurRequDTO();
            requisitionDTO1.setPurRequId(purchaseRequisition.getMaterialGuid());
            requisitionDTO1.setPurRequQuantity(purchaseRequisition.getQuantity());
            requisitionDTO1.setPurRequId(purchaseRequisition.getWorkorderGuid());
            requisitionDTO1.setPurRequParentId(entity.getWorkorderGuid());
            requisitionDTO1.setPurRequTenantId(entity.getTenantGuid());
            requisitionDTO1.setPurRequIsolation(entity.getTenantGuid());
            if (Objects.nonNull(purchaseRequisition.getToJson())) {
                requisitionDTO1.setPurRequExtension(purchaseRequisition.getToJson().toString());
            }
            requisitionDTO1.setPurRequClazz(ClazzEnum.PURCHASE_REQUISITION_DETAIL.name());
            requisitionDTOS.add(requisitionDTO1);
            saveOrUpdateDetail(entity, purchaseRequisition, linkSourceMap, linkDTOS, requisitionDTO1, materialMap, proofUnitQuantityList);
        });
        purchaseRequisitionService.saveOrUpdateBatch(requisitionDTOS);
        proofUnitQuantityService.saveOrUpdateBatch(proofUnitQuantityList);
        linkService.saveOrUpdateBatch(linkDTOS);
        if (isSave) {
            purchaseRequisitionService.update(requisitionDTO);
        }
    }

    /**
     * 保存采购申请明细
     * @param entity
     * @param purchaseRequisition
     * @param linkSourceMap
     * @param linkDTOS
     * @param requisitionDTO1
     * @param materialMap
     * @param proofUnitQuantityList
     */
    private void saveOrUpdateDetail(ErpPurchaseMgtPurchaseRequisition entity, ErpPurchaseMgtPurchaseRequisition purchaseRequisition, Map<String, String> linkSourceMap, List<LinkDTO> linkDTOS, PurRequDTO requisitionDTO1, Map<String, ErpMaterialMgtMaterial> materialMap, List<ProofUnitQuantityDTO> proofUnitQuantityList) {
        if (entity.getWorkorderGuid().equals(purchaseRequisition.getParentClassificationGuid())) {
            List<ProofUnitQuantityDTO> proofUnitQuantityDTOS = new ArrayList<>();
            if (StringUtils.isNotBlank(purchaseRequisition.getSourceGuid())) {
                LinkDTO linkDTO = new LinkDTO();
                //处理来源link连接
                linkDTO.setLinkTarget(purchaseRequisition.getWorkorderGuid());
                linkDTO.setLinkTypeCode(TypeEnum.SOURCE.getCode());
                linkDTO.setLinkId(IdWorker.getIdStr());
                if (Objects.nonNull(entity.getSourceValue())) {
                    if (entity.getSourceValue()==1) {
                        linkDTO.setLinkClazz(ClazzEnum.MATERIAL.name()+ StringPool.DASH+ClazzEnum.BOM_PROCESS.name()+StringPool.AMPERSAND+ClazzEnum.PURCHASE_REQUISITION_DETAIL.name());
                    }else if (entity.getSourceValue()==2) {
                        linkDTO.setLinkClazz(ClazzEnum.BUSINESS_ORDER_DETAIL.name()+StringPool.DASH+ClazzEnum.PURCHASE_REQUISITION_DETAIL.name());
                    }else if (entity.getSourceValue()==4){
                        linkDTO.setLinkClazz(ClazzEnum.PURCHASE_FORECASTING_DET.name()+StringPool.DASH+ClazzEnum.PURCHASE_REQUISITION_DETAIL.name());
                    }
                }
                linkDTO.setLinkIsolation(entity.getTenantGuid());
                linkDTO.setLinkSource(purchaseRequisition.getSourceGuid());
                //当来源为工单时根据工序工单id以及物料id获取link的id作为来源
                if (Objects.nonNull(entity.getSourceValue())&& entity.getSourceValue()==1){
                    if (cn.easyes.common.utils.CollectionUtils.isNotEmpty(linkSourceMap)){
                        String processWorkGuid = linkSourceMap.get(purchaseRequisition.getSourceGuid());
                        if (StringUtils.isNotBlank(processWorkGuid)){
                            LinkDTO bomLink = new LinkDTO();
                            bomLink.setLinkTarget(processWorkGuid);
                            bomLink.setLinkSource(purchaseRequisition.getMaterialGuid());
                            bomLink.setLinkIsolation(purchaseRequisition.getTenantGuid());
                            List<LinkDTO> linkDTOList = linkService.findList(bomLink);
                            if (!CollectionUtils.isEmpty(linkDTOList)){
                                linkDTO.setLinkSource(linkDTOList.get(0).getLinkId());
                            }
                        }
                    }
                }
                linkService.removeBatchByTargetAndClazz(linkDTO.getLinkClazz(), Arrays.asList(purchaseRequisition.getWorkorderGuid()),entity.getTenantGuid());
                linkDTOS.add(linkDTO);
            }
            //根据物料id获取单位id
            ProofUnitQuantityDTO unitQuantityDTO = new ProofUnitQuantityDTO();
            requisitionDTO1.setPurRequUnitCode(materialMap.get(purchaseRequisition.getMaterialGuid()).getUnitGuid());
            unitQuantityDTO.setProofUnitQuantityQuantity(purchaseRequisition.getQuantity());
            unitQuantityDTO.setProofUnitQuantityId(IdWorker.getIdStr());
            unitQuantityDTO.setProofUnitQuantityUnitId(requisitionDTO1.getPurRequUnitCode());
            unitQuantityDTO.setProofUnitQuantityParentId(purchaseRequisition.getWorkorderGuid());
            unitQuantityDTO.setProofUnitQuantityClazz(ClazzEnum.PROOF_UNIT_QUANTITY.name());
            proofUnitQuantityDTOS.add(unitQuantityDTO);
            if (Objects.nonNull(purchaseRequisition.getToJson())){
                String jsonStr = JSON.toJSONString(purchaseRequisition.getToJson());
                JSONObject jsonObject = JSONObject.parseObject(jsonStr);
                if (Objects.nonNull(jsonObject)&&jsonObject.containsKey("quantityPerItem")) {
                    ProofUnitQuantityDTO unitQuantityDTO1 = new ProofUnitQuantityDTO();
                    unitQuantityDTO1.setProofUnitQuantityQuantity(new BigDecimal(String.valueOf(jsonObject.get("quantityPerItem"))));
                    unitQuantityDTO1.setProofUnitQuantityUnitId("7caebe79177d9e07ee51ffdd35013b4a");
                    unitQuantityDTO1.setProofUnitQuantityParentId(purchaseRequisition.getWorkorderGuid());
                    unitQuantityDTO1.setProofUnitQuantityId(IdWorker.getIdStr());
                    unitQuantityDTO1.setProofUnitQuantityClazz(ClazzEnum.PROOF_UNIT_QUANTITY.name());
                    proofUnitQuantityDTOS.add(unitQuantityDTO1);
                }
            }
            proofUnitQuantityList.addAll(proofUnitQuantityDTOS);
        }
    }

    @Override
    public void syncDeleteRequistionToProof(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        //根据采购预测主表数据id获取采购预测明细数据
        PurRequDTO requisitionDTO = new PurRequDTO();
        requisitionDTO.setPurRequParentIds(ids);
        List<PurRequDTO> requisitionDTOS = purchaseRequisitionService.findList(requisitionDTO);
        List<String> delDetailGuids = requisitionDTOS.stream().map(PurRequDTO::getPurRequId).collect(Collectors.toList());
        //删除link关联数据
        linkService.removeByTargets(delDetailGuids, TypeEnum.SOURCE);
        //删除proof单位数据
        ProofUnitQuantityDTO quantityDTO = new ProofUnitQuantityDTO();
        quantityDTO.setProofUnitQuantityParentIds(delDetailGuids);
        quantityDTO.setProofUnitQuantityClazz(ClazzEnum.PROOF_UNIT_QUANTITY.name());
        List<ProofUnitQuantityDTO> quantityDTOList = proofUnitQuantityService.findList(quantityDTO);
        if (CollectionUtil.isNotEmpty(quantityDTOList)){
            List<String> quantityIds = quantityDTOList.stream().map(ProofUnitQuantityDTO::getProofUnitQuantityId).collect(Collectors.toList());
            proofUnitQuantityService.removeBatch(quantityIds);

        }
        //删除采购预测明细数据
        purchaseRequisitionService.removeBatch(delDetailGuids);
        purchaseRequisitionService.removeBatch(ids);
    }

    @Override
    public void updateNotCheck(ErpPurchaseMgtPurchaseRequisitionRO ro) {
        String creator = BaseContext.getCreator();
        String staffShortName = BaseContext.getStaffShortName();
        String creatorGuid = BaseContext.getCreatorGuid();
        String tenantGuid = BaseContext.getTenantGuid();
        String staffGuid = BaseContext.getStaffGuid();
        // 保存采购申请单
        ErpPurchaseMgtPurchaseRequisition entity = new ErpPurchaseMgtPurchaseRequisition();
        BeanUtil.copyProperties(ro, entity);
        if (entity.getReceiptDate() == null) {
            entity.setReceiptDate(LocalDateTime.now());
        }
        if (super.updateById(entity)) {
            // 保存采购申请单明细
            List<ErpPurchaseMgtPurchaseRequisition> purchaseRequisitionList = this.saveDetailData(entity, ro);
            //双写采购申请到proof
            ThreadUtil.execAsync(() -> {
                BaseContext.setCreator(creator);
                BaseContext.setCreatorGuid(creatorGuid);
                BaseContext.setTenantGuid(tenantGuid);
                BaseContext.setStaffGuid(staffGuid);
                BaseContext.setStaffShortName(staffShortName);
            IErpPurchaseMgtPurchaseRequisitionService purchaseMgtPurchaseRequisitionService = SpringBeanUtil.getBean(IErpPurchaseMgtPurchaseRequisitionService.class);
            purchaseMgtPurchaseRequisitionService.syncRequistionToProof(entity, purchaseRequisitionList,false);
            });
        }
    }

    /**
     * 编辑删除操作前检查
     *
     * @param id
     * @param isFlow 是否为流程操作
     */
    private void operationCheck(String id, boolean isFlow) {
        ErpPurchaseMgtPurchaseRequisition entity = baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new FlowException(BaseResultErrorCodeImpl.NO_REPETITION_DATA);
        }
        if (!baseMapper.getPurchaseState(id).equals(PurchaseStateEnum.UN.getValue())) {
            throw new FlowException(ResultErrorCode.STATE_NOT_UN_PURCHASE);
        }
        if (!isFlow && !entity.getAuditStatus().equals(BusinessStatusEnum.DRAFT.getStatus())) {
            throw new FlowException(BaseResultErrorCodeImpl.AUDIT_STATE_NOT_DRAFT);
        }
    }

    /**
     * 保存采购申请单明细数据
     *
     * @param pEntity 父工单实体
     * @param ro
     */
    @Transactional(rollbackFor = Exception.class)
    public List<ErpPurchaseMgtPurchaseRequisition> saveDetailData(ErpPurchaseMgtPurchaseRequisition pEntity, ErpPurchaseMgtPurchaseRequisitionRO ro) {
        // 0.更新数据来源采购申请相关数据
        this.updateWorkorderMaterialUsage(pEntity.getWorkorderGuid(), true);
        // 1.先删除旧数据
        if (!CollectionUtils.isEmpty(ro.getDelDetailIds())) {
            super.removeBatchByIds(ro.getDelDetailIds());
        }
        // 2.增加新数据
        List<ErpPurchaseMgtPurchaseRequisition> detailEntitys = new ArrayList<>();
        ro.getDetailList().forEach(detailRO -> {
            ErpPurchaseMgtPurchaseRequisition entity = new ErpPurchaseMgtPurchaseRequisition();
            BeanUtils.copyProperties(detailRO, entity);
            entity.setParentClassificationGuid(pEntity.getWorkorderGuid());
            // 新增时设置采购情况为未采购
            entity.setWorkorderState(PurchaseStateEnum.UN.getValue());
            // 冗余父工单数据
            entity.setWorkorderNumber(pEntity.getWorkorderNumber());
            entity.setSourceValue(pEntity.getSourceValue());
            entity.setWorkorderProperties(WorkorderPropertiesEnum.PURCHASE.getCode());
            entity.setTotalQuantity(detailRO.getTotalQuantity());
            detailEntitys.add(entity);

        });
        super.saveOrUpdateBatch(detailEntitys);
        /*//更新数据来源待开数量
        ro.getDetailList().forEach(s -> {
            if (Objects.nonNull(s.getSourceValue()) && s.getSourceValue().equals(PurchaseSourceEnum.WORK_ORDER.getKey())) {
                iErpFormPendingNumberService.updatePendingNumber(WorkflowKeyEnum.WORK_ORDER_PURCHASE, WorkflowKeyEnum.BUSINESS_MGT_ORDER
                        , baseMapper.getWorkOrderWaitingQuantity());
            }
        });*/
        // 3.最后更新数据来源采购申请相关数据
        this.updateWorkorderMaterialUsage(pEntity.getWorkorderGuid(), false);
        return detailEntitys;
    }

    /**
     * 删除采购申请单明细数据
     *
     * @param parentClassificationGuid 父工单guid
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteDetailData(String parentClassificationGuid) {
        // 先更新数据来源采购申请相关数据
        this.updateWorkorderMaterialUsage(parentClassificationGuid, true);
        // 根据父工单guid查询明细数据
        baseMapper.delete(Wrappers.<ErpPurchaseMgtPurchaseRequisition>lambdaQuery()
                .eq(ErpPurchaseMgtPurchaseRequisition::getParentClassificationGuid, parentClassificationGuid));
    }

    /**
     * 更新数据来源采购申请相关数据
     *
     * @param workorderGuid
     * @param isDelete
     */
    @Transactional(rollbackFor = Exception.class)
    public synchronized void updateWorkorderMaterialUsage(String workorderGuid, boolean isDelete) {
        List<ErpPurchaseMgtPurchaseRequisition> detailList = baseMapper.selectList(Wrappers.<ErpPurchaseMgtPurchaseRequisition>lambdaQuery()
                .eq(ErpPurchaseMgtPurchaseRequisition::getParentClassificationGuid, workorderGuid));
        for (ErpPurchaseMgtPurchaseRequisition detail : detailList) {

            // 数据来源为【工单】，更新工单材料用量表
            if (detail.getSourceValue().equals(PurchaseSourceEnum.WORK_ORDER.getKey())) {
                // 查询工单材料用量表数据
                ErpProductionMgtWorkorderMaterialUsage workorderMaterialUsage = iErpProductionMgtWorkorderMaterialUsageService.getById(detail.getSourceGuid());
                if (Objects.isNull(workorderMaterialUsage)) {
                    throw new FlowException(ResultErrorCode.DATA_NOT_STATUS);
                }
                BigDecimal purchasedRequisitionQuantity = workorderMaterialUsage.getPurchasedRequisitionQuantity();
                // 计算采购申请总数量
                BigDecimal total = isDelete ? purchasedRequisitionQuantity.subtract(detail.getQuantity()) : purchasedRequisitionQuantity.add(detail.getQuantity());
                // 比较采购申请总数量与物料总数量，设置采购申请状态
                if (total.compareTo(BigDecimal.ZERO) <= 0) {
                    workorderMaterialUsage.setPurchaseRequisitionState(PurchaseRequisitionStateEnum.UN.getValue());
                    total = BigDecimal.ZERO;
                } else if (total.compareTo(workorderMaterialUsage.getMaterialUsageQuantity().add(workorderMaterialUsage.getMaterialLossQuantity())) == 0) {
                    workorderMaterialUsage.setPurchaseRequisitionState(PurchaseRequisitionStateEnum.FULL.getValue());
                } else if (total.compareTo(workorderMaterialUsage.getMaterialUsageQuantity().add(workorderMaterialUsage.getMaterialLossQuantity())) == -1) {
                    workorderMaterialUsage.setPurchaseRequisitionState(PurchaseRequisitionStateEnum.PARTIAL.getValue());
                } else {
                    workorderMaterialUsage.setPurchaseRequisitionState(PurchaseRequisitionStateEnum.SUPER_OVER.getValue());
                }
                workorderMaterialUsage.setPurchasedRequisitionQuantity(total);
                // 更新工单材料用量表数据
                iErpProductionMgtWorkorderMaterialUsageService.updateById(workorderMaterialUsage);
            }

            // 数据来源为【工单-部件】，更新工单材料用量表
            if (detail.getSourceValue().equals(PurchaseSourceEnum.WORK_ORDER_PART.getKey())) {
                // 查询工单材料用量表数据
                ErpProductionMgtWorkorderMaterialUsage workorderMaterialUsage = iErpProductionMgtWorkorderMaterialUsageService.getById(detail.getSourceGuid());
                // 如果查不到说明传的是工单，此时需要创建物料用量数据并关联
                if (ObjUtil.isEmpty(workorderMaterialUsage)) {
                    ErpPurchaseMgtPurchaseRequisition erpPurchaseMgtPurchaseRequisition = baseMapper.selectById(detail.getSourceGuid());
                    ErpProductionMgtWorkorderMaterialUsage erpProductionMgtWorkorderMaterialUsage = new ErpProductionMgtWorkorderMaterialUsage();
                    erpProductionMgtWorkorderMaterialUsage.setMaterialGuid(detail.getMaterialGuid());
                    erpProductionMgtWorkorderMaterialUsage.setSourceGuid(detail.getSourceGuid());
                    erpProductionMgtWorkorderMaterialUsage.setSourceValue(MaterialUsageSourceValueEnum.COMPONENT_SUB_WORK_ORDER.getKey());
                    erpProductionMgtWorkorderMaterialUsage.setWorkorderGuid(detail.getSourceGuid());
                    erpProductionMgtWorkorderMaterialUsage.setPurchasedRequisitionQuantity(BigDecimal.ZERO);
                    erpProductionMgtWorkorderMaterialUsage.setMaterialUsageQuantity(erpPurchaseMgtPurchaseRequisition.getQuantity());
                    erpProductionMgtWorkorderMaterialUsage.setMaterialLossQuantity(BigDecimal.ZERO);

                    iErpProductionMgtWorkorderMaterialUsageService.save(erpProductionMgtWorkorderMaterialUsage);
                    detail.setSourceGuid(erpProductionMgtWorkorderMaterialUsage.getWorkorderMaterialUsageGuid());
                    updateById(detail);
                    workorderMaterialUsage = erpProductionMgtWorkorderMaterialUsage;
                }
                BigDecimal purchasedRequisitionQuantity = workorderMaterialUsage.getPurchasedRequisitionQuantity();
                // 计算采购申请总数量
                BigDecimal total = isDelete ? purchasedRequisitionQuantity.subtract(detail.getQuantity()) : purchasedRequisitionQuantity.add(detail.getQuantity());
                // 比较采购申请总数量与物料总数量，设置采购申请状态
                if (total.compareTo(BigDecimal.ZERO) <= 0) {
                    workorderMaterialUsage.setPurchaseRequisitionState(PurchaseRequisitionStateEnum.UN.getValue());
                    total = BigDecimal.ZERO;
                } else if (total.compareTo(workorderMaterialUsage.getMaterialUsageQuantity().add(workorderMaterialUsage.getMaterialLossQuantity())) == 0) {
                    workorderMaterialUsage.setPurchaseRequisitionState(PurchaseRequisitionStateEnum.FULL.getValue());
                } else if (total.compareTo(workorderMaterialUsage.getMaterialUsageQuantity().add(workorderMaterialUsage.getMaterialLossQuantity())) == -1) {
                    workorderMaterialUsage.setPurchaseRequisitionState(PurchaseRequisitionStateEnum.PARTIAL.getValue());
                } else {
                    workorderMaterialUsage.setPurchaseRequisitionState(PurchaseRequisitionStateEnum.SUPER_OVER.getValue());
                }
                workorderMaterialUsage.setPurchasedRequisitionQuantity(total);
                // 更新工单材料用量表数据
                iErpProductionMgtWorkorderMaterialUsageService.updateById(workorderMaterialUsage);
            }

            //来源是订单，更新订单明细扩展表
            if (detail.getSourceValue().equals(PurchaseSourceEnum.BUSINESS_ORDER.getKey())) {
                //获取订单明细数据
                ErpOrderDataVO vo = baseMapper.selectOrderData(detail.getSourceGuid());
                updateSourceOrder(vo, detail.getQuantity(), isDelete);
            }

        }
    }

    /**
     * 更新订单明细表数据
     *
     * @param orderData 订单明细数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSourceOrder(ErpOrderDataVO orderData, BigDecimal quantity, boolean isDelete) {
        if (Objects.isNull(orderData)) {
            throw new FlowException(xy.server.purchase.i18n.ResultErrorCode.DATA_NOT_STATUS);
        }
        if (StringUtils.isNotEmpty(orderData.getSaleOrderDataGuid())) {
            BigDecimal completionQuantity = orderData.getPurchaseQuantity();
            // 计算采购申请总数量
            BigDecimal total = isDelete ? completionQuantity.subtract(quantity) : completionQuantity.add(quantity);
            String value;
            // 比较采购申请总数量与物料总数量，设置采购申请状态
            if (total.compareTo(BigDecimal.ZERO) <= 0) {
                value = PurchaseRequisitionStateEnum.UN.getValue();
                total = BigDecimal.ZERO;
            } else if (total.compareTo(orderData.getOrderDataQuantity()) == 0) {
                value = PurchaseRequisitionStateEnum.FULL.getValue();
            } else if (total.compareTo(orderData.getOrderDataQuantity()) < 0) {
                value = PurchaseRequisitionStateEnum.PARTIAL.getValue();
            } else {
                value = PurchaseRequisitionStateEnum.SUPER_OVER.getValue();
            }
            baseMapper.updateSaleOrderData(total, value, orderData.getSaleOrderDataGuid());
        }
    }

    /**
     * 新增和修改时字段校验
     *
     * @param ro
     */
    private void saveOrUpdateVerify(ErpPurchaseMgtPurchaseRequisitionRO ro) {

    }

    /**
     * 撤回流程时事件
     *
     * @param processVO
     */
    @Override
    public void cancelCall(ActEventEntity processVO) {
        this.operationCheck(processVO.getBusinessKey(), true);
    }

    /**
     * 重启流程时事件
     *
     * @param processVO
     */
    @Override
    public void restartCall(ActEventEntity processVO) {
        this.operationCheck(processVO.getBusinessKey(), true);
        //更新采购申请主表审核状态
        Node node = nodeService.getById(processVO.getBusinessKey());
        if (BeanUtil.isNotEmpty(node)) {
            node.setAuditDate(new Date());
            node.setAuditStatus(BusinessStatusEnum.DRAFT.getStatus());
            nodeService.updateById(node);
        }
        // 重启流程后，下流程（采购订单）待开数量-1
        iErpFormPendingNumberService.minusOne(WorkflowKeyEnum.ORDER_PURCHASE, WorkflowKeyEnum.WORK_ORDER_PURCHASE, TableIdentificationEnum.erp_purchasing_mgt_application_wait_order);
    }

    @Override
    public void finishCall(ActEventEntity processVO) {
        //更新采购申请主表审核状态
        Node node = nodeService.getById(processVO.getBusinessKey());
        if (BeanUtil.isNotEmpty(node)) {
            node.setAuditDate(new Date());
            node.setAuditStatus(BusinessStatusEnum.FINISH.getStatus());
            nodeService.updateById(node);
        }
        // 审核完成后，下流程（采购订单）待开数量+1
        iErpFormPendingNumberService.plusOne(WorkflowKeyEnum.ORDER_PURCHASE, WorkflowKeyEnum.WORK_ORDER_PURCHASE, TableIdentificationEnum.erp_purchasing_mgt_application_wait_order);
    }
}
