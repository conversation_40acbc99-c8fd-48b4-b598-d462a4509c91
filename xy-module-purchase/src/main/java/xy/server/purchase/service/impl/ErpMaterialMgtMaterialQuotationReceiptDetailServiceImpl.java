package xy.server.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.enums.MaterialQuotationEnum;
import com.xunyue.common.enums.MaterialTypeEnum;
import com.xunyue.common.util.StringUtils;
import com.xunyue.config.exception.FlowException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import xy.server.material.entity.ErpMaterialMgtMaterial;
import xy.server.material.entity.model.qo.ErpMaterialMgtMaterialQO;
import xy.server.material.entity.model.ro.ErpMaterialMgtMaterialClassificationDetailRO;
import xy.server.material.entity.model.vo.ErpBasicMgtMaterialSeriesVO;
import xy.server.material.service.IErpMaterialMgtMaterialClassificationService;
import xy.server.material.service.IErpMaterialMgtMaterialService;
import xy.server.purchase.entity.ErpMaterialMgtMaterialQuotationReceiptDetail;
import xy.server.purchase.entity.model.qo.ErpCustomerlQuotationReceiptQO;
import xy.server.purchase.entity.model.ro.ErpMaterialMgtMaterialQuotationReceiptRO;
import xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationReceiptDetailVO;
import xy.server.purchase.entity.model.vo.ErpQuotationImportVO;
import xy.server.purchase.i18n.ResultErrorCode;
import xy.server.purchase.mapper.ErpMaterialMgtMaterialQuotationReceiptDetailMapper;
import xy.server.purchase.service.IErpMaterialMgtMaterialQuotationReceiptDetailService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 物料报价明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Service
public class ErpMaterialMgtMaterialQuotationReceiptDetailServiceImpl extends ServiceImpl<ErpMaterialMgtMaterialQuotationReceiptDetailMapper, ErpMaterialMgtMaterialQuotationReceiptDetail> implements IErpMaterialMgtMaterialQuotationReceiptDetailService {

    @Autowired
    private IErpMaterialMgtMaterialService materialService;
    @Autowired
    private IErpMaterialMgtMaterialClassificationService classificationService;

    /**
     * 插入报价明细数据
     *
     * @param ro
     */
    @Override
    public ErpQuotationImportVO saveData(ErpMaterialMgtMaterialQuotationReceiptRO ro) {
        ErpQuotationImportVO vo = new ErpQuotationImportVO();
        AtomicInteger importNoQuantity = new AtomicInteger();
        AtomicInteger importQuantity = new AtomicInteger();
        List<String> list1 = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ro.getDetailList())) {
            List<ErpMaterialMgtMaterialQuotationReceiptDetail> list = new ArrayList<>();
            ErpCustomerlQuotationReceiptQO receiptQO = new ErpCustomerlQuotationReceiptQO();
            receiptQO.setCustomerOrSupplierGuid(ro.getCustomerOrSupplierGuid());
            receiptQO.setEffectiveDate(ro.getEffectiveDate());
            ro.getDetailList().forEach(s -> {
                if (!StringUtils.isEmpty(s.getMaterialQuotationReceiptDetailGuid())){
                    receiptQO.setMaterialQuotationReceiptDetailGuid(s.getMaterialQuotationReceiptDetailGuid());
                }
                if (!StringUtils.isEmpty(s.getSearchCode())&&ro.getMaterialQuotationReceiptProperties().
                        equals(MaterialQuotationEnum.SUPPLIER_QUOTATION.getKey())){
                    String materialClassification = baseMapper.getMaterialClassification(s.getSearchCode());
                    if (StringUtils.isNotEmpty(materialClassification)) {
                        importQuantity.addAndGet(1);
                        s.setMaterialClassificationGuid(materialClassification);
                    } else {
                        importNoQuantity.addAndGet(1);
                        String message = s.getSearchCode() + "在数据库中不存在，导入失败";
                        list1.add(message);
                        return;
                    }
                }
                if (ro.getMaterialQuotationReceiptProperties().equals(MaterialQuotationEnum.CUSTOMER_QUOTATION.getKey())) {
                   receiptQO.setMaterialGuid(s.getMaterialGuid());
                }else {
                    receiptQO.setMaterialClassificationGuid(s.getMaterialClassificationGuid());
                }
                //判断客户/供应下商报价在该生效日期内是否有其他同物料已经存在
                String materialQuotationReceiptDetailGuid=baseMapper.getReceiptDetailGuid(receiptQO);
                s.setMaterialQuotationReceiptGuid(ro.getMaterialQuotationReceiptGuid());
                ErpMaterialMgtMaterialQuotationReceiptDetail detail = new ErpMaterialMgtMaterialQuotationReceiptDetail();
                if (Objects.nonNull(ro.getImportTent())&&ro.getImportTent()) {
                    if (StringUtils.isNotEmpty(materialQuotationReceiptDetailGuid)){
                        importNoQuantity.addAndGet(1);
                        String message = s.getMaterialName() + "在数据库中存在相同生效日期的同客户数据，导入失败";
                        list1.add(message);
                        return;
                    }
                    //根据物料分类名称获取物料分类id
                    ErpMaterialMgtMaterialQO qo = new ErpMaterialMgtMaterialQO();
                    qo.setMaterialCode(s.getMaterialCode());
                    List<String> list2 = new ArrayList<>();
                    if (ro.getMaterialQuotationReceiptProperties().equals(MaterialQuotationEnum.CUSTOMER_QUOTATION.getKey())) {
                        list2.add(MaterialTypeEnum.FINISHED_PRODUCT.getCode());
                    } else {
                        list2.add(MaterialTypeEnum.MAIN_INGREDIENT.getCode());
                        list2.add(MaterialTypeEnum.SEASONING.getCode());
                    }
                    qo.setMaterialTypeList(list2);
                    if (ro.getMaterialQuotationReceiptProperties().equals(MaterialQuotationEnum.CUSTOMER_QUOTATION.getKey())) {
                        ErpMaterialMgtMaterial only = materialService.findOnly(qo);
                        if (Objects.nonNull(only)) {
                            importQuantity.addAndGet(1);
                            s.setMaterialGuid(only.getMaterialGuid());
                            s.setMaterialClassificationGuid(only.getMaterialClassificationGuid());
                        } else {
                            importNoQuantity.addAndGet(1);
                            String message = StringUtils.blankToDefault(s.getMaterialName(),"")
                                    +StringUtils.blankToDefault(s.getMaterialCode(),"")+ "在数据库中不存在，导入失败";
                            list1.add(message);
                            return;
                        }
                    }
                }else {
                if (StringUtils.isNotEmpty(materialQuotationReceiptDetailGuid)){
                    throw new FlowException(ResultErrorCode.DATA_IS_EQUAL_STATUS);
                }}
                BeanUtil.copyProperties(s, detail);
                detail.setMaterialQuotationReceiptDetailGuid("");
                list.add(detail);
            });
            if (CollectionUtils.isNotEmpty(list)) {
                super.saveBatch(list);
            }
        }
        if (CollectionUtils.isNotEmpty(list1)) {
            vo.setMessage(list1);
        }
        vo.setImportQuantity(importQuantity.get());
        vo.setImportNoQuantity(importNoQuantity.get());
        return vo;
    }

    /**
     * 删除报价明细数据
     *
     * @param materialQuotationReceiptGuid
     */
    @Override
    public void deleteMessage(String materialQuotationReceiptGuid) {
        LambdaQueryWrapper<ErpMaterialMgtMaterialQuotationReceiptDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpMaterialMgtMaterialQuotationReceiptDetail::getMaterialQuotationReceiptGuid, materialQuotationReceiptGuid);
        baseMapper.delete(wrapper);
    }

    /**
     * 根据报价主表id获取明细数据
     *
     * @param materialQuotationIds
     * @return
     */
    @Override
    public List<ErpMaterialMgtMaterialQuotationReceiptDetailVO> getDataList(List<String> materialQuotationIds,Integer materialQuotationReceiptProperties) {
        List<ErpMaterialMgtMaterialQuotationReceiptDetailVO> dataList = baseMapper.getDataList(materialQuotationIds);
        if (CollectionUtils.isNotEmpty(dataList)) {
            if(1==materialQuotationReceiptProperties){
                materialService.loadMaterialObj(dataList, ErpMaterialMgtMaterialQuotationReceiptDetailVO::getMaterialGuid,ErpMaterialMgtMaterialQuotationReceiptDetailVO::setMaterialObj,null);
            }
            if (2==materialQuotationReceiptProperties){
                List<String> materialClassifications = dataList.stream().filter(x -> StringUtils.isNotEmpty(x.getMaterialClassificationGuid()))
                        .map(ErpMaterialMgtMaterialQuotationReceiptDetailVO::getMaterialClassificationGuid).collect(Collectors.toList());
                List<ErpMaterialMgtMaterialClassificationDetailRO> classificationDetailROS =  baseMapper.getMaterialClassificationValues(materialClassifications);
                if (CollectionUtils.isNotEmpty(classificationDetailROS)){
                    Map<String, List<ErpMaterialMgtMaterialClassificationDetailRO>> materialClassMap = classificationDetailROS.stream().collect(Collectors.groupingBy(ErpMaterialMgtMaterialClassificationDetailRO::getMaterialClassificationGuid));
                    dataList.forEach(x -> {
                        if (StringUtils.isNotEmpty(x.getMaterialClassificationGuid())){
                            List<ErpMaterialMgtMaterialClassificationDetailRO> classificationDetailROS1 = materialClassMap.get(x.getMaterialClassificationGuid());
                            if (CollectionUtils.isNotEmpty(classificationDetailROS1)){
//                                x.setMaterialClassificationName(classificationDetailROS1.get(0).getMaterialClassificationName());
                                x.setClassificationDetailROS(classificationDetailROS1);
                            }
                        }
                    });
                }
            }
        }
        return dataList;
    }
}
