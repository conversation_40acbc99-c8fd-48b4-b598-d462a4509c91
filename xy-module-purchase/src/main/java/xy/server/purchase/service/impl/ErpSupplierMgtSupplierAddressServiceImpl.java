package xy.server.purchase.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.basic.entity.OrganizationAddress;
import com.xunyue.basic.entity.model.dto.OrganizationAddressDTO;
import com.xunyue.basic.service.IOrganizationAddressService;
import com.xunyue.common.i18n.BaseResultErrorCodeImpl;
import com.xunyue.config.exception.FlowException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import xy.server.purchase.entity.ErpSupplierMgtSupplierAddress;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierAddressRO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierAddressVO;
import xy.server.purchase.mapper.ErpSupplierMgtSupplierAddressMapper;
import xy.server.purchase.service.IErpSupplierMgtSupplierAddressService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 供应商地址 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Service
public class ErpSupplierMgtSupplierAddressServiceImpl extends ServiceImpl<ErpSupplierMgtSupplierAddressMapper, ErpSupplierMgtSupplierAddress> implements IErpSupplierMgtSupplierAddressService {

    @Resource
    private IOrganizationAddressService organizationAddressService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveData(List<ErpSupplierMgtSupplierAddressRO> roList, List<String> delGuids, String supplierGuid) {
        // 先处理删除的数据
        if (!CollectionUtils.isEmpty(delGuids)) {
            baseMapper.deleteBatchIds(delGuids);
        }
        // 双写删除
        QueryWrapper<OrganizationAddress> organizationAddressQueryWrapper = new QueryWrapper<>();
        organizationAddressQueryWrapper.eq("organization_id", supplierGuid);
        organizationAddressService.remove(organizationAddressQueryWrapper);

        // 再处理新增和编辑的数据
        List<OrganizationAddressDTO> dtoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roList)) {
            List<ErpSupplierMgtSupplierAddress> entityList = new ArrayList<>();
            for (int i = 0; i < roList.size(); i++) {
                ErpSupplierMgtSupplierAddress entity = new ErpSupplierMgtSupplierAddress();
                BeanUtils.copyProperties(roList.get(i), entity);
                entity.setSupplierGuid(supplierGuid);
                entity.setSerialNumber(i + 1);
                entityList.add(entity);

                OrganizationAddressDTO organizationAddress = new OrganizationAddressDTO();
                organizationAddress.setOrganizationAddressTenantId(entity.getTenantGuid());
                organizationAddress.setOrganizationAddressId(entity.getSupplierAddressGuid());
                organizationAddress.setOrganizationAddressOrganizationId(entity.getSupplierGuid());
                organizationAddress.setOrganizationAddressAdministrativeAreaId(entity.getAdministrativeAreaGuid());
                organizationAddress.setOrganizationAddressAddress(entity.getAddress());
                organizationAddress.setOrganizationAddressIsDefault(entity.getIsDefault());
                organizationAddress.setOrganizationAddressDescription(entity.getDescription());
                organizationAddress.setOrganizationAddressAddressUnion("");
                dtoList.add(organizationAddress);
            }
            super.saveOrUpdateBatch(entityList);
            organizationAddressService.saveOrUpdateBatch(dtoList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delBySupplierGuid(String supplierGuid) {
        if (!StringUtils.isEmpty(supplierGuid)) {
            super.remove(Wrappers.<ErpSupplierMgtSupplierAddress>lambdaQuery().eq(ErpSupplierMgtSupplierAddress::getSupplierGuid, supplierGuid));

            // 双写删除
            organizationAddressService.getBaseMapper().deleteById(supplierGuid);
        }
    }

    @Override
    public List<ErpSupplierMgtSupplierAddressVO> getDataByIds(List<String> supplierGuids) {
        if (CollUtil.isEmpty(supplierGuids)) {
            return new ArrayList<>();
        }
        return baseMapper.getDataBySupplierGuids(supplierGuids);
    }

    /**
     * 处理导入Excel数据
     * @param ro
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcelDataHandle(ErpSupplierMgtSupplierAddressRO ro) {
        // 处理供应商
        if (!StringUtils.isEmpty(ro.getSupplierGuid())) {
            // 根据供应商编码查询供应商guid
            String supplierGuid = baseMapper.getSupplierGuidByCode(ro.getSupplierGuid());
            if (StringUtils.isEmpty(supplierGuid)) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "供应商【" + ro.getSupplierGuid() + "】不存在！");
            }
            ro.setSupplierGuid(supplierGuid);
        }
        // 处理是否默认
        if (ro.getIsDefault() != null && ro.getIsDefault()) {
            super.update(Wrappers.<ErpSupplierMgtSupplierAddress>lambdaUpdate().set(ErpSupplierMgtSupplierAddress::getIsDefault, false)
                    .eq(ErpSupplierMgtSupplierAddress::getSupplierGuid, ro.getSupplierGuid()));
        }
        // 保存数据到数据库
        ErpSupplierMgtSupplierAddress entity = new ErpSupplierMgtSupplierAddress();
        BeanUtils.copyProperties(ro, entity);
        super.save(entity);
    }
}

