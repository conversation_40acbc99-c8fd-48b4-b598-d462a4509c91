package xy.server.purchase.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.purchase.entity.ErpPurchaseProductionMgtWorkorderOrderData;
import xy.server.purchase.entity.model.qo.ErpProductionMgtWorkorderOrderDataQO;
import xy.server.purchase.entity.model.ro.ErpPurchaseProductionMgtWorkorderOrderDataRO;
import xy.server.purchase.entity.model.vo.ErpPurchaseProductionMgtWorkorderOrderDataVO;

import java.util.List;

/**
 * @apiNote 工单订单数据表 服务类
 * <AUTHOR>
 * @since 2023-11-08
 */
public interface IErpPurchaseProductionMgtWorkorderOrderDataService extends IService<ErpPurchaseProductionMgtWorkorderOrderData> {
                    boolean create(ErpPurchaseProductionMgtWorkorderOrderDataRO ro);

    boolean delete(String workorderOrderDataGuid);

    boolean deleteByBatch(List<String> workorderOrderDataGuids);

    boolean update(ErpPurchaseProductionMgtWorkorderOrderDataRO ro);

    boolean saveDate(InsertOrUpdateList<ErpPurchaseProductionMgtWorkorderOrderDataRO> dataList);

    ErpPurchaseProductionMgtWorkorderOrderDataVO getDataById(String workorderOrderDataGuid);

    List<ErpPurchaseProductionMgtWorkorderOrderDataVO> findList(ErpProductionMgtWorkorderOrderDataQO qo);

    IPage<ErpPurchaseProductionMgtWorkorderOrderDataVO> findPage(PageParams<ErpProductionMgtWorkorderOrderDataQO> pageParams);
}
