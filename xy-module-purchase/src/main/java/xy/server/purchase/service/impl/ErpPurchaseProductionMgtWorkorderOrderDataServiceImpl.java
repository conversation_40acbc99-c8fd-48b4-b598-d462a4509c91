package xy.server.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.purchase.entity.ErpPurchaseProductionMgtWorkorderOrderData;
import xy.server.purchase.entity.model.qo.ErpProductionMgtWorkorderOrderDataQO;
import xy.server.purchase.entity.model.ro.ErpPurchaseProductionMgtWorkorderOrderDataRO;
import xy.server.purchase.entity.model.vo.ErpPurchaseProductionMgtWorkorderOrderDataVO;
import xy.server.purchase.mapper.ErpPurchaseProductionMgtWorkorderOrderDataMapper;
import xy.server.purchase.service.IErpPurchaseProductionMgtWorkorderOrderDataService;

import java.util.List;

/**
 * <p>
 * 工单订单数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Service
public class ErpPurchaseProductionMgtWorkorderOrderDataServiceImpl extends ServiceImpl<ErpPurchaseProductionMgtWorkorderOrderDataMapper, ErpPurchaseProductionMgtWorkorderOrderData> implements IErpPurchaseProductionMgtWorkorderOrderDataService {
                    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpPurchaseProductionMgtWorkorderOrderDataRO ro){
        ErpPurchaseProductionMgtWorkorderOrderData entity = new ErpPurchaseProductionMgtWorkorderOrderData();
        BeanUtil.copyProperties(ro, entity);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String workorderOrderDataGuid){
        return super.removeById(workorderOrderDataGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> workorderOrderDataGuids) {
        for (String workorderOrderDataGuid : workorderOrderDataGuids) {
            super.removeById(workorderOrderDataGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpPurchaseProductionMgtWorkorderOrderDataRO ro){
        ErpPurchaseProductionMgtWorkorderOrderData entity = new ErpPurchaseProductionMgtWorkorderOrderData();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<ErpPurchaseProductionMgtWorkorderOrderDataRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public ErpPurchaseProductionMgtWorkorderOrderDataVO getDataById(String workorderOrderDataGuid){
        return baseMapper.getDataByGuid(workorderOrderDataGuid);
    }

    @Override
    public List<ErpPurchaseProductionMgtWorkorderOrderDataVO> findList(ErpProductionMgtWorkorderOrderDataQO qo){
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpPurchaseProductionMgtWorkorderOrderDataVO> findPage(PageParams<ErpProductionMgtWorkorderOrderDataQO> pageParams){
        IPage<ErpPurchaseProductionMgtWorkorderOrderDataVO> page = pageParams.buildPage();
        ErpProductionMgtWorkorderOrderDataQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }
}
