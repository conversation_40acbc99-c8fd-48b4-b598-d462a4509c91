package xy.server.purchase.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import org.springframework.web.multipart.MultipartFile;
import xy.server.purchase.entity.ErpMaterialMgtMaterialQuotationReceipt;
import xy.server.purchase.entity.model.qo.ErpMaterialMgtMaterialQuotationReceiptQO;
import xy.server.purchase.entity.model.ro.ErpMaterialMgtMaterialQuotationReceiptRO;
import xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationReceiptDetailVO;
import xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationReceiptVO;
import xy.server.purchase.entity.model.vo.ErpMaterialMgtMaterialQuotationVO;
import xy.server.purchase.entity.model.vo.ErpQuotationImportVO;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @apiNote 物料报价表 服务类
 * @since 2024-05-23
 */
public interface IErpMaterialMgtMaterialQuotationReceiptService extends IService<ErpMaterialMgtMaterialQuotationReceipt> {
    ErpQuotationImportVO create(ErpMaterialMgtMaterialQuotationReceiptRO ro);

    boolean delete(String materialQuotationReceiptGuid);

    boolean deleteByBatch(List<String> materialQuotationReceiptGuids);

    boolean update(ErpMaterialMgtMaterialQuotationReceiptRO ro);

    ErpQuotationImportVO saveDate(InsertOrUpdateList<ErpMaterialMgtMaterialQuotationReceiptRO> dataList);

    ErpMaterialMgtMaterialQuotationReceiptVO getDataById(String materialQuotationReceiptGuid);

    List<ErpMaterialMgtMaterialQuotationReceiptVO> findList(ErpMaterialMgtMaterialQuotationReceiptQO qo);

    IPage<ErpMaterialMgtMaterialQuotationReceiptVO> findPage(PageParams<ErpMaterialMgtMaterialQuotationReceiptQO> pageParams);

    /**
     * 应用报价-生效状态变更为已生效
     * @param list
     * @return
     */
    Boolean useQuotation(List<String> list);

    /**
     * 数据导入
     * @param file
     * @return
     */
    ErpQuotationImportVO importOrderDate(MultipartFile file) throws IOException;

    /**
     * 平铺明细数据
     * @param pageParams
     * @return
     */
    IPage<ErpMaterialMgtMaterialQuotationVO> findDetailPage(PageParams<ErpMaterialMgtMaterialQuotationReceiptQO> pageParams);

    /**
     * 获取最新的采购报价
     * @param qo
     * @return
     */
    List<ErpMaterialMgtMaterialQuotationReceiptDetailVO> getNewMoney(ErpMaterialMgtMaterialQuotationReceiptQO qo);
}
