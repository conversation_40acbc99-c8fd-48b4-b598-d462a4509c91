package xy.server.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.vo.SourceDescriptionAndPictureVO;
import com.xunyue.common.enums.*;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.common.util.LoadDataUtils;
import com.xunyue.common.util.UsedUtils;
import com.xunyue.common.util.XyBeanUtil;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import com.xunyue.exteriororder.entity.model.dto.OutgoingOrderDTO;
import com.xunyue.exteriororder.entity.model.dto.OutgoingOrderDetailDTO;
import com.xunyue.exteriororder.service.IOutgoingOrderDetailService;
import com.xunyue.exteriororder.service.IOutgoingOrderService;
import com.xunyue.node.common.enums.ClazzEnum;
import com.xunyue.node.common.enums.TypeEnum;
import com.xunyue.node.entity.model.dto.LinkDTO;
import com.xunyue.node.service.ILinkService;
import com.xunyue.order.entity.Order;
import com.xunyue.proof.entity.Proof;
import com.xunyue.proof.entity.model.dto.ProofUnitQuantityDTO;
import com.xunyue.proof.service.IProofUnitQuantityService;
import com.xunyue.tenant.sign.UserApi;
import com.xy.util.BaseContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.activiti.common.enums.BusinessStatusEnum;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.service.IErpFormPendingNumberService;
import xy.server.activiti.service.IProcessInstanceService;
import xy.server.activiti.utils.actEvent.ActEventEntity;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.basic.entity.model.vo.ErpBasicMgtCurrencyExchangeRateVO;
import xy.server.basic.entity.model.vo.ErpBasicMgtInvoiceTypeTaxRateVO;
import xy.server.basic.entity.model.vo.ErpWorkorderDataProcessParameterVO;
import xy.server.basic.mapper.ErpBasicMgtProductionProcessesTypeMapper;
import xy.server.basic.service.IErpBasicMgtCurrencyExchangeRateService;
import xy.server.basic.service.IErpBasicMgtInvoiceTypeTaxRateService;
import xy.server.basic.service.IErpBasicMgtProductionProcessesTypeService;
import xy.server.basic.service.IErpBusinessMgtOtherExpensesService;
import xy.server.basic.util.UuidUtils;
import xy.server.dto.XyMemberDto;
import xy.server.material.entity.ErpMaterialMgtMaterial;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;
import xy.server.material.service.IErpMaterialMgtMaterialService;
import xy.server.purchase.entity.*;
import xy.server.purchase.entity.model.qo.ErpOutgoingBusinessMgtOrderQO;
import xy.server.purchase.entity.model.qo.ErpPurchaseMgtPurchaseOrderQO;
import xy.server.purchase.entity.model.qo.ErpPurchaseProcessOutgoingOrderQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingBusinessMgtOrderRO;
import xy.server.purchase.entity.model.ro.ErpOutgoingDetaiStopRO;
import xy.server.purchase.entity.model.vo.*;
import xy.server.purchase.i18n.ResultErrorCode;
import xy.server.purchase.mapper.*;
import xy.server.purchase.service.IErpOutgoingBusinessMgtOrderService;
import xy.server.purchase.service.IErpOutgoingMgtApplicationService;
import xy.server.purchase.service.IErpPurchaseMgtPurchaseOrderDataService;
import xy.server.purchase.service.IErpSupplierMgtSupplierService;
import xy.server.work.entity.ErpProductionMgtWorkorder;
import xy.server.work.entity.ErpProductionMgtWorkorderOrderData;
import xy.server.work.entity.model.dto.BaseMapContainer;
import xy.server.work.entity.model.vo.ErpProductionMgtWorkorderDataProcessVO;
import xy.server.work.entity.model.vo.ErpProductionMgtWorkorderMaterialUsageVO;
import xy.server.work.service.IErpProductionMgtWorkorderDataProcessService;
import xy.server.work.service.IErpProductionMgtWorkorderOrderDataService;
import xy.server.work.service.IErpProductionMgtWorkorderService;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Service("ORDER.OUTGOING")
@Slf4j
public class ErpOutgoingBusinessMgtOrderServiceImpl extends ServiceImpl<ErpOutgoingBusinessMgtOrderMapper, ErpOutgoingBusinessMgtOrder> implements IErpOutgoingBusinessMgtOrderService, ActEventStrategyService {

    @Autowired
    private ErpOutgoingBusinessMgtOrderPlaceOfDeliveryMapper deliveryMapper;
    @Autowired
    private ErpOutgoingBusinessMgtOrderFileMapper fileMapper;
    @Autowired
    private ErpOutgoingMgtApplicationMapper erpOutgoingMgtApplicationMapper;
    @Autowired
    private ErpOutgoingDataProcessMapper erpOutgoingDataProcessMapper;
    @Autowired
    private ErpOutgoingBusinessMgtOrderDataMapper orderDataMapper;
    @Autowired
    private IErpSystemMgtOrderSerialNumberService erpSystemMgtOrderSerialNumberService;
    @Autowired
    private IProcessInstanceService iProcessInstanceService;
    @Autowired
    private IErpProductionMgtWorkorderOrderDataService orderRelationService;
    @Autowired
    private IErpPurchaseMgtPurchaseOrderDataService purchaseMgtPurchaseOrderDataService;
    @Autowired
    private IErpProductionMgtWorkorderService workorderService;
    @Autowired
    UsedUtils usedUtils;
    @Autowired
    private IErpProductionMgtWorkorderDataProcessService processService;
    @Autowired
    private ErpBasicMgtProductionProcessesTypeMapper processesTypeMapper;
    @Autowired
    private IErpFormPendingNumberService pendingNumberService;
    @Autowired
    private IErpSupplierMgtSupplierService supplierService;
    @Autowired
    private IErpBusinessMgtOtherExpensesService otherExpensesService;
    @Resource
    private IErpMaterialMgtMaterialService materialService;
    @Resource
    private IErpOutgoingMgtApplicationService outgoingMgtApplicationService;
    @Autowired
    private IErpBasicMgtProductionProcessesTypeService productionProcessesTypeService;

    @Autowired
    private UserApi userApi;

    @Autowired
    private IOutgoingOrderService outgoingOrderService;

    @Autowired
    private IOutgoingOrderDetailService outgoingOrderDetailService;

    @Autowired
    private IProofUnitQuantityService proofUnitQuantityService;

    @Autowired
    private ILinkService linkService;

    @Autowired
    private IErpBasicMgtInvoiceTypeTaxRateService rateService;

    @Autowired
    private IErpBasicMgtCurrencyExchangeRateService currencyExchangeRateService;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(ErpOutgoingBusinessMgtOrderRO ro) {
        //1-将数据插入订单表以及订单交货地点表
        ErpOutgoingBusinessMgtOrder entity = new ErpOutgoingBusinessMgtOrder();
        BeanUtil.copyProperties(ro, entity);
        //根据方法生成主订单的订单号
        //根据表单id获取关键字以及id值
        String orderNumber = erpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.ORDER_OUTGOING);
        entity.setOrderNumber(orderNumber);
        entity.setAuditStatus(BusinessStatusEnum.DRAFT.getStatus());
        entity.setOrderProperties(OrderPropertiesEnum.OUTGOING_ORDER.getCode());
        entity.setReceiptDate(LocalDateTime.now());
        baseMapper.insert(entity);
        XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();
        ThreadUtil.execAsync(() -> {
            userApi.setThreadLocal(memberDto);
            this.createOrder(entity, ro);
        });
        //供应商被使用
        usedUtils.isUsed("erp_supplier_mgt_supplier", ro.getCustomerOrSupplierGuid(), "", "supplier_guid");
        usedUtils.isUsed("erp_supplier_mgt_supplier", ro.getSettlementCustomerOrSupplierGuid(), "", "supplier_guid");
        ro.setOrderGuid(entity.getOrderGuid());
        ro.setOrderNumber(orderNumber);        //2-将数据插入订单文件表
        ro.getFileGuid().forEach(fileGuidAndNameVO -> {
            ErpOutgoingBusinessMgtOrderFile file = new ErpOutgoingBusinessMgtOrderFile();
            file.setFileGuid(fileGuidAndNameVO.getFileGuid());
            file.setOrderGuid(ro.getOrderGuid());
            fileMapper.insert(file);
        });
        //3-将数据插入订单明细加入订单明细表
        insertOrderData(ro, false);
        // 启动审核流程
        iProcessInstanceService.start(WorkflowKeyEnum.ORDER_OUTGOING, entity.getOrderGuid());
        return entity.getOrderGuid();
    }

    /**
     * 插入订单相关数据(订单数据表数据，订单交货地址数据)
     *
     * @param ro
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertOrderData(ErpOutgoingBusinessMgtOrderRO ro, Boolean type) {
        List<ErpOutgoingBusinessMgtOrderData> orderDataList = new ArrayList<>();
        ro.getSpecificList().forEach(
                erpOutgoingBusinessSpecifOrderDataRO -> {
                    //1-插入订单数据
                    //1.1-判断收货数量与总数量的大小
                    if (type) {
                        erpOutgoingBusinessSpecifOrderDataRO.setOutputQuantity(erpOutgoingBusinessSpecifOrderDataRO.getQuantity());
                    }
                    ErpOutgoingDataProcess dataProcess = erpOutgoingDataProcessMapper.selectById(erpOutgoingBusinessSpecifOrderDataRO.getWorkOrderProcessGuid());
                    ErpProductionMgtWorkorder workorder = erpOutgoingMgtApplicationMapper.selectById(dataProcess.getWorkorderGuid());
                    //1.2-处理工序工单外发状态
                    BigDecimal newOutsourcedRequisitionQuantity = dataProcess.getOutsourcedQuantity()
                            .add(erpOutgoingBusinessSpecifOrderDataRO.getQuantity());
                    int compare1 = newOutsourcedRequisitionQuantity.compareTo(workorder.getQuantity());
                    if (compare1 > 0) {
                        dataProcess.setOutsourceState(ErpOutgoingStatusEnum.SURPLUS_OUTGOING.getCode());
                    } else if (compare1 == 0) {
                        dataProcess.setOutsourceState(ErpOutgoingStatusEnum.ALL_OUTGOING.getCode());
                    } else {
                        dataProcess.setOutsourceState(ErpOutgoingStatusEnum.PARTIALLY_OUTGOING.getCode());
                    }
                    dataProcess.setOutsourcedQuantity(newOutsourcedRequisitionQuantity);
                    //外发订单数大于外发申请数
                    int compare = erpOutgoingBusinessSpecifOrderDataRO.getQuantity()
                            .compareTo(erpOutgoingBusinessSpecifOrderDataRO.getTotalQuantity());
                    if (compare > 0) {
                        //处理外发申请状态
                        BigDecimal superQuantity = dataProcess.getOutsourcedRequisitionQuantity()
                                .add(erpOutgoingBusinessSpecifOrderDataRO.getOutputQuantity());
                        int compare2 = superQuantity.compareTo(workorder.getQuantity());
                        if (compare2 >= 0) {
                            dataProcess.setOutsourceRequisitionState(ErpOutgoingApplicationStatusEnum.ALL_OUTGOING.getCode());
                        } else {
                            dataProcess.setOutsourceRequisitionState(ErpOutgoingApplicationStatusEnum.PARTIALLY_OUTGOING.getCode());
                        }
                    }
                    erpOutgoingDataProcessMapper.updateById(dataProcess);
                    //对应订单生成一张子工单用于计算待外发申请的数量
                    ErpProductionMgtWorkorder outgoingMgtApplication = new ErpProductionMgtWorkorder();
                    outgoingMgtApplication.setWorkorderNumber(erpOutgoingBusinessSpecifOrderDataRO.getApplicationNumber());
                    outgoingMgtApplication.setSourceValue(WorkorderPropertiesEnum.WORK_ORDER.getCode());
                    outgoingMgtApplication.setParentClassificationGuid(erpOutgoingBusinessSpecifOrderDataRO.getWorkorderGuid());
                    outgoingMgtApplication.setQuantity(erpOutgoingBusinessSpecifOrderDataRO.getQuantity());
                    erpOutgoingMgtApplicationMapper.insert(outgoingMgtApplication);
                    //修改外发申请的外发状态
                    LambdaQueryWrapper<ErpProductionMgtWorkorder> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(ErpProductionMgtWorkorder::getParentClassificationGuid, erpOutgoingBusinessSpecifOrderDataRO.getWorkorderGuid());
                    BigDecimal all = erpOutgoingMgtApplicationMapper.selectList(wrapper).stream()
                            .map(ErpProductionMgtWorkorder::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    ErpProductionMgtWorkorder workorder2 = erpOutgoingMgtApplicationMapper.selectById(erpOutgoingBusinessSpecifOrderDataRO.getWorkorderGuid());
                    int i = workorder2.getTotalQuantity().compareTo(all);
                    if (i <= 0) {
                        workorder2.setWorkorderState(ErpOutgoingApplicationStatusEnum.ALL_OUTGOING.getCode());
                    } else {
                        workorder2.setWorkorderState(ErpOutgoingApplicationStatusEnum.PARTIALLY_OUTGOING.getCode());
                    }
                    erpOutgoingMgtApplicationMapper.updateById(workorder2);
                    //将数据插入订单数据表
                    ErpOutgoingBusinessMgtOrderData orderData = new ErpOutgoingBusinessMgtOrderData();
                    BeanUtil.copyProperties(erpOutgoingBusinessSpecifOrderDataRO, orderData);
                    orderData.setSourceGuid(outgoingMgtApplication.getWorkorderGuid());
                    orderData.setSourceValue(WorkorderPropertiesEnum.WORK_ORDER.getCode());
                    orderData.setOrderGuid(ro.getOrderGuid());
                    orderData.setCompletionStatus(CompletionStatusEnum.NOT_FINISH.getCode());
                    orderData.setQuantity(erpOutgoingBusinessSpecifOrderDataRO.getQuantity());
                    orderDataMapper.insert(orderData);
                    orderDataList.add(orderData);
                    // 其他费用
                    if (CollUtil.isNotEmpty(erpOutgoingBusinessSpecifOrderDataRO.getIsdelectList())) {
                        orderDataMapper.deleteBatchIds(erpOutgoingBusinessSpecifOrderDataRO.getIsdelectList());
                    }
                    if (CollUtil.isNotEmpty(erpOutgoingBusinessSpecifOrderDataRO.getOtherExpensesList())) {
                        erpOutgoingBusinessSpecifOrderDataRO.getOtherExpensesList().forEach(other -> {
                            if (com.xunyue.common.util.StringUtils.isBlank(other.getOtherExpensesGuid())) {
                                other.setOtherExpensesGuid(UuidUtils.generateUUID());
                            }
                            other.setSourceGuid(orderData.getOrderDataGuid());
                            other.setSourceValus(OtherExpensesEnum.OUTSOURCE_ORDER.getCode());
                            other.setTenantGuid(BaseContext.getTenantGuid());
                            other.setCreator(BaseContext.getCreator());
                            other.setCreatorGuid(BaseContext.getCreatorGuid());
                            other.setCreateDate(LocalDateTime.now());
                            other.setLastUpdater(BaseContext.getCreator());
                            other.setLastUpdaterGuid(BaseContext.getCreator());
                            other.setLastUpdateDate(LocalDateTime.now());
                        });
                        baseMapper.saveOrUpdateOtherExpensesBatch(erpOutgoingBusinessSpecifOrderDataRO.getOtherExpensesList());
                    }
                    //处理上流程待开
                    //pendingNumberService.updatePendingNumber(WorkflowKeyEnum.ORDER_OUTGOING, WorkflowKeyEnum.WORK_ORDER_OUTGOING
                    //        , baseMapper.getWaitingQuantity());
                    //将工序工单guid插入
                    ErpProductionMgtWorkorderOrderData data = new ErpProductionMgtWorkorderOrderData();
                    data.setOrderDataGuid(orderData.getOrderDataGuid());
                    ErpProductionMgtWorkorderDataProcessVO processServiceById = processService.
                            getById(erpOutgoingBusinessSpecifOrderDataRO.getWorkOrderProcessGuid());
                    data.setWorkorderGuid(processServiceById.getWorkorderGuid());
                    orderRelationService.save(data);
                    //将订单数据对应收货地址插入订单交货地
                    ErpOutgoingBusinessMgtOrderPlaceOfDelivery delivery = new ErpOutgoingBusinessMgtOrderPlaceOfDelivery();
                    delivery.setOrderDataGuid(orderData.getOrderDataGuid());
                    delivery.setOrderGuid(ro.getOrderGuid());
                    delivery.setAddress(erpOutgoingBusinessSpecifOrderDataRO.getAddress());
                    delivery.setAdministrativeAreaGuid(erpOutgoingBusinessSpecifOrderDataRO.getAdministrativeAreaGuid());
                    deliveryMapper.insert(delivery);
                }
        );
        if (CollUtil.isNotEmpty(orderDataList)) {
            XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();
            ThreadUtil.execAsync(() -> {
                userApi.setThreadLocal(memberDto);
                this.createOrderDetail(orderDataList, ro.getOrderGuid());
            });
        }
    }

    /**
     * 审核完成事件（生成到货单）
     *
     * @param processVO
     */
    @Override
    public void finishCall(ActEventEntity processVO) {
        ErpOutgoingBusinessMgtOrder purchaseOrder = baseMapper.selectById(processVO.getBusinessKey());
        assert purchaseOrder != null;
        // 生成大的到货单
        ErpProductionMgtWorkorder entity = new ErpProductionMgtWorkorder();
        entity.setWorkorderNumber(purchaseOrder.getOrderNumber());
        entity.setReceiptDate(purchaseOrder.getReceiptDate());
        entity.setWorkorderProperties(WorkorderPropertiesEnum.MATERIAL_DELIVERY_FATHER.getCode());
        entity.setSourceValue(DeliveryOrderDataSourceEnum.OUTGOING_ORDER.getCode());
        entity.setSourceGuid(purchaseOrder.getOrderGuid());
        erpOutgoingMgtApplicationMapper.insert(entity);
        // 查询外发订单明细，生成到货单明细
        List<ErpPurchaseMgtPurchaseOrderData> purchaseOrderDataList = purchaseMgtPurchaseOrderDataService.list(Wrappers
                .<ErpPurchaseMgtPurchaseOrderData>lambdaQuery().eq(ErpPurchaseMgtPurchaseOrderData::getOrderGuid, purchaseOrder.getOrderGuid()));
        // 工单明细数据列表
        List<ErpProductionMgtWorkorder> detailList = new ArrayList<>(purchaseOrderDataList.size());
        // 工单订单数据列表
        List<ErpPurchaseProductionMgtWorkorderOrderData> workorderOrderDateList = new ArrayList<>(purchaseOrderDataList.size());
        purchaseOrderDataList.forEach(purchaseOrderData -> {
            ErpProductionMgtWorkorder detail = new ErpProductionMgtWorkorder();
            BeanUtils.copyProperties(purchaseOrderData, detail);
            // 默认未完成状态
            detail.setSourceGuid(purchaseOrderData.getOrderDataGuid());
            detail.setRequiredDeliveryTime(purchaseOrderData.getDeliveryDate());
            detail.setParentClassificationGuid(entity.getWorkorderGuid());
            // 冗余父工单数据
            detail.setWorkorderNumber(entity.getWorkorderNumber());
            detail.setWorkorderProperties(entity.getWorkorderProperties());
            detail.setSourceValue(entity.getSourceValue());
            // 先设置guid，用于跟工单订单数据相关联
            detail.setWorkorderGuid(IdUtil.fastSimpleUUID());
            detailList.add(detail);
        });
        workorderService.saveBatch(detailList);
        //审核完成以后，外发到货，运输计划，凭证管理的待开数加一
        pendingNumberService.plusOne(WorkflowKeyEnum.WORK_ORDER_OUTGOING_ARRIVAL, WorkflowKeyEnum.ORDER_OUTGOING);
        pendingNumberService.plusOne(WorkflowKeyEnum.TRANSPORTATION_PLAN_WORK_ORDER, WorkflowKeyEnum.ORDER_OUTGOING);
        pendingNumberService.plusOne(WorkflowKeyEnum.VOUCHER_MANAGEMENT, WorkflowKeyEnum.ORDER_OUTGOING);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String orderGuid) {
        //1-查询订单数据判断订单属性是否是未完成并且审核状态为草稿
        ErpOutgoingBusinessMgtOrder order = baseMapper.selectById(orderGuid);
        QueryWrapper<ErpOutgoingBusinessMgtOrderData> wrapper = new QueryWrapper<>();
        wrapper.eq("order_guid", orderGuid);
        List<ErpOutgoingBusinessMgtOrderData> orderDataList = orderDataMapper.selectList(wrapper);
        List<ErpOutgoingBusinessMgtOrderData> collect = orderDataList.stream()
                .filter(erpOutgoingBusinessMgtOrderData -> erpOutgoingBusinessMgtOrderData.getCompletionStatus().equals(CompletionStatusEnum.NOT_FINISH.getCode())).collect(Collectors.toList());
        if ((CollectionUtils.isNotEmpty(collect) && collect.size() <= 0) || !order.getAuditStatus().equals(BusinessStatusEnum.DRAFT.getStatus())) {
            throw new FlowException(ResultErrorCode.ALREADY_OUTGOING.getMsg(), ResultErrorCode.ALREADY_OUTGOING.getCode());
        }
        //2-删除订单数据
        baseMapper.deleteById(orderGuid);
        //删除2.0的数据
        outgoingOrderService.removeById(orderGuid);
        iProcessInstanceService.deleteProcessAndHisInst(orderGuid, WorkflowKeyEnum.ORDER_OUTGOING);
        //供应商被使用
        usedUtils.isUsed("erp_supplier_mgt_supplier", order.getCustomerOrSupplierGuid(), "supplier_guid");
        usedUtils.isUsed("erp_supplier_mgt_supplier", order.getSettlementCustomerOrSupplierGuid(), "supplier_guid");
        //3-删除订单文件数据
        LambdaQueryWrapper<ErpOutgoingBusinessMgtOrderFile> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(ErpOutgoingBusinessMgtOrderFile::getOrderGuid, orderGuid);
        fileMapper.delete(wrapper1);
        List<ErpOutgoingBusinessMgtOrderFile> fileList = fileMapper.selectList(wrapper1);
        fileList.forEach(erpOutgoingBusinessMgtOrderFile -> {
        });
        //4-删除订单数据表数据
        deleteSonData(wrapper, orderDataList);
        //5-删除订单收货地址数据
        QueryWrapper<ErpOutgoingBusinessMgtOrderPlaceOfDelivery> wrapper2 = new QueryWrapper<>();
        wrapper2.eq("order_guid", orderGuid);
        deliveryMapper.delete(wrapper2);
        //6-删除订单工单数据表的数据
        orderDataList.forEach(e -> {
            LambdaQueryWrapper<ErpProductionMgtWorkorderOrderData> wrapper3 = new LambdaQueryWrapper<>();
            wrapper3.eq(ErpProductionMgtWorkorderOrderData::getOrderDataGuid, e.getOrderDataGuid());
            orderRelationService.remove(wrapper3);
            outgoingOrderDetailService.removeById(e.getOrderDataGuid());
        });
        //处理上流程待开
        //pendingNumberService.updatePendingNumber(WorkflowKeyEnum.ORDER_OUTGOING, WorkflowKeyEnum.WORK_ORDER_OUTGOING
        //        , baseMapper.getWaitingQuantity());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSonData(QueryWrapper<ErpOutgoingBusinessMgtOrderData> wrapper, List<ErpOutgoingBusinessMgtOrderData> orderDataList) {
        //删除订单地址数据
        LambdaQueryWrapper<ErpOutgoingBusinessMgtOrderPlaceOfDelivery> place = new LambdaQueryWrapper<>();
        List<String> collect = orderDataList.stream().map(ErpOutgoingBusinessMgtOrderData::getOrderDataGuid).collect(Collectors.toList());
        collect.forEach(s -> {
            place.eq(ErpOutgoingBusinessMgtOrderPlaceOfDelivery::getOrderDataGuid, s);
            deliveryMapper.delete(place);
        });
        //4.1-判断是否有对应申请单的子工单产生
        List<String> collect1 = orderDataList.stream().map(ErpOutgoingBusinessMgtOrderData::getSourceGuid).collect(Collectors.toList());
        collect1.forEach(e -> {
            //修改外发申请的外发状态
            ErpProductionMgtWorkorder workorder1 = erpOutgoingMgtApplicationMapper.selectById(e);
            LambdaQueryWrapper<ErpProductionMgtWorkorder> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(ErpProductionMgtWorkorder::getParentClassificationGuid, workorder1.getParentClassificationGuid());
            BigDecimal all = erpOutgoingMgtApplicationMapper.selectList(wrapper1).stream()
                    .map(ErpProductionMgtWorkorder::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            ErpProductionMgtWorkorder workorder = erpOutgoingMgtApplicationMapper.selectById(workorder1.getParentClassificationGuid());
            int i = workorder.getTotalQuantity().compareTo(all);
            if (i <= 0) {
                workorder.setWorkorderState(ErpOutgoingApplicationStatusEnum.ALL_OUTGOING.getCode());
            } else {
                workorder.setWorkorderState(ErpOutgoingApplicationStatusEnum.PARTIALLY_OUTGOING.getCode());
            }
            erpOutgoingMgtApplicationMapper.updateById(workorder);
        });
        if (CollectionUtils.isNotEmpty(collect1)) {
            //4.2-恢复工序工单的已外发数量及状态和外发申请的数量
            List<WorkOrderProcessAndOutgoingApplicationVO> dataList = erpOutgoingDataProcessMapper.selectBySon(collect1);
            dataList.forEach(workOrderProcessAndOutgoingApplicationVO -> {
                ErpOutgoingDataProcess process = erpOutgoingDataProcessMapper.selectById(workOrderProcessAndOutgoingApplicationVO.getWorkOrderProcessGuid());
                ErpProductionMgtWorkorder workorder = erpOutgoingMgtApplicationMapper.selectById(process.getWorkorderGuid());
                //1.2-处理工序工单外发状态
                BigDecimal newOutsourcedRequisitionQuantity = process.getOutsourcedQuantity()
                        .subtract(workOrderProcessAndOutgoingApplicationVO.getSonOutgoingQuantity());
                int compare1 = newOutsourcedRequisitionQuantity.compareTo(workorder.getQuantity());
                if (compare1 > 0) {
                    process.setOutsourceState(ErpOutgoingStatusEnum.SURPLUS_OUTGOING.getCode());
                } else if (compare1 == 0) {
                    process.setOutsourceState(ErpOutgoingStatusEnum.ALL_OUTGOING.getCode());
                } else {
                    process.setOutsourceState(ErpOutgoingStatusEnum.PARTIALLY_OUTGOING.getCode());
                }
                int i1 = newOutsourcedRequisitionQuantity.compareTo(BigDecimal.ZERO);
                if (i1 == 0) {
                    process.setOutsourceState(ErpOutgoingStatusEnum.NOT_OUTGOING.getCode());
                }
                process.setOutsourcedQuantity(newOutsourcedRequisitionQuantity);
                //处理外发申请状态
                int compare = process.getOutsourcedQuantity()
                        .compareTo(process.getOutsourcedRequisitionQuantity());
                //如果外发申请数小于外发数
                if (compare > 0) {
                    int compare2 = newOutsourcedRequisitionQuantity.compareTo(process.getOutsourcedRequisitionQuantity());
                    if (compare2 >= 0) {
                        process.setOutsourceRequisitionState(ErpOutgoingApplicationStatusEnum.ALL_OUTGOING.getCode());
                    } else {
                        //判断申请数与工序加工数，修改外发申请状态
                        int i = process.getOutsourcedRequisitionQuantity().compareTo(workorder.getQuantity());
                        if (i >= 0) {
                            process.setOutsourceRequisitionState(ErpOutgoingApplicationStatusEnum.ALL_OUTGOING.getCode());
                        } else {
                            process.setOutsourceRequisitionState(ErpOutgoingApplicationStatusEnum.PARTIALLY_OUTGOING.getCode());
                        }
                    }
                }
                workOrderProcessAndOutgoingApplicationVO.setWorkOrderProcess(process);
            });
            List<ErpOutgoingDataProcess> collect2 = dataList.stream()
                    .map(WorkOrderProcessAndOutgoingApplicationVO::getWorkOrderProcess).collect(Collectors.toList());
            collect2.forEach(erpOutgoingDataProcess -> {
                erpOutgoingDataProcessMapper.updateById(erpOutgoingDataProcess);
            });
            //4.3-删除对应子工单的数据
            erpOutgoingMgtApplicationMapper.deleteBatchIds(collect1);
            orderDataMapper.delete(wrapper);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpOutgoingBusinessMgtOrderRO ro) {
        //1-查询订单数据判断订单属性是否是未完成并且审核状态为草稿
        ErpOutgoingBusinessMgtOrder order = baseMapper.selectById(ro.getOrderGuid());
        QueryWrapper<ErpOutgoingBusinessMgtOrderData> wrapper = new QueryWrapper<>();
        wrapper.eq("order_guid", ro.getOrderGuid());
        List<ErpOutgoingBusinessMgtOrderData> orderDataList = orderDataMapper.selectList(wrapper);
        List<ErpOutgoingBusinessMgtOrderData> collect = orderDataList.stream()
                .filter(erpOutgoingBusinessMgtOrderData -> erpOutgoingBusinessMgtOrderData.getCompletionStatus().equals(CompletionStatusEnum.NOT_FINISH.getCode())).collect(Collectors.toList());
        if ((CollectionUtils.isNotEmpty(collect) && collect.size() <= 0) || !order.getAuditStatus().equals(BusinessStatusEnum.DRAFT.getStatus())) {
            throw new FlowException(ResultErrorCode.ALREADY_OUTGOING.getMsg(), ResultErrorCode.ALREADY_OUTGOING.getCode());
        }
        //2-修改订单数据
        ErpOutgoingBusinessMgtOrder businessMgtOrder = BeanUtil.copyProperties(ro, ErpOutgoingBusinessMgtOrder.class);
        baseMapper.updateById(businessMgtOrder);
        XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();
        ThreadUtil.execAsync(() -> {
            userApi.setThreadLocal(memberDto);
            this.createOrder(businessMgtOrder, ro);
        });
        //3-删除订单文件数据,并插入新数据
        QueryWrapper<ErpOutgoingBusinessMgtOrderFile> wrapper1 = new QueryWrapper<>();
        wrapper1.eq("order_guid", ro.getOrderGuid());
        fileMapper.delete(wrapper1);
        if (CollectionUtils.isNotEmpty(ro.getFileGuid())) {
            ro.getFileGuid().forEach(fileGuidAndNameVO -> {
                ErpOutgoingBusinessMgtOrderFile file = new ErpOutgoingBusinessMgtOrderFile();
                file.setFileGuid(fileGuidAndNameVO.getFileGuid());
                file.setOrderGuid(ro.getOrderGuid());
                fileMapper.insert(file);
            });
        }
        //4-删除订单数据表数据,并插入新数据
        deleteSonData(wrapper, orderDataList);
        outgoingOrderDetailService.removeBatch(orderDataList.stream().map(
                s -> s.getOrderDataGuid()).collect(Collectors.toList())
        );
        insertOrderData(ro, true);
        //处理上流程待开
        //pendingNumberService.updatePendingNumber(WorkflowKeyEnum.ORDER_OUTGOING, WorkflowKeyEnum.WORK_ORDER_OUTGOING
        //        , baseMapper.getWaitingQuantity());
        return true;
    }

    @Override
    public ErpOutgoingBusinessMgtOrderVO getById(String orderGuid) {
        ErpOutgoingBusinessMgtOrderVO vo = baseMapper.getDataByGuid(orderGuid);
        return loadErpOutgoingBusinessMgtOrderVO(vo);

    }

    @NotNull
    private ErpOutgoingBusinessMgtOrderVO loadErpOutgoingBusinessMgtOrderVO(ErpOutgoingBusinessMgtOrderVO vo) {
        //根据订单id获取相关文件数据
        vo.getSpecificList().forEach(erpOutgoingOrderDataVO -> {
            erpOutgoingOrderDataVO.setProcessParameters(processesTypeMapper.getProcessParameters(erpOutgoingOrderDataVO.getWorkOrderProcessGuid()));
            String productName = erpOutgoingMgtApplicationMapper.selectproductName(erpOutgoingOrderDataVO.getWorkOrderProcessGuid());
            erpOutgoingOrderDataVO.setProductName(productName);
            String partName = erpOutgoingMgtApplicationMapper.selectPartName(erpOutgoingOrderDataVO.getWorkOrderProcessGuid());
            erpOutgoingOrderDataVO.setPartName(partName);
            erpOutgoingOrderDataVO.setSonList(erpOutgoingMgtApplicationMapper.getSonList(erpOutgoingOrderDataVO.getWorkOrderProcessGuid(), erpOutgoingOrderDataVO.getQuantity()));
        });
        return handleWarehousingSituation(vo);
    }

    /**
     * 处理完成状态
     *
     * @param vo
     * @return
     */
    public ErpOutgoingBusinessMgtOrderVO handleWarehousingSituation(ErpOutgoingBusinessMgtOrderVO vo) {
        List<FileGuidAndNameVO> collect1 = vo.getFileGuid().stream().filter(Objects::nonNull).collect(Collectors.toList());
        vo.setFileGuid(collect1);
        List<String> collect = vo.getSpecificList().stream().map(ErpOutgoingOrderDataVO::getCompletionStatus).collect(Collectors.toList());
        int sum = collect.stream().map(Integer::valueOf).mapToInt(Integer::intValue).sum();
        //计算入库情况阈值
        int size = collect.size();
        //终止情况的阈值
        int stopNumber = size * Integer.valueOf(CompletionStatusEnum.STOP_FINISH.getCode());
        //全部完成情况的阈值
        int finshNumber = size * Integer.valueOf(CompletionStatusEnum.ALL_FINISH.getCode());
        if (collect.contains(CompletionStatusEnum.PARTIALLY_FINISH.getCode())) {
            vo.setWarehousingSituation(CompletionStatusEnum.PARTIALLY_FINISH.getDesc());
        } else if (sum == stopNumber) {
            vo.setWarehousingSituation(CompletionStatusEnum.STOP_FINISH.getDesc());
        } else if (sum == finshNumber) {
            vo.setWarehousingSituation(CompletionStatusEnum.ALL_FINISH.getDesc());
        } else if (sum == Integer.valueOf(CompletionStatusEnum.NOT_FINISH.getCode())) {
            vo.setWarehousingSituation(CompletionStatusEnum.NOT_FINISH.getDesc());
        } else {
            vo.setWarehousingSituation(CompletionStatusEnum.PARTIALLY_FINISH.getDesc());
        }
        return vo;
    }

    @Override
    public List<ErpOutgoingBusinessMgtOrderVO> findList(ErpOutgoingBusinessMgtOrderQO qo) {
        if (StringUtils.isNotEmpty(qo.getKeyword())) {
            //根据关键字获取客户数据
            String[] keywords = qo.getKeyword().split(" ");
            List<String> list2 = Arrays.asList(keywords);
            qo.setKeywords(list2);
        }
        List<ErpOutgoingBusinessMgtOrderVO> voList = new ArrayList<>();
        List<String> workorderGuidS = baseMapper.selectOutgoing(qo);
        if (CollectionUtils.isEmpty(workorderGuidS)) {
            return voList;
        }
        List<String> collect = workorderGuidS.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            collect.forEach(s -> {
                ErpOutgoingBusinessMgtOrderVO vo = this.getById(s);
                voList.add(vo);
            });
        }
        return voList.stream()
                .distinct()
                .sorted(Comparator.comparing(ErpOutgoingBusinessMgtOrderVO::getCreateDate, LocalDateTime::compareTo).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public IPage<ErpOutgoingBusinessMgtOrderVO> findPage(PageParams<ErpOutgoingBusinessMgtOrderQO> pageParams) {
        IPage<ErpOutgoingBusinessMgtOrderVO> page = pageParams.buildPage();
        ErpOutgoingBusinessMgtOrderQO model = pageParams.getModel();
        if (StringUtils.isNotEmpty(model.getKeyword())) {
            //根据关键字获取客户数据
            String[] keywords = model.getKeyword().split(" ");
            List<String> list = Arrays.asList(keywords);
            model.setKeywords(list);
        }

        IPage<String> workorderGuidS = baseMapper.selectOutgoing(page, model);
        if (ObjUtil.isEmpty(workorderGuidS) || ObjUtil.isEmpty(workorderGuidS.getRecords())) {
            return page;
        }

        List<ErpOutgoingBusinessMgtOrderVO> erpOutgoingBusinessMgtOrderVOS = setDetailData(baseMapper.findDataByGuids(workorderGuidS.getRecords()));
        Map<String, ErpOutgoingBusinessMgtOrderVO> erpOutgotingBusinessMgtOrderVOMap = CollStreamUtil.toMap(erpOutgoingBusinessMgtOrderVOS, ErpOutgoingBusinessMgtOrderVO::getOrderGuid, Function.identity());

        return workorderGuidS.convert(erpOutgotingBusinessMgtOrderVOMap::get);
    }

    /**
     * 设置详细数据
     *
     * @param voList
     * @return
     */
    public List<ErpOutgoingBusinessMgtOrderVO> setDetailData(List<ErpOutgoingBusinessMgtOrderVO> voList) {
        if (CollUtil.isNotEmpty(voList)) {
            List<String> orderGuids = voList.stream().map(ErpOutgoingBusinessMgtOrderVO::getOrderGuid).distinct().collect(Collectors.toList());
            // 订单文件
            // List<FileGuidAndNameVO> orderFileGuids = baseMapper.getOrderFileGuids(orderGuids);
            List<FileGuidAndNameVO> fileListBatch = baseMapper.getFileListBatch(orderGuids);
            fileListBatch = fileListBatch.stream().filter(v -> Objects.nonNull(v.getFileGuid()))
                    .collect(Collectors.toList());

            List<ErpOutgoingOrderDataVO> orderDataListBatch = baseMapper.getOrderDataListBatch(orderGuids);

            List<String> materialGuids = orderDataListBatch.stream()
                    .map(ErpOutgoingOrderDataVO::getMaterialGuid)
                    .distinct().collect(Collectors.toList());


            List<String> workorderDataProcessGuids = orderDataListBatch.stream()
                    .map(ErpOutgoingOrderDataVO::getWorkOrderProcessGuid)
                    .distinct()
                    .collect(Collectors.toList());

            List<String> processWorkorderDataGuids = orderDataListBatch.stream()
                    .map(ErpOutgoingOrderDataVO::getProcessWorkOrderGuid)
                    .distinct()
                    .collect(Collectors.toList());

            // 获取工序参数
            List<ErpWorkorderDataProcessParameterVO> processParametersBatch = productionProcessesTypeService.getProcessParametersBatch(workorderDataProcessGuids);
            // 获取部件名称
            Map<String, String> partNamesMap = outgoingMgtApplicationService.selectPartNameBatch(workorderDataProcessGuids);
            // 获取产品名称
            Map<String, BaseMapContainer> productNamesMap = outgoingMgtApplicationService.selectproductNameBatchs(workorderDataProcessGuids);

            //通过工序工单id,获取产出工单集合
            List<ErpOutgoingApplicationVO> sonListBatch = erpOutgoingMgtApplicationMapper.getSonListBatch(processWorkorderDataGuids);
            // 获取物料用量
            List<ErpProductionMgtWorkorderMaterialUsageVO> materialUsageListBatch = erpOutgoingMgtApplicationMapper.getMaterialUsageListBatch(processWorkorderDataGuids);
            List<String> materialGuidMaterialUsageLists = materialUsageListBatch.stream().map(ErpProductionMgtWorkorderMaterialUsageVO::getMaterialGuid).distinct().collect(Collectors.toList());

            // 获取上工序产出
            List<ErpOutgoingApplicationVO> previousOutputWorkorderBatch = erpOutgoingMgtApplicationMapper.getPreviousOutputWorkorderBatch(processWorkorderDataGuids);
            List<String> materialPreviousOutputWorkorderLists = previousOutputWorkorderBatch.stream().map(ErpOutgoingApplicationVO::getMaterialGuid)
                    .distinct()
                    .collect(Collectors.toList());
            materialGuids.addAll(materialPreviousOutputWorkorderLists);

            materialGuids.addAll(materialGuidMaterialUsageLists);
            // 外发明细子集的物料
            List<String> materialGuidSonLists = sonListBatch.stream().map(ErpOutgoingApplicationVO::getMaterialGuid)
                    .distinct()
                    .collect(Collectors.toList());
            materialGuids.addAll(materialGuidSonLists);


            Map<String, ErpMaterialMgtMaterialVO> mapByIds = materialService.getMapByIds(materialGuids);

            orderDataListBatch.forEach(v -> {
                BaseMapContainer baseMapContainer = productNamesMap.get(v.getWorkOrderProcessGuid());
                if (ObjUtil.isNotEmpty(baseMapContainer)) {
                    // 产品名称
                    v.setProductName(baseMapContainer.getProcessDataName());
                    // 产品编码
                    v.setProductCode(baseMapContainer.getProcessDataCode());
                }
                // 部件名称
                v.setPartName(partNamesMap.get(v.getWorkOrderProcessGuid()));
                // 物料
                v.setMaterialObj(mapByIds.get(v.getMaterialGuid()));
                // 工序参数处理
                List<ErpWorkorderDataProcessParameterVO> processParameterVOS = processParametersBatch.stream()
                        .filter(parameter -> parameter.getWorkorderDataProcessGuid().equals(v.getWorkOrderProcessGuid()))
                        .collect(Collectors.toList());
                v.setProcessParameterVOS(processParameterVOS);
                // 拼接字符串
                v.setProcessParameters(productionProcessesTypeService.getStringJoint(processParameterVOS));

                // 明细的子集
                List<ErpOutgoingApplicationVO> sonList = sonListBatch.stream()
                        .filter(son -> son.getParentWorkOrderGuid().equals(v.getProcessWorkOrderGuid()))
                        .collect(Collectors.toList());
                sonList.forEach(s -> {
                    s.setOutputQuantity(s.getProportion().multiply(v.getTotalQuantity()));
                    s.setMaterialObj(mapByIds.get(s.getMaterialGuid()));
                });
                v.setSonList(sonList);

                // 明细的物料用量
                List<ErpProductionMgtWorkorderMaterialUsageVO> materialUsageList = materialUsageListBatch.stream()
                        .filter(usageVO -> usageVO.getWorkorderGuid().equals(v.getProcessWorkOrderGuid()))
                        .collect(Collectors.toList());
                materialUsageList.forEach(s -> {
                    s.setMaterialObj(mapByIds.get(s.getMaterialGuid()));
                });


                // 上工序产出
                List<ErpOutgoingApplicationVO> previousOutputWorkorders = previousOutputWorkorderBatch.stream()
                        .filter(son -> son.getProcessWorkorderGuid().equals(v.getProcessWorkOrderGuid()))
                        .collect(Collectors.toList());
                previousOutputWorkorders.forEach(s -> {
                    s.setMaterialObj(mapByIds.get(s.getMaterialGuid()));
                });
                v.setPreviousOutputWorkorderList(previousOutputWorkorders);
                v.setSonList(sonList);
                v.setWorkorderMaterialUsageList(materialUsageList);

            });

            // 处理其他费用
            // 加载其他费用数据
            otherExpensesService.loadBusinessMgtOtherExpenses(OtherExpensesEnum.OUTSOURCE_ORDER, orderDataListBatch
                    , ErpOutgoingOrderDataVO::getOrderDataGuid, ErpOutgoingOrderDataVO::setOtherExpensesList, null
            );

            List<FileGuidAndNameVO> finalFileListBatch = fileListBatch;
            voList.forEach(v -> {
                List<FileGuidAndNameVO> files = finalFileListBatch.stream()
                        .filter(u -> u.getOrderGuid().equals(v.getOrderGuid())).collect(Collectors.toList());
                v.setFileGuid(files);

                List<ErpOutgoingOrderDataVO> orderDataVOList = orderDataListBatch.stream()
                        .filter(u -> u.getOrderGuid().equals(v.getOrderGuid())).collect(Collectors.toList());

                v.setSpecificList(orderDataVOList);

                handleWarehousingSituation(v);
            });

            return voList;
        }

        return null;
    }

    @Override
    public Boolean removeIds(List<String> ids) {
        ids.forEach(this::delete);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveData(InsertOrUpdateList<ErpOutgoingBusinessMgtOrderRO> list) {
        List<String> guids = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list.getInsertList())) {
            list.getInsertList().forEach(v -> {
                String guid = create(v);
                guids.add(guid);
            });
        }
        if (CollectionUtils.isNotEmpty(list.getUpdateList())) {
            list.getUpdateList().forEach(
                    this::update);
        }
        return guids;
    }

    /**
     * 加工商预付明细列表查询
     *
     * @param qo
     * @return
     */
    @Override
    public List<ErpProcessOutgoingOrderVO> processFindList(ErpPurchaseProcessOutgoingOrderQO qo) {
        List<ErpProcessOutgoingOrderVO> list = baseMapper.processFindList(qo);
        List<ErpProcessOutgoingOrderVO> collect = list.stream().filter(erpProcessOutgoingOrderVO -> Objects
                .nonNull(erpProcessOutgoingOrderVO) && StringUtils.isEmpty(erpProcessOutgoingOrderVO
                .getAdvancePaymentDetailGuid())).collect(Collectors.toList());
        collect.forEach(erpProcessOutgoingOrderVO -> {
            erpProcessOutgoingOrderVO.setSourceValue(1);
            erpProcessOutgoingOrderVO.setProcessParameters(processesTypeMapper
                    .getProcessParameters(erpProcessOutgoingOrderVO.getWorkOrderProcessGuid()));
        });
        return collect;
    }

    /**
     * 外发到货单待开列表查询
     *
     * @param qo
     * @return
     */
    @Override
    public List<ErpPurchaseInventoryWorkorderDetailVO> findNotBilledList(ErpPurchaseMgtPurchaseOrderQO qo) {
        List<ErpPurchaseInventoryWorkorderDetailVO> collect = baseMapper.findNotBilledList(qo).stream().distinct().collect(Collectors.toList());
        LoadDataUtils.loadDataObjTool(collect,
                this::getOutgoingOrderVOBatch,
                ErpOutgoingArrivalVO::getOrderDataGuid,
                ErpPurchaseInventoryWorkorderDetailVO::getSourceGuid,
                ErpPurchaseInventoryWorkorderDetailVO::setOutgoingOrderVO,
                null);
        LoadDataUtils.loadDataObjTool(collect, materialService::getByIds,
                ErpMaterialMgtMaterialVO::getMaterialGuid,
                ErpPurchaseInventoryWorkorderDetailVO::getMaterialGuid,
                ErpPurchaseInventoryWorkorderDetailVO::setMaterialObj,
                null);
        collect.forEach(s -> {
            ErpOutgoingArrivalVO outgoingOrderVO = s.getOutgoingOrderVO();
            if (CollectionUtils.isNotEmpty(outgoingOrderVO.getSonList())) {
                List<ErpOutgoingApplicationVO> sonList = outgoingOrderVO.getSonList();
                BigDecimal total = sonList.stream().map(ErpOutgoingApplicationVO::getOutputQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                sonList.forEach(e -> {
                    e.setTotalAmountIncludingTax(s.getTotalAmountIncludingTax().multiply(e.getOutputQuantity().divide(total, 2, RoundingMode.HALF_UP)));
                    e.setQuotationUnitPriceIncludingTax(e.getTotalAmountIncludingTax().divide(e.getOutputQuantity(), 9, RoundingMode.HALF_UP));
                    e.setUnitPriceIncludingTax(e.getQuotationUnitPriceIncludingTax());
                    e.setTotalAmountWithoutTax(s.getTotalAmountIncludingTax().multiply(e.getOutputQuantity().divide(total, 2, RoundingMode.HALF_UP)));
                    e.setQuotationUnitPriceWithoutTax(e.getTotalAmountWithoutTax().divide(e.getOutputQuantity(), 9, RoundingMode.HALF_UP));
                    e.setUnitPriceWithoutTax(e.getQuotationUnitPriceWithoutTax());
                });
                outgoingOrderVO.setSonList(sonList);
            }
            s.setOutgoingOrderVO(outgoingOrderVO);
        });
        // 加载其他费用数据
        otherExpensesService.loadBusinessMgtOtherExpenses(OtherExpensesEnum.OUTSOURCE_ORDER, collect
                , ErpPurchaseInventoryWorkorderDetailVO::getSourceGuid, ErpPurchaseInventoryWorkorderDetailVO::setOtherExpensesList, null
        );
        return collect;
    }

    /**
     * 外发到货单待开列表查询
     *
     * @param qo
     * @return
     */
    @Override
    public List<ErpPurchaseInventoryWorkorderVO> findNotBilledTree(ErpPurchaseMgtPurchaseOrderQO qo) {
        List<ErpPurchaseInventoryWorkorderVO> resultList = new ArrayList<>();
        // 待开列表（明细）
        List<ErpPurchaseInventoryWorkorderDetailVO> notBilledList = baseMapper.findNotBilledList(qo).stream().distinct().collect(Collectors.toList());
        LoadDataUtils.loadDataObjTool(notBilledList,
                this::getOutgoingOrderVOBatch,
                ErpOutgoingArrivalVO::getOrderDataGuid,
                ErpPurchaseInventoryWorkorderDetailVO::getSourceGuid,
                ErpPurchaseInventoryWorkorderDetailVO::setOutgoingOrderVO,
                null);
        LoadDataUtils.loadDataObjTool(notBilledList, materialService::getByIds,
                ErpMaterialMgtMaterialVO::getMaterialGuid,
                ErpPurchaseInventoryWorkorderDetailVO::getMaterialGuid,
                ErpPurchaseInventoryWorkorderDetailVO::setMaterialObj,
                null);
        // 加载其他费用数据
        otherExpensesService.loadBusinessMgtOtherExpenses(OtherExpensesEnum.OUTSOURCE_ORDER, notBilledList
                , ErpPurchaseInventoryWorkorderDetailVO::getSourceGuid, ErpPurchaseInventoryWorkorderDetailVO::setOtherExpensesList, null
        );

        if (CollectionUtils.isNotEmpty(notBilledList)) {
            notBilledList.forEach(s -> {
                ErpOutgoingArrivalVO outgoingOrderVO = s.getOutgoingOrderVO();
                if (CollectionUtils.isNotEmpty(outgoingOrderVO.getSonList())) {
                    List<ErpOutgoingApplicationVO> sonList = outgoingOrderVO.getSonList();
                    BigDecimal total = sonList.stream().map(ErpOutgoingApplicationVO::getOutputQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    sonList.forEach(e -> {
                        e.setTotalAmountIncludingTax(s.getTotalAmountIncludingTax().multiply(e.getOutputQuantity().divide(total, 2, RoundingMode.HALF_UP)));
                        e.setQuotationUnitPriceIncludingTax(e.getTotalAmountIncludingTax().divide(e.getOutputQuantity(), 9, RoundingMode.HALF_UP));
                        e.setUnitPriceIncludingTax(e.getQuotationUnitPriceIncludingTax());
                        e.setTotalAmountWithoutTax(s.getTotalAmountIncludingTax().multiply(e.getOutputQuantity().divide(total, 2, RoundingMode.HALF_UP)));
                        e.setQuotationUnitPriceWithoutTax(e.getTotalAmountWithoutTax().divide(e.getOutputQuantity(), 9, RoundingMode.HALF_UP));
                        e.setUnitPriceWithoutTax(e.getQuotationUnitPriceWithoutTax());
                    });
                    outgoingOrderVO.setSonList(sonList);
                }
                s.setOutgoingOrderVO(outgoingOrderVO);
                // 这里随机一下guid，给前端用
                s.setCustomerGuid(IdUtil.fastSimpleUUID());
            });
            // 数据分组，按客户名称和结算客户名称
            Map<String, List<ErpPurchaseInventoryWorkorderDetailVO>> groupData = notBilledList
                    .stream()
                    .collect(Collectors.groupingBy(s -> s.getParentCustomerGuid() + "," + s.getSettlementCustomerOrSupplierGuid()));

            // 组装前端所需数据
            List<ErpPurchaseInventoryWorkorderVO> finalResultList = resultList;
            groupData.keySet().forEach(key -> {
                // 取出分组数据
                List<ErpPurchaseInventoryWorkorderDetailVO> detailData = groupData.get(key);
                // 取出第一条数据，后续数据的CustomerGuid和SettlementCustomerOrSupplierGuid是一致的
                // 处理交货日期，需要按交货日期排序
                ErpPurchaseInventoryWorkorderDetailVO row1 = detailData.stream()
                        .max(Comparator.comparing(ErpPurchaseInventoryWorkorderDetailVO::getRequiredDeliveryTime))
                        .get();

                ErpPurchaseInventoryWorkorderVO vo = new ErpPurchaseInventoryWorkorderVO();
                vo.setCustomerGuid(row1.getParentCustomerGuid());
                vo.setSettlementCustomerOrSupplierGuid(row1.getSettlementCustomerOrSupplierGuid());
                vo.setCustomerOrSupplierName(row1.getCustomerOrSupplierName());
                vo.setSettlementCustomerOrSupplierName(row1.getSettlementCustomerOrSupplierName());
                vo.setRequiredDeliveryTime(row1.getRequiredDeliveryTime());
                vo.setDetailList(detailData);

                finalResultList.add(vo);
            });

            // 按交货日期进行排序
            resultList = finalResultList.stream()
                    .sorted(Comparator.comparing(ErpPurchaseInventoryWorkorderVO::getRequiredDeliveryTime))
                    .collect(Collectors.toList());

            // 获取父guid列表
//            if (!org.springframework.util.CollectionUtils.isEmpty(notBilledList)) {
//                Set<ErpPurchaseInventoryWorkorderVO> list = new HashSet<>();
//                notBilledList.forEach(s -> {
//                    ErpPurchaseInventoryWorkorderVO vo = new ErpPurchaseInventoryWorkorderVO();
//                    vo.setCustomerGuid(s.getParentCustomerGuid());
//                    vo.setSettlementCustomerOrSupplierGuid(s.getSettlementCustomerOrSupplierGuid());
//                    list.add(vo);
//                });
//                list.forEach(l -> {
//                    ErpPurchaseInventoryWorkorderVO vo = new ErpPurchaseInventoryWorkorderVO();
//                    vo.setCustomerGuid(l.getCustomerGuid());
//                    List<ErpPurchaseInventoryWorkorderDetailVO> collect = notBilledList.stream().filter(s ->
//                                    s.getParentCustomerGuid().equals(l.getCustomerGuid()) && s.getSettlementCustomerOrSupplierGuid()
//                                            .equals(l.getSettlementCustomerOrSupplierGuid()))
//                            .distinct().peek(c ->
//                                    c.setCustomerGuid(String.valueOf(UUID.randomUUID()))).collect(Collectors.toList());
//                    vo.setDetailList(collect);
//                    vo.setCustomerOrSupplierName(supplierService.getById(l.getCustomerGuid()).getSupplierShortName());
//                    vo.setSettlementCustomerOrSupplierGuid(l.getSettlementCustomerOrSupplierGuid());
//                    vo.setSettlementCustomerOrSupplierName(supplierService.getById(l.getSettlementCustomerOrSupplierGuid()).getSupplierShortName());
//                    resultList.add(vo);
//                });
//            }
        }
        return resultList;
    }

    /**
     * 终止单据
     *
     * @param list
     * @return
     */
    @Override
    public Boolean stopOrRollback(List<ErpOutgoingDetaiStopRO> list) {
        list.forEach(s -> {
            ErpOutgoingBusinessMgtOrderData detail = orderDataMapper.selectById(s.getOrderDataGuid());
            detail.setOrderState(s.getCurrentStatus());
            orderDataMapper.updateById(detail);
        });
        return true;
    }

    /**
     * 获取顶级来源备注和图片
     *
     * @param sourceNumbers
     * @return
     */
    @Override
    public List<SourceDescriptionAndPictureVO> getSourceDescriptionAndPicture(List<String> sourceNumbers) {
        return baseMapper.getSourceDescriptionAndPicture(sourceNumbers);
    }

    /**
     * 判断是否符合操作流程的条件
     *
     * @param processVO
     */
    private void operationCheck(ActEventEntity processVO) {
        ErpOutgoingBusinessMgtOrder order = baseMapper.selectById(processVO.getBusinessKey());
        QueryWrapper<ErpOutgoingBusinessMgtOrderData> wrapper = new QueryWrapper<>();
        wrapper.eq("order_guid", processVO.getBusinessKey());
        List<ErpOutgoingBusinessMgtOrderData> orderDataList = orderDataMapper.selectList(wrapper);
        List<ErpOutgoingBusinessMgtOrderData> collect = orderDataList.stream()
                .filter(erpOutgoingBusinessMgtOrderData -> erpOutgoingBusinessMgtOrderData.getCompletionStatus().equals(CompletionStatusEnum.NOT_FINISH.getCode())).collect(Collectors.toList());
        if ((CollectionUtils.isNotEmpty(collect) && collect.size() <= 0) || !order.getAuditStatus().equals(BusinessStatusEnum.DRAFT.getStatus())) {
            throw new FlowException(ResultErrorCode.ALREADY_OUTGOING.getMsg(), ResultErrorCode.ALREADY_OUTGOING.getCode());
        }
    }

    /**
     * 重启流程时事件
     *
     * @param processVO
     */
    @Override
    public void restartCall(ActEventEntity processVO) {
        // 是否存在已开外发到货单
        List<String> outgoingArrivalNumbers = baseMapper.selectOutgoingArrivalNumbers(processVO.getBusinessKey());
        if (CollectionUtil.isNotEmpty(outgoingArrivalNumbers)) {
            throw new FlowException(ResultErrorCode.CANNOT_RESTART_THE_PROCESS_HAS_OUTGOING_ARRIVAL, StringUtils.join(outgoingArrivalNumbers));
        }

        // 删除到货单
        ErpProductionMgtWorkorder workorder = workorderService.getOne(Wrappers
                .<ErpProductionMgtWorkorder>lambdaQuery()
                .eq(ErpProductionMgtWorkorder::getWorkorderProperties, WorkorderPropertiesEnum.MATERIAL_DELIVERY_FATHER.getCode())
                .eq(ErpProductionMgtWorkorder::getSourceValue, DeliveryOrderDataSourceEnum.OUTGOING_ORDER.getCode())
                .eq(ErpProductionMgtWorkorder::getSourceGuid, processVO.getBusinessKey()));
        assert workorder != null;
        // 获取到货单工单guids
        List<String> workorderGuids = workorderService.getBaseMapper().selectList(Wrappers.<ErpProductionMgtWorkorder>lambdaQuery()
                        .eq(ErpProductionMgtWorkorder::getWorkorderProperties, WorkorderPropertiesEnum.MATERIAL_DELIVERY_FATHER.getCode())
                        .eq(ErpProductionMgtWorkorder::getSourceValue, DeliveryOrderDataSourceEnum.OUTGOING_ORDER.getCode())
                        .eq(ErpProductionMgtWorkorder::getParentClassificationGuid, workorder.getWorkorderGuid()))
                .stream().map(ErpProductionMgtWorkorder::getWorkorderGuid).collect(Collectors.toList());
        workorderGuids.add(workorder.getWorkorderGuid());
        // 删除到货工单
        workorderService.removeBatchByIds(workorderGuids);
        //审核完成以后，外发到货，运输计划，凭证管理的待开数减一
        pendingNumberService.minusOne(WorkflowKeyEnum.WORK_ORDER_OUTGOING_ARRIVAL, WorkflowKeyEnum.ORDER_OUTGOING);
        pendingNumberService.minusOne(WorkflowKeyEnum.TRANSPORTATION_PLAN_WORK_ORDER, WorkflowKeyEnum.ORDER_OUTGOING);
        pendingNumberService.minusOne(WorkflowKeyEnum.VOUCHER_MANAGEMENT, WorkflowKeyEnum.ORDER_OUTGOING);
    }

    @Override
    public List<ErpOutgoingArrivalVO> getOutgoingOrderVOBatch(List<String> sourceGuids) {
        List<ErpOutgoingArrivalVO> outgoingOrderVOBatch = baseMapper.getOutgoingOrderVOBatch(sourceGuids);
        LoadDataUtils.loadDataListTool(outgoingOrderVOBatch,
                outgoingMgtApplicationService::getSonListBatch,
                ErpOutgoingApplicationVO::getParentWorkOrderGuid,
                ErpOutgoingArrivalVO::getWorkOrderProcessGuid,
                ErpOutgoingArrivalVO::setSonList,
                null);
        outgoingOrderVOBatch.forEach(v -> {
            if (CollUtil.isNotEmpty(v.getSonList())) {
                v.getSonList().forEach(u -> {
                    u.setOutputQuantity(v.getQuantity().multiply(u.getProportion()));
                });
            }
        });
        return outgoingOrderVOBatch;
    }

    /**
     * 双写
     *
     * @param entity
     * @param ro
     */
    public void createOrder(ErpOutgoingBusinessMgtOrder entity, ErpOutgoingBusinessMgtOrderRO ro) {
        log.info("开始双写外发订单主表数据:{}", JSONUtil.toJsonStr(entity));
        OutgoingOrderDTO orderDTO = new OutgoingOrderDTO();
        orderDTO.setOutgoingOrderId(entity.getOrderGuid());
        orderDTO.setOutgoingOrderClazz(ClazzEnum.OUTGOING_ORDER.name());
        orderDTO.setOutgoingOrderIsolation(entity.getTenantGuid());
        orderDTO.setOutgoingOrderTenantId(entity.getTenantGuid());
        orderDTO.setOutgoingOrderUserCode(entity.getOrderNumber());
        orderDTO.setOutgoingOrderSupId(entity.getCustomerOrSupplierGuid());
        orderDTO.setOutgoingOrderSettleSupId(entity.getSettlementCustomerOrSupplierGuid());
        orderDTO.setOutgoingOrderDelvSup(entity.getDeliveryCustomerOrSupplierGuid());
        if (null != entity.getInvoiceTypeTaxRateGuid()) {
            ErpBasicMgtInvoiceTypeTaxRateVO rateData = rateService.getById(entity.getInvoiceTypeTaxRateGuid());
            orderDTO.setOutgoingOrderTaxRt(null != rateData ? rateData.getTaxRate() : null);
        }
        if(null!=entity.getCurrencyExchangeRateGuid()){
            ErpBasicMgtCurrencyExchangeRateVO currencyExchangeRateVO = currencyExchangeRateService.getById(entity.getCurrencyExchangeRateGuid());
            orderDTO.setOutgoingOrderCurRate(null != currencyExchangeRateVO ? currencyExchangeRateVO.getExchangeRate() : null);
        }
        outgoingOrderService.saveOrUpdate(orderDTO);
    }

    /**
     * 外发订单双写
     *
     * @param entityList
     * @param orderGuid
     */
    public void createOrderDetail(List<ErpOutgoingBusinessMgtOrderData> entityList, String orderGuid) {

        if (CollectionUtil.isNotEmpty(entityList)) {
            //根据采购预测的物料id获取单位数据
            List<String> materialGuids = entityList.stream().map(ErpOutgoingBusinessMgtOrderData::getMaterialGuid).distinct().collect(Collectors.toList());
            Map<String, ErpMaterialMgtMaterial> materialMap = new HashMap<>();
            if (CollUtil.isNotEmpty(materialGuids)) {
                List<ErpMaterialMgtMaterial> materialList = materialService.listByIds(materialGuids);
                materialMap = materialList.stream().collect(Collectors.toMap(ErpMaterialMgtMaterial::getMaterialGuid, material -> material));
            }
            //批量删除单位数量关联表
            List<String> parentIds = entityList.stream().map(ErpOutgoingBusinessMgtOrderData::getOrderDataGuid).distinct().collect(Collectors.toList());
            ProofUnitQuantityDTO quantityDTO = new ProofUnitQuantityDTO();
            quantityDTO.setProofUnitQuantityParentIds(parentIds);
            List<ProofUnitQuantityDTO> quantityDTOList = proofUnitQuantityService.findList(quantityDTO);
            if (CollectionUtil.isNotEmpty(quantityDTOList)) {
                proofUnitQuantityService.removeByIds(quantityDTOList.stream().map(ProofUnitQuantityDTO::getProofUnitQuantityId).collect(Collectors.toList()));
            }
            List<ProofUnitQuantityDTO> proofUnitQuantityList = new ArrayList<>();
            List<LinkDTO> linkDTOS = new ArrayList<>();
            List<OutgoingOrderDetailDTO> detailDTOList = new ArrayList<>();
            for (ErpOutgoingBusinessMgtOrderData orderData : entityList) {
                OutgoingOrderDetailDTO orderDetailDTO = XyBeanUtil.copyPropertiesByGst(orderData, OutgoingOrderDetailDTO.class);
                orderDetailDTO.setOutgoingOrderDetailParentId(orderGuid);
                orderDetailDTO.setOutgoingOrderDetailId(orderData.getOrderDataGuid());
                orderDetailDTO.setOutgoingOrderDetailClazz(ClazzEnum.OUTGOING_ORDER_DETAIL.name());
                orderDetailDTO.setOutgoingOrderDetailIsolation(orderData.getTenantGuid());
                orderDetailDTO.setOutgoingOrderDetailTenantId(orderData.getTenantGuid());
                orderDetailDTO.setOutgoingOrderDetailMaterialId(orderData.getMaterialGuid());
                orderDetailDTO.setOutgoingOrderDetailDeliveryDate(ObjectUtil.isNotEmpty(orderData.getDeliveryDate()) ? Date.from(orderData.getDeliveryDate().atZone(ZoneId.systemDefault()).toInstant()) : null);
                orderDetailDTO.setOutgoingOrderDetailExpProcessId(orderData.getExperienceProductionProcessGuid());
                orderDetailDTO.setOutgoingOrderDetailGiftQty(orderData.getGiftQuantity());
                orderDetailDTO.setOutgoingOrderDetailSpareQty(orderData.getSpareQuantity());
                orderDetailDTO.setOutgoingOrderDetailUsedQty(orderData.getUsingInventoryQuantity());
                orderDetailDTO.setOutgoingOrderDetailTaxInclPrice(orderData.getQuotationUnitPriceIncludingTax());
                orderDetailDTO.setOutgoingOrderDetailUnitPriceTax(orderData.getUnitPriceIncludingTax());
                orderDetailDTO.setOutgoingOrderDetailTaxInclTotal(orderData.getTotalAmountIncludingTax());
                orderDetailDTO.setOutgoingOrderDetailTaxExclPrice(orderData.getQuotationUnitPriceWithoutTax());
                orderDetailDTO.setOutgoingOrderDetailTaxExclUnitPrice(orderData.getUnitPriceWithoutTax());
                orderDetailDTO.setOutgoingOrderDetailTaxExclTotal(orderData.getTotalAmountWithoutTax());
                orderDetailDTO.setOutgoingOrderDetailLocUnitPrice(orderData.getLocalCurrencyUnitPrice());
                orderDetailDTO.setOutgoingOrderDetailLocalAmount(orderData.getLocalCurrencyTotalAmount());
                orderDetailDTO.setOutgoingOrderDetailSettlePrice(orderData.getSettlementUnitPrice());
                orderDetailDTO.setOutgoingOrderDetailSettleAmount(orderData.getSettlementTotalAmount());
                //入库实例
                orderDetailDTO.setOutgoingOrderDetailQuantity(orderData.getQuantity());
                orderDetailDTO.setOutgoingOrderDetailTenantId(orderData.getTenantGuid());
                orderDetailDTO.setOutgoingOrderDetailId(orderData.getOrderDataGuid());
                orderDetailDTO.setOutgoingOrderDetailUnitCode(Optional.ofNullable(materialMap)
                        .map(map -> map.get(orderData.getMaterialGuid()))
                        .map(ErpMaterialMgtMaterial::getUnitGuid)
                        .orElse(""));
                detailDTOList.add(orderDetailDTO);
                LinkDTO linkDTO = new LinkDTO();
                linkDTO.setLinkTarget(orderData.getOrderDataGuid());
                linkDTO.setLinkSource(orderData.getSourceGuid());
                linkDTO.setLinkTypeCode(TypeEnum.SOURCE.getCode());
                linkDTO.setLinkId(IdWorker.getIdStr());
                linkDTO.setLinkClazz(ClazzEnum.OUT_APPLY_DET.name()+ StringPool.DASH+ClazzEnum.OUTGOING_ORDER_DETAIL.name());
                linkDTO.setLinkIsolation(orderData.getTenantGuid());
                linkDTOS.add(linkDTO);
                //根据物料id获取单位id&&保存单据单位数量关联表
                ProofUnitQuantityDTO unitQuantityDTO = new ProofUnitQuantityDTO();
                unitQuantityDTO.setProofUnitQuantityQuantity(orderData.getQuantity());
                unitQuantityDTO.setProofUnitQuantityId(IdWorker.getIdStr());
                unitQuantityDTO.setProofUnitQuantityUnitId(orderDetailDTO.getOutgoingOrderDetailUnitCode());
                unitQuantityDTO.setProofUnitQuantityParentId(orderData.getOrderDataGuid());
                unitQuantityDTO.setProofUnitQuantityClazz(ClazzEnum.PROOF_UNIT_QUANTITY.name());
                proofUnitQuantityList.add(unitQuantityDTO);
            }

            //无来源不进行保存link关系
            if (CollUtil.isNotEmpty(linkDTOS)) {
                linkService.saveOrUpdateBatch(linkDTOS);
            }
            outgoingOrderDetailService.saveBatch(detailDTOList);
            proofUnitQuantityService.saveOrUpdateBatch(proofUnitQuantityList);
        }


    }
}
