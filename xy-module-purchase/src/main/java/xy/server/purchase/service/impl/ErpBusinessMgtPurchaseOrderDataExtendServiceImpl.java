package xy.server.purchase.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import xy.server.purchase.entity.ErpBusinessMgtPurchaseOrderDataExtend;
import xy.server.purchase.mapper.ErpBusinessMgtPurchaseOrderDataExtendMapper;
import xy.server.purchase.service.IErpBusinessMgtPurchaseOrderDataExtendService;

/**
 * <p>
 * 订单数据异常处理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Service
public class ErpBusinessMgtPurchaseOrderDataExtendServiceImpl extends ServiceImpl<ErpBusinessMgtPurchaseOrderDataExtendMapper, ErpBusinessMgtPurchaseOrderDataExtend> implements IErpBusinessMgtPurchaseOrderDataExtendService {

}
