package xy.server.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.config.util.PageParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.purchase.entity.ErpOutgoingDataProcess;
import xy.server.purchase.entity.model.qo.ErpOutgoingDataProcessQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingDataProcessRO;
import xy.server.purchase.entity.model.vo.ErpOutgoingDataProcessVO;
import xy.server.purchase.mapper.ErpOutgoingDataProcessMapper;
import xy.server.purchase.service.IErpOutgoingDataProcessService;

import java.util.List;

/**
 * <p>
 * 工单数据表工序工单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
@Service
public class ErpOutgoingDataProcessServiceImpl extends ServiceImpl<ErpOutgoingDataProcessMapper, ErpOutgoingDataProcess> implements IErpOutgoingDataProcessService {
                    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpOutgoingDataProcessRO ro){
        ErpOutgoingDataProcess entity = new ErpOutgoingDataProcess();
        BeanUtil.copyProperties(ro, entity);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String workorderDataProcessGuid){
        return super.removeById(workorderDataProcessGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpOutgoingDataProcessRO ro){
        ErpOutgoingDataProcess entity = new ErpOutgoingDataProcess();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    public ErpOutgoingDataProcessVO getById(String workorderDataProcessGuid){

        return baseMapper.getDataByGuid(workorderDataProcessGuid);
    }

    @Override
    public List<ErpOutgoingDataProcessVO> findList(ErpOutgoingDataProcessQO qo, String tenantGuid){
        qo.setTenantGuid(tenantGuid);
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpOutgoingDataProcessVO> findPage(PageParams<ErpOutgoingDataProcessQO> pageParams){
        IPage<ErpOutgoingDataProcessVO> page = pageParams.buildPage();
        ErpOutgoingDataProcessQO model = pageParams.getModel();
        model.setTenantGuid(pageParams.getTenantGuid());
        return baseMapper.findPage(page, model);
    }
}
