package xy.server.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.basic.entity.SupplierLevel;
import com.xunyue.basic.entity.model.dto.CustomerLevelDTO;
import com.xunyue.basic.entity.model.dto.SupplierLevelDTO;
import com.xunyue.basic.service.ISupplierLevelService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.common.i18n.BaseResultErrorCodeImpl;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import com.xunyue.node.common.enums.ClazzEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import xy.server.purchase.entity.ErpSupplierMgtSupplierLevel;
import xy.server.purchase.entity.model.qo.ErpSupplierMgtSupplierLevelQO;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierLevelRO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierLevelVO;
import xy.server.purchase.i18n.ResultErrorCode;
import xy.server.purchase.mapper.ErpSupplierMgtSupplierLevelMapper;
import xy.server.purchase.service.IErpSupplierMgtSupplierLevelService;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商级别 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Service
public class ErpSupplierMgtSupplierLevelServiceImpl extends ServiceImpl<ErpSupplierMgtSupplierLevelMapper, ErpSupplierMgtSupplierLevel> implements IErpSupplierMgtSupplierLevelService {

    @Resource
    private ISupplierLevelService supplierLevelService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpSupplierMgtSupplierLevelRO ro) {
        this.saveOrUpdateVerify(ro);

        ErpSupplierMgtSupplierLevel entity = new ErpSupplierMgtSupplierLevel();
        BeanUtil.copyProperties(ro, entity);
        super.save(entity);

        // 双写供应商级别
        SupplierLevelDTO supplierLevelDTO = buildSupplierLevelDTO(entity);
        supplierLevelService.saveOrUpdateBatch(Collections.singletonList(supplierLevelDTO));
        return true;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String supplierLevelGuid) {
        ErpSupplierMgtSupplierLevel entity = baseMapper.selectById(supplierLevelGuid);
        // 数据不存在
        if (Objects.isNull(entity)) {
            throw new FlowException(BaseResultErrorCodeImpl.NO_REPETITION_DATA);
        }
        // 数据在使用，不能删除
        if (entity.getIsUsed()) {
            throw new FlowException(ResultErrorCode.DELETE_IS_USED);
        }
        // 双写删除
        supplierLevelService.removeById(supplierLevelGuid);
        return this.removeAll(supplierLevelGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByBatch(List<String> ids) {
        for (String id : ids) {
            ErpSupplierMgtSupplierLevel entity = baseMapper.selectById(id);
            // 数据不存在
            if (Objects.isNull(entity)) {
                throw new FlowException(BaseResultErrorCodeImpl.NO_REPETITION_DATA);
            }
            // 数据在使用，不能删除
            if (entity.getIsUsed()) {
                throw new FlowException(ResultErrorCode.DELETE_BY_BATCH_IS_USED);
            }
        }
        // 双写删除
        supplierLevelService.removeBatchByIds(ids);
        return super.removeBatchByIds(ids);
    }

    /**
     * 判断删除自身及其子级
     *
     * @param pGuid
     * @return
     */
    private boolean removeAll(String pGuid) {
        List<ErpSupplierMgtSupplierLevel> childrenList = baseMapper.getListByParentClassificationGuid(pGuid);
        // 过滤掉【已被使用】的数据
        List<String> ids = childrenList.stream().filter(entity -> !entity.getIsUsed())
                .map(vo -> vo.getSupplierLevelGuid())
                .collect(Collectors.toList());
        if (ids.size() != childrenList.size()) {
            throw new FlowException(ResultErrorCode.DELETE_CHILDEREN_IS_USED);
        }

        // 双写删除
        supplierLevelService.removeBatchByIds(ids);

        return super.removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpSupplierMgtSupplierLevelRO ro) {
        this.saveOrUpdateVerify(ro);

        ErpSupplierMgtSupplierLevel entity = new ErpSupplierMgtSupplierLevel();
        BeanUtil.copyProperties(ro, entity);
        super.updateById(entity);

        // 双写供应商级别
        SupplierLevelDTO supplierLevelDTO = buildSupplierLevelDTO(entity);
        supplierLevelService.saveOrUpdateBatch(Collections.singletonList(supplierLevelDTO));

        return true;
    }

    /**
     * 构建双写数据
     *
     * @param entity
     * @return
     */
    private SupplierLevelDTO buildSupplierLevelDTO(ErpSupplierMgtSupplierLevel entity) {
        SupplierLevelDTO supplierLevelDTO = new SupplierLevelDTO();
        supplierLevelDTO.setSupplierLevelId(entity.getSupplierLevelGuid());
        supplierLevelDTO.setSupplierLevelName(entity.getSupplierLevelName());
        if (StrUtil.isEmpty(entity.getParentClassificationGuid())) {
            supplierLevelDTO.setSupplierLevelParentId(null);
        } else {
            supplierLevelDTO.setSupplierLevelParentId(entity.getParentClassificationGuid());
        }
        supplierLevelDTO.setSupplierLevelClazz(ClazzEnum.SUPPLIER_LEVEL.name());
        supplierLevelDTO.setSupplierLevelTenantId(entity.getTenantGuid());
        supplierLevelDTO.setSupplierLevelDescription(entity.getDescription());
        if (ObjUtil.isNotNull(entity.getState())) {
            supplierLevelDTO.setSupplierLevelState(entity.getState() ? 1 : 0);
        }
        supplierLevelDTO.setSupplierLevelTenantId(entity.getTenantGuid());
        return supplierLevelDTO;
    }

    @Override
    public ErpSupplierMgtSupplierLevelVO getById(String supplierLevelGuid) {

        return baseMapper.getDataByGuid(supplierLevelGuid);
    }

    @Override
    public List<ErpSupplierMgtSupplierLevelVO> getDataByIds(List<String> supplierLevelGuids) {
        if (CollectionUtils.isEmpty(supplierLevelGuids)) {
            return Collections.emptyList();
        }
        List<ErpSupplierMgtSupplierLevel> supplierLevelList = baseMapper.selectBatchIds(supplierLevelGuids);
        List<ErpSupplierMgtSupplierLevelVO> resultList = supplierLevelList.stream().map(item -> {
            ErpSupplierMgtSupplierLevelVO supplierLevelVO = new ErpSupplierMgtSupplierLevelVO();
            BeanUtil.copyProperties(item, supplierLevelVO);
            supplierLevelVO.setType(item.getState());
            return supplierLevelVO;
        }).collect(Collectors.toList());
        return resultList;
    }

    @Override
    public List<ErpSupplierMgtSupplierLevelVO> findList(ErpSupplierMgtSupplierLevelQO qo, String tenantGuid) {
        if (!StringUtils.isEmpty(qo.getKeyword())) {
            qo.setKeywords(Arrays.asList(qo.getKeyword().split(" ")));
        }
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpSupplierMgtSupplierLevelVO> findPage(PageParams<ErpSupplierMgtSupplierLevelQO> pageParams) {
        IPage<ErpSupplierMgtSupplierLevelVO> page = pageParams.buildPage();
        ErpSupplierMgtSupplierLevelQO model = pageParams.getModel();
        if (!StringUtils.isEmpty(model.getKeyword())) {
            model.setKeywords(Arrays.asList(model.getKeyword().split(" ")));
        }
        return baseMapper.findPage(page, model);
    }

    /**
     * 重新排序
     *
     * @param entityDtoList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reordered(List<SortEntityDto> entityDtoList) {
        entityDtoList.forEach(sortEntityDto -> {
            LambdaUpdateWrapper<ErpSupplierMgtSupplierLevel> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ErpSupplierMgtSupplierLevel::getSerialNumber, sortEntityDto.getSerialNumber());
            updateWrapper.eq(ErpSupplierMgtSupplierLevel::getSupplierLevelGuid, sortEntityDto.getGuid());
            update(updateWrapper);
        });
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveDate(InsertOrUpdateList<ErpSupplierMgtSupplierLevelRO> dataList, String tenantGuid) {
        if (!CollectionUtils.isEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(data -> {
                data.setTenantGuid(tenantGuid);
                this.create(data);
            });
        }

        if (!CollectionUtils.isEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcelDataHandle(ErpSupplierMgtSupplierLevelRO ro) {
        // 处理子级数据（根据父级名称获取父级guid）
        if (!StringUtils.isEmpty(ro.getParentClassificationGuid())) {
            // 根据名称获取guid
            ErpSupplierMgtSupplierLevel pEntity = baseMapper.selectOne(Wrappers.<ErpSupplierMgtSupplierLevel>lambdaQuery()
                    .select(ErpSupplierMgtSupplierLevel::getSupplierLevelGuid)
                    .eq(ErpSupplierMgtSupplierLevel::getSupplierLevelName, ro.getParentClassificationGuid()));
            if (pEntity == null || StringUtils.isEmpty(pEntity.getSupplierLevelGuid())) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "父级供应商级别【" + ro.getParentClassificationGuid() + "】不存在！");
            }
            ro.setParentClassificationGuid(pEntity.getSupplierLevelGuid());
        }
        // 保存数据到数据库
        this.create(ro);
    }

    /**
     * 新增和修改时字段校验
     *
     * @param ro
     */
    private void saveOrUpdateVerify(ErpSupplierMgtSupplierLevelRO ro) {
        // 判断名称是否重复
        LambdaQueryWrapper<ErpSupplierMgtSupplierLevel> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ErpSupplierMgtSupplierLevel::getTenantGuid, ro.getTenantGuid())
                .eq(ErpSupplierMgtSupplierLevel::getSupplierLevelName, ro.getSupplierLevelName())
                .ne(ro.getSupplierLevelGuid() != null, ErpSupplierMgtSupplierLevel::getSupplierLevelGuid, ro.getSupplierLevelGuid());
        if (baseMapper.exists(queryWrapper)) {
            throw new FlowException(ResultErrorCode.REPETITION_SUPPLIER_LEVEL_NAME);
        }
    }
}
