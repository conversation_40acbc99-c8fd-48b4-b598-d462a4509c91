package xy.server.purchase.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseOrderPlaceOfDelivery;
import xy.server.purchase.entity.model.ro.ErpPurchaseMgtPurchaseOrderDataRO;
import xy.server.purchase.mapper.ErpPurchaseMgtPurchaseOrderPlaceOfDeliveryMapper;
import xy.server.purchase.service.IErpPurchaseMgtPurchaseOrderPlaceOfDeliveryService;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 订单交货地点管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Service
public class ErpPurchaseMgtPurchaseOrderPlaceOfDeliveryServiceImpl extends ServiceImpl<ErpPurchaseMgtPurchaseOrderPlaceOfDeliveryMapper, ErpPurchaseMgtPurchaseOrderPlaceOfDelivery> implements IErpPurchaseMgtPurchaseOrderPlaceOfDeliveryService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDate(String orderGuid, List<ErpPurchaseMgtPurchaseOrderDataRO> orderDataList) {
        if (!CollectionUtils.isEmpty(orderDataList)) {
            List<ErpPurchaseMgtPurchaseOrderPlaceOfDelivery> entityList = new ArrayList<>();
            orderDataList.forEach(orderData -> {
                ErpPurchaseMgtPurchaseOrderPlaceOfDelivery entity = baseMapper.selectOne(Wrappers.<ErpPurchaseMgtPurchaseOrderPlaceOfDelivery>lambdaQuery()
                        .eq(ErpPurchaseMgtPurchaseOrderPlaceOfDelivery::getOrderGuid, orderGuid)
                        .eq(ErpPurchaseMgtPurchaseOrderPlaceOfDelivery::getOrderDataGuid, orderData.getOrderDataGuid()));
                if (ObjectUtil.isNull(entity)) { entity = new ErpPurchaseMgtPurchaseOrderPlaceOfDelivery(); }
                entity.setOrderGuid(orderGuid);
                entity.setOrderDataGuid(orderData.getOrderDataGuid());
                entity.setAddress(orderData.getAddress());
                entity.setAdministrativeAreaGuid(orderData.getAdministrativeAreaGuid());
                entityList.add(entity);
            });
            super.saveOrUpdateBatch(entityList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByOrderGuid(String orderGuid, List<String> delOrderDataGuids) {
        if (!StringUtils.isEmpty(orderGuid)) {
            super.remove(Wrappers.<ErpPurchaseMgtPurchaseOrderPlaceOfDelivery>lambdaQuery()
                    .eq(ErpPurchaseMgtPurchaseOrderPlaceOfDelivery::getOrderGuid, orderGuid)
                    .in(!CollectionUtils.isEmpty(delOrderDataGuids), ErpPurchaseMgtPurchaseOrderPlaceOfDelivery::getOrderDataGuid, delOrderDataGuids));
        }
    }
}
