package xy.server.purchase.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.config.excel.EasyExcelDataHandleService;
import com.xunyue.config.util.PageParams;
import xy.server.purchase.entity.ErpSupplierMgtSupplierLevel;
import xy.server.purchase.entity.model.qo.ErpSupplierMgtSupplierLevelQO;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierLevelRO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierLevelVO;

import java.util.List;

/**
 * <p>
 * 供应商级别 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
public interface IErpSupplierMgtSupplierLevelService extends IService<ErpSupplierMgtSupplierLevel>, EasyExcelDataHandleService<ErpSupplierMgtSupplierLevelRO> {
    boolean create(ErpSupplierMgtSupplierLevelRO ro);

    boolean delete(String supplierLevelGuid);

    Boolean deleteByBatch(List<String> ids);

    boolean update(ErpSupplierMgtSupplierLevelRO ro);

    ErpSupplierMgtSupplierLevelVO getById(String supplierLevelGuid);

    List<ErpSupplierMgtSupplierLevelVO> findList(ErpSupplierMgtSupplierLevelQO qo, String tenantGuid);

    IPage<ErpSupplierMgtSupplierLevelVO> findPage(PageParams<ErpSupplierMgtSupplierLevelQO> pageParams);

    Boolean reordered(List<SortEntityDto> entityDtoList);

    Boolean saveDate(InsertOrUpdateList<ErpSupplierMgtSupplierLevelRO> dataList, String tenantGuid);

    List<ErpSupplierMgtSupplierLevelVO> getDataByIds(List<String> supplierLevelGuids);
}
