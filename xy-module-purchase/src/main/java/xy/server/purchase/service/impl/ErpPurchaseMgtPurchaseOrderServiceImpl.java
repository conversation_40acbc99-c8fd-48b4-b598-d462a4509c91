package xy.server.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.vo.SourceDescriptionAndPictureVO;
import com.xunyue.common.enums.*;
import com.xunyue.common.i18n.BaseResultErrorCodeImpl;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.common.util.DateUtils;
import com.xunyue.common.util.LoadDataUtils;
import com.xunyue.common.util.UsedUtils;
import com.xunyue.common.util.XyBeanUtil;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import com.xunyue.exteriororder.entity.model.dto.ProcOrderDTO;
import com.xunyue.exteriororder.service.IProcOrderService;
import com.xunyue.node.common.enums.ClazzEnum;
import com.xunyue.node.entity.Node;
import com.xunyue.node.service.INodeService;
import com.xunyue.order.entity.Order;
import com.xunyue.order.entity.model.dto.OrderDTO;
import com.xunyue.tenant.sign.UserApi;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import xy.server.activiti.common.enums.BusinessStatusEnum;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.service.IErpFormPendingNumberService;
import xy.server.activiti.utils.actEvent.ActEventEntity;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.basic.entity.model.vo.ErpBasicMgtCurrencyExchangeRateVO;
import xy.server.basic.entity.model.vo.ErpBasicMgtInvoiceTypeTaxRateVO;
import xy.server.basic.service.IErpBasicMgtCurrencyExchangeRateService;
import xy.server.basic.service.IErpBasicMgtInvoiceTypeTaxRateService;
import xy.server.basic.service.IErpBusinessMgtOtherExpensesService;
import xy.server.dto.XyMemberDto;
import xy.server.filter.enums.TableIdentificationEnum;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;
import xy.server.material.service.IErpMaterialMgtMaterialService;
import xy.server.policy.common.constant.EarlyWarningConstant;
import xy.server.policy.common.enums.EarlyWarningEnum;
import xy.server.policy.event.EarlyWarningEvent;
import xy.server.policy.event.EarlyWarningRemoveEvent;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseOrder;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseOrderData;
import xy.server.purchase.entity.model.qo.ErpGetNewDataQO;
import xy.server.purchase.entity.model.qo.ErpPurchaseMgtPurchaseOrderQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingDetaiStopRO;
import xy.server.purchase.entity.model.ro.ErpPurchaseMgtPurchaseOrderRO;
import xy.server.purchase.entity.model.ro.ErpUpdateToJsonRO;
import xy.server.purchase.entity.model.ro.ModifyMaterialAndSyncWorkOrderRO;
import xy.server.purchase.entity.model.vo.*;
import xy.server.purchase.i18n.ResultErrorCode;
import xy.server.purchase.mapper.ErpPurchaseMgtPurchaseOrderDataMapper;
import xy.server.purchase.mapper.ErpPurchaseMgtPurchaseOrderMapper;
import xy.server.purchase.service.IErpPurchaseMgtPurchaseOrderDataService;
import xy.server.purchase.service.IErpPurchaseMgtPurchaseOrderFileService;
import xy.server.purchase.service.IErpPurchaseMgtPurchaseOrderService;
import xy.server.purchase.service.IErpSupplierMgtSupplierService;
import xy.server.work.entity.ErpProductionMgtWorkorder;
import xy.server.work.entity.ErpProductionMgtWorkorderMaterialUsage;
import xy.server.work.entity.ErpProductionMgtWorkorderOrderData;
import xy.server.work.entity.model.ro.ErpupdatePutMaterielRO;
import xy.server.work.service.IErpProductionMgtWorkorderMaterialUsageService;
import xy.server.work.service.IErpProductionMgtWorkorderOrderDataService;
import xy.server.work.service.IErpProductionMgtWorkorderService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 采购订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service("ORDER.PURCHASE")
@Slf4j
public class ErpPurchaseMgtPurchaseOrderServiceImpl extends ServiceImpl<ErpPurchaseMgtPurchaseOrderMapper, ErpPurchaseMgtPurchaseOrder> implements IErpPurchaseMgtPurchaseOrderService, ActEventStrategyService {
    private final IErpSystemMgtOrderSerialNumberService iErpSystemMgtOrderSerialNumberService;
    private final IErpPurchaseMgtPurchaseOrderDataService iErpPurchaseMgtPurchaseOrderDataService;
    private final IErpPurchaseMgtPurchaseOrderFileService iErpPurchaseMgtPurchaseOrderFileService;
    private final IErpProductionMgtWorkorderService iErpProductionMgtWorkorderService;
    private final IErpProductionMgtWorkorderOrderDataService iErpProductionMgtWorkorderOrderDataService;
    private final UsedUtils usedUtils;
    private final IErpFormPendingNumberService iErpFormPendingNumberService;
    private final IErpSupplierMgtSupplierService supplierService;
    private final ErpPurchaseMgtPurchaseOrderDataMapper orderDataMapper;
    private final IErpBusinessMgtOtherExpensesService otherExpensesService;
    private final IErpProductionMgtWorkorderMaterialUsageService erpProductionMgtWorkorderMaterialUsageService;
    private final IErpMaterialMgtMaterialService materialMgtMaterialService;
    private final UserApi userApi;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final IProcOrderService procOrderService;
    private final INodeService nodeService;
    private final IErpBasicMgtInvoiceTypeTaxRateService rateService;
    private final IErpBasicMgtCurrencyExchangeRateService currencyExchangeRateService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ErpPurchaseMgtPurchaseOrder create(ErpPurchaseMgtPurchaseOrderRO ro) {
        // 保存采购订单
        ErpPurchaseMgtPurchaseOrder entity = new ErpPurchaseMgtPurchaseOrder();
        BeanUtil.copyProperties(ro, entity);
        String orderNumber = "";
        if (null != ro.getIsItMaterial() && ro.getIsItMaterial()) {
            orderNumber = iErpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.ORDER_PURCHASE_INGREDIENTS);
        } else {
//            默认原料 此代码处理永久原辅料编码分开
            orderNumber = iErpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.ORDER_PURCHASE);
        }
        entity.setOrderNumber(orderNumber);
        if (entity.getReceiptDate() == null) {
            entity.setReceiptDate(LocalDateTime.now());
        }
        // 设置订单属性
        entity.setOrderProperties(OrderPropertiesEnum.PURCHASE_ORDER.getCode());
        if (super.save(entity)) {
            XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();
            ThreadUtil.execAsync(() -> {
                userApi.setThreadLocal(memberDto);
                this.createOrder(entity, ro);
            });
            // 修改供应商已被使用字段
            usedUtils.isUsed("erp_supplier_mgt_supplier", entity.getCustomerOrSupplierGuid(), null, "supplier_guid");
            usedUtils.isUsed("erp_supplier_mgt_supplier", entity.getSettlementCustomerOrSupplierGuid(), null, "supplier_guid");
            // 保存采购申请单明细
            this.saveAssociatedData(entity.getOrderGuid(), ro);
            // 更新待开数量
            //iErpFormPendingNumberService.updatePendingNumber(WorkflowKeyEnum.ORDER_PURCHASE, WorkflowKeyEnum.WORK_ORDER_PURCHASE,
            //        baseMapper.selectCountPendingList(), TableIdentificationEnum.erp_purchasing_mgt_application_wait_order);
            // 启动审核流程
            iProcessInstanceService.start(WorkflowKeyEnum.ORDER_PURCHASE, entity.getOrderGuid());
            return entity;
        }
        return entity;
    }

    /**
     * 双写
     *
     * @param entity
     * @param ro
     */
    public void createOrder(ErpPurchaseMgtPurchaseOrder entity, ErpPurchaseMgtPurchaseOrderRO ro) {
        log.info("开始双写采购订单主表数据:{}", JSONUtil.toJsonStr(entity));
        ProcOrderDTO orderDTO = XyBeanUtil.copyPropertiesByGst(entity, ProcOrderDTO.class);
        orderDTO.setOrder(XyBeanUtil.copyPropertiesByGst(entity, Order.class));
        orderDTO.setProcOrderId(entity.getOrderGuid());
        orderDTO.setProcOrderClazz(ClazzEnum.PROCUREMENT_ORDER.name());
        orderDTO.setProcOrderUserCode(entity.getOrderNumber());
        orderDTO.setProcOrderSupId(entity.getCustomerOrSupplierGuid());
        orderDTO.setProcOrderSettleSupId(entity.getSettlementCustomerOrSupplierGuid());
        orderDTO.setProcOrderDelvSup(entity.getDeliveryCustomerOrSupplierGuid());
        if (Objects.nonNull(entity.getReceiptDate())){
            orderDTO.setProcOrderReceiptDate(DateUtils.toDate(entity.getReceiptDate()));
        }
        if(null!=entity.getInvoiceTypeTaxRateGuid()){
            ErpBasicMgtInvoiceTypeTaxRateVO rateData = rateService.getById(entity.getInvoiceTypeTaxRateGuid());
            orderDTO.setProcOrderTaxRt(null != rateData ? rateData.getTaxRate() : null);
        }
        if(null!=entity.getCurrencyExchangeRateGuid()){
            ErpBasicMgtCurrencyExchangeRateVO currencyExchangeRateVO = currencyExchangeRateService.getById(entity.getCurrencyExchangeRateGuid());
            orderDTO.setProcOrderCurRate(null != currencyExchangeRateVO ? currencyExchangeRateVO.getExchangeRate() : null);
        }
        procOrderService.saveOrUpdate(orderDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ErpPurchaseMgtPurchaseOrder update(ErpPurchaseMgtPurchaseOrderRO ro) {
        ErpPurchaseMgtPurchaseOrder oldEntity = baseMapper.selectById(ro.getOrderGuid());

        ErpPurchaseMgtPurchaseOrder entity = new ErpPurchaseMgtPurchaseOrder();
        BeanUtil.copyProperties(ro, entity);
        if (entity.getReceiptDate() == null) {
            entity.setReceiptDate(LocalDateTime.now());
        }
        if (super.updateById(entity)) {
            XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();
            ThreadUtil.execAsync(() -> {
                userApi.setThreadLocal(memberDto);
                this.createOrder(entity, ro);
            });
            // 修改供应商已被使用字段
            usedUtils.isUsed("erp_supplier_mgt_supplier", entity.getCustomerOrSupplierGuid(), oldEntity.getCustomerOrSupplierGuid(), "supplier_guid");
            usedUtils.isUsed("erp_supplier_mgt_supplier", entity.getSettlementCustomerOrSupplierGuid(), oldEntity.getSettlementCustomerOrSupplierGuid(), "supplier_guid");
            // 保存采购申请单明细
            this.saveAssociatedData(entity.getOrderGuid(), ro);
            // 更新待开数量
            //iErpFormPendingNumberService.updatePendingNumber(WorkflowKeyEnum.ORDER_PURCHASE, WorkflowKeyEnum.WORK_ORDER_PURCHASE,
            //        baseMapper.selectCountPendingList(), TableIdentificationEnum.erp_purchasing_mgt_application_wait_order);
            return entity;
        }
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ErpPurchaseMgtPurchaseOrder> saveDate(InsertOrUpdateList<ErpPurchaseMgtPurchaseOrderRO> dataList) {
        List<ErpPurchaseMgtPurchaseOrder> resultList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(dataList.getInsertList())) {
            List<ErpPurchaseMgtPurchaseOrder> iList = dataList.getInsertList().stream().map(this::create).collect(Collectors.toList());
            resultList.addAll(iList);
        }
        if (!CollectionUtils.isEmpty(dataList.getUpdateList())) {
            List<ErpPurchaseMgtPurchaseOrder> uList = dataList.getUpdateList().stream().map(this::update).collect(Collectors.toList());
            resultList.addAll(uList);
        }
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String orderGuid) {
        ErpPurchaseMgtPurchaseOrder entity = baseMapper.selectById(orderGuid);
        if (super.removeById(orderGuid)) {
            // 修改供应商已被使用字段
            usedUtils.isUsed("erp_supplier_mgt_supplier", entity.getCustomerOrSupplierGuid(), "supplier_guid");
            usedUtils.isUsed("erp_supplier_mgt_supplier", entity.getSettlementCustomerOrSupplierGuid(), "supplier_guid");

            // 删除采购申请单明细
            this.removeAssociatedData(orderGuid);
            // 更新待开数量
            //iErpFormPendingNumberService.updatePendingNumber(WorkflowKeyEnum.ORDER_PURCHASE, WorkflowKeyEnum.WORK_ORDER_PURCHASE,
            //        baseMapper.selectCountPendingList(), TableIdentificationEnum.erp_purchasing_mgt_application_wait_order);
            // 删除流程相关数据
            iProcessInstanceService.deleteProcessAndHisInst(orderGuid, WorkflowKeyEnum.ORDER_PURCHASE);
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> ids) {
        for (String id : ids) {
            ErpPurchaseMgtPurchaseOrder entity = baseMapper.selectById(id);
            if (super.removeById(id)) {
                // 修改供应商已被使用字段
                usedUtils.isUsed("erp_supplier_mgt_supplier", entity.getCustomerOrSupplierGuid(), "supplier_guid");
                usedUtils.isUsed("erp_supplier_mgt_supplier", entity.getSettlementCustomerOrSupplierGuid(), "supplier_guid");
                // 删除采购申请单明细
                this.removeAssociatedData(id);
                // 更新待开数量
                //iErpFormPendingNumberService.updatePendingNumber(WorkflowKeyEnum.ORDER_PURCHASE, WorkflowKeyEnum.WORK_ORDER_PURCHASE,
                //        baseMapper.selectCountPendingList(), TableIdentificationEnum.erp_purchasing_mgt_application_wait_order);
                // 删除流程相关数据
                iProcessInstanceService.deleteProcessAndHisInst(id, WorkflowKeyEnum.ORDER_PURCHASE);
            }
        }
        return true;
    }

    @Override
    public ErpPurchaseMgtPurchaseOrderVO getById(String orderGuid) {
        ErpPurchaseMgtPurchaseOrderVO erpPurchaseMgtPurchaseOrderVO = baseMapper.getDataByGuid(orderGuid);
        List<ErpPurchaseMgtPurchaseOrderDataVO> orderDataVOS = iErpPurchaseMgtPurchaseOrderDataService.getDataByOrderGuids(Lists.newArrayList(orderGuid));
        erpPurchaseMgtPurchaseOrderVO.setDetailList(orderDataVOS);
        return erpPurchaseMgtPurchaseOrderVO;
    }

    @Override
    @SneakyThrows
    public List<ErpPurchaseMgtPurchaseOrderVO> findList(ErpPurchaseMgtPurchaseOrderQO qo) {
        List<ErpPurchaseMgtPurchaseOrderVO> resultList = baseMapper.findList(qo);
        if (CollectionUtil.isNotEmpty(resultList)) {
            // 供应商
            List<String> supplierGuids = resultList.stream().map(ErpPurchaseMgtPurchaseOrderVO::getCustomerOrSupplierGuid).distinct().collect(Collectors.toList());
            // 结算供应商
            List<String> settlementSupplierGuids = resultList.stream().map(ErpPurchaseMgtPurchaseOrderVO::getSettlementCustomerOrSupplierGuid).distinct().collect(Collectors.toList());
            List<String> allSupplierGuids = Stream.concat(supplierGuids.stream(), settlementSupplierGuids.stream()).distinct().collect(Collectors.toList());
            XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();
            CompletableFuture<List<ErpSupplierMgtSupplierVO>> supplierMgtSupplierTask = CompletableFuture.supplyAsync(() -> {
                userApi.setThreadLocal(memberDto);
                return supplierService.getDataByIds(allSupplierGuids);
            });
            // 采购订单明细列表
            List<String> orderGuids = resultList.stream().map(ErpPurchaseMgtPurchaseOrderVO::getOrderGuid).distinct().collect(Collectors.toList());
            CompletableFuture<List<ErpPurchaseMgtPurchaseOrderDataVO>> orderDataTask = CompletableFuture.supplyAsync(() -> {
                userApi.setThreadLocal(memberDto);
                return iErpPurchaseMgtPurchaseOrderDataService.getDataByOrderGuids(orderGuids);
            });
            // 采购订单文件列表
            CompletableFuture<List<ErpPurchaseMgtPurchaseOrderFileVO>> orderFileTask = CompletableFuture.supplyAsync(() -> {
                userApi.setThreadLocal(memberDto);
                return iErpPurchaseMgtPurchaseOrderFileService.getDataByOrderGuids(orderGuids);
            });
            CompletableFuture.allOf(supplierMgtSupplierTask, orderDataTask, orderFileTask).join();

            List<ErpSupplierMgtSupplierVO> supplierMgtSupplierList = supplierMgtSupplierTask.get();
            List<ErpPurchaseMgtPurchaseOrderDataVO> orderDataList = orderDataTask.get();
            List<ErpPurchaseMgtPurchaseOrderFileVO> orderFileList = orderFileTask.get();

            Map<String, ErpSupplierMgtSupplierVO> allSupplierVOMap = supplierMgtSupplierList.stream().collect(Collectors.toMap(ErpSupplierMgtSupplierVO::getSupplierGuid, item -> item));
            Map<String, List<ErpPurchaseMgtPurchaseOrderDataVO>> orderDataGroupMap = orderDataList.stream().collect(Collectors.groupingBy(ErpPurchaseMgtPurchaseOrderDataVO::getOrderGuid));
            Map<String, List<ErpPurchaseMgtPurchaseOrderFileVO>> orderFileGroupMap = orderFileList.stream().collect(Collectors.groupingBy(ErpPurchaseMgtPurchaseOrderFileVO::getOrderGuid));

            resultList.forEach(item -> {
                item.setSupplierObj(allSupplierVOMap.get(item.getCustomerOrSupplierGuid()));
                item.setSettlementSupplierObj(allSupplierVOMap.get(item.getSettlementCustomerOrSupplierGuid()));
                item.setDetailList(orderDataGroupMap.get(item.getOrderGuid()));
                item.setFileList(orderFileGroupMap.get(item.getOrderGuid()));
            });
        }
        return resultList;
    }

    @Override
    public IPage<ErpPurchaseMgtPurchaseOrderVO> findPage(PageParams<ErpPurchaseMgtPurchaseOrderQO> pageParams) {
        IPage<ErpPurchaseMgtPurchaseOrderVO> page = pageParams.buildPage();
        ErpPurchaseMgtPurchaseOrderQO model = pageParams.getModel();
        IPage<ErpPurchaseMgtPurchaseOrderVO> resultPage = baseMapper.findPage(page, model);
        if (CollectionUtil.isNotEmpty(resultPage.getRecords())) {
            // 供应商
            List<String> supplierGuids = resultPage.getRecords().stream().map(ErpPurchaseMgtPurchaseOrderVO::getCustomerOrSupplierGuid).distinct().collect(Collectors.toList());
            // 结算供应商
            List<String> settlementSupplierGuids = resultPage.getRecords().stream().map(ErpPurchaseMgtPurchaseOrderVO::getSettlementCustomerOrSupplierGuid).distinct().collect(Collectors.toList());
            List<String> allSupplierGuids = Stream.concat(supplierGuids.stream(), settlementSupplierGuids.stream()).distinct().collect(Collectors.toList());
            Map<String, ErpSupplierMgtSupplierVO> allSupplierVOMap = supplierService.getDataByIds(allSupplierGuids).stream().collect(Collectors.toMap(ErpSupplierMgtSupplierVO::getSupplierGuid, item -> item));

            // 采购订单明细列表
            List<String> orderGuids = resultPage.getRecords().stream().map(ErpPurchaseMgtPurchaseOrderVO::getOrderGuid).distinct().collect(Collectors.toList());
            List<ErpPurchaseMgtPurchaseOrderDataVO> orderDataVOS = iErpPurchaseMgtPurchaseOrderDataService.getDataByOrderGuids(orderGuids);
            Map<String, List<ErpPurchaseMgtPurchaseOrderDataVO>> orderDataGroupMap = orderDataVOS.stream().collect(Collectors.groupingBy(ErpPurchaseMgtPurchaseOrderDataVO::getOrderGuid));

            // 采购订单文件列表
            List<ErpPurchaseMgtPurchaseOrderFileVO> OrderFileVOS = iErpPurchaseMgtPurchaseOrderFileService.getDataByOrderGuids(orderGuids);
            Map<String, List<ErpPurchaseMgtPurchaseOrderFileVO>> orderFileGroupMap = OrderFileVOS.stream().collect(Collectors.groupingBy(ErpPurchaseMgtPurchaseOrderFileVO::getOrderGuid));

            resultPage.getRecords().forEach(item -> {
                item.setSupplierObj(allSupplierVOMap.get(item.getCustomerOrSupplierGuid()));
                item.setSettlementSupplierObj(allSupplierVOMap.get(item.getSettlementCustomerOrSupplierGuid()));
                item.setDetailList(orderDataGroupMap.get(item.getOrderGuid()));
                item.setFileList(orderFileGroupMap.get(item.getOrderGuid()));
            });
        }
        return resultPage;
    }

    @Override
    public List<ErpPurchaseInventoryWorkorderDetailVO> findNotBilledList(ErpPurchaseMgtPurchaseOrderQO qo) {
        List<ErpPurchaseInventoryWorkorderDetailVO> erpPurchaseInventoryWorkorderDetailVOS = findNotBilledListDetail(qo);
        // 加载其他费用数据
        otherExpensesService.loadBusinessMgtOtherExpenses(OtherExpensesEnum.PURCHASE_ORDER, erpPurchaseInventoryWorkorderDetailVOS, ErpPurchaseInventoryWorkorderDetailVO::getSourceGuid, ErpPurchaseInventoryWorkorderDetailVO::setOtherExpensesList, null);
        return erpPurchaseInventoryWorkorderDetailVOS;
    }

    @Override
    public List<ErpPurchaseInventoryWorkorderVO> findNotBilledTree(ErpPurchaseMgtPurchaseOrderQO qo) {
        List<ErpPurchaseInventoryWorkorderVO> resultList = new ArrayList<>();
        // 待开列表（明细）
        List<ErpPurchaseInventoryWorkorderDetailVO> notBilledList = findNotBilledListDetail(qo);

        // 获取父guid列表
        if (!CollectionUtils.isEmpty(notBilledList)) {
            // 使用流处理进行分组和转换
            resultList = notBilledList.stream().collect(Collectors.groupingBy(ErpPurchaseInventoryWorkorderDetailVO::getSupplierName,  // 根据 groupId 分组
                    Collectors.collectingAndThen(Collectors.toList(), groupedList -> {
                        // 对每个分组进行转换成 ErpPurchaseInventoryWorkorderVO
                        ErpPurchaseInventoryWorkorderVO vo = new ErpPurchaseInventoryWorkorderVO();
                        vo.setCustomerGuid(groupedList.get(0).getParentCustomerGuid());
                        vo.setSettlementCustomerOrSupplierGuid(groupedList.get(0).getSettlementCustomerOrSupplierGuid());  // 假设 groupId 在每个分组中是相同的，取第一个元素的 groupId
                        vo.setDetailList(groupedList.stream().distinct().peek(c -> c.setCustomerGuid(String.valueOf(UUID.randomUUID()))).collect(Collectors.toList()));
                        vo.setCustomerOrSupplierName(groupedList.get(0).getCustomerOrSupplierName());
                        vo.setSettlementCustomerOrSupplierName(groupedList.get(0).getSettlementCustomerOrSupplierName());
                        return vo;
                    }))).values().stream().collect(Collectors.toList());
        }

        // 加载其他费用数据
        otherExpensesService.loadBusinessMgtOtherExpenses(OtherExpensesEnum.PURCHASE_ORDER, resultList.stream().flatMap(x -> x.getDetailList().stream()).collect(Collectors.toList()), ErpPurchaseInventoryWorkorderDetailVO::getSourceGuid, ErpPurchaseInventoryWorkorderDetailVO::setOtherExpensesList, null);

        return resultList;
    }

    @SneakyThrows
    public List<ErpPurchaseInventoryWorkorderDetailVO> findNotBilledListDetail(ErpPurchaseMgtPurchaseOrderQO qo) {
        // 待开列表（明细）
        // 第一步查询出来全部的待开明细，这里只查询的数量、workorderGuid
        List<ErpPurchaseInventoryWorkorderDetailVO> notBilledList = baseMapper.findNotBilledListSimple(qo).stream().distinct().collect(Collectors.toList());

        if (CollUtil.isNotEmpty(notBilledList)) {

            List<String> workorderGuids = notBilledList.stream().map(ErpPurchaseInventoryWorkorderDetailVO::getWorkorderGuid).distinct().collect(Collectors.toList());
            XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();

            // 获取待入库数量等信息
            List<String> finalWorkorderGuids = workorderGuids;
            CompletableFuture<List<ErpPurchaseInventoryWorkorderDetailVO>> notBilledQuantityListTask = CompletableFuture.supplyAsync(() -> {
                if (CollUtil.isEmpty(finalWorkorderGuids)) {
                    return new ArrayList<>();
                }
                userApi.setThreadLocal(memberDto);
                return LoadDataUtils.batchLoadDataAsync(baseMapper::findNotBilledListNotBilledQuantity, finalWorkorderGuids, memberDto, 2000L);
            });
            CompletableFuture.allOf(notBilledQuantityListTask).join();

            List<ErpPurchaseInventoryWorkorderDetailVO> notBilledQuantityList = notBilledQuantityListTask.get();

            Map<String, ErpPurchaseInventoryWorkorderDetailVO> notBilledQuantityListMap = notBilledQuantityList.stream().collect(Collectors.toMap(ErpPurchaseInventoryWorkorderDetailVO::getWorkorderGuid, u -> u));

            notBilledList.forEach(v -> {
                // 计算数量
                setNotBilledQuantity(v, notBilledQuantityListMap);
            });

            // 过滤已经开完的单
            if (qo.getIsIgnore() == null || !qo.getIsIgnore()) {
                notBilledList = notBilledList.stream().filter(v -> v.getNotBilledQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            }
            if (qo.getIsPackagingQuantity() != null && qo.getIsPackagingQuantity()) {
                notBilledList = notBilledList.stream().filter(v -> v.getPackagingQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            }

            // 根据workorderGuid二次回表查询
            workorderGuids = notBilledList.stream().map(ErpPurchaseInventoryWorkorderDetailVO::getWorkorderGuid).distinct().collect(Collectors.toList());
            qo.setWorkorderGuids(workorderGuids);
            notBilledList = baseMapper.findNotBilledList(qo);

            // 补充待开数据，上单据关联的数据
            List<String> materials = notBilledList.stream().map(ErpPurchaseInventoryWorkorderDetailVO::getMaterialGuid).distinct().collect(Collectors.toList());
            List<String> sourceGuids = notBilledList.stream().map(ErpPurchaseInventoryWorkorderDetailVO::getSourceGuid).distinct().collect(Collectors.toList());
            List<String> orderDataGuids = notBilledList.stream().map(ErpPurchaseInventoryWorkorderDetailVO::getOrderDataGuid).distinct().collect(Collectors.toList());
            // 异步分片获取
            CompletableFuture<List<ErpMaterialMgtMaterialVO>> loadMaterialObjTask = CompletableFuture.supplyAsync(() -> {
                if (CollUtil.isEmpty(materials)) {
                    return new ArrayList<>();
                }
                userApi.setThreadLocal(memberDto);
                return LoadDataUtils.batchLoadDataAsync(materialMgtMaterialService::getByIds, materials, memberDto, 1000L);
            });
            CompletableFuture<List<ErpPurchaseMgtPurchaseOrderData>> loadDataLisTask = CompletableFuture.supplyAsync(() -> {
                if (CollUtil.isEmpty(sourceGuids)) {
                    return new ArrayList<>();
                }
                userApi.setThreadLocal(memberDto);
                return LoadDataUtils.batchLoadDataAsync(orderDataMapper::selectBatchIds, sourceGuids, memberDto, 1000L);
            });
            // 获取生产工单号
            CompletableFuture<List<ErpPurchaseInventoryWorkorderDetailVO>> workorderNumberListTask = CompletableFuture.supplyAsync(() -> {
                if (CollUtil.isEmpty(orderDataGuids)) {
                    return new ArrayList<>();
                }
                userApi.setThreadLocal(memberDto);
                return LoadDataUtils.batchLoadDataAsync(baseMapper::findNotBilledListWorkorderNumber, orderDataGuids, memberDto, 1000L);
            });
            CompletableFuture.allOf(loadMaterialObjTask, loadDataLisTask, workorderNumberListTask).join();

            List<ErpMaterialMgtMaterialVO> materialVOList = loadMaterialObjTask.get();
            List<ErpPurchaseMgtPurchaseOrderData> dataList = loadDataLisTask.get();
            List<ErpPurchaseInventoryWorkorderDetailVO> workorderNumberList = workorderNumberListTask.get();

            Map<String, ErpMaterialMgtMaterialVO> materialVOMap = materialVOList.stream().collect(Collectors.toMap(ErpMaterialMgtMaterialVO::getMaterialGuid, u -> u));

            Map<String, ErpPurchaseMgtPurchaseOrderData> dataMap = dataList.stream().collect(Collectors.toMap(ErpPurchaseMgtPurchaseOrderData::getOrderDataGuid, u -> u));

            Map<String, String> workorderNumberMap = workorderNumberList.stream().collect(Collectors.toMap(ErpPurchaseInventoryWorkorderDetailVO::getOrderDataGuid, ErpPurchaseInventoryWorkorderDetailVO::getSourceProductOrderNumber));
            notBilledList.forEach(v -> {
                v.setMaterialObj(materialVOMap.get(v.getMaterialGuid()));
                v.setSourceObj(dataMap.get(v.getSourceGuid()));
                v.setSourceProductOrderNumber(workorderNumberMap.get(v.getOrderDataGuid()));

                // 计算数量
                setNotBilledQuantity(v, notBilledQuantityListMap);

                v.setTotalAmountIncludingTax(v.getUnitPriceIncludingTax().multiply(v.getNotBilledQuantity()));
                v.setTotalAmountWithoutTax(v.getUnitPriceWithoutTax().multiply(v.getNotBilledQuantity()));
                v.setSettlementTotalAmount(v.getSettlementUnitPrice().multiply(v.getNotBilledQuantity()));
                v.setLocalCurrencyTotalAmount(v.getLocalCurrencyUnitPrice().multiply(v.getNotBilledQuantity()));
            });

            return notBilledList;
        }

        return new ArrayList<>();
    }

    public void setNotBilledQuantity(ErpPurchaseInventoryWorkorderDetailVO v, Map<String, ErpPurchaseInventoryWorkorderDetailVO> notBilledQuantityListMap) {
        BigDecimal quantity = v.getQuantity();
        BigDecimal packagingOrderQuantity = v.getPackagingOrderQuantity();
        if (ObjUtil.isNull(quantity)) {
            quantity = BigDecimal.ZERO;
        }
        if (ObjUtil.isNull(packagingOrderQuantity)) {
            packagingOrderQuantity = BigDecimal.ZERO;
        }
        BigDecimal notBilledQuantity = BigDecimal.ZERO;
        BigDecimal packagingQuantity = BigDecimal.ZERO;
        ErpPurchaseInventoryWorkorderDetailVO notBilledQuantityData = notBilledQuantityListMap.get(v.getWorkorderGuid());
        if (ObjUtil.isNotNull(notBilledQuantityData)) {
            notBilledQuantity = notBilledQuantityData.getQuantity();
            packagingQuantity = notBilledQuantityData.getPackagingQuantity();
        }
        v.setNotBilledQuantity(quantity.subtract(notBilledQuantity));
        v.setPackagingQuantity(packagingOrderQuantity.subtract(packagingQuantity));
    }

    /**
     * 终止单据
     *
     * @param list
     * @return
     */
    @Override
    public Boolean stopOrRollback(List<ErpOutgoingDetaiStopRO> list) {
        list.forEach(s -> {
            ErpPurchaseMgtPurchaseOrderData detail = orderDataMapper.selectById(s.getOrderDataGuid());
            detail.setOrderState(s.getCurrentStatus());
            orderDataMapper.updateById(detail);
        });
        return true;
    }

    /**
     * 获取顶级来源备注和图片
     *
     * @param sourceGuids
     * @return
     */
    @Override
    public List<SourceDescriptionAndPictureVO> getSourceDescriptionAndPicture(List<String> sourceGuids) {
        return baseMapper.getSourceDescriptionAndPicture(sourceGuids);
    }

    /**
     * 分页查看历史单价列表
     *
     * @param pageParams
     * @return
     */
    @Override
    public IPage<ErpPurchaseMgtHistoricalUnitPriceVO> selectHistoricalUnitPricePage(PageParams<ErpPurchaseMgtPurchaseOrderQO> pageParams) {
        IPage<ErpPurchaseMgtHistoricalUnitPriceVO> page = pageParams.buildPage();
        ErpPurchaseMgtPurchaseOrderQO model = pageParams.getModel();
        return baseMapper.selectHistoricalUnitPricePage(page, model);
    }

    /**
     * 设置最近到货价格
     *
     * @param dtoList
     * @return
     */
    @Override
    public List<ErpPurchaseMgtSetTheLatestArrivalPriceDTO> setTheLatestArrivalPrice(List<ErpPurchaseMgtSetTheLatestArrivalPriceDTO> dtoList) {
        if (!CollectionUtils.isEmpty(dtoList)) {
            dtoList.forEach(item -> {
                BigDecimal quotationUnitPriceIncludingTax = baseMapper.getLatestArrivalPrice(item.getSupplierGuid(), item.getMaterialGuid());
                item.setQuotationUnitPriceIncludingTax(quotationUnitPriceIncludingTax);
            });
        }
        return dtoList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean modifyMaterialAndSyncWorkOrder(ModifyMaterialAndSyncWorkOrderRO modifyMaterialAndSyncWorkOrderRO) {
        ErpPurchaseMgtPurchaseOrderData erpPurchaseMgtPurchaseOrderData = orderDataMapper.selectById(modifyMaterialAndSyncWorkOrderRO.getOrderDatatGuid());
        // 更新采购订单数量
        ErpPurchaseMgtPurchaseOrderData entityOrderData = new ErpPurchaseMgtPurchaseOrderData();
        entityOrderData.setOrderDataGuid(modifyMaterialAndSyncWorkOrderRO.getOrderDatatGuid());
        entityOrderData.setQuantity(modifyMaterialAndSyncWorkOrderRO.getQuantity());
        orderDataMapper.updateById(entityOrderData);
        if (ObjUtil.isEmpty(erpPurchaseMgtPurchaseOrderData)) {
            throw new FlowException(ResultErrorCode.DATA_NOT_FIND);
        }

        // 采购申请
        ErpProductionMgtWorkorder virtualWorkOrder = iErpProductionMgtWorkorderService.getById(erpPurchaseMgtPurchaseOrderData.getSourceGuid());
        if (ObjUtil.isEmpty(virtualWorkOrder)) {
            throw new FlowException(ResultErrorCode.PURCHASE_APPLY_DATA_NOT_FIND);
        }

        ErpProductionMgtWorkorder purchaseApplySonWorkOrder = iErpProductionMgtWorkorderService.getById(virtualWorkOrder.getParentClassificationGuid());
        if (ObjUtil.isEmpty(purchaseApplySonWorkOrder)) {
            throw new FlowException(ResultErrorCode.PURCHASE_APPLY_DATA_NOT_FIND);
        }

        // 物料用量
        ErpProductionMgtWorkorderMaterialUsage materialUsage = erpProductionMgtWorkorderMaterialUsageService.getById(purchaseApplySonWorkOrder.getSourceGuid());
        if (ObjUtil.isEmpty(materialUsage)) {
            // 表示非工单来源 - 直接修改物料
            erpPurchaseMgtPurchaseOrderData.setMaterialGuid(modifyMaterialAndSyncWorkOrderRO.getModifyMaterialGuid());
            orderDataMapper.updateById(erpPurchaseMgtPurchaseOrderData);
            return true;
        }

        ErpProductionMgtWorkorderMaterialUsage modifyMaterialUsage = BeanUtil.copyProperties(materialUsage, ErpProductionMgtWorkorderMaterialUsage.class);
        modifyMaterialUsage.setMaterialGuid(modifyMaterialAndSyncWorkOrderRO.getModifyMaterialGuid());

        // 修改物料用量-工序工单信息
        ErpupdatePutMaterielRO erpUpdatePutMaterialRo = new ErpupdatePutMaterielRO();
        erpUpdatePutMaterialRo.setMaterialGuid(modifyMaterialAndSyncWorkOrderRO.getModifyMaterialGuid());
        erpUpdatePutMaterialRo.setWorkorderMaterialUsageGuid(materialUsage.getWorkorderMaterialUsageGuid());
        erpUpdatePutMaterialRo.setMaterialUsageQuantity(modifyMaterialAndSyncWorkOrderRO.getMaterialUsageQuantity());
        erpUpdatePutMaterialRo.setProportion(modifyMaterialAndSyncWorkOrderRO.getProportion());
        erpUpdatePutMaterialRo.setMachiningNumber(modifyMaterialAndSyncWorkOrderRO.getMachiningNumber());
        erpUpdatePutMaterialRo.setMachiningNumber(modifyMaterialAndSyncWorkOrderRO.getMachiningNumber());
        erpUpdatePutMaterialRo.setWorkorderGuid(materialUsage.getWorkorderGuid());
//        if (!erpProductionMgtWorkorderMaterialUsageService.updatePutMateriel(Lists.newArrayList(erpUpdatePutMaterialRo))) {
//            throw new FlowException(ResultErrorCode.SYNC_MATERIAL_USAGE_FAILURE);
//        }
        if (!erpProductionMgtWorkorderMaterialUsageService.updatePutMaterielCurrent(erpUpdatePutMaterialRo)) {
            throw new FlowException(ResultErrorCode.SYNC_MATERIAL_USAGE_FAILURE);
        }

        // 同步采购订单信息
//        ErpProductionMgtWorkorderMaterialUsage updatedMaterialUsage = erpProductionMgtWorkorderMaterialUsageService.getById(purchaseApplySonWorkOrder.getSourceGuid());
//        erpPurchaseMgtPurchaseOrderData.setMaterialGuid(updatedMaterialUsage.getMaterialGuid());
//        erpPurchaseMgtPurchaseOrderData.setQuantity(updatedMaterialUsage.getMaterialUsageQuantity());
//        erpPurchaseMgtPurchaseOrderData.setTotalQuantity(updatedMaterialUsage.getMaterialUsageQuantity());
//        orderDataMapper.updateById(erpPurchaseMgtPurchaseOrderData);

        return true;
    }

    @Override
    public List<PurchaseRequisitionWorkOrderDataVO> getPurchaseRequisitionWorkOrderData(List<String> workorderGuids) {
        if (CollUtil.isEmpty(workorderGuids)) {
            return new ArrayList<>();
        }
        List<PurchaseRequisitionWorkOrderDataVO> requisitionWorkOrderData = orderDataMapper.getPurchaseRequisitionWorkOrderData(workorderGuids);
        /**
         * 加载物料详情
         */
        LoadDataUtils.loadDataObjTool(requisitionWorkOrderData, materialMgtMaterialService::getByIds, ErpMaterialMgtMaterialVO::getMaterialGuid, PurchaseRequisitionWorkOrderDataVO::getMaterialGuid, PurchaseRequisitionWorkOrderDataVO::setMaterialObj, null);

        return requisitionWorkOrderData;
    }

    /**
     * 获取某个客户的某个物料最近的每件数量
     *
     * @param qo
     * @return
     */
    @Override
    public BigDecimal getNewData(ErpGetNewDataQO qo) {
        return baseMapper.getNewData(qo);
    }

    /**
     * 修改采购订单明细tojson
     *
     * @param list
     * @return
     */
    @Override
    public Boolean updateToJson(List<ErpUpdateToJsonRO> list) {
        if (CollUtil.isNotEmpty(list)) {
            List<ErpPurchaseMgtPurchaseOrderData> updateList = new ArrayList<>();
            list.forEach(item -> {
                ErpPurchaseMgtPurchaseOrderData erpPurchaseMgtPurchaseOrderData = new ErpPurchaseMgtPurchaseOrderData();
                erpPurchaseMgtPurchaseOrderData.setOrderDataGuid(item.getOrderDataGuid());
                erpPurchaseMgtPurchaseOrderData.setToJson(item.getToJson());
                updateList.add(erpPurchaseMgtPurchaseOrderData);
            });
            return iErpPurchaseMgtPurchaseOrderDataService.updateBatchById(updateList);
        }
        return false;
    }

    /**
     * 保存采购订单相关表数据
     *
     * @param orderGuid
     * @param ro
     */
    private void saveAssociatedData(String orderGuid, ErpPurchaseMgtPurchaseOrderRO ro) {
        // 保存采购订单文件
        iErpPurchaseMgtPurchaseOrderFileService.saveDate(orderGuid, ro.getFileList(), ro.getDelFileIds());
        // 保存采购订单明细
        iErpPurchaseMgtPurchaseOrderDataService.saveDate(orderGuid, ro.getDetailList(), ro.getDelDetailIds());
    }

    /**
     * 删除关联表数据
     *
     * @param orderGuid
     */
    private void removeAssociatedData(String orderGuid) {
        // 删除采购订单明细
        iErpPurchaseMgtPurchaseOrderDataService.delByOrderGuid(orderGuid);
        // 删除采购订单文件
        iErpPurchaseMgtPurchaseOrderFileService.delByOrderGuid(orderGuid);
    }

    /**
     * 编辑删除操作前检查
     *
     * @param id
     * @param isFlow 是否为流程操作
     */
    private ErpPurchaseMgtPurchaseOrder operationCheck(String id, boolean isFlow) {
        ErpPurchaseMgtPurchaseOrder entity = baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new FlowException(BaseResultErrorCodeImpl.NO_REPETITION_DATA);
        }
        if (!baseMapper.getArrivalState(id).equals(ArrivalStateEnum.UN.getValue())) {
            throw new FlowException(ResultErrorCode.STATE_NOT_UN_ARRIVAL);
        }
        if (!isFlow && !entity.getAuditStatus().equals(BusinessStatusEnum.DRAFT.getStatus())) {
            throw new FlowException(BaseResultErrorCodeImpl.AUDIT_STATE_NOT_DRAFT);
        }
        return entity;
    }

    /**
     * 撤回流程时事件
     *
     * @param processVO
     */
    @Override
    public void cancelCall(ActEventEntity processVO) {
//        this.operationCheck(processVO.getBusinessKey(), true);
    }

    /**
     * 重启流程时事件
     *
     * @param processVO
     */
    @Override
    public void restartCall(ActEventEntity processVO) {
//        this.operationCheck(processVO.getBusinessKey(), true);
        // 删除到货单
        ErpProductionMgtWorkorder workorder = iErpProductionMgtWorkorderService.getOne(Wrappers.<ErpProductionMgtWorkorder>lambdaQuery().eq(ErpProductionMgtWorkorder::getWorkorderProperties, WorkorderPropertiesEnum.MATERIAL_DELIVERY_FATHER.getCode()).eq(ErpProductionMgtWorkorder::getSourceValue, DeliveryOrderDataSourceEnum.PURCHASE_ORDER.getCode()).eq(ErpProductionMgtWorkorder::getSourceGuid, processVO.getBusinessKey()));
        assert workorder != null;
        if (baseMapper.findNextFlow(workorder.getWorkorderNumber()) > 0) {
            throw new FlowException(BaseResultErrorCodeImpl.NEXT_FLOW_FAIL);
        }
        //回写订单状态
        Node node = nodeService.getById(processVO.getBusinessKey());
        if (BeanUtil.isNotEmpty(node)) {
            node.setAuditStatus(BusinessStatusEnum.DRAFT.getStatus());
            nodeService.updateById(node);
        }
        // 获取到货单工单guids
        List<String> workorderGuids = iErpProductionMgtWorkorderService.getBaseMapper().selectList(Wrappers.<ErpProductionMgtWorkorder>lambdaQuery().eq(ErpProductionMgtWorkorder::getWorkorderProperties, WorkorderPropertiesEnum.MATERIAL_DELIVERY_FATHER.getCode()).eq(ErpProductionMgtWorkorder::getSourceValue, DeliveryOrderDataSourceEnum.PURCHASE_ORDER.getCode()).eq(ErpProductionMgtWorkorder::getParentClassificationGuid, workorder.getWorkorderGuid())).stream().map(ErpProductionMgtWorkorder::getWorkorderGuid).collect(Collectors.toList());
        workorderGuids.add(workorder.getWorkorderGuid());
        // 删除到货工单
        iErpProductionMgtWorkorderService.removeBatchByIds(workorderGuids);
        // 删除工单订单数据
        iErpProductionMgtWorkorderOrderDataService.remove(Wrappers.<ErpProductionMgtWorkorderOrderData>lambdaQuery().in(ErpProductionMgtWorkorderOrderData::getWorkorderGuid, workorderGuids));

        // 审核完成后，物料到货单待开数量-1
        iErpFormPendingNumberService.minusOne(WorkflowKeyEnum.WORK_ORDER_MATERIAL_DELIVERY, WorkflowKeyEnum.ORDER_PURCHASE, TableIdentificationEnum.erp_inventory_mgt_material_arrival_fast_wait);

        // 删除创建的预警
        LambdaQueryWrapper<ErpPurchaseMgtPurchaseOrderData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpPurchaseMgtPurchaseOrderData::getOrderGuid, processVO.getBusinessKey());
        wrapper.select(ErpPurchaseMgtPurchaseOrderData::getOrderDataGuid);
        List<ErpPurchaseMgtPurchaseOrderData> purchaseOrderDataList = iErpPurchaseMgtPurchaseOrderDataService.list(wrapper);
        purchaseOrderDataList.forEach(v -> {
            EarlyWarningRemoveEvent earlyWarningEvent = new EarlyWarningRemoveEvent(userApi.getThreadLocalXyMemberDto(), EarlyWarningEnum.PURCHASE_ORDER_OVERDUE, v.getOrderDataGuid());
            earlyWarningEvent.put(EarlyWarningConstant.EARLY_WARNING_FLAG_KEY, EarlyWarningConstant.EARLY_WARNING_FLAG_CANCEL);
            applicationEventPublisher.publishEvent(earlyWarningEvent);
        });

    }

    /**
     * 审核完成事件（生成到货单）
     *
     * @param processVO
     */
    @Override
    public void finishCall(ActEventEntity processVO) {
        ErpPurchaseMgtPurchaseOrder purchaseOrder = baseMapper.selectById(processVO.getBusinessKey());
        //回写订单状态
        Node node = nodeService.getById(purchaseOrder.getOrderGuid());
        if (BeanUtil.isNotEmpty(node)) {
            node.setAuditDate(new Date());
            node.setAuditStatus(BusinessStatusEnum.FINISH.getStatus());
            nodeService.updateById(node);
        }
        assert purchaseOrder != null;
        // 生成大的到货单
        ErpProductionMgtWorkorder entity = new ErpProductionMgtWorkorder();
        entity.setWorkorderNumber(purchaseOrder.getOrderNumber());
        entity.setReceiptDate(purchaseOrder.getReceiptDate());
        entity.setWorkorderProperties(WorkorderPropertiesEnum.MATERIAL_DELIVERY_FATHER.getCode());
        entity.setSourceValue(DeliveryOrderDataSourceEnum.PURCHASE_ORDER.getCode());
        entity.setSourceGuid(purchaseOrder.getOrderGuid());
        iErpProductionMgtWorkorderService.save(entity);
        // 查询采购订单明细，生成到货单明细
        List<ErpPurchaseMgtPurchaseOrderData> purchaseOrderDataList = iErpPurchaseMgtPurchaseOrderDataService.list(Wrappers.<ErpPurchaseMgtPurchaseOrderData>lambdaQuery().eq(ErpPurchaseMgtPurchaseOrderData::getOrderGuid, purchaseOrder.getOrderGuid()));
        // 工单明细数据列表
        List<ErpProductionMgtWorkorder> detailList = new ArrayList<>(purchaseOrderDataList.size());
        // 工单订单数据列表
        List<ErpProductionMgtWorkorderOrderData> workorderOrderDateList = new ArrayList<>(purchaseOrderDataList.size());
        purchaseOrderDataList.forEach(purchaseOrderData -> {
            ErpProductionMgtWorkorder detail = new ErpProductionMgtWorkorder();
            BeanUtils.copyProperties(purchaseOrderData, detail);
            // 默认未完成状态
            //detail.setWorkorderState(CompletionStatusEnum.NOT_FINISH.getCode());
            detail.setSourceGuid(purchaseOrderData.getOrderDataGuid());
            detail.setRequiredDeliveryTime(purchaseOrderData.getDeliveryDate());
            detail.setParentClassificationGuid(entity.getWorkorderGuid());
            // 冗余父工单数据
            detail.setWorkorderNumber(entity.getWorkorderNumber());
            detail.setWorkorderProperties(entity.getWorkorderProperties());
            detail.setSourceValue(entity.getSourceValue());
            // 先设置guid，用于跟工单订单数据相关联
            detail.setWorkorderGuid(IdUtil.fastSimpleUUID());
            detailList.add(detail);
            // 组装工单订单数据
            ErpProductionMgtWorkorderOrderData workorderOrderDate = new ErpProductionMgtWorkorderOrderData();
            workorderOrderDate.setWorkorderGuid(detail.getWorkorderGuid());
            workorderOrderDate.setOrderDataGuid(purchaseOrderData.getOrderDataGuid());
            workorderOrderDateList.add(workorderOrderDate);
            // 生成预警事件
            if (ObjUtil.isNotNull(purchaseOrderData.getDeliveryDate())) {
                EarlyWarningEvent earlyWarningEvent = new EarlyWarningEvent(userApi.getThreadLocalXyMemberDto(), EarlyWarningEnum.PURCHASE_ORDER_OVERDUE, purchaseOrderData.getOrderDataGuid());
                // 将货期传入决策流程
                earlyWarningEvent.put("delivery_date", DateUtil.format(purchaseOrderData.getDeliveryDate(), "yyyy-MM-dd HH:mm"));
                applicationEventPublisher.publishEvent(earlyWarningEvent);
            }
            //审核通过后会写node的第三方id
            Node dataNode = nodeService.getById(purchaseOrderData.getOrderDataGuid());
            if (BeanUtil.isNotEmpty(dataNode)) {
                dataNode.setExtension(detail.getWorkorderGuid());
                nodeService.updateById(dataNode);
            }
        });
        iErpProductionMgtWorkorderService.saveBatch(detailList);
        // 批量保存工单订单数据
        iErpProductionMgtWorkorderOrderDataService.saveBatch(workorderOrderDateList);

        // 审核完成后，物料到货单待开数量+1
        iErpFormPendingNumberService.plusOne(WorkflowKeyEnum.WORK_ORDER_MATERIAL_DELIVERY, WorkflowKeyEnum.ORDER_PURCHASE, TableIdentificationEnum.erp_inventory_mgt_material_arrival_fast_wait);
    }

}
