package xy.server.purchase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.config.excel.EasyExcelDataHandleService;
import xy.server.purchase.entity.ErpSupplierMgtSupplierContact;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierContactRO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierContactVO;

import java.util.List;

/**
 * <p>
 * 供应商联系人 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
public interface IErpSupplierMgtSupplierContactService extends IService<ErpSupplierMgtSupplierContact>, EasyExcelDataHandleService<ErpSupplierMgtSupplierContactRO> {

    /**
     * 保存数据
     * @param roList ro列表
     * @param delGuids 删除的Guids
     * @param supplierGuid 供应商Guid
     * @return
     */
    void saveData(List<ErpSupplierMgtSupplierContactRO> roList, List<String> delGuids, String supplierGuid);

    /**
     * 根据供应商Guid删除数据
     * @param supplierGuid
     * @return
     */
    void delBySupplierGuid(String supplierGuid);

    List<ErpSupplierMgtSupplierContactVO> getDataByIds(List<String> supplierGuids);
}
