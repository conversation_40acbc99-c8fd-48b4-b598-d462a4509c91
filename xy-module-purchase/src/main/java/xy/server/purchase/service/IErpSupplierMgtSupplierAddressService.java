package xy.server.purchase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.config.excel.EasyExcelDataHandleService;
import xy.server.purchase.entity.ErpSupplierMgtSupplierAddress;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierAddressRO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierAddressVO;

import java.util.List;

/**
 * <p>
 * 供应商地址 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
public interface IErpSupplierMgtSupplierAddressService extends IService<ErpSupplierMgtSupplierAddress>, EasyExcelDataHandleService<ErpSupplierMgtSupplierAddressRO> {

    /**
     * 保存数据
     * @param roList ro列表
     * @param delGuids 删除的guids
     * @param supplierGuid 供应商Guid
     * @return
     */
    void saveData(List<ErpSupplierMgtSupplierAddressRO> roList, List<String> delGuids, String supplierGuid);

    /**
     * 根据供应商Guid删除数据
     * @param supplierGuid
     * @return
     */
    void delBySupplierGuid(String supplierGuid);

    List<ErpSupplierMgtSupplierAddressVO> getDataByIds(List<String> supplierGuids);
}
