package xy.server.purchase.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.basic.entity.OrganizationContact;
import com.xunyue.basic.entity.model.dto.OrganizationContactDTO;
import com.xunyue.basic.service.IOrganizationContactService;
import com.xunyue.common.i18n.BaseResultErrorCodeImpl;
import com.xunyue.config.exception.FlowException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import xy.server.purchase.entity.ErpSupplierMgtSupplierContact;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierContactRO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierContactVO;
import xy.server.purchase.mapper.ErpSupplierMgtSupplierContactMapper;
import xy.server.purchase.service.IErpSupplierMgtSupplierContactService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 供应商联系人 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Service
public class ErpSupplierMgtSupplierContactServiceImpl extends ServiceImpl<ErpSupplierMgtSupplierContactMapper, ErpSupplierMgtSupplierContact> implements IErpSupplierMgtSupplierContactService {

    @Resource
    private IOrganizationContactService organizationContactService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveData(List<ErpSupplierMgtSupplierContactRO> roList, List<String> delGuids, String supplierGuid) {
        // 先处理删除的数据
        if (!CollectionUtils.isEmpty(delGuids)) {
            baseMapper.deleteBatchIds(delGuids);
        }

        QueryWrapper<OrganizationContact> organizationContactQueryWrapper = new QueryWrapper<>();
        organizationContactQueryWrapper.eq("organization_id", supplierGuid);
        organizationContactService.remove(organizationContactQueryWrapper);

        // 再处理新增和编辑的数据
        if (!CollectionUtils.isEmpty(roList)) {
            List<ErpSupplierMgtSupplierContact> entityList = new ArrayList<>();
            List<OrganizationContactDTO> organizationContactDTOList = new ArrayList<>();
            for (int i = 0; i < roList.size(); i++) {
                ErpSupplierMgtSupplierContact entity = new ErpSupplierMgtSupplierContact();
                BeanUtils.copyProperties(roList.get(i), entity);
                entity.setSupplierGuid(supplierGuid);
                entity.setSerialNumber(i + 1);
                entityList.add(entity);


                OrganizationContactDTO organizationContactDTO = new OrganizationContactDTO();
                organizationContactDTO.setOrganizationContactTenantId(entity.getTenantGuid());
                organizationContactDTO.setOrganizationContactId(entity.getSupplierContactGuid());
                organizationContactDTO.setOrganizationContactOrganizationId(entity.getSupplierGuid());
                organizationContactDTO.setOrganizationContactTelephone(entity.getTelephone());
                organizationContactDTO.setOrganizationContactMobilePhone(entity.getMobilephone());
                organizationContactDTO.setOrganizationContactEmail(entity.getEmail());
                organizationContactDTO.setOrganizationContactFax(entity.getFax());
                organizationContactDTO.setOrganizationContactQqNumber(entity.getQqNumber());
                organizationContactDTO.setOrganizationContactWechatNumber(entity.getWechatNumber());
                organizationContactDTO.setOrganizationContactIsDefault(entity.getIsDefault());
                organizationContactDTO.setOrganizationContactDescription(entity.getDescription());
                organizationContactDTOList.add(organizationContactDTO);
            }
            super.saveOrUpdateBatch(entityList);

            organizationContactService.saveOrUpdateBatch(organizationContactDTOList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delBySupplierGuid(String supplierGuid) {
        if (!StringUtils.isEmpty(supplierGuid)) {
            super.remove(Wrappers.<ErpSupplierMgtSupplierContact>lambdaQuery().eq(ErpSupplierMgtSupplierContact::getSupplierGuid, supplierGuid));
        }
    }

    @Override
    public List<ErpSupplierMgtSupplierContactVO> getDataByIds(List<String> supplierGuids) {
        if (CollUtil.isEmpty(supplierGuids)) {
            return new ArrayList<>();
        }
        return baseMapper.getDataBySupplierGuids(supplierGuids);
    }

    /**
     * 处理导入Excel数据
     *
     * @param ro
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcelDataHandle(ErpSupplierMgtSupplierContactRO ro) {
        // 处理供应商
        if (!StringUtils.isEmpty(ro.getSupplierGuid())) {
            // 根据供应商编码查询供应商guid
            String supplierGuid = baseMapper.getSupplierGuidByCode(ro.getSupplierGuid());
            if (StringUtils.isEmpty(supplierGuid)) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "供应商【" + ro.getSupplierGuid() + "】不存在！");
            }
            ro.setSupplierGuid(supplierGuid);
        }
        // 处理是否默认
        if (ro.getIsDefault() != null && ro.getIsDefault()) {
            super.update(Wrappers.<ErpSupplierMgtSupplierContact>lambdaUpdate().set(ErpSupplierMgtSupplierContact::getIsDefault, false)
                    .eq(ErpSupplierMgtSupplierContact::getSupplierGuid, ro.getSupplierGuid()));
        }
        // 保存数据到数据库
        ErpSupplierMgtSupplierContact entity = new ErpSupplierMgtSupplierContact();
        BeanUtils.copyProperties(ro, entity);
        super.save(entity);
    }
}
