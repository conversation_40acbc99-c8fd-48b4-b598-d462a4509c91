package xy.server.purchase.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseRequisition;
import xy.server.purchase.entity.model.qo.ErpPurchaseMgtPurchaseRequisitionQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingDetaiStopRO;
import xy.server.purchase.entity.model.ro.ErpPurchaseMgtPurchaseRequisitionRO;
import xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseRequisitionVO;
import xy.server.purchase.entity.model.vo.ErpPurchaseSourceRequisitionVO;

import java.util.List;

/**
 * <AUTHOR>
 * @data 2023/9/13 10:51
 * @apiNote 采购申请单service
 */
public interface IErpPurchaseMgtPurchaseRequisitionService extends IService<ErpPurchaseMgtPurchaseRequisition> {
    ErpPurchaseMgtPurchaseRequisitionRO create(ErpPurchaseMgtPurchaseRequisitionRO ro);

    boolean update(ErpPurchaseMgtPurchaseRequisitionRO ro);

    List<String> saveDate(InsertOrUpdateList<ErpPurchaseMgtPurchaseRequisitionRO> dataList);

    boolean delete(String workorderGuid);

    boolean deleteByBatch(List<String> ids);

    ErpPurchaseMgtPurchaseRequisitionVO getOneById(String workorderGuid);

    List<ErpPurchaseMgtPurchaseRequisitionVO> findList(ErpPurchaseMgtPurchaseRequisitionQO qo);

    IPage<ErpPurchaseMgtPurchaseRequisitionVO> findPage(PageParams<ErpPurchaseMgtPurchaseRequisitionQO> pageParams);

    /**
     * 待开列表查询（只显示采购申请明细数据）
     * @param qo
     * @return
     */
    List<ErpPurchaseMgtPurchaseRequisitionVO> findNotBilledList(ErpPurchaseMgtPurchaseRequisitionQO qo);

    /**
     * 待开列表查询（包含主工单）
     * @param qo
     * @return
     */
    List<ErpPurchaseMgtPurchaseRequisitionVO> findNotBilledTree(ErpPurchaseMgtPurchaseRequisitionQO qo);

    /**
     * 采购申请数据源查询
     * @param qo
     * @return
     */
    List<ErpPurchaseSourceRequisitionVO> waitingFindList(ErpPurchaseMgtPurchaseRequisitionQO qo);

    /**
     * 终止单据
     * @param list
     * @return
     */
    Boolean stopOrRollback(List<ErpOutgoingDetaiStopRO> list);

    /**
     * 生成采购申请单
     * @param qo
     * @return
     */
    Boolean generatePurchaseRequisition(ErpPurchaseMgtPurchaseRequisitionQO qo);

    /**
     *包含业务预测以及采购申请的数据
     * @param qo
     * @return
     */
    List<ErpPurchaseMgtPurchaseRequisitionVO> findNotBilledListES(ErpPurchaseMgtPurchaseRequisitionQO qo);

    /**
     * 包含业务预测以及采购申请的数据待开列表查询（包含主工单）
     * @param qo
     * @return
     */
    List<ErpPurchaseMgtPurchaseRequisitionVO> findNotBilledTreeES(ErpPurchaseMgtPurchaseRequisitionQO qo);

    void syncRequistionToProof(ErpPurchaseMgtPurchaseRequisition entity, List<ErpPurchaseMgtPurchaseRequisition> purchaseRequisitionList,Boolean isSave);


    void syncDeleteRequistionToProof(List<String> ids);

    void updateNotCheck(ErpPurchaseMgtPurchaseRequisitionRO purchaseRequisitionRO);
}
