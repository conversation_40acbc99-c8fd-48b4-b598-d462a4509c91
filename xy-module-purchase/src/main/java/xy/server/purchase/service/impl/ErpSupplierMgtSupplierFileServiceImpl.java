package xy.server.purchase.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.basic.entity.OrganizationFile;
import com.xunyue.basic.entity.model.dto.OrganizationFileDTO;
import com.xunyue.basic.service.IOrganizationFileService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import xy.server.purchase.entity.ErpSupplierMgtSupplierFile;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierFileRO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierFileVO;
import xy.server.purchase.mapper.ErpSupplierMgtSupplierFileMapper;
import xy.server.purchase.service.IErpSupplierMgtSupplierFileService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 供应商文件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Service
public class ErpSupplierMgtSupplierFileServiceImpl extends ServiceImpl<ErpSupplierMgtSupplierFileMapper, ErpSupplierMgtSupplierFile> implements IErpSupplierMgtSupplierFileService {

    @Resource
    private IOrganizationFileService organizationFileService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveData(List<ErpSupplierMgtSupplierFileRO> roList, List<String> delGuids, String supplierGuid) {
        // 先处理删除的数据
        if (!CollectionUtils.isEmpty(delGuids)) {
            baseMapper.deleteBatchIds(delGuids);
        }

        // 双写删除
        QueryWrapper<OrganizationFile> organizationFileQueryWrapper = new QueryWrapper<>();
        organizationFileQueryWrapper.eq("organization_id", supplierGuid);
        organizationFileService.remove(organizationFileQueryWrapper);

        // 再处理新增和编辑的数据
        if (!CollectionUtils.isEmpty(roList)) {
            List<ErpSupplierMgtSupplierFile> entityList = new ArrayList<>();
            List<OrganizationFileDTO> fileDTOList = new ArrayList<>();
            for (int i = 0; i < roList.size(); i++) {
                ErpSupplierMgtSupplierFile entity = new ErpSupplierMgtSupplierFile();
                BeanUtils.copyProperties(roList.get(i), entity);
                entity.setSupplierGuid(supplierGuid);
                entity.setSerialNumber(i + 1);
                entityList.add(entity);

                OrganizationFileDTO organizationFileDTO = new OrganizationFileDTO();
                organizationFileDTO.setOrganizationFileTenantId(entity.getTenantGuid());
                organizationFileDTO.setOrganizationFileOrganizationId(entity.getSupplierGuid());
                organizationFileDTO.setOrganizationFileId(entity.getSupplierFileGuid());
                organizationFileDTO.setOrganizationFileFileType(entity.getFileType());
                organizationFileDTO.setOrganizationFileFileId(entity.getFileGuid());
                organizationFileDTO.setOrganizationFileDescription(entity.getDescription());
                fileDTOList.add(organizationFileDTO);
            }
            super.saveOrUpdateBatch(entityList);

            organizationFileService.saveOrUpdateBatch(fileDTOList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delBySupplierGuid(String supplierGuid) {
        if (!StringUtils.isEmpty(supplierGuid)) {
            super.remove(Wrappers.<ErpSupplierMgtSupplierFile>lambdaQuery().eq(ErpSupplierMgtSupplierFile::getSupplierGuid, supplierGuid));
        }
    }

    @Override
    public List<ErpSupplierMgtSupplierFileVO> getDataByIds(List<String> supplierGuids) {
        if(CollUtil.isEmpty(supplierGuids)){
            return new ArrayList<>();
        }
        return baseMapper.getDataBySupplierGuids(supplierGuids);
    }
}
