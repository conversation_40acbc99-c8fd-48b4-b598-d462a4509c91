package xy.server.purchase.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.excel.EasyExcelDataHandleService;
import com.xunyue.config.util.PageParams;
import org.springframework.web.multipart.MultipartFile;
import xy.server.dto.XyMemberDto;
import xy.server.purchase.entity.ErpSupplierMgtSupplier;
import xy.server.purchase.entity.model.qo.ErpSupplierMgtSupplierQO;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierRO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 * 供应商 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
public interface IErpSupplierMgtSupplierService extends IService<ErpSupplierMgtSupplier>, EasyExcelDataHandleService<ErpSupplierMgtSupplierRO> {
    ErpSupplierMgtSupplier create(ErpSupplierMgtSupplierRO ro);

    boolean delete(String supplierGuid);


    boolean deleteByBatch(List<String> ids);

    ErpSupplierMgtSupplier update(ErpSupplierMgtSupplierRO ro);

    ErpSupplierMgtSupplierVO getById(String supplierGuid);

    List<ErpSupplierMgtSupplierVO> findList(ErpSupplierMgtSupplierQO qo, String tenantGuid);

    IPage<ErpSupplierMgtSupplierVO> findPage(PageParams<ErpSupplierMgtSupplierQO> pageParams);

    List<ErpSupplierMgtSupplier> saveDate(InsertOrUpdateList<ErpSupplierMgtSupplierRO> dataList, String tenantGuid);

    /**
     * 查询已审核的供应商列表
     * @param qo
     * @return
     */
    List<ErpSupplierMgtSupplierVO> selectList(ErpSupplierMgtSupplierQO qo);

    /**
     * 下拉快选择供应商
     * @return
     */
    List<ErpSupplierMgtSupplier> dropDownSelect();

    /**
     * 下载Excel模板
     * @param response
     */
    void downloadTemplate(HttpServletResponse response);

    /**
     * 导入Excel
     * @param file
     * @return
     */
    Boolean importExcel(MultipartFile file, boolean isErrorResume);

    /**
     * 批量获取供应商数据
     * @param supplierGuids
     * @return
     */
    List<ErpSupplierMgtSupplierVO> getDataByIds(List<String> supplierGuids);

    /**
     * 批量获取供应商数据，返回Map
     * @param supplierGuids
     * @return
     */
    Map<String , ErpSupplierMgtSupplierVO> getDataByIdsMap(List<String> supplierGuids);

    /**
     * 异步批量获取供应商数据，返回Map
     * @param supplierGuids
     * @return
     */
    CompletableFuture<Map<String , ErpSupplierMgtSupplierVO>> getDataByIdsMapAsync(List<String> supplierGuids , XyMemberDto xyMemberDto);
}
