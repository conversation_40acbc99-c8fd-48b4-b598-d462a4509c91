package xy.server.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.basic.entity.model.dto.CustomerClassificationDTO;
import com.xunyue.basic.entity.model.dto.SupplierClassificationDTO;
import com.xunyue.basic.entity.model.dto.SupplierDTO;
import com.xunyue.basic.service.ISupplierClassificationService;
import com.xunyue.basic.service.ISupplierService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.common.i18n.BaseResultErrorCodeImpl;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import com.xunyue.node.common.enums.ClazzEnum;
import com.xunyue.tenant.sign.UserApi;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import xy.server.dto.XyMemberDto;
import xy.server.purchase.entity.ErpSupplierMgtSupplierClassification;
import xy.server.purchase.entity.model.qo.ErpSupplierMgtSupplierClassificationQO;
import xy.server.purchase.entity.model.ro.ErpSupplierMgtSupplierClassificationRO;
import xy.server.purchase.entity.model.vo.ErpSupplierMgtSupplierClassificationVO;
import xy.server.purchase.i18n.ResultErrorCode;
import xy.server.purchase.mapper.ErpSupplierMgtSupplierClassificationMapper;
import xy.server.purchase.service.IErpSupplierMgtSupplierClassificationService;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service
public class ErpSupplierMgtSupplierClassificationServiceImpl extends ServiceImpl<ErpSupplierMgtSupplierClassificationMapper, ErpSupplierMgtSupplierClassification> implements IErpSupplierMgtSupplierClassificationService{

    @Autowired
    public UserApi userApi;
    @Autowired
    public ISupplierClassificationService supplierClassificationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpSupplierMgtSupplierClassificationRO ro) {
        this.saveOrUpdateVerify(ro);

        ErpSupplierMgtSupplierClassification entity = new ErpSupplierMgtSupplierClassification();
        BeanUtil.copyProperties(ro, entity);
        boolean save = super.save(entity);
        //异步双写
        XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();
        ThreadUtil.execAsync(() -> {
            userApi.setThreadLocal(memberDto);
            SupplierClassificationDTO customerClassificationDTO=new SupplierClassificationDTO();
            this.savecustomerClassificationService(entity,customerClassificationDTO);
            supplierClassificationService.save(customerClassificationDTO);
        });
        return save;
    }


    /**
     * 双写
     * @param entity
     * @param customerClassificationDTO
     */
    private void savecustomerClassificationService(ErpSupplierMgtSupplierClassification entity, SupplierClassificationDTO customerClassificationDTO) {
        customerClassificationDTO.setSupplierClassificationId(entity.getSupplierClassificationGuid());
        customerClassificationDTO.setSupplierClassificationClazz(ClazzEnum.SUPPLIER_CLASSIFICATION.name());
        customerClassificationDTO.setSupplierClassificationName(entity.getSupplierClassificationName());
        customerClassificationDTO.setSupplierClassificationIsBanned(!entity.getState());
        customerClassificationDTO.setSupplierClassificationDescription(entity.getDescription());
        customerClassificationDTO.setSupplierClassificationIsolation(entity.getTenantGuid());
        customerClassificationDTO.setSupplierClassificationSupplierClassificationAttributeValue(entity.getSupplierClassificationAttributeValue());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String supplierClassificationGuid) {
        ErpSupplierMgtSupplierClassification entity = baseMapper.selectById(supplierClassificationGuid);
        // 数据不存在
        if (Objects.isNull(entity)) {
            throw new FlowException(BaseResultErrorCodeImpl.NO_REPETITION_DATA);
        }
        // 数据在使用，不能删除
        if (entity.getIsUsed()) {
            throw new FlowException(ResultErrorCode.DELETE_IS_USED);
        }
        supplierClassificationService.removeById(supplierClassificationGuid);
        return removeAll(supplierClassificationGuid);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> ids) {
        for (String id : ids) {
            ErpSupplierMgtSupplierClassification entity = baseMapper.selectById(id);
            // 数据不存在
            if (Objects.isNull(entity)) {
                throw new FlowException(BaseResultErrorCodeImpl.NO_REPETITION_DATA);
            }
            // 数据在使用，不能删除
            if (entity.getIsUsed()) {
                throw new FlowException(ResultErrorCode.DELETE_BY_BATCH_IS_USED);
            }

        }
        return super.removeByIds(ids);
    }

    /**
     * 判断删除自身及其子级
     * @param pGuid
     * @return
     */
    private boolean removeAll(String pGuid) {
        List<ErpSupplierMgtSupplierClassification> childrenList = baseMapper.getListByParentClassificationGuid(pGuid);
        // 过滤掉【已被使用】的数据
        List<String> ids = childrenList.stream().filter(entity -> !entity.getIsUsed())
                .map(vo -> vo.getSupplierClassificationGuid())
                .collect(Collectors.toList());
        if (ids.size() != childrenList.size()) {
            throw new FlowException(ResultErrorCode.DELETE_CHILDEREN_IS_USED);
        }

        return super.removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpSupplierMgtSupplierClassificationRO ro) {
        // 查找旧数据
        ErpSupplierMgtSupplierClassification oldEntity = baseMapper.selectById(ro.getSupplierClassificationGuid());
        // 数据不存在
        if (Objects.isNull(oldEntity)) {
            throw new FlowException(BaseResultErrorCodeImpl.NO_REPETITION_DATA);
        }

        this.saveOrUpdateVerify(ro);

        ErpSupplierMgtSupplierClassification entity = new ErpSupplierMgtSupplierClassification();
        BeanUtil.copyProperties(ro, entity);
        boolean b = super.updateById(entity);
        //异步双写
        XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();
        ThreadUtil.execAsync(() -> {
            userApi.setThreadLocal(memberDto);
            SupplierClassificationDTO customerClassificationDTO=new SupplierClassificationDTO();
            this.savecustomerClassificationService(entity,customerClassificationDTO);
            supplierClassificationService.save(customerClassificationDTO);
        });
        return b;
    }

    @Override
    public ErpSupplierMgtSupplierClassificationVO getById(String supplierClassificationGuid) {
        ErpSupplierMgtSupplierClassificationVO vo = baseMapper.getDataByGuid(supplierClassificationGuid);
        // 数据不存在
        if (Objects.isNull(vo)) {
            throw new FlowException(BaseResultErrorCodeImpl.NO_REPETITION_DATA);
        }
        return vo;
    }

    @Override
    public List<ErpSupplierMgtSupplierClassificationVO> findList(ErpSupplierMgtSupplierClassificationQO qo, String tenantGuid) {
        if (!StringUtils.isEmpty(qo.getKeyword())) {
            qo.setKeywords(Arrays.asList(qo.getKeyword().split(" ")));
        }
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpSupplierMgtSupplierClassificationVO> findPage(PageParams<ErpSupplierMgtSupplierClassificationQO> pageParams) {
        IPage<ErpSupplierMgtSupplierClassificationVO> page = pageParams.buildPage();
        ErpSupplierMgtSupplierClassificationQO model = pageParams.getModel();
        if (!StringUtils.isEmpty(model.getKeyword())) {
            model.setKeywords(Arrays.asList(model.getKeyword().split(" ")));
        }
        return baseMapper.findPage(page, model);
    }

    /**
     * 重新排序
     *
     * @param entityDtoList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reordered(List<SortEntityDto> entityDtoList) {
        entityDtoList.forEach(sortEntityDto -> {
            LambdaUpdateWrapper<ErpSupplierMgtSupplierClassification> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ErpSupplierMgtSupplierClassification::getSerialNumber, sortEntityDto.getSerialNumber());
            updateWrapper.eq(ErpSupplierMgtSupplierClassification::getSupplierClassificationGuid, sortEntityDto.getGuid());
            update(updateWrapper);
        });
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveDate(InsertOrUpdateList<ErpSupplierMgtSupplierClassificationRO> dataList, String tenantGuid) {
        if (!CollectionUtils.isEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(data -> {
                data.setTenantGuid(tenantGuid);
                this.create(data);
            });
        }

        if (!CollectionUtils.isEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    /**
     * 批量获取数据
     * @param ids
     * @return
     */
    @Override
    public List<ErpSupplierMgtSupplierClassificationVO> getDataByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return baseMapper.getDataByGuids(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcelDataHandle(ErpSupplierMgtSupplierClassificationRO ro) {
        if (!StringUtils.isEmpty(ro.getParentClassificationGuid())) {
            // 根据名称获取guid
            ErpSupplierMgtSupplierClassification pEntity = baseMapper.selectOne(Wrappers.<ErpSupplierMgtSupplierClassification>lambdaQuery()
                    .select(ErpSupplierMgtSupplierClassification::getSupplierClassificationGuid)
                    .eq(ErpSupplierMgtSupplierClassification::getSupplierClassificationName, ro.getParentClassificationGuid()));
            if (pEntity == null || StringUtils.isEmpty(pEntity.getSupplierClassificationGuid())) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "父级供应商分类【" + ro.getParentClassificationGuid() + "】不存在！");
            }
            ro.setParentClassificationGuid(pEntity.getSupplierClassificationGuid());
        }
        // 保存数据到数据库
        this.create(ro);
    }


    /**
     * 新增和修改时字段校验
     * @param ro
     */
    private void saveOrUpdateVerify(ErpSupplierMgtSupplierClassificationRO ro) {
        // 判断名称是否重复
        LambdaQueryWrapper<ErpSupplierMgtSupplierClassification> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ErpSupplierMgtSupplierClassification::getSupplierClassificationName, ro.getSupplierClassificationName())
                .ne(ro.getSupplierClassificationGuid() != null, ErpSupplierMgtSupplierClassification::getSupplierClassificationGuid, ro.getSupplierClassificationGuid());
        if (baseMapper.exists(queryWrapper)) {
            throw new FlowException(ResultErrorCode.REPETITION_SUPPLIER_CLASSIFICATION_NAME);
        }
    }
}
