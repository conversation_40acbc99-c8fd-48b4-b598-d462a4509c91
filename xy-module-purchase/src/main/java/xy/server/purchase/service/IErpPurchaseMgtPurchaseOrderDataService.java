package xy.server.purchase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseOrderData;
import xy.server.purchase.entity.model.ro.ErpPurchaseMgtPurchaseOrderDataRO;
import xy.server.purchase.entity.model.vo.ErpPurchaseMgtBusinessOrderDataVO;
import xy.server.purchase.entity.model.vo.ErpPurchaseMgtPurchaseOrderDataVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 订单数据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
public interface IErpPurchaseMgtPurchaseOrderDataService extends IService<ErpPurchaseMgtPurchaseOrderData> {

    /**
     * 保存采购订单明细
     * @param orderGuid 订单表GUID
     * @param detailList 明细列表
     * @param delDetailIds 删除ids
     */
    void saveDate(String orderGuid, List<ErpPurchaseMgtPurchaseOrderDataRO> detailList, List<String> delDetailIds);

    /**
     * 根据订单guid删除数据
     * @param orderGuid
     */
    void delByOrderGuid(String orderGuid);

    List<ErpPurchaseMgtPurchaseOrderDataVO> getDataByOrderGuids(List<String> orderGuids);

    /**
     * 根据采购订单明细guids获取业务订单数据Map（采购成品流程链路）
     * @param purchaseOrderDataGuids
     * @return
     */
    Map<String, ErpPurchaseMgtBusinessOrderDataVO> getBusinessOrderDataMapByPurchaseOrderDataGuids(List<String> purchaseOrderDataGuids);
}
