package xy.server.purchase.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.config.util.PageParams;
import xy.server.purchase.entity.ErpOutgoingDataProcess;
import xy.server.purchase.entity.model.qo.ErpOutgoingDataProcessQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingDataProcessRO;
import xy.server.purchase.entity.model.vo.ErpOutgoingDataProcessVO;

import java.util.List;

/**
 * <p>
 * 工单数据表工序工单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
public interface IErpOutgoingDataProcessService extends IService<ErpOutgoingDataProcess> {
                    boolean create(ErpOutgoingDataProcessRO ro);

    boolean delete(String workorderDataProcessGuid);

    boolean update(ErpOutgoingDataProcessRO ro);

    ErpOutgoingDataProcessVO getById(String workorderDataProcessGuid);

    List<ErpOutgoingDataProcessVO> findList(ErpOutgoingDataProcessQO qo, String tenantGuid);

    IPage<ErpOutgoingDataProcessVO> findPage(PageParams<ErpOutgoingDataProcessQO> pageParams);
}
