package xy.server.purchase.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.vo.SourceDescriptionAndPictureVO;
import com.xunyue.config.util.PageParams;
import xy.server.purchase.entity.ErpPurchaseMgtPurchaseOrder;
import xy.server.purchase.entity.model.qo.ErpGetNewDataQO;
import xy.server.purchase.entity.model.qo.ErpPurchaseMgtPurchaseOrderQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingDetaiStopRO;
import xy.server.purchase.entity.model.ro.ErpPurchaseMgtPurchaseOrderRO;
import xy.server.purchase.entity.model.ro.ErpUpdateToJsonRO;
import xy.server.purchase.entity.model.ro.ModifyMaterialAndSyncWorkOrderRO;
import xy.server.purchase.entity.model.vo.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 采购订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
public interface IErpPurchaseMgtPurchaseOrderService extends IService<ErpPurchaseMgtPurchaseOrder> {
    ErpPurchaseMgtPurchaseOrder create(ErpPurchaseMgtPurchaseOrderRO ro);

    ErpPurchaseMgtPurchaseOrder update(ErpPurchaseMgtPurchaseOrderRO ro);

    List<ErpPurchaseMgtPurchaseOrder> saveDate(InsertOrUpdateList<ErpPurchaseMgtPurchaseOrderRO> dataList);

    boolean delete(String orderGuid);

    boolean deleteByBatch(List<String> ids);

    ErpPurchaseMgtPurchaseOrderVO getById(String orderGuid);

    List<ErpPurchaseMgtPurchaseOrderVO> findList(ErpPurchaseMgtPurchaseOrderQO qo);

    IPage<ErpPurchaseMgtPurchaseOrderVO> findPage(PageParams<ErpPurchaseMgtPurchaseOrderQO> pageParams);

    /**
     * 采购订单-父到货工单待开列表查询（只显示明细数据）
     * @param qo
     * @return
     */
    List<ErpPurchaseInventoryWorkorderDetailVO> findNotBilledList(ErpPurchaseMgtPurchaseOrderQO qo);

    /**
     * 采购订单-父到货工单待开列表查询（包含主工单）
     * @param qo
     * @return
     */
    List<ErpPurchaseInventoryWorkorderVO> findNotBilledTree(ErpPurchaseMgtPurchaseOrderQO qo);

    /**
     * 终止单据
     * @param list
     * @return
     */
    Boolean stopOrRollback(List<ErpOutgoingDetaiStopRO> list);

    /**
     * 获取顶级来源备注和图片
     * @param sourceGuids
     * @return
     */
    List<SourceDescriptionAndPictureVO> getSourceDescriptionAndPicture(List<String> sourceGuids);

    /**
     * 分页查看历史单价列表
     * @param pageParams
     * @return
     */
    IPage<ErpPurchaseMgtHistoricalUnitPriceVO> selectHistoricalUnitPricePage(PageParams<ErpPurchaseMgtPurchaseOrderQO> pageParams);

    /**
     * 设置最近到货价格
     * @param dtoList
     * @return
     */
    List<ErpPurchaseMgtSetTheLatestArrivalPriceDTO> setTheLatestArrivalPrice(List<ErpPurchaseMgtSetTheLatestArrivalPriceDTO> dtoList);

    /**
     * 修改物料并同步
     * @param modifyMaterialAndSyncWorkOrderRO
     * @return
     */
    Boolean modifyMaterialAndSyncWorkOrder(ModifyMaterialAndSyncWorkOrderRO modifyMaterialAndSyncWorkOrderRO);

    /**
     * 根据采购申请来源工单数据Guid查询工序工单等信息
     * @param workorderGuids
     * @return
     */
    List<PurchaseRequisitionWorkOrderDataVO> getPurchaseRequisitionWorkOrderData(List<String> workorderGuids);
    /**
     * 获取某个客户的某个物料最近的每件数量
     * @param qo
     * @return
     */
    BigDecimal getNewData(ErpGetNewDataQO qo);

    /**
     * 修改采购订单明细tojson
     * @param list
     * @return
     */
    Boolean updateToJson(List<ErpUpdateToJsonRO> list);
}
