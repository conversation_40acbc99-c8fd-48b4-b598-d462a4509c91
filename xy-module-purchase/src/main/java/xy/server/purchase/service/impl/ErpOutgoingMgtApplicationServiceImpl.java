package xy.server.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.enums.*;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.common.util.LoadDataUtils;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import com.xunyue.exteriororder.entity.model.dto.OutApplyDTO;
import com.xunyue.exteriororder.entity.model.dto.OutApplyDetDTO;
import com.xunyue.exteriororder.service.IOutApplyDetService;
import com.xunyue.exteriororder.service.IOutApplyService;
import com.xunyue.node.common.enums.ClazzEnum;
import com.xunyue.node.common.enums.TypeEnum;
import com.xunyue.node.entity.model.dto.LinkDTO;
import com.xunyue.node.service.ILinkService;
import com.xunyue.tenant.sign.UserApi;
import com.xunyue.bom.service.IBomNodeQuantityService;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.activiti.common.enums.BusinessStatusEnum;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.service.IErpFormPendingNumberService;
import xy.server.activiti.service.IProcessInstanceService;
import xy.server.activiti.utils.actEvent.ActEventEntity;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.basic.entity.ErpBasicMgtProductionProcessesType;
import xy.server.basic.entity.model.vo.ErpWorkorderDataProcessParameterVO;
import xy.server.basic.mapper.ErpBasicMgtProductionProcessesTypeMapper;
import xy.server.basic.service.IErpBasicMgtProductionProcessesTypeService;
import xy.server.dto.XyMemberDto;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;
import xy.server.material.mapper.ErpMaterialMgtMaterialMapper;
import xy.server.material.service.IErpMaterialMgtMaterialService;
import xy.server.purchase.entity.ErpOutgoingDataProcess;
import xy.server.purchase.entity.model.qo.ErpOutgoingMgtApplicationQO;
import xy.server.purchase.entity.model.qo.ErpOutgoingQO;
import xy.server.purchase.entity.model.ro.ErpOutgoingApplicationFormRO;
import xy.server.purchase.entity.model.ro.ErpOutgoingDetaiStopRO;
import xy.server.purchase.entity.model.ro.ErpOutgoingMgtApplicationRO;
import xy.server.purchase.entity.model.vo.ErpOutgoingApplicationFormVO;
import xy.server.purchase.entity.model.vo.ErpOutgoingApplicationVO;
import xy.server.purchase.i18n.ResultErrorCode;
import xy.server.purchase.mapper.ErpOutgoingDataProcessMapper;
import xy.server.purchase.mapper.ErpOutgoingMgtApplicationMapper;
import xy.server.purchase.service.IErpOutgoingMgtApplicationService;
import xy.server.work.entity.ErpProductionMgtWorkorder;
import xy.server.work.entity.model.dto.BaseMapContainer;
import xy.server.work.entity.model.vo.ErpProcessProductionStatusVO;
import xy.server.work.mapper.ErpProductionMgtWorkorderMapper;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <p>
 * 工单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Service("WORK_ORDER.OUTGOING")
public class ErpOutgoingMgtApplicationServiceImpl extends ServiceImpl<ErpOutgoingMgtApplicationMapper, ErpProductionMgtWorkorder> implements IErpOutgoingMgtApplicationService, ActEventStrategyService {

    @Autowired
    private IErpSystemMgtOrderSerialNumberService erpSystemMgtOrderSerialNumberService;
    @Autowired
    private IErpBasicMgtProductionProcessesTypeService productionProcessesTypeService;
    @Resource
    private ErpBasicMgtProductionProcessesTypeMapper erpBasicMgtProductionProcessesTypeMapper;
    @Autowired
    private ErpOutgoingDataProcessMapper erpOutgoingDataProcessMapper;
    @Autowired
    private IProcessInstanceService iProcessInstanceService;
    @Autowired
    private IErpFormPendingNumberService pendingNumberService;
    @Autowired
    private ErpMaterialMgtMaterialMapper materialMapper;
    @Resource
    private IErpMaterialMgtMaterialService materialService;
    @Autowired
    private ErpOutgoingMgtApplicationMapper erpOutgoingMgtApplicationMapper;
    @Autowired
    private ErpProductionMgtWorkorderMapper erpProductionMgtWorkorderMapper;
    @Autowired
    private IOutApplyService outApplyService;
    @Autowired
    private ILinkService linkService;
    @Autowired
    private IOutApplyDetService outApplyDetService;
    @Autowired
    private IBomNodeQuantityService bomNodeQuantityService;


    @Resource
    private UserApi userApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ErpOutgoingMgtApplicationRO create(ErpOutgoingMgtApplicationRO ro) {
        //生成工单表
        //1-将数据插入工单表
        ErpProductionMgtWorkorder erpProductionMgtWorkorder = new ErpProductionMgtWorkorder();
        BeanUtil.copyProperties(ro, erpProductionMgtWorkorder);
        //根据方法生成主工单的工单号
        //根据表单id获取关键字以及id值
        String orderNumber = erpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.WORK_ORDER_OUTGOING);
        erpProductionMgtWorkorder.setWorkorderNumber(orderNumber);
        erpProductionMgtWorkorder.setWorkorderState(ErpOutgoingApplicationStatusEnum.NOT_OUTGOING.getCode());
        erpProductionMgtWorkorder.setWorkorderProperties(WorkorderPropertiesEnum.OUTSOURCE.getCode());
        baseMapper.insert(erpProductionMgtWorkorder);
        //异步双写
        XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();
        ThreadUtil.execAsync(() -> {
            userApi.setThreadLocal(memberDto);
            OutApplyDTO outApplyDTO = new OutApplyDTO();
            this.saveOutApply(erpProductionMgtWorkorder, outApplyDTO);
            outApplyService.save(outApplyDTO);
        });
        ro.setWorkorderNumber(erpProductionMgtWorkorder.getWorkorderNumber());
        String workorderGuid = erpProductionMgtWorkorder.getWorkorderGuid();
        //插入子工单数据
        ChildrenWorkData(ro, workorderGuid, true);
        if (!BusinessStatusEnum.FINISH.getStatus().equals(ro.getAuditStatus())) {
            // 启动审核流程
            iProcessInstanceService.start(WorkflowKeyEnum.WORK_ORDER_OUTGOING, workorderGuid);
        }
        ro.setWorkorderGuid(workorderGuid);
        return ro;
    }

    private void saveOutApply(ErpProductionMgtWorkorder erpProductionMgtWorkorder, OutApplyDTO outApplyDTO) {
        outApplyDTO.setOutApplyUserCode(erpProductionMgtWorkorder.getWorkorderNumber());
        outApplyDTO.setOutApplyIsolation(erpProductionMgtWorkorder.getTenantGuid());
        outApplyDTO.setOutApplyReceiptType(erpProductionMgtWorkorder.getWorkorderTypeGuid());
        outApplyDTO.setOutApplyId(erpProductionMgtWorkorder.getWorkorderGuid());
        outApplyDTO.setOutApplyClazz(ClazzEnum.OUT_APPLY.name());
        outApplyDTO.setOutApplyIsolation(erpProductionMgtWorkorder.getTenantGuid());
        outApplyDTO.setOutApplyTenantId(erpProductionMgtWorkorder.getTenantGuid());
        outApplyDTO.setOutApplyReceiptDate(ObjectUtil.isNotEmpty(erpProductionMgtWorkorder.getReceiptDate()) ? Date.from(erpProductionMgtWorkorder.getReceiptDate().atZone(ZoneId.systemDefault()).toInstant()) : null);
    }


    /**
     * 将子工单数据插入数据库
     *
     * @param ro
     * @param workorderGuid
     */
    @Transactional(rollbackFor = Exception.class)
    public void ChildrenWorkData(ErpOutgoingMgtApplicationRO ro, String workorderGuid, Boolean judgment) {
        //2-为两条数据赋予父id数据插入工单数据表
        ro.getOutgoingApplicationList().forEach(erpOutgoingApplicationFormRO -> {
            //修改工单工序表的收货数量以及外发总数字段，quantity，outputQuantity
            QueryWrapper<ErpOutgoingDataProcess> wrapper = new QueryWrapper<>();
            wrapper.eq("workorder_data_process_guid", erpOutgoingApplicationFormRO.getWorkOrderProcessGuid());
            ErpOutgoingDataProcess dataProcess = erpOutgoingDataProcessMapper.selectOne(wrapper);
            ErpProductionMgtWorkorder workorder = baseMapper.selectById(dataProcess.getWorkorderGuid());
            BigDecimal outputQuantity = workorder.getQuantity();
            int i = outputQuantity.compareTo(BigDecimal.ZERO);
            if (i <= 0) {
                throw new FlowException(ResultErrorCode.PROCESS_THAT_YIELDS_ZERO_OUTPUT, dataProcess.getWorkorderDataProcessGuid());
            }
            BigDecimal requisitionQuantity = dataProcess.getOutsourcedRequisitionQuantity();
            if (Objects.isNull(requisitionQuantity)) {
                requisitionQuantity = BigDecimal.ZERO;
            }
            int result = erpOutgoingApplicationFormRO.getTotalQuantity()
                    .add(requisitionQuantity).compareTo(outputQuantity);
            //不做限制-存在补货的情况
            /* if (result > 0) {
                throw new FlowException(ResultErrorCode.NOT_SURPLUS_APPLICATION.getMsg(), ResultErrorCode.NOT_SURPLUS_APPLICATION.getCode());
            } else*/
            if (result == 0) {
                //修改工单工序状态为全部外发
                dataProcess.setOutsourceRequisitionState(ErpOutgoingApplicationStatusEnum.ALL_OUTGOING.getCode());
                //修改已外发申请数
                dataProcess.setOutsourcedRequisitionQuantity(erpOutgoingApplicationFormRO.getQuantity()
                        .add(requisitionQuantity));
            } else {
                //修改工单工序状态为部分外发
                dataProcess.setOutsourceRequisitionState(ErpOutgoingApplicationStatusEnum.PARTIALLY_OUTGOING.getCode());
                //修改已外发申请数
                dataProcess.setOutsourcedRequisitionQuantity(erpOutgoingApplicationFormRO.getTotalQuantity()
                        .add(requisitionQuantity));
            }
            erpOutgoingDataProcessMapper.updateById(dataProcess);
            ErpProductionMgtWorkorder erpProductionMgtWorkorder = new ErpProductionMgtWorkorder();
            BeanUtil.copyProperties(erpOutgoingApplicationFormRO, erpProductionMgtWorkorder);
            erpProductionMgtWorkorder.setParentClassificationGuid(workorderGuid);
            erpProductionMgtWorkorder.setWorkorderTypeGuid(ro.getWorkorderTypeGuid());
            erpProductionMgtWorkorder.setWorkorderProperties(WorkorderPropertiesEnum.OUTSOURCE.getCode());
            erpProductionMgtWorkorder.setSourceGuid(erpOutgoingApplicationFormRO.getWorkOrderProcessGuid());
            erpProductionMgtWorkorder.setSourceValue(WorkorderPropertiesEnum.OUTSOURCE.getCode());
            erpProductionMgtWorkorder.setProductMaterialGuid(erpOutgoingApplicationFormRO.getActProductMaterialGuid());
            erpProductionMgtWorkorder.setWorkorderNumber(ro.getWorkorderNumber());
            erpProductionMgtWorkorder.setWorkorderState(ErpOutgoingApplicationStatusEnum.NOT_OUTGOING.getCode());
            erpProductionMgtWorkorder.setWorkorderGuid("");
            erpProductionMgtWorkorder.setWorkorderGuid(IdWorker.getIdStr());
            erpOutgoingApplicationFormRO.setWorkorderGuid(erpProductionMgtWorkorder.getWorkorderGuid());
            baseMapper.insert(erpProductionMgtWorkorder);
            erpOutgoingApplicationFormRO.setTenantGuid(erpProductionMgtWorkorder.getTenantGuid());
        });

        if (CollUtil.isNotEmpty(ro.getOutgoingApplicationList())) {
            XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();
            ThreadUtil.execAsync(() -> {
                userApi.setThreadLocal(memberDto);
            });
                this.saveOutApplyDetail(ro.getOutgoingApplicationList(), workorderGuid);
        }
    }

    //双写外发明细
    private void saveOutApplyDetail(List<ErpOutgoingApplicationFormRO> outgoingApplicationList, String workorderGuid) {
        if (CollectionUtils.isNotEmpty(outgoingApplicationList)) {
            outgoingApplicationList.forEach(x -> {

                OutApplyDetDTO outApplyDetDTO = new OutApplyDetDTO();
                outApplyDetDTO.setOutApplyDetId(x.getWorkorderGuid());
                outApplyDetDTO.setOutApplyDetClazz(ClazzEnum.OUT_APPLY_DET.name());
                outApplyDetDTO.setOutApplyDetWorkorderCode(x.getWorkorderNumber());
                outApplyDetDTO.setOutApplyDetProductName(x.getProductName());
                outApplyDetDTO.setOutApplyDetAssemblyName(x.getPartName());
                outApplyDetDTO.setOutApplyDetProcessName(x.getProcessName());
                outApplyDetDTO.setOutApplyDetIsolation(x.getTenantGuid());
                outApplyDetDTO.setOutApplyDetTenantId(x.getTenantGuid());
                outApplyDetDTO.setOutApplyDetParentId(workorderGuid);

                outApplyDetDTO.setOutApplyDetProcessParameters(x.getProcessName());
                outApplyDetDTO.setOutApplyDetProductionProcDescrip(x.getProcessName());
                outApplyDetDTO.setOutApplyDetQuantity(x.getQuantity());
                outApplyDetDTO.setOutApplyDetLossQat(x.getLossQuantity());
                outApplyDetDTO.setOutApplyDetTotalQat(x.getOutputQuantity());
                outApplyDetDTO.setOutApplyDetReceivedQat(x.getTotalQuantity());
//                outApplyDetDTO.setOutApplyDetAnswerReceivedQat(x.getProcessName());
                outApplyDetDTO.setOutApplyDetReqDate(x.getRequiredDeliveryTime());
//                outApplyDetDTO.setOutApplyDetOutRatio(x.getProcessName());
                outApplyDetService.save(outApplyDetDTO);

                LinkDTO linkDTO = new LinkDTO();
                linkDTO.setLinkTarget(outApplyDetDTO.getOutApplyDetId());
                linkDTO.setLinkSource(x.getSourceGuid());
                linkDTO.setLinkTypeCode(TypeEnum.SOURCE.getCode());
                linkDTO.setLinkId(IdWorker.getIdStr());
                linkDTO.setLinkClazz(ClazzEnum.BOM_PROCESS.name() + StringPool.DASH + ClazzEnum.OUT_APPLY_DET.name());
                linkDTO.setLinkIsolation(x.getTenantGuid());
                linkService.save(linkDTO);

            });

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String workorderGuid) {
        //查询工单数据判断工单属性是否是未外发
        operationCheck(workorderGuid);
        //删除工单数据
        baseMapper.deleteById(workorderGuid);
        //刪除主表数据
        outApplyService.removeById(workorderGuid);
        //根据工单id，查询工单父id为当前id的数据
        QueryWrapper<ErpProductionMgtWorkorder> wrapper = new QueryWrapper<>();
        wrapper.eq("parent_classification_guid", workorderGuid);
        List<ErpProductionMgtWorkorder> workorderList = baseMapper.selectList(wrapper);
        workorderList.forEach(erpProductionMgtWorkorder -> {
            //修改工单工序表的收货数量以及外发总数字段，quantity，outputQuantity
            QueryWrapper<ErpOutgoingDataProcess> wrapper1 = new QueryWrapper<>();
            wrapper1.eq("workorder_data_process_guid", erpProductionMgtWorkorder.getSourceGuid());
            ErpOutgoingDataProcess dataProcess = erpOutgoingDataProcessMapper.selectOne(wrapper1);
            ErpProductionMgtWorkorder workorder = baseMapper.selectById(dataProcess.getWorkorderGuid());
            int result = workorder.getQuantity()
                    .compareTo(dataProcess.getOutsourcedRequisitionQuantity().subtract(erpProductionMgtWorkorder.getTotalQuantity()));
            int i = dataProcess.getOutsourcedRequisitionQuantity().subtract(erpProductionMgtWorkorder.getTotalQuantity()).compareTo(BigDecimal.ZERO);
            if (i == 0) {
                dataProcess.setOutsourceRequisitionState(ErpOutgoingApplicationStatusEnum.NOT_OUTGOING.getCode());
            } else {
                if (result == 0) {
                    //修改工单工序状态为全部外发
                    dataProcess.setOutsourceRequisitionState(ErpOutgoingApplicationStatusEnum.ALL_OUTGOING.getCode());
                } else {
                    //修改工单工序状态为部分外发
                    dataProcess.setOutsourceRequisitionState(ErpOutgoingApplicationStatusEnum.PARTIALLY_OUTGOING.getCode());
                }
            }
            //修改已外发申请数
            dataProcess.setOutsourcedRequisitionQuantity(dataProcess.getOutsourcedRequisitionQuantity()
                    .subtract(erpProductionMgtWorkorder.getTotalQuantity()));
            erpOutgoingDataProcessMapper.updateById(dataProcess);
            //删除明细表数据
            outApplyDetService.removeById(erpProductionMgtWorkorder.getWorkorderGuid());
        });
        baseMapper.delete(wrapper);
        iProcessInstanceService.deleteProcessAndHisInst(workorderGuid, WorkflowKeyEnum.WORK_ORDER_OUTGOING);
        return true;
    }

    /**
     * 判断能否删除或者编辑
     *
     * @param workorderGuid
     */
    private void operationCheck(String workorderGuid) {
        ErpProductionMgtWorkorder erpProductionMgtWorkorder = baseMapper.selectById(workorderGuid);
        if (!erpProductionMgtWorkorder.getWorkorderState().equals(ErpOutgoingApplicationStatusEnum.NOT_OUTGOING.getCode())
                || !erpProductionMgtWorkorder.getAuditStatus().equals(BusinessStatusEnum.DRAFT.getStatus())) {
            throw new FlowException(ResultErrorCode.ALREADY_OUTGOING.getMsg(), ResultErrorCode.ALREADY_OUTGOING.getCode());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpOutgoingMgtApplicationRO ro) {
        //查询工单数据判断工单属性是否是未外发
        String workorderGuid = ro.getWorkorderGuid();
        operationCheck(workorderGuid);
        //修改主工单数据
        ErpProductionMgtWorkorder workorder = BeanUtil.copyProperties(ro, ErpProductionMgtWorkorder.class);
        baseMapper.updateById(workorder);
        //异步双写
        XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();
        ThreadUtil.execAsync(() -> {
            userApi.setThreadLocal(memberDto);
            OutApplyDTO outApplyDTO = new OutApplyDTO();
            this.saveOutApply(workorder, outApplyDTO);
            outApplyService.saveOrUpdate(outApplyDTO);
        });
        //删除原来子工单的数据
        QueryWrapper<ErpProductionMgtWorkorder> wrapper = new QueryWrapper<>();
        wrapper.eq("parent_classification_guid", ro.getWorkorderGuid());
        List<ErpProductionMgtWorkorder> workorderList = baseMapper.selectList(wrapper);
        workorderList.forEach(erpProductionMgtWorkorder -> {
            //修改工单工序表的收货数量以及外发总数字段，quantity，outputQuantity
            QueryWrapper<ErpOutgoingDataProcess> wrapper1 = new QueryWrapper<>();
            wrapper1.eq("workorder_data_process_guid", erpProductionMgtWorkorder.getSourceGuid());
            ErpOutgoingDataProcess dataProcess = erpOutgoingDataProcessMapper.selectOne(wrapper1);
            ErpProductionMgtWorkorder workorder1 = baseMapper.selectById(dataProcess.getWorkorderGuid());
            int result = workorder1.getQuantity()
                    .compareTo(dataProcess.getOutsourcedRequisitionQuantity().subtract(erpProductionMgtWorkorder.getTotalQuantity()));
            int i = dataProcess.getOutsourcedRequisitionQuantity().subtract(erpProductionMgtWorkorder.getTotalQuantity()).compareTo(BigDecimal.ZERO);
            if (i == 0) {
                dataProcess.setOutsourceRequisitionState(ErpOutgoingApplicationStatusEnum.NOT_OUTGOING.getCode());
            } else {
                if (result == 0) {
                    //修改工单工序状态为全部外发
                    dataProcess.setOutsourceRequisitionState(ErpOutgoingApplicationStatusEnum.ALL_OUTGOING.getCode());
                } else {
                    //修改工单工序状态为部分外发
                    dataProcess.setOutsourceRequisitionState(ErpOutgoingApplicationStatusEnum.PARTIALLY_OUTGOING.getCode());
                }
            }
            //修改已外发申请数
            dataProcess.setOutsourcedRequisitionQuantity(dataProcess.getOutsourcedRequisitionQuantity()
                    .subtract(erpProductionMgtWorkorder.getTotalQuantity()));
            erpOutgoingDataProcessMapper.updateById(dataProcess);
            //删除明细表数据
            outApplyDetService.removeById(erpProductionMgtWorkorder.getWorkorderGuid());
        });
        baseMapper.delete(wrapper);
        //插入新的数据
        ChildrenWorkData(ro, ro.getWorkorderGuid(), false);
        return true;
    }

    @Override
    public ErpOutgoingApplicationFormVO getById(String workorderGuid) {
        ErpOutgoingApplicationFormVO vo = new ErpOutgoingApplicationFormVO();
        //获取工单主表的数据
        ErpProductionMgtWorkorder erpProductionMgtWorkorder = baseMapper.selectById(workorderGuid);
        BeanUtil.copyProperties(erpProductionMgtWorkorder, vo);
        //根据主工单id，获取子工单数据集合
        List<ErpProductionMgtWorkorder> outgoingApplicationROList = baseMapper.selectOutgoingData(workorderGuid);
        List<ErpOutgoingApplicationFormRO> list = new ArrayList<>();
        outgoingApplicationROList.forEach(e -> {
            LambdaQueryWrapper<ErpOutgoingDataProcess> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ErpOutgoingDataProcess::getWorkorderDataProcessGuid, e.getSourceGuid());
            ErpOutgoingDataProcess process = erpOutgoingDataProcessMapper.selectOne(wrapper);
            if (Objects.nonNull(process)) {
                //工序工单的工单
                ErpProductionMgtWorkorder workorder = baseMapper.selectById(process.getWorkorderGuid());
                ErpOutgoingApplicationFormRO outgoingApplicationRO = new ErpOutgoingApplicationFormRO();
                BeanUtil.copyProperties(e, outgoingApplicationRO);
                outgoingApplicationRO.setOutputQuantity(e.getQuantity().add(process.getLossQuantity()));
                int i = e.getProportion().compareTo(BigDecimal.ZERO);
                if (i > 0) {
                    outgoingApplicationRO.setLossQuantity(e.getQuantity().divide(e.getProportion(), 0, RoundingMode.UP));
                    outgoingApplicationRO.setActQuantity(outgoingApplicationRO.getOutputQuantity().divide(e.getProportion(), 0, RoundingMode.CEILING));
                } else {
                    outgoingApplicationRO.setLossQuantity(BigDecimal.ONE);
                    outgoingApplicationRO.setActQuantity(outgoingApplicationRO.getOutputQuantity());
                }
                outgoingApplicationRO.setWorkOrderProcessGuid(process.getWorkorderDataProcessGuid());
                outgoingApplicationRO.setProcessorQuantity(process.getOutputQuantity());
                //根据物料分类id获取部件名称以及产品名称
                String partName = baseMapper.selectPartName(outgoingApplicationRO.getWorkOrderProcessGuid());
                outgoingApplicationRO.setPartName(partName);
                outgoingApplicationRO.setMaterialObj(materialMapper.getDataByGuid(baseMapper.selectPartMaterialGuid(outgoingApplicationRO.getWorkOrderProcessGuid())));
                String productName = baseMapper.selectproductName(outgoingApplicationRO.getWorkOrderProcessGuid());
                outgoingApplicationRO.setProductName(productName);
                outgoingApplicationRO.setMaterialGuid(baseMapper.selectPartMaterialGuid(outgoingApplicationRO.getWorkOrderProcessGuid()));
                ErpBasicMgtProductionProcessesType processesType = erpBasicMgtProductionProcessesTypeMapper
                        .selectById(workorder.getProductionProcessesTypeGuid());
                outgoingApplicationRO.setProcessName(processesType.getProductionProcessesTypeName());
                outgoingApplicationRO.setProcessParameters(erpBasicMgtProductionProcessesTypeMapper.getProcessParameters(outgoingApplicationRO.getWorkOrderProcessGuid()));
                outgoingApplicationRO.setSonList(baseMapper.getSonList(process.getWorkorderGuid(), outgoingApplicationRO.getTotalQuantity()));
                outgoingApplicationRO.setParentClassificationGuid(workorderGuid);
                outgoingApplicationRO.setWorkorderNumber(workorder.getWorkorderNumber());
                outgoingApplicationRO.setProductionProcessesDescription(workorder.getProductionProcessesDescription());
                list.add(outgoingApplicationRO);
            }
        });
        vo.setOutgoingApplicationList(list);
        return vo;
    }

    /**
     * 批量查询优化，将for循环的SQL查询提取出来
     *
     * @param workorderGuids
     * @return
     */
    public List<ErpOutgoingApplicationFormVO> getByIds(List<String> workorderGuids) {
        List<ErpOutgoingApplicationFormVO> result = new ArrayList<>();
        //获取工单主表的数据
        List<ErpProductionMgtWorkorder> productionMgtWorkorders = baseMapper.selectBatchIds(workorderGuids);
        //根据主工单id，获取子工单数据集合
        List<ErpProductionMgtWorkorder> outgoingApplicationROList = baseMapper.selectOutgoingDataBatch(workorderGuids);
        // 来源
        List<String> sourceGuids = outgoingApplicationROList.stream().map(ErpProductionMgtWorkorder::getSourceGuid)
                .distinct()
                .collect(Collectors.toList());

        LambdaQueryWrapper<ErpOutgoingDataProcess> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ErpOutgoingDataProcess::getWorkorderDataProcessGuid, sourceGuids);
        // 外发工序工单
        List<ErpOutgoingDataProcess> outgoingDataProcesses = erpOutgoingDataProcessMapper.selectList(wrapper);
        Map<String, ErpOutgoingDataProcess> dataProcessMap = outgoingDataProcesses
                .stream()
                .collect(Collectors.toMap(ErpOutgoingDataProcess::getWorkorderDataProcessGuid, u -> u, (e, o) -> e));
        List<String> processDataWorkorderGuids = outgoingDataProcesses.stream()
                .map(ErpOutgoingDataProcess::getWorkorderGuid)
                .distinct()
                .collect(Collectors.toList());
        List<String> workorderDataProcessGuids = outgoingDataProcesses.stream()
                .map(ErpOutgoingDataProcess::getWorkorderDataProcessGuid)
                .distinct()
                .collect(Collectors.toList());

        // 获取工序参数
        List<ErpWorkorderDataProcessParameterVO> processParametersBatch = productionProcessesTypeService.getProcessParametersBatch(workorderDataProcessGuids);
        // 获取部件名称
        Map<String, String> partNamesMap = selectPartNameBatch(workorderDataProcessGuids);
        // 获取产品名称
        Map<String, String> productNamesMap = selectproductNameBatch(workorderDataProcessGuids);

        //通过工序工单id,获取产出工单集合
        List<ErpOutgoingApplicationVO> sonListBatch = baseMapper.getSonListBatch(processDataWorkorderGuids);

        // 所有工序工单的工单
        List<ErpProductionMgtWorkorder> processDataFatherWorkorders = baseMapper.selectBatchIds(processDataWorkorderGuids);
        Map<String, ErpProductionMgtWorkorder> productionMgtWorkorderMap = processDataFatherWorkorders
                .stream()
                .collect(Collectors.toMap(ErpProductionMgtWorkorder::getWorkorderGuid, u -> u, (e, o) -> e));

        // 工序类型
        List<String> productionProcessesTypeGuids = processDataFatherWorkorders.stream()
                .map(ErpProductionMgtWorkorder::getProductionProcessesTypeGuid)
                .distinct()
                .collect(Collectors.toList());
        // 工序类型集合转成Map，方便后续取值
        Map<String, ErpBasicMgtProductionProcessesType> productionProcessesTypeMap = productionProcessesTypeService.getMapByIds(productionProcessesTypeGuids);

        // todo
        List<BaseMapContainer> partMaterialGuids = baseMapper.selectPartMaterialGuidBatch(processDataWorkorderGuids);
        List<BaseMapContainer> partMaterialGuid2s = baseMapper.selectPartMaterialGuidBatchWorkorderGuid(processDataWorkorderGuids);
        partMaterialGuids.addAll(partMaterialGuid2s);
        Map<String, String> materialGuidMapProcessDataWorkorderGuids = partMaterialGuids
                .stream()
                .collect(Collectors.toMap(BaseMapContainer::getName, BaseMapContainer::getValue, (e, o) -> e));

        // 获取物料信息
        List<String> materialGuids = partMaterialGuids.stream().map(BaseMapContainer::getValue)
                .distinct()
                .collect(Collectors.toList());
        // 外发明细子集的物料
        List<String> materialGuidSonLists = sonListBatch.stream().map(ErpOutgoingApplicationVO::getMaterialGuid)
                .distinct()
                .collect(Collectors.toList());
        materialGuids.addAll(materialGuidSonLists);

        // 获取物料数据
        Map<String, ErpMaterialMgtMaterialVO> serviceMapByIds = materialService.getMapByIds(materialGuids);

        // 设置数据
        productionMgtWorkorders.forEach(pWorkorder -> {
            List<ErpProductionMgtWorkorder> outgoingApplicationDataList = outgoingApplicationROList.stream()
                    .filter(v -> v.getParentClassificationGuid().equals(pWorkorder.getWorkorderGuid()))
                    .collect(Collectors.toList());

            List<ErpOutgoingApplicationFormRO> list = new ArrayList<>();

            outgoingApplicationDataList.forEach(e -> {
                ErpOutgoingDataProcess process = dataProcessMap.get(e.getSourceGuid());
                if (Objects.nonNull(process)) {
                    //工序工单的工单
                    ErpProductionMgtWorkorder workorder = productionMgtWorkorderMap.get(process.getWorkorderGuid());
                    ErpOutgoingApplicationFormRO outgoingApplicationRO = new ErpOutgoingApplicationFormRO();
                    BeanUtil.copyProperties(e, outgoingApplicationRO);
                    outgoingApplicationRO.setOutputQuantity(e.getQuantity().add(process.getLossQuantity()));
                    int i = e.getProportion().compareTo(BigDecimal.ZERO);
                    if (i > 0) {
                        BigDecimal none = workorder.getQuantity();
                        if (BigDecimal.ZERO.compareTo(none) == 0) {
                            none = BigDecimal.ONE;
                        }
                        outgoingApplicationRO.setLossQuantity(e.getQuantity().multiply(process.getLossQuantity()).divide(none, 0, RoundingMode.UP));
                        outgoingApplicationRO.setActQuantity(outgoingApplicationRO.getOutputQuantity().divide(e.getProportion(), 0, RoundingMode.CEILING));
                    } else {
                        outgoingApplicationRO.setLossQuantity(BigDecimal.ONE);
                        outgoingApplicationRO.setActQuantity(outgoingApplicationRO.getOutputQuantity());
                    }
                    outgoingApplicationRO.setWorkOrderProcessGuid(process.getWorkorderDataProcessGuid());
                    outgoingApplicationRO.setProcessorQuantity(process.getOutputQuantity());
                    //根据物料分类id获取部件名称以及产品名称
                    outgoingApplicationRO.setPartName(partNamesMap.get(outgoingApplicationRO.getWorkOrderProcessGuid()));


                    String materialGuid = materialGuidMapProcessDataWorkorderGuids.get(process.getWorkorderGuid());
                    serviceMapByIds.get(materialGuid);

                    outgoingApplicationRO.setMaterialObj(serviceMapByIds.get(materialGuid));

                    outgoingApplicationRO.setProductName(productNamesMap.get(outgoingApplicationRO.getWorkOrderProcessGuid()));
                    outgoingApplicationRO.setMaterialGuid(materialGuid);

                    ErpBasicMgtProductionProcessesType processesType = productionProcessesTypeMap.get(workorder.getProductionProcessesTypeGuid());
                    outgoingApplicationRO.setProcessName(processesType.getProductionProcessesTypeName());
                    // 工序参数处理
                    List<ErpWorkorderDataProcessParameterVO> processParameterVOS = processParametersBatch.stream()
                            .filter(parameter -> parameter.getWorkorderDataProcessGuid().equals(outgoingApplicationRO.getWorkOrderProcessGuid()))
                            .collect(Collectors.toList());
                    outgoingApplicationRO.setProcessParameterVOS(processParameterVOS);
                    // 拼接字符串
                    outgoingApplicationRO.setProcessParameters(productionProcessesTypeService.getStringJoint(processParameterVOS));

                    // 明细的子集
                    List<ErpOutgoingApplicationVO> sonList = sonListBatch.stream()
                            .filter(son -> son.getParentWorkOrderGuid().equals(process.getWorkorderGuid()))
                            .collect(Collectors.toList());
                    sonList.forEach(s -> {
                        s.setOutputQuantity(s.getProportion().multiply(outgoingApplicationRO.getTotalQuantity()));
                        s.setMaterialObj(serviceMapByIds.get(s.getMaterialGuid()));
                    });
                    outgoingApplicationRO.setSonList(sonList);

                    outgoingApplicationRO.setParentClassificationGuid(pWorkorder.getWorkorderGuid());
                    outgoingApplicationRO.setWorkorderNumber(workorder.getWorkorderNumber());
                    outgoingApplicationRO.setProductionProcessesDescription(workorder.getProductionProcessesDescription());

                    list.add(outgoingApplicationRO);
                }
            });

            ErpOutgoingApplicationFormVO vo = new ErpOutgoingApplicationFormVO();
            BeanUtil.copyProperties(pWorkorder, vo);

            vo.setOutgoingApplicationList(list);

            result.add(vo);
        });

        return result;
    }

    /**
     * 获取子工单数据集合
     *
     * @param outgoingApplicationROList
     * @return
     */
    @NotNull
    private List<ErpOutgoingApplicationFormRO> getErpProductionMgtOutgoingApplicationROS(List<ErpOutgoingApplicationFormRO> outgoingApplicationROList) {
        if (ObjUtil.isEmpty(outgoingApplicationROList)) {
            return Collections.emptyList();
        }
        List<ErpOutgoingApplicationFormRO> list = new ArrayList<>();
        // 工序工单
        List<String> workorderDataProcessGuids = outgoingApplicationROList.stream()
                .map(ErpOutgoingApplicationFormRO::getWorkorderDataProcessGuid)
                .distinct()
                .collect(Collectors.toList());
        List<String> processDataWorkorderGuids = outgoingApplicationROList.stream()
                .map(ErpOutgoingApplicationFormRO::getWorkorderGuid)
                .distinct()
                .collect(Collectors.toList());
        // 获取工序参数
        List<ErpWorkorderDataProcessParameterVO> processParametersBatch = productionProcessesTypeService.getProcessParametersBatch(workorderDataProcessGuids);
        // 获取部件名称
        Map<String, String> partNamesMap = selectPartNameBatch(workorderDataProcessGuids);
        // 获取产品名称
        Map<String, String> productNamesMap = selectproductNameBatch(workorderDataProcessGuids);

        //通过工序工单id,获取产出工单集合
        List<ErpOutgoingApplicationVO> sonListBatch = baseMapper.getSonListBatch(processDataWorkorderGuids);

        // 所有工序工单的工单
        List<ErpProductionMgtWorkorder> processDataFatherWorkorders = baseMapper.selectBatchIds(processDataWorkorderGuids);
        Map<String, ErpProductionMgtWorkorder> productionMgtWorkorderMap = processDataFatherWorkorders
                .stream()
                .collect(Collectors.toMap(ErpProductionMgtWorkorder::getWorkorderGuid, u -> u, (e, o) -> e));

        // 工序类型
        List<String> productionProcessesTypeGuids = processDataFatherWorkorders.stream()
                .map(ErpProductionMgtWorkorder::getProductionProcessesTypeGuid)
                .distinct()
                .collect(Collectors.toList());
        // 工序类型集合转成Map，方便后续取值
        Map<String, ErpBasicMgtProductionProcessesType> productionProcessesTypeMap = productionProcessesTypeService.getMapByIds(productionProcessesTypeGuids);

        List<BaseMapContainer> partMaterialGuids = baseMapper.selectPartMaterialGuidBatch(processDataWorkorderGuids);
        List<BaseMapContainer> partMaterialGuid2s = baseMapper.selectPartMaterialGuidBatchWorkorderGuid(processDataWorkorderGuids);
        partMaterialGuids.addAll(partMaterialGuid2s);
        Map<String, String> materialGuidMapProcessDataWorkorderGuids = partMaterialGuids
                .stream()
                .filter(v ->StrUtil.isNotBlank(v.getValue())&& StrUtil.isNotBlank(v.getName()))
                .collect(Collectors.toMap(BaseMapContainer::getName, BaseMapContainer::getValue, (e, o) -> e));

        // 获取物料信息
        List<String> materialGuids = partMaterialGuids.stream().map(BaseMapContainer::getValue)
                .distinct()
                .collect(Collectors.toList());
        // 外发明细子集的物料
        List<String> materialGuidSonLists = sonListBatch.stream().map(ErpOutgoingApplicationVO::getMaterialGuid)
                .distinct()
                .collect(Collectors.toList());
        materialGuids.addAll(materialGuidSonLists);

        List<ErpOutgoingApplicationFormRO> specificationsBatch = erpOutgoingDataProcessMapper.getSpecificationsBatch(workorderDataProcessGuids);
        if (CollUtil.isNotEmpty(specificationsBatch)) {
            // 获取上工序产出物料
            List<String> upProcessMaterialGuids = specificationsBatch.stream().map(ErpOutgoingApplicationFormRO::getMaterialGuid).distinct().collect(Collectors.toList());
            materialGuids.addAll(upProcessMaterialGuids);
        }

        // 获取物料数据
        Map<String, ErpMaterialMgtMaterialVO> serviceMapByIds = materialService.getMapByIds(materialGuids);


        //遍历集合根据物料分类id获取产品名称，以及他上级的部件名
        outgoingApplicationROList.forEach(outgoingDataProcess -> {
            ErpProductionMgtWorkorder workorder = lambdaQuery().eq(ErpProductionMgtWorkorder::getWorkorderGuid, outgoingDataProcess.getWorkorderGuid()).one();
            if (Objects.nonNull(workorder)) {
                ErpOutgoingApplicationFormRO outgoingApplicationRO = new ErpOutgoingApplicationFormRO();
                BeanUtil.copyProperties(workorder, outgoingApplicationRO);
                int i = outgoingDataProcess.getLossQuantity().compareTo(BigDecimal.ZERO);
                if (i == 0) {
                    outgoingApplicationRO.setProportion(BigDecimal.ONE);
                }
                outgoingApplicationRO.setLossQuantity(outgoingDataProcess.getLossQuantity());
                //交货数
                outgoingApplicationRO.setTotalQuantity(workorder.getQuantity().subtract(outgoingDataProcess.getOutsourcedRequisitionQuantity()));
                //外发总数
                outgoingApplicationRO.setOutputQuantity(workorder.getQuantity().add(outgoingDataProcess.getLossQuantity()));
                outgoingApplicationRO.setActQuantity(outgoingApplicationRO.getOutputQuantity().divide(outgoingApplicationRO.getProportion(), 0, RoundingMode.CEILING));
                outgoingApplicationRO.setWorkOrderProcessGuid(outgoingDataProcess.getWorkorderDataProcessGuid());
                //根据工序工单id获取上工序的产出长宽高
                ErpOutgoingApplicationFormRO ro = specificationsBatch.stream()
                        .filter(v -> v.getWorkorderDataProcessGuid().equals(outgoingDataProcess.getWorkorderDataProcessGuid()))
                        .findFirst()
                        .orElse(null);
                if (Objects.nonNull(ro)) {
                    ErpMaterialMgtMaterialVO upProcessOutMaterial = serviceMapByIds.get(ro.getMaterialGuid());
                    String specificationsLength = materialService.getSpecificationByType(upProcessOutMaterial, CostSchemeCalcEnum.LONG.getCalcVariableGuid(), "1");
                    String specificationsWidth = materialService.getSpecificationByType(upProcessOutMaterial, CostSchemeCalcEnum.WIDE.getCalcVariableGuid(), "1");
                    String specificationsHeight = materialService.getSpecificationByType(upProcessOutMaterial, CostSchemeCalcEnum.TALL.getCalcVariableGuid(), "1");

                    outgoingApplicationRO.setSpecificationsLength(new BigDecimal(specificationsLength));
                    outgoingApplicationRO.setSpecificationsWidth(new BigDecimal(specificationsWidth));
                    outgoingApplicationRO.setSpecificationsHeight(new BigDecimal(specificationsHeight));
                }
                //根据物料分类id获取部件名称以及产品名称
                // String partName = baseMapper.selectPartName(outgoingApplicationRO.getWorkOrderProcessGuid());
                outgoingApplicationRO.setPartName(partNamesMap.get(outgoingApplicationRO.getWorkOrderProcessGuid()));
                // 物料名称
                String materialGuid = materialGuidMapProcessDataWorkorderGuids.get(outgoingDataProcess.getWorkorderGuid());
                serviceMapByIds.get(materialGuid);

                outgoingApplicationRO.setMaterialObj(serviceMapByIds.get(materialGuid));
                // 产品名称
                outgoingApplicationRO.setProductName(productNamesMap.get(outgoingApplicationRO.getWorkOrderProcessGuid()));
                // 工序
                ErpBasicMgtProductionProcessesType processesType = productionProcessesTypeMap.get(workorder.getProductionProcessesTypeGuid());
                outgoingApplicationRO.setProcessName(processesType.getProductionProcessesTypeName());
                // 工序参数
                List<ErpWorkorderDataProcessParameterVO> processParameterVOS = processParametersBatch.stream()
                        .filter(parameter -> parameter.getWorkorderDataProcessGuid().equals(outgoingApplicationRO.getWorkOrderProcessGuid()))
                        .collect(Collectors.toList());
                outgoingApplicationRO.setProcessParameterVOS(processParameterVOS);
                outgoingApplicationRO.setProcessParameters(productionProcessesTypeService.getStringJoint(processParameterVOS));

                // 明细的子集
                List<ErpOutgoingApplicationVO> sonList = sonListBatch.stream()
                        .filter(son -> son.getParentWorkOrderGuid().equals(outgoingDataProcess.getWorkorderGuid()))
                        .collect(Collectors.toList());
                sonList.forEach(s -> {
                    s.setOutputQuantity(s.getProportion().multiply(outgoingApplicationRO.getTotalQuantity()));
                    s.setMaterialObj(serviceMapByIds.get(s.getMaterialGuid()));
                });
                outgoingApplicationRO.setSonList(sonList);

                list.add(outgoingApplicationRO);
            } else {
                throw new FlowException(ResultErrorCode.PROCESSOR_WORK_ORDER_NOT_STATUS, outgoingDataProcess.getWorkorderDataProcessGuid());
            }
        });
        return list;
    }

    @Override
    public List<ErpOutgoingApplicationFormVO> findList(ErpOutgoingMgtApplicationQO qo) {
        if (StringUtils.isNotEmpty(qo.getKeyword())) {
            //根据关键字获取客户数据
            String[] keywords = qo.getKeyword().split(" ");
            List<String> list2 = Arrays.asList(keywords);
            qo.setKeywords(list2);
        }
        List<ErpOutgoingApplicationFormVO> voList = new ArrayList<>();
        List<String> workorderGuidS = baseMapper.selectOutgoing(null, qo);
        if (CollectionUtils.isEmpty(workorderGuidS)) {
            return voList;
        }
        List<String> collect = workorderGuidS.stream().distinct().collect(Collectors.toList());
        // 获取明细数据
        voList = getByIds(collect);

        if (CollectionUtils.isNotEmpty(voList)) {
            voList.forEach(ErpOutgoingMgtApplicationServiceImpl::getOutgoingState);
            voList.sort(Comparator.comparing(ErpOutgoingApplicationFormVO::getWorkorderNumber, (t1, t2) -> t2.compareTo(t1)));
        }

        return voList;
    }

    /**
     * 处理外发申请状态
     *
     * @param vo
     */
    private static ErpOutgoingApplicationFormVO getOutgoingState(ErpOutgoingApplicationFormVO vo) {
        //处理外发申请状态
        List<String> collect1 = vo.getOutgoingApplicationList().stream()
                .map(form -> form.getWorkorderState() == null ? "0" : form.getWorkorderState())
                .collect(Collectors.toList());
        int sum = collect1.stream().map(Integer::valueOf).mapToInt(Integer::intValue).sum();
        //计算入库情况阈值
        int size = collect1.size();
        //终止情况的阈值
        int stopNumber = size * Integer.valueOf(CompletionStatusEnum.STOP_FINISH.getCode());
        //全部完成情况的阈值
        int finshNumber = size * Integer.valueOf(CompletionStatusEnum.ALL_FINISH.getCode());
        if (collect1.contains(CompletionStatusEnum.PARTIALLY_FINISH.getCode())) {
            vo.setWorkorderState(CompletionStatusEnum.PARTIALLY_FINISH.getCode());
        } else if (sum == stopNumber) {
            vo.setWorkorderState(CompletionStatusEnum.STOP_FINISH.getCode());
        } else if (sum == finshNumber) {
            vo.setWorkorderState(CompletionStatusEnum.ALL_FINISH.getCode());
        } else if (sum == Integer.valueOf(CompletionStatusEnum.NOT_FINISH.getCode())) {
            vo.setWorkorderState(CompletionStatusEnum.NOT_FINISH.getCode());
        } else {
            vo.setWorkorderState(CompletionStatusEnum.PARTIALLY_FINISH.getCode());
        }
        return vo;
    }

    @Override
    public IPage<ErpOutgoingApplicationFormVO> findPage(PageParams<ErpOutgoingMgtApplicationQO> pageParams) {
        IPage<ErpOutgoingApplicationFormVO> page = pageParams.buildPage();
        ErpOutgoingMgtApplicationQO model = pageParams.getModel();
        if (StringUtils.isNotEmpty(model.getKeyword())) {
            //根据关键字获取客户数据
            String[] keywords = model.getKeyword().split(" ");
            List<String> list = Arrays.asList(keywords);
            model.setKeywords(list);
        }
        List<ErpOutgoingApplicationFormVO> voList = new ArrayList<>();
        List<String> workorderGuidS = baseMapper.selectOutgoing(page, model);
        if (CollectionUtils.isEmpty(workorderGuidS)) {
            return page;
        }
        List<String> collect = workorderGuidS.stream().distinct().collect(Collectors.toList());
        // 获取明细数据
        voList = getByIds(collect);

        if (CollectionUtils.isNotEmpty(voList)) {

            voList.forEach(ErpOutgoingMgtApplicationServiceImpl::getOutgoingState);

            voList.sort(Comparator.comparing(ErpOutgoingApplicationFormVO::getWorkorderNumber, (t1, t2) -> t2.compareTo(t1)));
        }
        page.setRecords(voList);
        // page.setTotal(voList.size());
        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean removeIds(List<String> ids) {
        ids.forEach(this::delete);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveData(InsertOrUpdateList<ErpOutgoingMgtApplicationRO> list) {
        List<String> ids = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list.getInsertList())) {
            list.getInsertList().forEach(item -> {
                ErpOutgoingMgtApplicationRO erpOutgoingMgtApplicationRO = this.create(item);
                ids.add(erpOutgoingMgtApplicationRO.getWorkorderGuid());
            });
        }
        if (CollectionUtils.isNotEmpty(list.getUpdateList())) {
            list.getUpdateList().forEach(item -> {
                this.update(item);
                ids.add(item.getWorkorderGuid());
            });
        }
        return ids;
    }

    @Override
    public IPage<ErpOutgoingApplicationFormRO> allFindList(PageParams<ErpOutgoingQO> qo) {
        ErpOutgoingQO model = qo.getModel();
        IPage<ErpOutgoingQO> page = qo.buildPage();
        if (ObjUtil.isNull(model)) {
            model = new ErpOutgoingQO();
        }
        if (ObjUtil.isEmpty(model.getAuditStatus())) {
            model.setAuditStatus(Lists.newArrayList(BusinessStatusEnum.FINISH.getStatus()));
        }
        IPage<ErpOutgoingApplicationFormRO> dataProcessList = baseMapper.selectMessages(page, model);

        List<ErpOutgoingApplicationFormRO> roList = getErpProductionMgtOutgoingApplicationROS(dataProcessList.getRecords()).stream().distinct().collect(Collectors.toList());
        dataProcessList.setRecords(roList);
//        return roList.stream()
//                .sorted(Comparator.comparing(ErpOutgoingApplicationFormRO::getRequiredDeliveryTime))
//                .collect(Collectors.toList());
        return dataProcessList;
    }

    /**
     * 获取外发申请明细数据
     *
     * @param qo
     * @return
     */
    @Override
    public List<ErpOutgoingApplicationFormRO> specificFindList(ErpOutgoingMgtApplicationQO qo) throws ExecutionException, InterruptedException {
        List<ErpOutgoingApplicationFormRO> list = baseMapper.getNewFindList(qo).stream().distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(list)) {
            // 获取工序工单
            List<String> workorderDataProcessGuids = list.stream().map(ErpOutgoingApplicationFormRO::getWorkOrderProcessGuid).distinct().collect(Collectors.toList());
            // 获取工序工单
            List<String> workorderGuids = list.stream().map(ErpOutgoingApplicationFormRO::getTmpGuid).distinct().collect(Collectors.toList());
            //获取上工序类型
            List<String> typeGuids = list.stream().map(ErpOutgoingApplicationFormRO::getProductionProcessesTypeGuid).distinct().collect(Collectors.toList());
            // 加载物料信息
            materialService.loadMaterialObj(list, ErpOutgoingApplicationFormRO::getMaterialGuid, ErpOutgoingApplicationFormRO::setMaterialObj, null);
            // 获取工序参数
            List<ErpWorkorderDataProcessParameterVO> processParametersBatch = productionProcessesTypeService.getProcessParametersBatch(workorderDataProcessGuids);
            //通过工序工单id,获取产出工单集合
            List<ErpOutgoingApplicationVO> sonListBatch = erpOutgoingMgtApplicationMapper.getSonListBatch(workorderGuids);
            // 加载物料信息
            materialService.loadMaterialObj(sonListBatch, ErpOutgoingApplicationVO::getMaterialGuid, ErpOutgoingApplicationVO::setMaterialObj, null);
            XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();
            CompletableFuture<List<ErpWorkorderDataProcessParameterVO>> processParametersBatchFuture = CompletableFuture.supplyAsync(() -> {
                userApi.setThreadLocal(memberDto);
                return productionProcessesTypeService.getProcessParametersBatch(workorderDataProcessGuids);
            });
            CompletableFuture<List<ErpOutgoingApplicationVO>> sonListBatchFuture = CompletableFuture.supplyAsync(() -> {
                userApi.setThreadLocal(memberDto);
                return erpOutgoingMgtApplicationMapper.getSonListBatch(workorderGuids);
            });

            // 上工序
            CompletableFuture<List<ErpProcessProductionStatusVO>> processTaskFuture = CompletableFuture.supplyAsync(() -> {
                if (CollUtil.isEmpty(typeGuids)) {
                    return new ArrayList<>();
                }
                userApi.setThreadLocal(memberDto);
                return erpProductionMgtWorkorderMapper.previousProcessType(typeGuids);
            });
            CompletableFuture.allOf(processParametersBatchFuture, sonListBatchFuture, processTaskFuture).join();
            List<ErpProcessProductionStatusVO> productionList = processTaskFuture.get();
            Map<String, String> productionMap = Optional.ofNullable(productionList)
                    .orElse(Collections.emptyList())
                    .stream().filter(Objects::nonNull)
                    .filter(item -> StrUtil.isNotBlank(item.getThisWorkorderGuid()) && StrUtil.isNotBlank(item.getProcessName()))
                    .collect(Collectors.toMap(
                            ErpProcessProductionStatusVO::getThisWorkorderGuid,
                            ErpProcessProductionStatusVO::getProcessName,
                            (v1, v2) -> v2
                    ));
            List<ErpProcessProductionStatusVO> erpProcessProductionStatusVOS = erpProductionMgtWorkorderMapper.previousProcessType(typeGuids);
            list.forEach(v -> {
                //赋值上工序
                if (BeanUtil.isNotEmpty(productionMap)) {
                    v.setOnProcessName(productionMap.get(v.getProductionProcessesTypeGuid()));
                }
                // 工序参数处理
                List<ErpWorkorderDataProcessParameterVO> processParameterVOS = processParametersBatch.stream()
                        .filter(parameter -> parameter.getWorkorderDataProcessGuid().equals(v.getWorkOrderProcessGuid()))
                        .collect(Collectors.toList());
                v.setProcessParameterVOS(processParameterVOS);
                // 拼接字符串
                v.setProcessParameters(productionProcessesTypeService.getStringJoint(processParameterVOS));

                // 明细的子集
                List<ErpOutgoingApplicationVO> sonList = sonListBatch.stream()
                        .filter(son -> son.getParentWorkOrderGuid().equals(v.getTmpGuid()))
                        .collect(Collectors.toList());
                sonList.forEach(s -> s.setOutputQuantity(s.getProportion().multiply(v.getTotalQuantity())));
                v.setSonList(sonList);
            });


        }

        return list;
    }

    /**
     * 待开列表查询
     *
     * @param qo
     * @return
     */
    @Override
    public List<ErpOutgoingApplicationFormVO> newFindList(ErpOutgoingMgtApplicationQO qo) throws ExecutionException, InterruptedException {
        List<ErpOutgoingApplicationFormRO> list = baseMapper.getNewFindList(qo);
        List<ErpOutgoingApplicationFormVO> list2 = new ArrayList<>();

        if (CollUtil.isNotEmpty(list)) {
            // 获取工序工单
            List<String> workorderDataProcessGuids = list.stream().map(ErpOutgoingApplicationFormRO::getWorkOrderProcessGuid).distinct().collect(Collectors.toList());
            // 获取工序工单
            List<String> workorderGuids = list.stream().map(ErpOutgoingApplicationFormRO::getTmpGuid).distinct().collect(Collectors.toList());
            //获取上工序类型
            List<String> typeGuids = list.stream().map(ErpOutgoingApplicationFormRO::getProductionProcessesTypeGuid).distinct().collect(Collectors.toList());
            // 加载物料信息
            //  materialService.loadMaterialObj(list, ErpOutgoingApplicationFormRO::getMaterialGuid, ErpOutgoingApplicationFormRO::setMaterialObj, null);
            XyMemberDto memberDto = userApi.getThreadLocalXyMemberDto();
            CompletableFuture<List<ErpWorkorderDataProcessParameterVO>> processParametersBatchFuture = CompletableFuture.supplyAsync(() -> {
                userApi.setThreadLocal(memberDto);
                return productionProcessesTypeService.getProcessParametersBatch(workorderDataProcessGuids);
            });
            CompletableFuture<List<ErpOutgoingApplicationVO>> sonListBatchFuture = CompletableFuture.supplyAsync(() -> {
                userApi.setThreadLocal(memberDto);
                return erpOutgoingMgtApplicationMapper.getSonListBatch(workorderGuids);
            });

            // 上工序
            CompletableFuture<List<ErpProcessProductionStatusVO>> processTaskFuture = CompletableFuture.supplyAsync(() -> {
                if (CollUtil.isEmpty(typeGuids)) {
                    return new ArrayList<>();
                }
                userApi.setThreadLocal(memberDto);
                return erpProductionMgtWorkorderMapper.previousProcessType(typeGuids);
            });
            CompletableFuture.allOf(processParametersBatchFuture, sonListBatchFuture, processTaskFuture).join();
            List<ErpProcessProductionStatusVO> productionList = processTaskFuture.get();
            Map<String, String> productionMap = Optional.ofNullable(productionList)
                    .orElse(Collections.emptyList())
                    .stream().filter(Objects::nonNull)
                    .filter(item -> StrUtil.isNotBlank(item.getThisWorkorderGuid()) && StrUtil.isNotBlank(item.getProcessName()))
                    .collect(Collectors.toMap(
                            ErpProcessProductionStatusVO::getThisWorkorderGuid,
                            ErpProcessProductionStatusVO::getProcessName,
                            (v1, v2) -> v2
                    ));
            // 获取工序参数
            List<ErpWorkorderDataProcessParameterVO> processParametersBatch = processParametersBatchFuture.get();
            //通过工序工单id,获取产出工单集合
            List<ErpOutgoingApplicationVO> sonListBatch = sonListBatchFuture.get();

            // 加载物料信息
            List<String> materialGuids = list.stream()
                    .map(ErpOutgoingApplicationFormRO::getMaterialGuid)
                    .collect(Collectors.toList());
            List<String> materialGuids_tmp = sonListBatch.stream()
                    .map(ErpOutgoingApplicationVO::getMaterialGuid)
                    .collect(Collectors.toList());
            materialGuids.addAll(materialGuids_tmp);
            materialGuids = materialGuids.stream().distinct().collect(Collectors.toList());
            List<ErpMaterialMgtMaterialVO> materialVOS = materialService.getByIds(materialGuids);

            LoadDataUtils.loadDataObjTool(list,
                    materialVOS,
                    ErpMaterialMgtMaterialVO::getMaterialGuid,
                    ErpOutgoingApplicationFormRO::getMaterialGuid,
                    ErpOutgoingApplicationFormRO::setMaterialObj);

            LoadDataUtils.loadDataObjTool(sonListBatch,
                    materialVOS,
                    ErpMaterialMgtMaterialVO::getMaterialGuid,
                    ErpOutgoingApplicationVO::getMaterialGuid,
                    ErpOutgoingApplicationVO::setMaterialObj);

            // 加载物料信息
//            materialService.loadMaterialObj(sonListBatch, ErpOutgoingApplicationVO::getMaterialGuid, ErpOutgoingApplicationVO::setMaterialObj, null);

            list.forEach(v -> {
                //赋值上工序
                if (BeanUtil.isNotEmpty(productionMap)) {
                    v.setOnProcessName(productionMap.get(v.getProductionProcessesTypeGuid()));
                }
                // 工序参数处理
                List<ErpWorkorderDataProcessParameterVO> processParameterVOS = processParametersBatch.stream()
                        .filter(parameter -> parameter.getWorkorderDataProcessGuid().equals(v.getWorkOrderProcessGuid()))
                        .collect(Collectors.toList());
                v.setProcessParameterVOS(processParameterVOS);
                // 拼接字符串
                v.setProcessParameters(productionProcessesTypeService.getStringJoint(processParameterVOS));

                // 明细的子集
                List<ErpOutgoingApplicationVO> sonList = sonListBatch.stream()
                        .filter(son -> son.getParentWorkOrderGuid().equals(v.getTmpGuid()))
                        .collect(Collectors.toList());
                sonList.forEach(s -> s.setOutputQuantity(s.getProportion().multiply(v.getTotalQuantity())));
                v.setSonList(sonList);
            });


        }
        // 获取父guid列表
        if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
            Set<ErpOutgoingApplicationFormVO> list1 = new HashSet<>();
            list.forEach(s -> {
                ErpOutgoingApplicationFormVO vo = new ErpOutgoingApplicationFormVO();
                vo.setWorkorderGuid(s.getParentClassificationGuid());
                vo.setWorkorderNumber(s.getApplicationNumber());
                list1.add(vo);
            });
            list1.forEach(l -> {
                List<ErpOutgoingApplicationFormRO> collect = list.stream().filter(s ->
                                s.getParentClassificationGuid().equals(l.getWorkorderGuid()))
                        .distinct().collect(Collectors.toList());
                collect.forEach(x -> {
                    x.setWorkorderNumber(x.getWorkorderNumber());
                });
                l.setOutgoingApplicationList(collect);
                list2.add(l);
            });
        }
        return list2;

    }

    /**
     * 终止单据
     *
     * @param list
     * @return
     */
    @Override
    public Boolean stopOrRollback(List<ErpOutgoingDetaiStopRO> list) {
        List<ErpProductionMgtWorkorder> list1 = new ArrayList<>();
        list.forEach(s -> {
            ErpProductionMgtWorkorder detail = baseMapper.selectById(s.getWorkorderGuid());
            detail.setCurrentStatus(s.getCurrentStatus());
            list1.add(detail);
        });
        super.updateBatchById(list1);
        return true;
    }

    /**
     * 生成外发申请单
     *
     * @param qo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateOutgoingApplication(PageParams<ErpOutgoingQO> qo) {
        List<ErpOutgoingApplicationFormRO> toBeOpenedList = this.allFindList(qo).getRecords();
        if (CollectionUtil.isEmpty(toBeOpenedList)) {
            throw new FlowException(ResultErrorCode.ALL_OUTGOING_APPLICATIONS_HAVE_BEEN_MADE);
        }
        ErpOutgoingMgtApplicationRO applicationRO = new ErpOutgoingMgtApplicationRO();
        // 申请类型默认为“正常外发”
        applicationRO.setWorkorderTypeGuid("1");
        applicationRO.setReceiptDate(LocalDateTime.now());
        applicationRO.setAuditStatus(BusinessStatusEnum.FINISH.getStatus());
        applicationRO.setAuditDate(LocalDateTime.now());
        // 忽略字段
        String[] ignoreFields = {"applicationNumber", "createDate", "creator", "creatorGuid", "lastUpdateDate", "lastUpdater", "lastUpdaterGuid", "tenantGuid"};
        applicationRO.setOutgoingApplicationList(BeanUtil.copyToList(toBeOpenedList, ErpOutgoingApplicationFormRO.class,
                CopyOptions.create().setIgnoreProperties(ignoreFields)));
        return null != this.create(applicationRO);
    }

    /**
     * 获取待开外发申请数
     *
     * @param roList
     * @param s
     */
    private List<ErpOutgoingApplicationFormRO>
    getWaitQuantity(List<ErpOutgoingApplicationFormRO> roList, String s) {
        roList.forEach(erpOutgoingApplicationFormRO -> {
            erpOutgoingApplicationFormRO.setApplicationNumber(s);
            //根据该工单id作为表单来源id查询子工单是否有生成
            List<ErpProductionMgtWorkorder> list1 = lambdaQuery()
                    .eq(ErpProductionMgtWorkorder::getParentClassificationGuid, erpOutgoingApplicationFormRO.getWorkorderGuid()).list();
            BigDecimal waitOutgoingQuantity = erpOutgoingApplicationFormRO.getTotalQuantity();
            if (CollectionUtils.isNotEmpty(list1)) {
                BigDecimal sum = list1.stream().map(ErpProductionMgtWorkorder::getQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal subtract = waitOutgoingQuantity.subtract(sum);
                int result = subtract.compareTo(BigDecimal.ZERO);
                if (result > 0) {
                    erpOutgoingApplicationFormRO.setWaitOutgoingQuantity(subtract);
                } else {
                    erpOutgoingApplicationFormRO.setWaitOutgoingQuantity(BigDecimal.ZERO);
                }

            } else {
                erpOutgoingApplicationFormRO.setWaitOutgoingQuantity(waitOutgoingQuantity);
            }
        });
        //过滤掉待开数量为0的数据
        return roList.stream().filter(erpOutgoingApplicationFormRO ->
                        (erpOutgoingApplicationFormRO.getWaitOutgoingQuantity().compareTo(BigDecimal.ZERO) > 0) &&
                                (!erpOutgoingApplicationFormRO.getCurrentStatus().equals("3")))
                .collect(Collectors.toList());
    }

    /**
     * 已完成
     *
     * @param processVO
     */
    @Override
    public void finishCall(ActEventEntity processVO) {
        //审核完成以后，形式发票的待开数加一
        pendingNumberService.plusOne(WorkflowKeyEnum.ORDER_OUTGOING, WorkflowKeyEnum.WORK_ORDER_OUTGOING);
    }

    /**
     * 重启流程时事件
     *
     * @param processVO
     */
    @Override
    public void restartCall(ActEventEntity processVO) {
        // 是否存在已开外发订单
        List<String> outgoingOrderNumbers = baseMapper.selectOutgoingOrderNumbers(processVO.getBusinessKey());
        if (CollectionUtil.isNotEmpty(outgoingOrderNumbers)) {
            throw new FlowException(ResultErrorCode.CANNOT_RESTART_THE_PROCESS_HAS_OUTGOING_ORDER, StringUtils.join(outgoingOrderNumbers));
        }

        //重启以后，形式发票的待开数减一
        pendingNumberService.minusOne(WorkflowKeyEnum.ORDER_OUTGOING, WorkflowKeyEnum.WORK_ORDER_OUTGOING);
    }

    @Override
    public Map<String, String> selectPartNameBatch(List<String> workOrderProcessGuid) {
        if (CollUtil.isNotEmpty(workOrderProcessGuid)) {
            List<BaseMapContainer> partNames = baseMapper.selectPartNameBatch(workOrderProcessGuid);
            if (CollUtil.isNotEmpty(partNames)) {
                return partNames.stream().collect(Collectors.toMap(BaseMapContainer::getName, BaseMapContainer::getValue, (e, n) -> e));
            }
        }
        return new HashMap<>(0);
    }

    @Override
    public Map<String, String> selectproductNameBatch(List<String> workOrderProcessGuids) {
        if (CollUtil.isNotEmpty(workOrderProcessGuids)) {
            List<BaseMapContainer> partNames = baseMapper.selectproductNameBatch(workOrderProcessGuids);
            if (CollUtil.isNotEmpty(partNames)) {
                return partNames.stream().collect(Collectors.toMap(BaseMapContainer::getName, BaseMapContainer::getValue, (e, n) -> e));
            }
        }
        return new HashMap<>(0);
    }

    @Override
    public Map<String, BaseMapContainer> selectproductNameBatchs(List<String> workOrderProcessGuids) {
        if (CollUtil.isNotEmpty(workOrderProcessGuids)) {
            List<BaseMapContainer> partNames = baseMapper.selectproductNameBatchs(workOrderProcessGuids);
            if (CollUtil.isNotEmpty(partNames)) {
                return partNames.stream().collect(Collectors.toMap(BaseMapContainer::getName, u -> u));
            }
        }
        return new HashMap<>(0);
    }

    @Override
    public List<ErpOutgoingApplicationVO> getSonListBatch(List<String> workorderGuids) {
        List<ErpOutgoingApplicationVO> sonListBatch = baseMapper.getSonListBatch(workorderGuids);
        LoadDataUtils.loadDataObjTool(sonListBatch,
                materialService::getByIds,
                ErpMaterialMgtMaterialVO::getMaterialGuid,
                ErpOutgoingApplicationVO::getMaterialGuid,
                ErpOutgoingApplicationVO::setMaterialObj,
                null);
        return sonListBatch;
    }
}
