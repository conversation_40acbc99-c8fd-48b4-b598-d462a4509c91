package xy.server.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.mes.entity.MesEquipmentRegistrationStatus;
import xy.server.mes.entity.model.qo.MesEquipmentRegistrationStatusQO;
import xy.server.mes.entity.model.vo.MesEquipmentRegistrationStatusVO;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Mapper
public interface MesEquipmentRegistrationStatusMapper extends BaseMapper<MesEquipmentRegistrationStatus> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    MesEquipmentRegistrationStatusVO getDataByGuid(@Param("guid") String guid);

    /**
     *  根据设备guid获取上机信息
     */
    MesEquipmentRegistrationStatusVO getDataByEquipmentGuid(@Param("equipmentGuid") String equipmentGuid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<MesEquipmentRegistrationStatusVO> findList(@Param("model") MesEquipmentRegistrationStatusQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<MesEquipmentRegistrationStatusVO> findPage(@Param("page") IPage<MesEquipmentRegistrationStatusVO> page,@Param("model") MesEquipmentRegistrationStatusQO model);

    void updateByEquipmentGuid(@Param("equipmentGuid") String equipmentGuid,@Param("bool") boolean bool);
}
