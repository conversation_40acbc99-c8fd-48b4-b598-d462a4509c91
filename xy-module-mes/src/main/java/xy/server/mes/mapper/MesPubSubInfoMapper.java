package xy.server.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.mes.entity.MesPubSubInfo;
import xy.server.mes.entity.model.qo.MesPubSubInfoQO;
import xy.server.mes.entity.model.vo.MesPubSubInfoVO;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Mapper
public interface MesPubSubInfoMapper extends BaseMapper<MesPubSubInfo> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    MesPubSubInfoVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @return
     */
    List<MesPubSubInfoVO> findList();

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<MesPubSubInfoVO> findPage(@Param("page") IPage<MesPubSubInfoVO> page,@Param("model") MesPubSubInfoQO model);

    String getMqttUrl();
}
