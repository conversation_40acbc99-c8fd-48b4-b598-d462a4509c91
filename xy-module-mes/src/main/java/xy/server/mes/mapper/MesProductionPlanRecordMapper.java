package xy.server.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import xy.server.mes.entity.MesProductionPlanRecord;
import xy.server.mes.entity.model.qo.MesProductionPlanRecordQO;
import xy.server.mes.entity.model.ro.MesProductionPlanRecordRO;
import xy.server.mes.entity.model.vo.MesProductionPlanRecordVO;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Mapper
@Repository
public interface MesProductionPlanRecordMapper extends BaseMapper<MesProductionPlanRecord> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    MesProductionPlanRecordVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<MesProductionPlanRecordVO> findList(@Param("model") MesProductionPlanRecordQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<MesProductionPlanRecordVO> findPage(@Param("page") IPage<MesProductionPlanRecordVO> page,@Param("model") MesProductionPlanRecordQO model);

    List<MesProductionPlanRecordRO> findByTasksGuid(@Param("tasksGuid") String tasksGuid);

    Integer updateByTasksGuid(@Param("model") MesProductionPlanRecordRO mesRo);

    Integer updateQuantityByTasksGuid(@Param("tasksGuid") String tasksGuid,
                                      @Param("goodCount") Integer goodCount,
                                      @Param("badCount") Integer badCount);

}
