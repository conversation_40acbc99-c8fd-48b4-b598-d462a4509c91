package xy.server.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import xy.server.mes.entity.MesDataClassification;
import xy.server.mes.entity.model.qo.MesDataClassificationQO;
import xy.server.mes.entity.model.vo.MesDataClassificationVO;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@Mapper
@Repository
public interface MesDataClassificationMapper extends BaseMapper<MesDataClassification> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    MesDataClassificationVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<MesDataClassificationVO> findList(@Param("model") MesDataClassificationQO qo);

    /**
     * 列表查询
     * @return
     */
    List<MesDataClassificationVO> findTreeData();

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<MesDataClassificationVO> findPage(@Param("page") IPage<MesDataClassificationVO> page, @Param("model") MesDataClassificationQO model);

    List<MesDataClassificationVO> findMesDataClassification();

    List<MesDataClassificationVO> findMesFiledList(@Param("model") MesDataClassificationQO qo);
}
