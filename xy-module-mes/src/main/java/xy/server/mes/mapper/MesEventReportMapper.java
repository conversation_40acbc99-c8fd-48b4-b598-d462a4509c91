package xy.server.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import xy.server.mes.entity.MesEventReport;

import java.util.List;
import java.util.Map;

@Mapper
@Repository
public interface MesEventReportMapper extends BaseMapper<MesEventReport> {

    List<MesEventReport> findReportData(@Param("model") MesEventReport qo);

    List<MesEventReport> findReportDataForTime(@Param("model") MesEventReport qo);

    List<Map<String,Object>> findStatisticalData(@Param("model") MesEventReport qo);

    List<Map<String,Object>> findMonthReportData(@Param("model") MesEventReport qo);

    List<MesEventReport> findReportDataByMonth(@Param("model") MesEventReport qo);

    List<MesEventReport> findProductionStandardReport(@Param("model") MesEventReport qo);

    List<MesEventReport> findProductionBackPerformanceReport(@Param("model") MesEventReport qo);

    List<Map<String,Object>> getTaskTimeByTasksGuid(@Param("tasksGuid") String tasksGuid);

    List<Map<String, Object>> findReportDataForTimeToFsyj(@Param("model") MesEventReport qo);
}
