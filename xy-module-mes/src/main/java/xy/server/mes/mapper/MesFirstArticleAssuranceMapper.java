package xy.server.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.mes.entity.MesFirstArticleAssurance;
import xy.server.mes.entity.model.qo.MesFirstArticleAssuranceQO;
import xy.server.mes.entity.model.vo.MesFirstArticleAssuranceVO;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Mapper
public interface MesFirstArticleAssuranceMapper extends BaseMapper<MesFirstArticleAssurance> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    MesFirstArticleAssuranceVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<MesFirstArticleAssuranceVO> findList(@Param("model") MesFirstArticleAssuranceQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<MesFirstArticleAssuranceVO> findPage(@Param("page") IPage<MesFirstArticleAssuranceVO> page,@Param("model") MesFirstArticleAssuranceQO model);

    MesFirstArticleAssuranceVO getOneByTaskGuid(@Param("tasksGuid") String tasksGuid);
}
