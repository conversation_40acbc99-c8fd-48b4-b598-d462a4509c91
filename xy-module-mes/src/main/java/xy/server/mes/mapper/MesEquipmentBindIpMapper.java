package xy.server.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.mes.entity.MesEquipmentBindIp;
import xy.server.mes.entity.model.qo.MesEquipmentBindIpQO;
import xy.server.mes.entity.model.vo.MesEquipmentBindIpVO;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-14
 */
@Mapper
public interface MesEquipmentBindIpMapper extends BaseMapper<MesEquipmentBindIp> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    MesEquipmentBindIpVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<MesEquipmentBindIpVO> findList(@Param("model") MesEquipmentBindIpQO qo);

    /**
     * 根据ip查询对应设备信息
     * @param qo
     * @return
     */
    MesEquipmentBindIpVO findListByIpAddress(@Param("model") MesEquipmentBindIpQO qo);


    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<MesEquipmentBindIpVO> findPage(@Param("page") IPage<MesEquipmentBindIpVO> page,@Param("model") MesEquipmentBindIpQO model);
}
