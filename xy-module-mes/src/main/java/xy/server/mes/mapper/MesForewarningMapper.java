package xy.server.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.mes.entity.MesForewarning;
import xy.server.mes.entity.model.qo.MesForewarningQO;
import xy.server.mes.entity.model.vo.MesForewarningVO;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Mapper
public interface MesForewarningMapper extends BaseMapper<MesForewarning> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    MesForewarningVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<MesForewarningVO> findList(@Param("model") MesForewarningQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<MesForewarningVO> findPage(@Param("page") IPage<MesForewarningVO> page,@Param("model") MesForewarningQO model);

    List<MesForewarningVO> initMesForewarningValue();

}
