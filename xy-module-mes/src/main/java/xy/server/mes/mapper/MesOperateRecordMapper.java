package xy.server.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.mes.entity.MesOperateRecord;
import xy.server.mes.entity.model.qo.MesOperateRecordQO;
import xy.server.mes.entity.model.vo.MesOperateRecordVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Mapper
public interface MesOperateRecordMapper extends BaseMapper<MesOperateRecord> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    MesOperateRecordVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<MesOperateRecordVO> findList(@Param("model") MesOperateRecordQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<MesOperateRecordVO> findPage(@Param("page") IPage<MesOperateRecordVO> page,@Param("model") MesOperateRecordQO model);

    MesOperateRecordVO getOneOperate(@Param("tasksGuid") String tasksGuid);

    void updateByTableId(@Param("id") String id,@Param("endTime")  LocalDateTime endTime);
}
