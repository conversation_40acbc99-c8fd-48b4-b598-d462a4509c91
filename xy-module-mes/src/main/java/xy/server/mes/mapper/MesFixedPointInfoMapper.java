package xy.server.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.mes.entity.MesFixedPointInfo;
import xy.server.mes.entity.model.qo.MesFixedPointInfoQO;
import xy.server.mes.entity.model.vo.MesFixedPointInfoVO;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Mapper
public interface MesFixedPointInfoMapper extends BaseMapper<MesFixedPointInfo> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    MesFixedPointInfoVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<MesFixedPointInfoVO> findList(@Param("model") MesFixedPointInfoQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<MesFixedPointInfoVO> findPage(@Param("page") IPage<MesFixedPointInfoVO> page,@Param("model") MesFixedPointInfoQO model);

    // 根据设备guid查询定点信息
    MesFixedPointInfoVO getDataByEquipmentGuid(@Param("equipmentGuid") String equipmentGuid);
}
