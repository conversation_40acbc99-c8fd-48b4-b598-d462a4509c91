package xy.server.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.mes.entity.MesDefectiveProductsRecord;
import xy.server.mes.entity.model.qo.MesDefectiveProductsRecordQO;
import xy.server.mes.entity.model.vo.MesDefectiveProductsRecordVO;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-28
 */
@Mapper
public interface MesDefectiveProductsRecordMapper extends BaseMapper<MesDefectiveProductsRecord> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    MesDefectiveProductsRecordVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<MesDefectiveProductsRecordVO> findList(@Param("model") MesDefectiveProductsRecordQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<MesDefectiveProductsRecordVO> findPage(@Param("page") IPage<MesDefectiveProductsRecordVO> page,@Param("model") MesDefectiveProductsRecordQO model);

    Integer getOneByTaskGuid(@Param("taskGuid") String taskGuid);
}
