package xy.server.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.mes.entity.MesEquipmentRegistration;
import xy.server.mes.entity.model.qo.MesEquipmentRegistrationQO;
import xy.server.mes.entity.model.vo.MesEquipmentRegistrationVO;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Mapper
public interface MesEquipmentRegistrationMapper extends BaseMapper<MesEquipmentRegistration> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    MesEquipmentRegistrationVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<MesEquipmentRegistrationVO> findList(@Param("model") MesEquipmentRegistrationQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<MesEquipmentRegistrationVO> findPage(@Param("page") IPage<MesEquipmentRegistrationVO> page,@Param("model") MesEquipmentRegistrationQO model);
}
