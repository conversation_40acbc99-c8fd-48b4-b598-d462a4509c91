package xy.server.mes.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.mes.entity.MesDefectiveProductsRecord;
import xy.server.mes.entity.model.qo.MesDefectiveProductsRecordQO;
import xy.server.mes.entity.model.ro.MesDefectiveProductsRecordRO;
import xy.server.mes.entity.model.vo.MesDefectiveProductsRecordVO;

import java.util.List;

/**
 * @apiNote  服务类
 * <AUTHOR>
 * @since 2024-09-28
 */
public interface IMesDefectiveProductsRecordService extends IService<MesDefectiveProductsRecord> {
                boolean create(MesDefectiveProductsRecordRO ro);

    boolean delete(String mesDefectiveProductsGuid);

    boolean deleteByBatch(List<String> mesDefectiveProductsGuids);

    boolean update(MesDefectiveProductsRecordRO ro);

    boolean saveDate(InsertOrUpdateList<MesDefectiveProductsRecordRO> dataList);

    MesDefectiveProductsRecordVO getDataById(String mesDefectiveProductsGuid);

    List<MesDefectiveProductsRecordVO> findList(MesDefectiveProductsRecordQO qo);

    IPage<MesDefectiveProductsRecordVO> findPage(PageParams<MesDefectiveProductsRecordQO> pageParams);

    Integer getOneByTaskGuid(String taskGuid);
}
