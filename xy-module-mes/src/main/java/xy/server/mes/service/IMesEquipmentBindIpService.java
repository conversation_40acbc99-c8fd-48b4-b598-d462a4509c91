package xy.server.mes.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.mes.entity.MesEquipmentBindIp;
import xy.server.mes.entity.model.qo.MesEquipmentBindIpQO;
import xy.server.mes.entity.model.ro.MesEquipmentBindIpRO;
import xy.server.mes.entity.model.vo.MesEquipmentBindIpVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @apiNote  服务类
 * <AUTHOR>
 * @since 2024-08-14
 */
public interface IMesEquipmentBindIpService extends IService<MesEquipmentBindIp> {
                boolean create(MesEquipmentBindIpRO ro);

    boolean delete(String equipmentBindIpGuid);

    boolean deleteByBatch(List<String> equipmentBindIpGuids);

    boolean update(MesEquipmentBindIpRO ro);

    boolean saveDate(InsertOrUpdateList<MesEquipmentBindIpRO> dataList);

    MesEquipmentBindIpVO getDataById(String equipmentBindIpGuid);

    List<MesEquipmentBindIpVO> findList(MesEquipmentBindIpQO qo);

    IPage<MesEquipmentBindIpVO> findPage(PageParams<MesEquipmentBindIpQO> pageParams);

    MesEquipmentBindIpVO getOneByIp(HttpServletRequest request, HttpServletResponse response);
}
