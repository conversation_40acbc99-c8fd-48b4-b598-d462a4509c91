package xy.server.mes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.mes.entity.MesFixedPointInfo;
import xy.server.mes.entity.model.qo.MesFixedPointInfoQO;
import xy.server.mes.entity.model.ro.MesFixedPointInfoRO;
import xy.server.mes.entity.model.vo.MesFixedPointInfoVO;
import xy.server.mes.mapper.MesFixedPointInfoMapper;
import xy.server.mes.service.IMesFixedPointInfoService;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Service
public class MesFixedPointInfoServiceImpl extends ServiceImpl<MesFixedPointInfoMapper, MesFixedPointInfo> implements IMesFixedPointInfoService {
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(MesFixedPointInfoRO ro){
        MesFixedPointInfo entity = new MesFixedPointInfo();
        BeanUtil.copyProperties(ro, entity);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String mesFixedPointGuid){
        return super.removeById(mesFixedPointGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> mesFixedPointGuids) {
        for (String mesFixedPointGuid : mesFixedPointGuids) {
            super.removeById(mesFixedPointGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MesFixedPointInfoRO ro){
        MesFixedPointInfo entity = new MesFixedPointInfo();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<MesFixedPointInfoRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public MesFixedPointInfoVO getDataById(String mesFixedPointGuid){
        return baseMapper.getDataByGuid(mesFixedPointGuid);
    }

    // 根据设备guid查询定点信息
    @Override
    public MesFixedPointInfoVO getDataByEquipmentGuid(String equipmentGuid){
        return baseMapper.getDataByEquipmentGuid(equipmentGuid);
    }

    @Override
    public List<MesFixedPointInfoVO> findList(MesFixedPointInfoQO qo){
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<MesFixedPointInfoVO> findPage(PageParams<MesFixedPointInfoQO> pageParams){
        IPage<MesFixedPointInfoVO> page = pageParams.buildPage();
        MesFixedPointInfoQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }
}
