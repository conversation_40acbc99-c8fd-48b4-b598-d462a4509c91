package xy.server.mes.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.mes.entity.MesFirstArticleAssurance;
import xy.server.mes.entity.model.qo.MesFirstArticleAssuranceQO;
import xy.server.mes.entity.model.ro.MesFirstArticleAssuranceRO;
import xy.server.mes.entity.model.vo.MesFirstArticleAssuranceVO;

import java.util.List;

/**
 * @apiNote  服务类
 * <AUTHOR>
 * @since 2024-11-05
 */
public interface IMesFirstArticleAssuranceService extends IService<MesFirstArticleAssurance> {
                boolean create(MesFirstArticleAssuranceRO ro);

    boolean delete(String id);

    boolean deleteByBatch(List<String> ids);

    boolean update(MesFirstArticleAssuranceRO ro);

    boolean saveDate(InsertOrUpdateList<MesFirstArticleAssuranceRO> dataList,String tenantGuid);

    MesFirstArticleAssuranceVO getDataById(String id);

    List<MesFirstArticleAssuranceVO> findList(MesFirstArticleAssuranceQO qo);

    IPage<MesFirstArticleAssuranceVO> findPage(PageParams<MesFirstArticleAssuranceQO> pageParams);

    MesFirstArticleAssuranceVO getOneByTaskGuid(String tasksGuid);
}
