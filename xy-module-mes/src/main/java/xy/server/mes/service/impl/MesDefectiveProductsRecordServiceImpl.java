package xy.server.mes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.mes.entity.MesDefectiveProductsRecord;
import xy.server.mes.entity.model.qo.MesDefectiveProductsRecordQO;
import xy.server.mes.entity.model.ro.MesDefectiveProductsRecordRO;
import xy.server.mes.entity.model.vo.MesDefectiveProductsRecordVO;
import xy.server.mes.mapper.MesDefectiveProductsRecordMapper;
import xy.server.mes.service.IMesDefectiveProductsRecordService;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-28
 */
@Service
public class MesDefectiveProductsRecordServiceImpl extends ServiceImpl<MesDefectiveProductsRecordMapper, MesDefectiveProductsRecord> implements IMesDefectiveProductsRecordService {
                @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(MesDefectiveProductsRecordRO ro){
        MesDefectiveProductsRecord entity = new MesDefectiveProductsRecord();
        BeanUtil.copyProperties(ro, entity);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String mesDefectiveProductsGuid){
        return super.removeById(mesDefectiveProductsGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> mesDefectiveProductsGuids) {
        for (String mesDefectiveProductsGuid : mesDefectiveProductsGuids) {
            super.removeById(mesDefectiveProductsGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MesDefectiveProductsRecordRO ro){
        MesDefectiveProductsRecord entity = new MesDefectiveProductsRecord();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<MesDefectiveProductsRecordRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public MesDefectiveProductsRecordVO getDataById(String mesDefectiveProductsGuid){
        return baseMapper.getDataByGuid(mesDefectiveProductsGuid);
    }

    @Override
    public List<MesDefectiveProductsRecordVO> findList(MesDefectiveProductsRecordQO qo){
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<MesDefectiveProductsRecordVO> findPage(PageParams<MesDefectiveProductsRecordQO> pageParams){
        IPage<MesDefectiveProductsRecordVO> page = pageParams.buildPage();
        MesDefectiveProductsRecordQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }

    @Override
    public Integer getOneByTaskGuid(String taskGuid) {
        return baseMapper.getOneByTaskGuid(taskGuid);
    }
}
