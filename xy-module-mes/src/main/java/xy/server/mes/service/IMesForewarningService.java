package xy.server.mes.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.mes.entity.MesForewarning;
import xy.server.mes.entity.model.qo.MesForewarningQO;
import xy.server.mes.entity.model.ro.MesForewarningRO;
import xy.server.mes.entity.model.vo.MesForewarningVO;

import java.util.List;

/**
 * @apiNote  服务类
 * <AUTHOR>
 * @since 2024-06-13
 */
public interface IMesForewarningService extends IService<MesForewarning> {
                    boolean create(MesForewarningRO ro);

    boolean delete(String mesForewarningGuid);

    boolean deleteByBatch(List<String> mesForewarningGuids);

    boolean update(MesForewarningRO ro);

    boolean saveData(InsertOrUpdateList<MesForewarningRO> dataList);

    MesForewarningVO getDataById(String mesForewarningGuid);

    List<MesForewarningVO> findList(MesForewarningQO qo);

    IPage<MesForewarningVO> findPage(PageParams<MesForewarningQO> pageParams);

    void initMesForewarningValue();
}
