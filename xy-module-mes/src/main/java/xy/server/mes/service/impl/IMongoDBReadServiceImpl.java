package xy.server.mes.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xunyue.tenant.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import xy.server.mes.entity.model.ro.MesRealTimeDataRO;
import xy.server.mes.entity.model.vo.MongoDBMesPrimitiveDataVO;
import xy.server.mes.entity.model.vo.MongoDBMesRealTimeDataVO;
import xy.server.mes.service.IMongoDBReadService;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class IMongoDBReadServiceImpl implements IMongoDBReadService {
    @Value("${mes-platform.path}")
    private String mesPlatformPath;


    @Override
    public List<MongoDBMesRealTimeDataVO> readAndProcessRealTimeData(LocalDateTime ldStartTime, LocalDateTime ldEndTime) {
        return requestMongoDBData("/mes/mongodb-read-data/read-real-time-data",
                                  ldStartTime, ldEndTime,
                                  MongoDBMesRealTimeDataVO.class);
    }

    @Override
    public List<MongoDBMesPrimitiveDataVO> readAndProcessPrimitiveData(LocalDateTime ldStartTime, LocalDateTime ldEndTime) {
        return requestMongoDBData("/mes/mongodb-read-data/read-primitive-data",
                                  ldStartTime, ldEndTime,
                                  MongoDBMesPrimitiveDataVO.class);
    }

    /**
     * 通用的MongoDB数据请求方法
     *
     * @param endpoint API端点路径
     * @param ldStartTime 开始时间
     * @param ldEndTime 结束时间
     * @param clazz 目标VO类型
     * @param <T> 泛型类型
     * @return 转换后的VO对象列表
     */
    private <T> List<T> requestMongoDBData(String endpoint, LocalDateTime ldStartTime,
                                           LocalDateTime ldEndTime, Class<T> clazz) {
        // 构建请求参数
        MesRealTimeDataRO mesRealTimeDataRO = new MesRealTimeDataRO();
        mesRealTimeDataRO.setStartTime(ldStartTime.toString());
        mesRealTimeDataRO.setEndTime(ldEndTime.toString());
        String body = JSONObject.toJSONString(mesRealTimeDataRO);

        String url = mesPlatformPath + endpoint;
        String responseStr = "";
        try {
            responseStr = HttpUtil.doPostJSON(url, body);
            log.info("请求地址：{},请求参数，{} ,接口返回值:{}", url, body, responseStr);

            // 使用通用方法解析响应
            return parseResponseToVO(responseStr, clazz);

        } catch (Exception e) {
            log.error("doPostJSON请求失败,请求url={} ,请求路径={}, 请求参数={}", responseStr, url, body);
            log.error("doPostJSON请求失败，异常信息:{} ", e);
            return Collections.emptyList();
        }
    }

    /**
     * 通用的响应解析方法
     *
     * @param responseStr HTTP响应字符串
     * @param clazz 目标VO类型
     * @param <T> 泛型类型
     * @return 转换后的VO对象列表
     */
    private <T> List<T> parseResponseToVO(String responseStr, Class<T> clazz) {
        if (!StringUtils.hasText(responseStr)) {
            log.warn("响应数据为空");
            return Collections.emptyList();
        }

        try {
            // 解析响应JSON
            JSONObject responseJson = JSONObject.parseObject(responseStr);

            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (code == null || code != 0) {
                log.warn("接口返回异常，code: {}, msg: {}", code, responseJson.getString("msg"));
                return Collections.emptyList();
            }

            // 直接使用fastjson反序列化data数组为VO列表
            List<T> dataList = JSONObject.parseArray(
                    responseJson.getString("data"),
                    clazz
            );

            if (dataList == null) {
                log.info("接口返回数据为空");
                return Collections.emptyList();
            }

            log.info("成功转换{}数据，共{}条记录", clazz.getSimpleName(), dataList.size());

            return dataList;

        } catch (Exception e) {
            log.error("解析{}响应数据失败，响应内容: {}", clazz.getSimpleName(), responseStr, e);
            return Collections.emptyList();
        }
    }
}
