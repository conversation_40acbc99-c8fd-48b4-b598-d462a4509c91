package xy.server.mes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.mes.entity.MesPubSubInfo;
import xy.server.mes.entity.model.qo.MesPubSubInfoQO;
import xy.server.mes.entity.model.ro.MesPubSubInfoRO;
import xy.server.mes.entity.model.vo.MesPubSubInfoVO;
import xy.server.mes.mapper.MesPubSubInfoMapper;
import xy.server.mes.service.IMesPubSubInfoService;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Slf4j
@Service
public class MesPubSubInfoServiceImpl extends ServiceImpl<MesPubSubInfoMapper, MesPubSubInfo> implements IMesPubSubInfoService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(MesPubSubInfoRO ro){
        MesPubSubInfo entity = new MesPubSubInfo();
        BeanUtil.copyProperties(ro, entity);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String mesPubSubInfoGuid){
        return super.removeById(mesPubSubInfoGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> mesPubSubInfoGuids) {
        for (String mesPubSubInfoGuid : mesPubSubInfoGuids) {
            super.removeById(mesPubSubInfoGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MesPubSubInfoRO ro){
        MesPubSubInfo entity = new MesPubSubInfo();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<MesPubSubInfoRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public MesPubSubInfoVO getDataById(String mesPubSubInfoGuid){
        return baseMapper.getDataByGuid(mesPubSubInfoGuid);
    }

    @Override
    public List<MesPubSubInfoVO> findList(){
        return baseMapper.findList();
    }

    @Override
    public IPage<MesPubSubInfoVO> findPage(PageParams<MesPubSubInfoQO> pageParams){
        IPage<MesPubSubInfoVO> page = pageParams.buildPage();
        MesPubSubInfoQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }

    @Override
    public String getMqttUrl() {
        return baseMapper.getMqttUrl();
    }

}
