package xy.server.mes.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.mes.entity.MesDataClassification;
import xy.server.mes.entity.model.qo.MesDataClassificationQO;
import xy.server.mes.entity.model.ro.MesDataClassificationRO;
import xy.server.mes.entity.model.vo.MesDataClassificationVO;

import java.util.List;
import java.util.Map;

/**
 * @apiNote  服务类
 * <AUTHOR>
 * @since 2024-04-22
 */
public interface IMesDataClassificationService extends IService<MesDataClassification> {
    boolean create(MesDataClassificationRO ro);

    boolean delete(String dataClassificationGuid);

    boolean deleteByBatch(List<String> dataClassificationGuids);

    boolean update(MesDataClassificationRO ro);

    boolean saveDate(InsertOrUpdateList<MesDataClassificationRO> dataList,String tenantGuid);

    MesDataClassificationVO getDataById(String dataClassificationGuid);

    List<MesDataClassificationVO> findList(MesDataClassificationQO qo);

    List<MesDataClassificationVO> findTreeData();

    List<MesDataClassificationVO> findMesDataClassification();

    IPage<MesDataClassificationVO> findPage(PageParams<MesDataClassificationQO> pageParams);

    void handelInitData(Map<String, Object> queueMap);

    List<MesDataClassificationVO> findMesFiledList(MesDataClassificationQO qo);
}
