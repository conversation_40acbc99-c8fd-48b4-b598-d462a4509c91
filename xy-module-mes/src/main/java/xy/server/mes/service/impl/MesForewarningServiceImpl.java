package xy.server.mes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import com.xunyue.config.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.mes.entity.MesForewarning;
import xy.server.mes.entity.model.qo.MesForewarningQO;
import xy.server.mes.entity.model.ro.MesForewarningRO;
import xy.server.mes.entity.model.vo.MesForewarningVO;
import xy.server.mes.mapper.MesForewarningMapper;
import xy.server.mes.mapper.MesWarningInformationMapper;
import xy.server.mes.service.IMesForewarningService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Service
public class MesForewarningServiceImpl extends ServiceImpl<MesForewarningMapper, MesForewarning> implements IMesForewarningService {

    @Autowired
    private RedisUtil redisUtils;

    @Autowired
    private MesWarningInformationMapper mesWarningInformationMapper;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(MesForewarningRO ro){
        MesForewarning entity = new MesForewarning();
        BeanUtil.copyProperties(ro, entity);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String mesForewarningGuid){
        return super.removeById(mesForewarningGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> mesForewarningGuids) {
        for (String mesForewarningGuid : mesForewarningGuids) {
            super.removeById(mesForewarningGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MesForewarningRO ro){
        MesForewarning entity = new MesForewarning();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveData(InsertOrUpdateList<MesForewarningRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            for (MesForewarningRO mesForewarningRO : dataList.getInsertList()) {
                mesForewarningRO.setIsPercent(false);
            }
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public MesForewarningVO getDataById(String mesForewarningGuid){
        return baseMapper.getDataByGuid(mesForewarningGuid);
    }

    @Override
    public List<MesForewarningVO> findList(MesForewarningQO qo){
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<MesForewarningVO> findPage(PageParams<MesForewarningQO> pageParams){
        IPage<MesForewarningVO> page = pageParams.buildPage();
        MesForewarningQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }

    /**
     *  预警字段：
     *  停机时间、调机时间、装版时间、任务完成时间、维修时间、故障时间
     *  停机次数、装版次数、超计划数、取样数
     */
    @Override
    public void initMesForewarningValue(){
        List<MesForewarningVO> list = baseMapper.initMesForewarningValue();
        if(ObjectUtils.isNotEmpty(list)){
            for (MesForewarningVO mesForewarningVO : list) {
                // 将预警的类别与预警范围存入redis
                Map map = new HashMap();
                map.put("minValue",mesForewarningVO.getMinValue());
                map.put("maxValue",mesForewarningVO.getMaxValue());
                map.put("equipmentCode",mesForewarningVO.getEquipmentCode());
                map.put("wxUserId",mesForewarningVO.getWxUserId());
                map.put("mesForewarningLevelName",mesForewarningVO.getMesForewarningLevelName());
                redisUtils.set(mesForewarningVO.getMesForewarningName(),map);
            }
        }
    }

}
