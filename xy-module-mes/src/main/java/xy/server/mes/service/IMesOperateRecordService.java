package xy.server.mes.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.mes.entity.MesOperateRecord;
import xy.server.mes.entity.model.qo.MesOperateRecordQO;
import xy.server.mes.entity.model.ro.MesOperateRecordRO;
import xy.server.mes.entity.model.vo.MesOperateRecordVO;

import java.util.List;

/**
 * @apiNote  服务类
 * <AUTHOR>
 * @since 2024-11-07
 */
public interface IMesOperateRecordService extends IService<MesOperateRecord> {
                boolean create(MesOperateRecordRO ro);

    boolean delete(String id);

    boolean deleteByBatch(List<String> ids);

    boolean update(MesOperateRecordRO ro);

    boolean saveDate(InsertOrUpdateList<MesOperateRecordRO> dataList,String tenantGuid);

    MesOperateRecordVO getDataById(String id);

    List<MesOperateRecordVO> findList(MesOperateRecordQO qo);

    IPage<MesOperateRecordVO> findPage(PageParams<MesOperateRecordQO> pageParams);

    MesOperateRecordVO getOneOperate(String tasksGuid);
}
