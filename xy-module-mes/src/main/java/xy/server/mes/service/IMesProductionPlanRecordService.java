package xy.server.mes.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.mes.entity.MesProductionPlanRecord;
import xy.server.mes.entity.model.qo.MesProductionPlanRecordQO;
import xy.server.mes.entity.model.ro.MesProductionPlanRecordRO;
import xy.server.mes.entity.model.vo.MesProductionPlanRecordVO;

import java.util.List;

/**
 * @apiNote  服务类
 * <AUTHOR>
 * @since 2024-05-27
 */
public interface IMesProductionPlanRecordService extends IService<MesProductionPlanRecord> {
    boolean create(MesProductionPlanRecordRO ro);

    boolean delete(String id);

    boolean deleteByBatch(List<String> ids);

    boolean update(MesProductionPlanRecordRO ro);

    boolean saveDate(InsertOrUpdateList<MesProductionPlanRecordRO> dataList);

    MesProductionPlanRecordVO getDataById(String id);

    List<MesProductionPlanRecordVO> findList(MesProductionPlanRecordQO qo);

    IPage<MesProductionPlanRecordVO> findPage(PageParams<MesProductionPlanRecordQO> pageParams);

    List<MesProductionPlanRecordRO> findByTasksGuid(String tasksGuid);

    Integer updateByTasksGuid(MesProductionPlanRecordRO mesRo);

    Boolean deleteByTaskGuid(String taskGuid);
}
