package xy.server.mes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.mes.entity.MesEquipmentRegistrationStatus;
import xy.server.mes.entity.model.qo.MesEquipmentRegistrationStatusQO;
import xy.server.mes.entity.model.ro.MesEquipmentRegistrationStatusRO;
import xy.server.mes.entity.model.vo.MesEquipmentRegistrationStatusVO;
import xy.server.mes.mapper.MesEquipmentRegistrationStatusMapper;
import xy.server.mes.service.IMesEquipmentRegistrationStatusService;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Service
public class MesEquipmentRegistrationStatusServiceImpl extends ServiceImpl<MesEquipmentRegistrationStatusMapper, MesEquipmentRegistrationStatus> implements IMesEquipmentRegistrationStatusService {
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(MesEquipmentRegistrationStatusRO ro) {
        MesEquipmentRegistrationStatus entity = new MesEquipmentRegistrationStatus();
        BeanUtil.copyProperties(ro, entity);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String mesEquipmentRegistrationStatusGuid) {
        return super.removeById(mesEquipmentRegistrationStatusGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> mesEquipmentRegistrationStatusGuids) {
        for (String mesEquipmentRegistrationStatusGuid : mesEquipmentRegistrationStatusGuids) {
            super.removeById(mesEquipmentRegistrationStatusGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MesEquipmentRegistrationStatusRO ro) {
        MesEquipmentRegistrationStatus entity = new MesEquipmentRegistrationStatus();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<MesEquipmentRegistrationStatusRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public MesEquipmentRegistrationStatusVO getDataById(String mesEquipmentRegistrationStatusGuid) {
        return baseMapper.getDataByGuid(mesEquipmentRegistrationStatusGuid);
    }

    @Override
    public List<MesEquipmentRegistrationStatusVO> findList(MesEquipmentRegistrationStatusQO qo) {
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<MesEquipmentRegistrationStatusVO> findPage(PageParams<MesEquipmentRegistrationStatusQO> pageParams) {
        IPage<MesEquipmentRegistrationStatusVO> page = pageParams.buildPage();
        MesEquipmentRegistrationStatusQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }

    // 根据设备guid查询是否上机记录
    @Override
    public MesEquipmentRegistrationStatusVO getDataByEquipmentGuid(String equipmentGuid) {
        MesEquipmentRegistrationStatusVO dataByEquipmentGuid = baseMapper.getDataByEquipmentGuid(equipmentGuid);
        dataByEquipmentGuid = ObjectUtils.isEmpty(dataByEquipmentGuid) ? new MesEquipmentRegistrationStatusVO() : dataByEquipmentGuid;
        return dataByEquipmentGuid;
    }

    @Override
    public boolean updateByEquipmentGuid(String equipmentGuid,boolean bool) {
        baseMapper.updateByEquipmentGuid(equipmentGuid,bool);
        return true;
    }

}
