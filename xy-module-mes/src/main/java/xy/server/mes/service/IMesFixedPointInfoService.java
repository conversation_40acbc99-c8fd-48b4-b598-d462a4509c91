package xy.server.mes.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.mes.entity.MesFixedPointInfo;
import xy.server.mes.entity.model.qo.MesFixedPointInfoQO;
import xy.server.mes.entity.model.ro.MesFixedPointInfoRO;
import xy.server.mes.entity.model.vo.MesFixedPointInfoVO;

import java.util.List;

/**
 * @apiNote  服务类
 * <AUTHOR>
 * @since 2024-07-10
 */
public interface IMesFixedPointInfoService extends IService<MesFixedPointInfo> {
                boolean create(MesFixedPointInfoRO ro);

    boolean delete(String mesFixedPointGuid);

    boolean deleteByBatch(List<String> mesFixedPointGuids);

    boolean update(MesFixedPointInfoRO ro);

    boolean saveDate(InsertOrUpdateList<MesFixedPointInfoRO> dataList);

    MesFixedPointInfoVO getDataById(String mesFixedPointGuid);

    MesFixedPointInfoVO getDataByEquipmentGuid(String equipmentGuid);

    List<MesFixedPointInfoVO> findList(MesFixedPointInfoQO qo);

    IPage<MesFixedPointInfoVO> findPage(PageParams<MesFixedPointInfoQO> pageParams);
}
