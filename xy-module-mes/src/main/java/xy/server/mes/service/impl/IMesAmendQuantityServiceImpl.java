package xy.server.mes.service.impl;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import xy.server.mes.entity.MesAmendQuantity;
import xy.server.mes.entity.model.ro.MesProductionPlanRecordRO;
import xy.server.mes.mapper.MesProductionPlanRecordMapper;
import xy.server.mes.service.IMesAmendQuantityService;

import java.util.List;

@Service
public class IMesAmendQuantityServiceImpl implements IMesAmendQuantityService {

    @Autowired
    MesProductionPlanRecordMapper mesProductionPlanRecordMapper;
    @Override
    public Boolean amendQuantity(MesAmendQuantity mesAmendQuantity) {
        // 根据任务guid查询mes开始任务记录
        List<MesProductionPlanRecordRO> byTasksGuid = mesProductionPlanRecordMapper.findByTasksGuid(mesAmendQuantity.getTasksGuid());
        if (ObjectUtils.isNotEmpty(byTasksGuid)) {
            MesProductionPlanRecordRO mesProductionPlanRecordRO = byTasksGuid.get(0);
            Integer goodCount = calculateTotalGoodCount(mesProductionPlanRecordRO, mesAmendQuantity);
            Integer badCount = calculateTotalBadCount(mesProductionPlanRecordRO, mesAmendQuantity);
            mesProductionPlanRecordMapper.updateQuantityByTasksGuid(mesAmendQuantity.getTasksGuid(), goodCount, badCount);
        }
        return true;
    }

    public Integer calculateTotalGoodCount(MesProductionPlanRecordRO mesProductionPlanRecordRO,
                                       MesAmendQuantity mesAmendQuantity) {
        return (mesProductionPlanRecordRO != null ? mesProductionPlanRecordRO.getGoodCount() : 0) +
                (mesAmendQuantity != null ? mesAmendQuantity.getNonDefectiveQuantity() : 0);
    }

    public Integer calculateTotalBadCount(MesProductionPlanRecordRO mesProductionPlanRecordRO,
                                      MesAmendQuantity mesAmendQuantity) {
        return (mesProductionPlanRecordRO != null ? mesProductionPlanRecordRO.getBadCount() : 0) +
                (mesAmendQuantity != null ? mesAmendQuantity.getDefectsQuantity() : 0);
    }
}
