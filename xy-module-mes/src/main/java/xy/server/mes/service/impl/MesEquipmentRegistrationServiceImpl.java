package xy.server.mes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import com.xunyue.tenant.util.HttpUtil;
import com.xy.util.BaseContext;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.basic.util.MinioUtilS;
import xy.server.mes.entity.MesEquipmentRegistration;
import xy.server.mes.entity.model.qo.MesEquipmentRegistrationQO;
import xy.server.mes.entity.model.ro.MesEquipmentRegistrationRO;
import xy.server.mes.entity.model.ro.MesEquipmentRegistrationStatusRO;
import xy.server.mes.entity.model.vo.MesEquipmentRegistrationStatusVO;
import xy.server.mes.entity.model.vo.MesEquipmentRegistrationVO;
import xy.server.mes.mapper.MesEquipmentRegistrationMapper;
import xy.server.mes.service.IMesEquipmentRegistrationService;
import xy.server.mes.service.IMesEquipmentRegistrationStatusService;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Service
public class MesEquipmentRegistrationServiceImpl extends ServiceImpl<MesEquipmentRegistrationMapper, MesEquipmentRegistration> implements IMesEquipmentRegistrationService {

    @Autowired
    private MinioUtilS minioUtilS;

    @Value("${mes-platform.path}")
    private String mesPlatformPath;

    @Autowired
    private IMesEquipmentRegistrationStatusService iMesEquipmentRegistrationStatusService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(MesEquipmentRegistrationRO ro){
        MesEquipmentRegistration entity = new MesEquipmentRegistration();
        BeanUtil.copyProperties(ro, entity);
        boolean bool = super.save(entity);
        if(bool){
            // 进行上机登记状态信息保存
            MesEquipmentRegistrationStatusVO dataByEquipmentGuid = iMesEquipmentRegistrationStatusService.getDataByEquipmentGuid(entity.getRegistrationEquipmentGuid());
            if(ObjectUtils.isNotEmpty(dataByEquipmentGuid.getEquipmentGuid())){
                // true是上机，false是下机
                iMesEquipmentRegistrationStatusService.updateByEquipmentGuid(entity.getRegistrationEquipmentGuid(),true);
            }else {
                MesEquipmentRegistrationStatusRO mesEquipmentRegistrationStatusRO = new MesEquipmentRegistrationStatusRO();
                mesEquipmentRegistrationStatusRO.setEquipmentGuid(entity.getRegistrationEquipmentGuid());
                mesEquipmentRegistrationStatusRO.setEquipmentName(entity.getEquipmentName());
                mesEquipmentRegistrationStatusRO.setEquipmentCode(entity.getEquipmentCode());
                mesEquipmentRegistrationStatusRO.setEquipmentStatus(true);
                iMesEquipmentRegistrationStatusService.create(mesEquipmentRegistrationStatusRO);
            }
            String strEntity = JSONObject.toJSONString(entity);
            // 保存成功之后，发送请求给web前端，继续进行上机登记操作。
            HttpUtil.doPostJSON(mesPlatformPath+"/mes/mes-notice-to-erp-web/notificationWebByMqtt",strEntity);
        }
        return bool;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String mesEquipmentRegistrationGuid){
        return super.removeById(mesEquipmentRegistrationGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> mesEquipmentRegistrationGuids) {
        for (String mesEquipmentRegistrationGuid : mesEquipmentRegistrationGuids) {
            super.removeById(mesEquipmentRegistrationGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MesEquipmentRegistrationRO ro){
        MesEquipmentRegistration entity = new MesEquipmentRegistration();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<MesEquipmentRegistrationRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public MesEquipmentRegistrationVO getDataById(String mesEquipmentRegistrationGuid){
        return baseMapper.getDataByGuid(mesEquipmentRegistrationGuid);
    }

    /**
     *  将存储的多图片字符串名称，调用minio获取预览图片的地址
     * @param qo
     * @return
     */
    @Override
    public List<MesEquipmentRegistrationVO> findList(MesEquipmentRegistrationQO qo){
        // 获取当前登录人staffGuid
        String currentStaffGuid = BaseContext.getStaffGuid();
        qo.setCreatorGuid(currentStaffGuid);
        List<MesEquipmentRegistrationVO> MesEquipmentRegistrationVOList = baseMapper.findList(qo);
        if(ObjectUtils.isNotEmpty(MesEquipmentRegistrationVOList)){
            for (MesEquipmentRegistrationVO mesEquipmentRegistrationVO : MesEquipmentRegistrationVOList) {
                List<String> previewAddressList = new ArrayList<>();
                if (mesEquipmentRegistrationVO.getFileName().contains(",")) {
                    String[] filesPath = mesEquipmentRegistrationVO.getFileName().split(",");
                    for (String path : filesPath) {
                        previewAddressList.add(minioUtilS.getPreviewAddress(path));
                    }
                } else {
                    String previewAddress = minioUtilS.getPreviewAddress(mesEquipmentRegistrationVO.getFileName());
                    previewAddressList.add(previewAddress);
                }
                mesEquipmentRegistrationVO.setPreviewAddressList(previewAddressList);
            }
        }
        return MesEquipmentRegistrationVOList;
    }

    @Override
    public IPage<MesEquipmentRegistrationVO> findPage(PageParams<MesEquipmentRegistrationQO> pageParams){
        IPage<MesEquipmentRegistrationVO> page = pageParams.buildPage();
        MesEquipmentRegistrationQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }
}
