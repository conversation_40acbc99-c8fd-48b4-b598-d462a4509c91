package xy.server.mes.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xunyue.config.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import xy.server.mes.entity.MesWarningInformation;
import xy.server.mes.mapper.MesWarningInformationMapper;
import xy.server.mes.wechat.SendUtil;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@Service
public class NotificationService {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final MesWarningInformationMapper mesWarningInformationMapper;
    private final SendUtil sendUtil;

    @Autowired
    private RedisUtil redisUtils;

    @Autowired
    public NotificationService(MesWarningInformationMapper mesWarningInformationMapper,
                               SendUtil sendUtil) {
        this.mesWarningInformationMapper = mesWarningInformationMapper;
        this.sendUtil = sendUtil;
    }

    @Scheduled(fixedRate = 10000)
    public void inspectionToNotification() {
        checkAndNotify("调机时间", "tjStartTime", "tjEndTime");
        checkAndNotify("较版时间", "jbStartTime", "jbEndTime");
    }

    private void checkAndNotify(String redisKey, String startTimeField, String endTimeField) {
        Object timeObjStr = redisUtils.get(redisKey);
        if (ObjectUtils.isEmpty(timeObjStr)) {
            return;
        }
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(timeObjStr));
        Integer minValue = jsonObject.getInteger("minValue");
        Integer maxValue = jsonObject.getInteger("maxValue");
        String strWxUserId = jsonObject.getString("wxUserId");
        String equipmentCode = jsonObject.getString("equipmentCode");
        String mesForewarningLevelName = jsonObject.getString("mesForewarningLevelName");

        List<MesWarningInformation> mesForewarningData = mesWarningInformationMapper.getMesForewarningData();
        if (ObjectUtils.isEmpty(mesForewarningData)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        for (MesWarningInformation info : mesForewarningData) {
            if (isTaskTerminated(info, endTimeField)) {
                log.debug("任务已被终止，跳过通知.");
                System.err.println("任务被终止！！！");
                continue;
            }

            String startTimeStr = getFieldValue(info, startTimeField);
            if (startTimeStr == null) {
                continue;
            }

            LocalDateTime startDateTime = LocalDateTime.parse(startTimeStr, FORMATTER);
            Duration duration = Duration.between(startDateTime, now);
            long minutes = duration.toMinutes();

            if (minValue > minutes || minutes > maxValue) {
                notifyIfNotExceeded(info, strWxUserId, equipmentCode, mesForewarningLevelName, redisKey, minutes);
            }
        }
    }

    /**
     *  判断字段是否已经结束
     * @param info
     * @param endTimeField
     * @return
     */
    private boolean isTaskTerminated(MesWarningInformation info, String endTimeField) {
        String endTimeStr = getFieldValue(info, endTimeField);
        System.err.println("当前结束时间： "+endTimeStr);
        return endTimeStr != null;
    }

    /**
     *  提前定义好所有的预警字段
     * @param info
     * @param fieldName
     * @return
     */
    private String getFieldValue(MesWarningInformation info, String fieldName) {
        switch (fieldName) {
            case "tjStartTime":
                return info.getTjStartTime();
            case "jbStartTime":
                return info.getJbStartTime();
            case "tjEndTime":
                return info.getTjEndTime();
            case "jbEndTime":
                return info.getJbEndTime();
            default:
                return null;
        }
    }

    /**
     *  请求微信接口，发送公众号通知信息
     * @param info
     * @param strWxUserId
     * @param equipmentCode
     * @param mesForewarningLevelName
     * @param redisKey
     * @param minutes
     */
    private void notifyIfNotExceeded(MesWarningInformation info, String strWxUserId, String equipmentCode,
                                     String mesForewarningLevelName, String redisKey, long minutes) {
        String key = "templateCallCount:" + info.getTasksGuid() + ":" + redisKey;
        Object obj = ObjectUtils.isNotEmpty(redisUtils.get(key)) ? redisUtils.get(key): null;
        Integer callCount = 0;
        if (ObjectUtils.isEmpty(obj)) {
            redisUtils.set(key, 0);
        } else {
            callCount = Integer.valueOf(obj.toString());
        }
        if (callCount < 3) {
            String[] wxUserIds = strWxUserId.split(",");
            for (String wxUserId : wxUserIds) {
                String json = sendUtil.getTemplateData(wxUserId, "预警提示", "设备编码" + equipmentCode + "预警",
                        "当前预警级别" + mesForewarningLevelName + "," + redisKey + "超过预警值！",
                        "点击查看", "#/pages/VisitorsRegister/VisitrsRegisterDetail?ID=");
                String msg = sendUtil.sendTemplate(json, 0);
            }
            // 更新调用计数
            redisUtils.incr(key,1);
        }
        log.info("时间差为 {} 分钟.", minutes);
    }
}
