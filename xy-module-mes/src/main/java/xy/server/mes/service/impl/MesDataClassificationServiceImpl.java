package xy.server.mes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import com.xunyue.config.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.mes.entity.MesDataClassification;
import xy.server.mes.entity.model.qo.MesDataClassificationQO;
import xy.server.mes.entity.model.ro.MesDataClassificationRO;
import xy.server.mes.entity.model.vo.MesDataClassificationVO;
import xy.server.mes.mapper.MesDataClassificationMapper;
import xy.server.mes.service.IMesDataClassificationService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@Service
public class MesDataClassificationServiceImpl extends ServiceImpl<MesDataClassificationMapper, MesDataClassification> implements IMesDataClassificationService {

    @Autowired
    private RedisUtil redisUtils;
//
//    @Autowired
//    private IMesRealTimeDataService mesRealTimeDataService;
//
//    @Autowired
//    private CodeGeneratorUtil codeGeneratorUtil;
//
//    @Autowired
//    private ClientConnectUtil connectUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(MesDataClassificationRO ro){
        ro.setTenantGuid("2caf3cb6216111eea71b49e0880a97d9");
        ro.setDeleted(false);
        ro.setIncrementalCorrection(false);
        MesDataClassification entity = new MesDataClassification();
        BeanUtil.copyProperties(ro, entity);
        // 新增时也需要将redis缓存的采集字段名称的数据更新
        findMesDataClassification();
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String dataClassificationGuid){
        boolean bool = super.removeById(dataClassificationGuid);
        // 更新redis数据
        findMesDataClassification();
        return bool;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> dataClassificationGuids) {
        for (String dataClassificationGuid : dataClassificationGuids) {
            super.removeById(dataClassificationGuid);
        }
        // 更新redis数据
        findMesDataClassification();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MesDataClassificationRO ro){
        MesDataClassification entity = new MesDataClassification();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<MesDataClassificationRO> dataList,String tenantGuid) {
        if (CollUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(e -> {
                e.setTenantGuid(tenantGuid);
                this.create(e);
            });
        }

        if (CollUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(e -> {
                e.setTenantGuid(tenantGuid);
                this.update(e);
            });
        }
        // 更新redis数据
        findMesDataClassification();
        return true;
    }

    @Override
    public MesDataClassificationVO getDataById(String dataClassificationGuid){
        return baseMapper.getDataByGuid(dataClassificationGuid);
    }

    @Override
    public List<MesDataClassificationVO> findList(MesDataClassificationQO qo){
        List<MesDataClassificationVO> mesDataClassificationList = baseMapper.findList(qo);
        return mesDataClassificationList;
    }

    @Override
    public IPage<MesDataClassificationVO> findPage(PageParams<MesDataClassificationQO> pageParams){
        IPage<MesDataClassificationVO> page = pageParams.buildPage();
        MesDataClassificationQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }

    // 返回树形结构数据
    @Override
    public List<MesDataClassificationVO> findTreeData(){
        List<MesDataClassificationVO> resultList = null;
        return resultList;

    }

    /**
     *  初始化加载数采分类数据，并缓存至redis中，后面由定时任务读取redis中的数据进行查询读写
     *  此处缓存的数据是用来进行获取采集的字段是什么
     *  @return
     */
    @Override
    public List<MesDataClassificationVO> findMesDataClassification() {
        List<MesDataClassificationVO> mesDataClassification = baseMapper.findMesDataClassification();
        if(ObjectUtil.isNotEmpty(mesDataClassification)){
            List<MesDataClassificationVO> sqlServerList = mesDataClassification.stream().filter(item -> {
                return item.getGatherDataSource().equals("sqlserver");
            }).collect(Collectors.toList());
            List<MesDataClassificationVO> udpList = mesDataClassification.stream().filter(item -> {
                return item.getGatherDataSource().equals("udp");
            }).collect(Collectors.toList());
            List<MesDataClassificationVO> txtList = mesDataClassification.stream().filter(item -> {
                return item.getGatherDataSource().equals("txt");
            }).collect(Collectors.toList());
            List<MesDataClassificationVO> mqttList = mesDataClassification.stream().filter(item -> {
                return item.getGatherDataSource().equals("mqtt");
            }).collect(Collectors.toList());
            redisUtils.set("mes_data_sql_server",sqlServerList);
            redisUtils.set("mes_data_udp",udpList);
            redisUtils.set("mes_data_txt",txtList);
            redisUtils.set("mes_data_mqtt",mqttList);
        } else {
            redisUtils.set("mes_data_sql_server",null);
            redisUtils.set("mes_data_udp",null);
            redisUtils.set("mes_data_txt",null);
            redisUtils.set("mes_data_mqtt",null);
        }
        return baseMapper.findMesDataClassification();
    }

    /**
     * 处理队列的同步数据信息
     * @param queueMap
     */
    public void handelInitData(Map<String, Object> queueMap)  {
//        try {
//            String[] parts = (String[]) queueMap.get("parts");
//            Map map = (Map) queueMap.get("map");
//
//            Object mesObj  = redisUtils.get("mes_data_udp");
//            String jsonString = JSON.toJSONString(mesObj);
//            List<MesDataClassificationVO> mesDataClassificationVOS = JSONArray.parseArray(jsonString, MesDataClassificationVO.class);
//            if(ObjectUtil.isNotEmpty(mesDataClassificationVOS)){
//                for (MesDataClassificationVO item : mesDataClassificationVOS) {
//
//                    map.put("value",parts[Integer.valueOf(item.getDataField())]);
//                    JSONObject json = new JSONObject(map);
//                    connectUtil.pub("/MES/GETUDP/"+parts[1]+"/"+item.getDescription(),json.toString());
//
//                    mesRealTimeDataService.createByDataType(item.getDataClassificationName(),
//                            parts[Integer.valueOf(item.getDataField())],item.getDataClassificationGuid(),parts[1],parts[1],
//                            item.getIncrementalCorrection(),item.getCollectionUnit(), codeGeneratorUtil.generateSerialNumber());
//                }
//            }
//        } catch (Exception e){
//            System.out.println("处理队列数据时发生异常：： "+e);
//        }
    }

    @Override
    public List<MesDataClassificationVO> findMesFiledList(MesDataClassificationQO qo) {
//        List<MesDataClassificationVO> mesFiledList = baseMapper.findMesFiledList(qo);
//        return mesFiledList;
        return null;
    }
}
