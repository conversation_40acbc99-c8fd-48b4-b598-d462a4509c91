package xy.server.mes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.mes.entity.MesFirstArticleAssurance;
import xy.server.mes.entity.model.qo.MesFirstArticleAssuranceQO;
import xy.server.mes.entity.model.ro.MesFirstArticleAssuranceRO;
import xy.server.mes.entity.model.vo.MesFirstArticleAssuranceVO;
import xy.server.mes.mapper.MesFirstArticleAssuranceMapper;
import xy.server.mes.service.IMesFirstArticleAssuranceService;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Service
public class MesFirstArticleAssuranceServiceImpl extends ServiceImpl<MesFirstArticleAssuranceMapper, MesFirstArticleAssurance> implements IMesFirstArticleAssuranceService {
                @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(MesFirstArticleAssuranceRO ro){
        MesFirstArticleAssurance entity = new MesFirstArticleAssurance();
        BeanUtil.copyProperties(ro, entity);
        entity.setDeleted(false);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String id){
        return super.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> ids) {
        for (String id : ids) {
            super.removeById(id);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MesFirstArticleAssuranceRO ro){
        MesFirstArticleAssurance entity = new MesFirstArticleAssurance();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<MesFirstArticleAssuranceRO> dataList,String tenantGuid) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(e -> {
                e.setTenantGuid(tenantGuid);
                this.create(e);
            });
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public MesFirstArticleAssuranceVO getDataById(String id){
        return baseMapper.getDataByGuid(id);
    }

    @Override
    public List<MesFirstArticleAssuranceVO> findList(MesFirstArticleAssuranceQO qo){
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<MesFirstArticleAssuranceVO> findPage(PageParams<MesFirstArticleAssuranceQO> pageParams){
        IPage<MesFirstArticleAssuranceVO> page = pageParams.buildPage();
        MesFirstArticleAssuranceQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }

    @Override
    public MesFirstArticleAssuranceVO getOneByTaskGuid(String tasksGuid) {
        MesFirstArticleAssuranceVO oneByTaskGuid = baseMapper.getOneByTaskGuid(tasksGuid);
        return oneByTaskGuid;
    }
}
