package xy.server.mes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.mes.entity.MesOperateRecord;
import xy.server.mes.entity.model.qo.MesOperateRecordQO;
import xy.server.mes.entity.model.ro.MesOperateRecordRO;
import xy.server.mes.entity.model.vo.MesOperateRecordVO;
import xy.server.mes.mapper.MesOperateRecordMapper;
import xy.server.mes.service.IMesOperateRecordService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Service
public class MesOperateRecordServiceImpl extends ServiceImpl<MesOperateRecordMapper, MesOperateRecord> implements IMesOperateRecordService {
                @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(MesOperateRecordRO ro){
        MesOperateRecord entity = new MesOperateRecord();
        BeanUtil.copyProperties(ro, entity);
        entity.setDeleted(false);
        entity.setStartTime(LocalDateTime.now());
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String id){
        return super.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> ids) {
        for (String id : ids) {
            super.removeById(id);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MesOperateRecordRO ro){
        MesOperateRecord entity = new MesOperateRecord();
        BeanUtil.copyProperties(ro, entity);
        entity.setEndTime(LocalDateTime.now());
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<MesOperateRecordRO> dataList,String tenantGuid) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            for (MesOperateRecordRO mesOperateRecordRO : dataList.getUpdateList()) {
                baseMapper.updateByTableId(mesOperateRecordRO.getId(),LocalDateTime.now());
            }
        }
        return true;
    }

    @Override
    public MesOperateRecordVO getDataById(String id){
        return baseMapper.getDataByGuid(id);
    }

    @Override
    public List<MesOperateRecordVO> findList(MesOperateRecordQO qo){
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<MesOperateRecordVO> findPage(PageParams<MesOperateRecordQO> pageParams){
        IPage<MesOperateRecordVO> page = pageParams.buildPage();
        MesOperateRecordQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }

    @Override
    public MesOperateRecordVO getOneOperate(String tasksGuid) {
        MesOperateRecordVO mesOperateRecordVO = baseMapper.getOneOperate(tasksGuid);
        return mesOperateRecordVO;
    }
}
