package xy.server.mes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.IpAdrressUtil;
import com.xunyue.config.util.PageParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.mes.entity.MesEquipmentBindIp;
import xy.server.mes.entity.model.qo.MesEquipmentBindIpQO;
import xy.server.mes.entity.model.ro.MesEquipmentBindIpRO;
import xy.server.mes.entity.model.vo.MesEquipmentBindIpVO;
import xy.server.mes.mapper.MesEquipmentBindIpMapper;
import xy.server.mes.service.IMesEquipmentBindIpService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-14
 */
@Service
public class MesEquipmentBindIpServiceImpl extends ServiceImpl<MesEquipmentBindIpMapper, MesEquipmentBindIp> implements IMesEquipmentBindIpService {
                @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(MesEquipmentBindIpRO ro){
        MesEquipmentBindIp entity = new MesEquipmentBindIp();
        BeanUtil.copyProperties(ro, entity);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String equipmentBindIpGuid){
        return super.removeById(equipmentBindIpGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> equipmentBindIpGuids) {
        for (String equipmentBindIpGuid : equipmentBindIpGuids) {
            super.removeById(equipmentBindIpGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MesEquipmentBindIpRO ro){
        MesEquipmentBindIp entity = new MesEquipmentBindIp();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<MesEquipmentBindIpRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public MesEquipmentBindIpVO getDataById(String equipmentBindIpGuid){
        return baseMapper.getDataByGuid(equipmentBindIpGuid);
    }

    @Override
    public List<MesEquipmentBindIpVO> findList(MesEquipmentBindIpQO qo){
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<MesEquipmentBindIpVO> findPage(PageParams<MesEquipmentBindIpQO> pageParams){
        IPage<MesEquipmentBindIpVO> page = pageParams.buildPage();
        MesEquipmentBindIpQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }

    @Override
    public MesEquipmentBindIpVO getOneByIp(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 获取客户端IP地址
            String clientIP = IpAdrressUtil.getIpAdrress(request);

            MesEquipmentBindIpQO qo = new MesEquipmentBindIpQO();
            qo.setIp(clientIP);

            MesEquipmentBindIpVO vo = baseMapper.findListByIpAddress(qo);
            return ObjectUtils.isNotNull(vo) ? vo.setIp(clientIP) : new MesEquipmentBindIpVO().setIp(clientIP);
        } catch (Exception e) {
            // 记录异常日志
            log.error("当前客户端IP未匹配设备:", e);
            throw new RuntimeException("当前客户端IP未匹配设备: " + IpAdrressUtil.getIpAdrress(request), e);
        }
    }


}
