package xy.server.mes.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.mes.entity.MesEquipmentRegistrationStatus;
import xy.server.mes.entity.model.qo.MesEquipmentRegistrationStatusQO;
import xy.server.mes.entity.model.ro.MesEquipmentRegistrationStatusRO;
import xy.server.mes.entity.model.vo.MesEquipmentRegistrationStatusVO;

import java.util.List;

/**
 * @apiNote  服务类
 * <AUTHOR>
 * @since 2024-06-25
 */
public interface IMesEquipmentRegistrationStatusService extends IService<MesEquipmentRegistrationStatus> {

    boolean create(MesEquipmentRegistrationStatusRO ro);

    boolean delete(String mesEquipmentRegistrationStatusGuid);

    boolean deleteByBatch(List<String> mesEquipmentRegistrationStatusGuids);

    boolean update(MesEquipmentRegistrationStatusRO ro);

    boolean saveDate(InsertOrUpdateList<MesEquipmentRegistrationStatusRO> dataList);

    MesEquipmentRegistrationStatusVO getDataById(String mesEquipmentRegistrationStatusGuid);

    List<MesEquipmentRegistrationStatusVO> findList(MesEquipmentRegistrationStatusQO qo);

    IPage<MesEquipmentRegistrationStatusVO> findPage(PageParams<MesEquipmentRegistrationStatusQO> pageParams);

    MesEquipmentRegistrationStatusVO getDataByEquipmentGuid(String equipmentGuid);

    boolean updateByEquipmentGuid(String equipmentGuid,boolean bool);
}
