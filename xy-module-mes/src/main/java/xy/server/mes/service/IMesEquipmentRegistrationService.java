package xy.server.mes.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.mes.entity.MesEquipmentRegistration;
import xy.server.mes.entity.model.qo.MesEquipmentRegistrationQO;
import xy.server.mes.entity.model.ro.MesEquipmentRegistrationRO;
import xy.server.mes.entity.model.vo.MesEquipmentRegistrationVO;

import java.util.List;

/**
 * @apiNote  服务类
 * <AUTHOR>
 * @since 2024-06-21
 */
public interface IMesEquipmentRegistrationService extends IService<MesEquipmentRegistration> {
                boolean create(MesEquipmentRegistrationRO ro);

    boolean delete(String mesEquipmentRegistrationGuid);

    boolean deleteByBatch(List<String> mesEquipmentRegistrationGuids);

    boolean update(MesEquipmentRegistrationRO ro);

    boolean saveDate(InsertOrUpdateList<MesEquipmentRegistrationRO> dataList);

    MesEquipmentRegistrationVO getDataById(String mesEquipmentRegistrationGuid);

    List<MesEquipmentRegistrationVO> findList(MesEquipmentRegistrationQO qo);

    IPage<MesEquipmentRegistrationVO> findPage(PageParams<MesEquipmentRegistrationQO> pageParams);
}
