package xy.server.mes.service.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xy.server.mes.entity.MesEventReport;
import xy.server.mes.mapper.MesEventReportMapper;
import xy.server.mes.service.IMesEventReportService;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Service
public class MesEventReportServiceImpl extends ServiceImpl<MesEventReportMapper, MesEventReport> implements IMesEventReportService {

    /**
     *  印刷设备日报表
     * @param qo
     * @return
     */
    @Override
    public List<MesEventReport> findReportData(MesEventReport qo) {
        List<MesEventReport> list;
        try {
            list = baseMapper.findReportData(qo);
            if(ObjectUtils.isNotEmpty(list)){
                for (MesEventReport mesEventReport : list) {
                    if(ObjectUtils.isNotEmpty(mesEventReport.getRankedData())){
                        analyzeMesCharacter(mesEventReport.getRankedData(),mesEventReport);
                    }
                }
            }
        } catch (Exception e) {
            log.error("报表数据查询出错：" + e.getMessage(), e);
            throw new RuntimeException("报表数据查询出错", e);
        }
        return list != null ? list : Collections.emptyList();
    }

    @Override
    public List<MesEventReport> findReportDataForTime(MesEventReport qo) {
        List<MesEventReport> list;
        try {
            list = baseMapper.findReportDataForTime(qo);
            if(ObjectUtils.isNotEmpty(list)){
                for (MesEventReport mesEventReport : list) {
                    if(ObjectUtils.isNotEmpty(mesEventReport.getRankedData())){
                        analyzeMesCharacter(mesEventReport.getRankedData(),mesEventReport);
                    }
                }
            }
        } catch (Exception e) {
            log.error("报表数据查询出错：" + e.getMessage(), e);
            throw new RuntimeException("报表数据查询出错", e);
        }
        return list != null ? list : Collections.emptyList();
    }

    @Override
    public List<Map<String,Object>> findStatisticalData(MesEventReport qo) {
        List<Map<String,Object>> list;
        try {
            list = baseMapper.findStatisticalData(qo);
            if(ObjectUtils.isNotEmpty(list)){

            }
        } catch (Exception e) {
            log.error("报表数据查询出错：" + e.getMessage(), e);
            throw new RuntimeException("报表数据查询出错", e);
        }
        return list != null ? list : Collections.emptyList();
    }

    @Override
    public List<Map<String,Object>> findMonthReportData(MesEventReport qo) {
        List<Map<String,Object>> list;
        try {
            list = baseMapper.findMonthReportData(qo);

        } catch (Exception e) {
            log.error("报表数据查询出错：" + e.getMessage(), e);
            throw new RuntimeException("报表数据查询出错", e);
        }
        return list != null ? list : Collections.emptyList();
    }

    @Override
    public List<MesEventReport> findReportDataByMonth(MesEventReport qo) {
        List<MesEventReport> list;
        try {
            list = baseMapper.findReportDataByMonth(qo);
            if(ObjectUtils.isNotEmpty(list)){
                for (MesEventReport mesEventReport : list) {
                    if(ObjectUtils.isNotEmpty(mesEventReport.getRankedData())){
                        analyzeMesCharacter(mesEventReport.getRankedData(),mesEventReport);
                    }
                }
            }
        } catch (Exception e) {
            log.error("报表数据查询出错：" + e.getMessage(), e);
            throw new RuntimeException("报表数据查询出错", e);
        }
        return list != null ? list : Collections.emptyList();
    }

    @Override
    public List<MesEventReport> findProductionStandardReport(MesEventReport qo) {
        List<MesEventReport> list = baseMapper.findProductionStandardReport(qo);
        return list;
    }

    @Override
    public List<MesEventReport> findProductionBackPerformanceReport(MesEventReport qo) {
        List<MesEventReport> list = baseMapper.findProductionBackPerformanceReport(qo);
        return list;
    }

    @Override
    public List<Map<String,Object>> getTaskTimeByTasksGuid(String tasksGuid) {
        return baseMapper.getTaskTimeByTasksGuid(tasksGuid);
    }

    @Override
    public List<Map<String,Object>> findReportDataForTimeToFsyj(MesEventReport qo) {
        List<Map<String,Object>> list;
        try {
            list = baseMapper.findReportDataForTimeToFsyj(qo);
            if(ObjectUtils.isNotEmpty(list)){
                for (Map<String, Object> map : list) {
//                    analyzeMesCharacterToMap(map.get("ranked_data").toString(),map);
                    Object o = map.get("ranked_data");
                    if(ObjectUtils.isNotEmpty(o)){
                        String rankedData = o.toString();
                        analyzeMesCharacterToMap(rankedData,map);
                    }
                }
            }

        } catch (Exception e) {
            log.error("报表数据查询出错：" + e.getMessage(), e);
            throw new RuntimeException("报表数据查询出错", e);
        }
        return list != null ? list : Collections.emptyList();
    }

//    @Override
//    public List<Map<String,Object>> findMonthReportData(MesEventReport qo) {
//        List<Map<String,Object>> list;
//        try {
//            list = baseMapper.findMonthReportData(qo);
//            if(ObjectUtils.isNotEmpty(list)){
//                int i= 0 ;
//                for (Map<String, Object> stringObjectMap : list) {
//                    Object o = stringObjectMap.get("ranked_data");
//                    if(ObjectUtils.isNotEmpty(o)){
//                        String rankedData = o.toString();
//                        analyzeMesCharacterToMap(rankedData,list.get(i));
//                        i++;
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("报表数据查询出错：" + e.getMessage(), e);
//            throw new RuntimeException("报表数据查询出错", e);
//        }
//        return list != null ? list : Collections.emptyList();
//    }

    public void analyzeMesCharacter(String rankedData,MesEventReport mesEventReport){
        try{
            // 使用正则表达式拆分字符串，去除空格和管道符
            String[] parts = rankedData.split("\\s*\\|\\s*");
            // 创建一个Map来存储键和值
            Map<String, String> dataMap = new HashMap<>();
            // 遍历并处理每个部分
            for (String part : parts) {
                // 去除括号，并按冒号拆分键和值
                String[] keyValue = part.replaceAll("\\(", "").replaceAll("\\)", "").split(": ");
                if (keyValue.length == 2) {
                    String key = keyValue[0];
                    String value = keyValue[1];
                    dataMap.put(key, value);
                }
            }

            for (Map.Entry<String, String> entry : dataMap.entrySet()) {
                log.info("当前采集字段 : {}, 采集的值 : {}",entry.getKey(), entry.getValue());
                if(entry.getKey().contains("开机信号")){
                    mesEventReport.setBootTimes(Integer.valueOf(entry.getValue()));
                } else if(entry.getKey().contains("取样数量")){
                    mesEventReport.setSamplingPaperCapacity(Integer.valueOf(entry.getValue()));
                } else if(entry.getKey().contains("完成数量")){
                    mesEventReport.setCompletedQuantity(Integer.valueOf(entry.getValue()));
                }else if(entry.getKey().contains("润版液温度")){
                    mesEventReport.setStandardPlateTemp(Integer.valueOf(entry.getValue())/10);
                } else if(entry.getKey().contains("运行时间统计")){
                    mesEventReport.setRunTimeTotal(new BigDecimal(entry.getValue()));
                } else if(entry.getKey().contains("环境湿度")){
                    mesEventReport.setStandardHumidity(Integer.valueOf(entry.getValue())/10);
                } else if(entry.getKey().contains("运行信号")){
                    mesEventReport.setSamplingFrequency(Integer.valueOf(entry.getValue()));
                } else if(entry.getKey().contains("润版液导电率")){
                    BigDecimal divisor = new BigDecimal("100");
                    mesEventReport.setStandardCondRate(new BigDecimal(entry.getValue()).divide(divisor,2));
                } else if(entry.getKey().contains("生产速度")){
                    mesEventReport.setProductionSpeed(Integer.valueOf(entry.getValue()));
                } else if(entry.getKey().contains("停机次数")){
                    mesEventReport.setRunningStopsFrequency(Integer.valueOf(entry.getValue()));
                } else if(entry.getKey().contains("润版液ph")){
                    mesEventReport.setStandardPh(Integer.valueOf(entry.getValue())/10);
                } else if(entry.getKey().contains("取样次数")){
                    mesEventReport.setSamplingFrequency(Integer.valueOf(entry.getValue()));
                } else if(entry.getKey().contains("环境温度")){
                    mesEventReport.setStandardTemp(Integer.valueOf(entry.getValue())/10);
                } else if(entry.getKey().contains("累计产量")){
                    mesEventReport.setTotalCompletedQuantity(Integer.valueOf(entry.getValue()));
                } else if(entry.getKey().contains("累计运行时间统计")){
                    mesEventReport.setTotalRunTimeTotal(new BigDecimal(entry.getValue()));
                } else if(entry.getKey().contains("累计停机次数")){
                    mesEventReport.setTotalRunningStopsFrequency(Integer.valueOf(entry.getValue()));
                }
                // ... 后续增加其他采集字段即可
            }
        }catch (Exception e){
            System.err.println("报错了 == "+e);
            log.error("报表数据查询解析字段出错：" + e.getMessage(), e);
        }
    }


    public void analyzeMesCharacterToMap(String rankedData,Map<String,Object> map){
        try{
            // 使用正则表达式拆分字符串，去除空格和管道符
            String[] parts = rankedData.split("\\s*\\|\\s*");
            // 创建一个Map来存储键和值
            Map<String, String> dataMap = new HashMap<>();
            // 遍历并处理每个部分
            for (String part : parts) {
                // 去除括号，并按冒号拆分键和值
                String[] keyValue = part.replaceAll("\\(", "").replaceAll("\\)", "").split(": ");
                if (keyValue.length == 2) {
                    String key = keyValue[0];
                    String value = keyValue[1];
                    dataMap.put(key, value);
                }
            }

            for (Map.Entry<String, String> entry : dataMap.entrySet()) {
                log.info("当前采集字段 : {}, 采集的值 : {}",entry.getKey(), entry.getValue());
                if(entry.getKey().contains("开机信号")){
                    map.put("bootTimes",Integer.valueOf(entry.getValue()));
//                    mesEventReport.setBootTimes(Integer.valueOf(entry.getValue()));
                } else if(entry.getKey().contains("取样数量")){
                    map.put("samplingPaperCapacity",Integer.valueOf(entry.getValue()));
//                    mesEventReport.setSamplingPaperCapacity(Integer.valueOf(entry.getValue()));
                } else if(entry.getKey().contains("完成数量")){
                    map.put("completedQuantity",Integer.valueOf(entry.getValue()));
//                    mesEventReport.setCompletedQuantity(Integer.valueOf(entry.getValue()));
                }else if(entry.getKey().contains("润版液温度")){
                    map.put("standardPlateTemp",Integer.valueOf(entry.getValue())/10);
//                    mesEventReport.setStandardPlateTemp(Integer.valueOf(entry.getValue())/10);
                } else if(entry.getKey().contains("运行时间统计")){
                    map.put("runTimeTotal",new BigDecimal(entry.getValue()));
//                    mesEventReport.setRunTimeTotal(new BigDecimal(entry.getValue()));
                } else if(entry.getKey().contains("环境湿度")){
                    map.put("standardHumidity",Integer.valueOf(entry.getValue())/10);
//                    mesEventReport.setStandardHumidity(Integer.valueOf(entry.getValue())/10);
                } else if(entry.getKey().contains("运行信号")){
                    map.put("samplingFrequency",Integer.valueOf(entry.getValue()));
//                    mesEventReport.setSamplingFrequency(Integer.valueOf(entry.getValue()));
                } else if(entry.getKey().contains("润版液导电率")){

                    BigDecimal divisor = new BigDecimal("100");
                    map.put("standardCondRate",new BigDecimal(entry.getValue()).divide(divisor,2));
//                    mesEventReport.setStandardCondRate(new BigDecimal(entry.getValue()).divide(divisor,2));
                } else if(entry.getKey().contains("生产速度")){
                    map.put("productionSpeed",Integer.valueOf(entry.getValue()));
//                    mesEventReport.setProductionSpeed(Integer.valueOf(entry.getValue()));
                } else if(entry.getKey().contains("停机次数")){
                    map.put("runningStopsFrequency",Integer.valueOf(entry.getValue()));
//                    mesEventReport.setRunningStopsFrequency(Integer.valueOf(entry.getValue()));
                } else if(entry.getKey().contains("润版液ph")){
                    map.put("standardPh",Integer.valueOf(entry.getValue())/10);
//                    mesEventReport.setStandardPh(Integer.valueOf(entry.getValue())/10);
                } else if(entry.getKey().contains("取样次数")){
                    map.put("samplingFrequency",Integer.valueOf(entry.getValue()));
//                    mesEventReport.setSamplingFrequency(Integer.valueOf(entry.getValue()));
                } else if(entry.getKey().contains("环境温度")){
                    map.put("standardTemp",Integer.valueOf(entry.getValue())/10);
//                    mesEventReport.setStandardTemp(Integer.valueOf(entry.getValue())/10);
                } else if(entry.getKey().contains("累计产量")){
                    map.put("totalCompletedQuantity",Integer.valueOf(entry.getValue()));
//                    mesEventReport.setTotalCompletedQuantity(Integer.valueOf(entry.getValue()));
                } else if(entry.getKey().contains("累计运行时间统计")){
                    map.put("totalRunTimeTotal",Integer.valueOf(entry.getValue()));
//                    mesEventReport.setTotalRunTimeTotal(new BigDecimal(entry.getValue()));
                } else if(entry.getKey().contains("累计停机次数")){
                    map.put("totalRunningStopsFrequency",Integer.valueOf(entry.getValue()));
//                    mesEventReport.setTotalRunningStopsFrequency(Integer.valueOf(entry.getValue()));
                }
                // ... 后续增加其他采集字段即可
            }
        }catch (Exception e){
            System.err.println("报错了 == "+e);
            log.error("报表数据查询解析字段出错：" + e.getMessage(), e);
        }
    }

}
