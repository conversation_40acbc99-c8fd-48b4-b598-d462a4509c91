package xy.server.mes.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.mes.entity.MesPubSubInfo;
import xy.server.mes.entity.model.qo.MesPubSubInfoQO;
import xy.server.mes.entity.model.ro.MesPubSubInfoRO;
import xy.server.mes.entity.model.vo.MesPubSubInfoVO;

import java.util.List;

/**
 * @apiNote  服务类
 * <AUTHOR>
 * @since 2024-06-27
 */
public interface IMesPubSubInfoService extends IService<MesPubSubInfo> {
    boolean create(MesPubSubInfoRO ro);

    boolean delete(String mesPubSubInfoGuid);

    boolean deleteByBatch(List<String> mesPubSubInfoGuids);

    boolean update(MesPubSubInfoRO ro);

    boolean saveDate(InsertOrUpdateList<MesPubSubInfoRO> dataList);

    MesPubSubInfoVO getDataById(String mesPubSubInfoGuid);

    List<MesPubSubInfoVO> findList();

    IPage<MesPubSubInfoVO> findPage(PageParams<MesPubSubInfoQO> pageParams);

    String getMqttUrl();
}
