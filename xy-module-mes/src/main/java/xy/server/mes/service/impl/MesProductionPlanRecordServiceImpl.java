package xy.server.mes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.mes.entity.MesProductionPlanRecord;
import xy.server.mes.entity.model.qo.MesProductionPlanRecordQO;
import xy.server.mes.entity.model.ro.MesProductionPlanRecordRO;
import xy.server.mes.entity.model.vo.MesProductionPlanRecordVO;
import xy.server.mes.mapper.MesProductionPlanRecordMapper;
import xy.server.mes.service.IMesProductionPlanRecordService;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Service
public class MesProductionPlanRecordServiceImpl extends ServiceImpl<MesProductionPlanRecordMapper, MesProductionPlanRecord> implements IMesProductionPlanRecordService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(MesProductionPlanRecordRO ro){
        MesProductionPlanRecord entity = new MesProductionPlanRecord();
        BeanUtil.copyProperties(ro, entity);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String id){
        return super.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> ids) {
        for (String id : ids) {
            super.removeById(id);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MesProductionPlanRecordRO ro){
        MesProductionPlanRecord entity = new MesProductionPlanRecord();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<MesProductionPlanRecordRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public MesProductionPlanRecordVO getDataById(String id){
        return baseMapper.getDataByGuid(id);
    }

    @Override
    public List<MesProductionPlanRecordVO> findList(MesProductionPlanRecordQO qo){
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<MesProductionPlanRecordVO> findPage(PageParams<MesProductionPlanRecordQO> pageParams){
        IPage<MesProductionPlanRecordVO> page = pageParams.buildPage();
        MesProductionPlanRecordQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }

    @Override
    public List<MesProductionPlanRecordRO> findByTasksGuid(String tasksGuid) {
        return baseMapper.findByTasksGuid(tasksGuid);
    }

    @Override
    public Integer updateByTasksGuid(MesProductionPlanRecordRO mesRo) {
        return baseMapper.updateByTasksGuid(mesRo);
    }

    @Override
    public Boolean deleteByTaskGuid(String taskGuid) {
        List<MesProductionPlanRecordRO> byTasksGuid = baseMapper.findByTasksGuid(taskGuid);
        if(ObjectUtils.isNotEmpty(byTasksGuid)){
            MesProductionPlanRecordRO mesProductionPlanRecordRO = byTasksGuid.get(0);
            return super.removeById(mesProductionPlanRecordRO.getId());
        }
        return true;
    }
}
