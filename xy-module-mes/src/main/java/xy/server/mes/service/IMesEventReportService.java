package xy.server.mes.service;

import xy.server.mes.entity.MesEventReport;

import java.util.List;
import java.util.Map;

public interface IMesEventReportService {

    List<MesEventReport> findReportData(MesEventReport qo);

    List<MesEventReport> findReportDataForTime(MesEventReport qo);

    List<Map<String,Object>> findStatisticalData(MesEventReport qo);

    List<Map<String,Object>> findMonthReportData(MesEventReport qo);

    List<MesEventReport> findReportDataByMonth(MesEventReport qo);

    List<MesEventReport> findProductionStandardReport(MesEventReport qo);

    List<MesEventReport> findProductionBackPerformanceReport(MesEventReport qo);

    List<Map<String, Object>> getTaskTimeByTasksGuid(String tasksGuid);

    List<Map<String, Object>> findReportDataForTimeToFsyj(MesEventReport qo);
}
