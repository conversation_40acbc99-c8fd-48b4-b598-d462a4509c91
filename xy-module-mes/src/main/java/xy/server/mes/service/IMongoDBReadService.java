package xy.server.mes.service;

import xy.server.mes.entity.model.vo.MongoDBMesPrimitiveDataVO;
import xy.server.mes.entity.model.vo.MongoDBMesRealTimeDataVO;

import java.time.LocalDateTime;
import java.util.List;

public interface IMongoDBReadService {
    List<MongoDBMesRealTimeDataVO> readAndProcessRealTimeData(LocalDateTime ldStartTime, LocalDateTime ldEndTime);

    List<MongoDBMesPrimitiveDataVO> readAndProcessPrimitiveData(LocalDateTime ldStartTime, LocalDateTime ldEndTime);
}
