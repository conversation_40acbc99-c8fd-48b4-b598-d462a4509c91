package xy.server.mes.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.mes.entity.model.qo.MesFixedPointInfoQO;
import xy.server.mes.entity.model.ro.MesFixedPointInfoRO;
import xy.server.mes.entity.model.vo.MesFixedPointInfoVO;
import xy.server.mes.service.IMesFixedPointInfoService;

import java.util.List;


/**
 * @apiNote  controller
 * <AUTHOR>
 * @since 2024-07-10
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "")
@RestController
@RequestMapping("/mes/mes-fixed-point-info")
@SystemClassLog(code = "MesFixedPointInfoController")
public class MesFixedPointInfoController {
    private final IMesFixedPointInfoService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增")
    public Boolean createOne(@RequestBody @Validated MesFixedPointInfoRO ro){
        return service.create(ro);
    }

    @DeleteMapping("/delete/{mesFixedPointGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除")
    public Boolean delete(@PathVariable("mesFixedPointGuid")String mesFixedPointGuid){
        return service.delete(mesFixedPointGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除")
    public Boolean deleteByBatch(@RequestBody List<String> mesFixedPointGuids){
        return service.deleteByBatch(mesFixedPointGuids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改")
    public Boolean update(@RequestBody @Validated MesFixedPointInfoRO ro){
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    public Boolean saveDate(@RequestBody @Validated InsertOrUpdateList<MesFixedPointInfoRO> dataList){
        return service.saveDate(dataList);
    }

    @GetMapping("/getOne/{mesFixedPointGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条")
    public MesFixedPointInfoVO getOne(@PathVariable("mesFixedPointGuid")String mesFixedPointGuid){
        return service.getDataById(mesFixedPointGuid);
    }

    @GetMapping("/getDataByEquipmentGuid/{equipmentGuid}")
    @ApiOperation(value = "根据设备guid查询定点信息")
    @SystemMethodLog(type = "query", description = "根据设备guid查询定点信息")
    public MesFixedPointInfoVO getDataByEquipmentGuid(@PathVariable("equipmentGuid")String equipmentGuid){
        return service.getDataByEquipmentGuid(equipmentGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询")
    public List<MesFixedPointInfoVO> findList(@RequestBody @Validated MesFixedPointInfoQO qo){
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询")
    public IPage<MesFixedPointInfoVO> findPage(@RequestBody @Validated PageParams<MesFixedPointInfoQO> pageParams){
        return service.findPage(pageParams);
    }

}
