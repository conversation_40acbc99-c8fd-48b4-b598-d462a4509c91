package xy.server.mes.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.mes.entity.model.qo.MesDataClassificationQO;
import xy.server.mes.entity.model.ro.MesDataClassificationRO;
import xy.server.mes.entity.model.vo.MesDataClassificationVO;
import xy.server.mes.service.IMesDataClassificationService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * @apiNote  controller
 * <AUTHOR>
 * @since 2024-04-22
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "MES数采分类接口")
@RestController
@RequestMapping("/mes/mes-data-classification")
@SystemClassLog(code = "MesDataClassificationController")
public class MesDataClassificationController {
    private final IMesDataClassificationService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增")
    public Boolean createOne(@RequestBody @Validated MesDataClassificationRO ro){
        return service.create(ro);
    }

    @DeleteMapping("/delete/{dataClassificationGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除")
    public Boolean delete(@PathVariable("dataClassificationGuid")String dataClassificationGuid){
        return service.delete(dataClassificationGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除")
    public Boolean deleteByBatch(@RequestBody List<String> dataClassificationGuids){
        return service.deleteByBatch(dataClassificationGuids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改")
    public Boolean update(@RequestBody @Validated MesDataClassificationRO ro){
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    public Boolean saveDate(@RequestBody @Validated InsertOrUpdateList<MesDataClassificationRO> dataList, HttpServletRequest request){
        String tenantGuid = request.getHeader("tenantGuid");
        return service.saveDate(dataList,tenantGuid);
    }

    @GetMapping("/getOne/{dataClassificationGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条")
    public MesDataClassificationVO getOne(@PathVariable("dataClassificationGuid")String dataClassificationGuid){
        return service.getDataById(dataClassificationGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询")
    public List<MesDataClassificationVO> findList(@RequestBody @Validated MesDataClassificationQO qo){
        return service.findList(qo);
    }

    @PostMapping("/findMesFiledList")
    @ApiOperation(value = "查询所有")
    @SystemMethodLog(type = "query", description = "查询所有")
    public List<MesDataClassificationVO> findMesFiledList(@RequestBody @Validated MesDataClassificationQO qo){
        return service.findMesFiledList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询")
    public IPage<MesDataClassificationVO> findPage(@RequestBody @Validated PageParams<MesDataClassificationQO> pageParams){
        return service.findPage(pageParams);
    }

    @PostMapping("/findTreeData")
    @ApiOperation(value = "树形结构查询")
    @SystemMethodLog(type = "query", description = "树形结构查询")
    public List<MesDataClassificationVO> findTreeData(){
        return service.findTreeData();
    }

}
