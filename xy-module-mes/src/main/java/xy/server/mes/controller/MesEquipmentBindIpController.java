package xy.server.mes.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.mes.entity.model.qo.MesEquipmentBindIpQO;
import xy.server.mes.entity.model.ro.MesEquipmentBindIpRO;
import xy.server.mes.entity.model.vo.MesEquipmentBindIpVO;
import xy.server.mes.service.IMesEquipmentBindIpService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * @apiNote  controller
 * <AUTHOR>
 * @since 2024-08-14
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "")
@RestController
@RequestMapping("/mes/mes-equipment-bind-ip")
@SystemClassLog(code = "MesEquipmentBindIpController")
public class MesEquipmentBindIpController {
    private final IMesEquipmentBindIpService service;

                @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增")
    public Boolean createOne(@RequestBody @Validated MesEquipmentBindIpRO ro){
        return service.create(ro);
    }

    @DeleteMapping("/delete/{equipmentBindIpGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除")
    public Boolean delete(@PathVariable("equipmentBindIpGuid")String equipmentBindIpGuid){
        return service.delete(equipmentBindIpGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除")
    public Boolean deleteByBatch(@RequestBody List<String> equipmentBindIpGuids){
        return service.deleteByBatch(equipmentBindIpGuids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改")
    public Boolean update(@RequestBody @Validated MesEquipmentBindIpRO ro){
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    public Boolean saveDate(@RequestBody @Validated InsertOrUpdateList<MesEquipmentBindIpRO> dataList){
        return service.saveDate(dataList);
    }

    @GetMapping("/getOne/{equipmentBindIpGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条")
    public MesEquipmentBindIpVO getOne(@PathVariable("equipmentBindIpGuid")String equipmentBindIpGuid){
        return service.getDataById(equipmentBindIpGuid);
    }

    @GetMapping("/getOneByIp")
    @ApiOperation(value = "单条查询当前电脑的ip并返回设备信息")
    @SystemMethodLog(type = "query", description = "单条查询当前电脑的ip并返回设备信息")
    public MesEquipmentBindIpVO getOneByIp(HttpServletRequest request, HttpServletResponse response){
        return service.getOneByIp( request, response);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询")
    public List<MesEquipmentBindIpVO> findList(@RequestBody @Validated MesEquipmentBindIpQO qo){
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询")
    public IPage<MesEquipmentBindIpVO> findPage(@RequestBody @Validated PageParams<MesEquipmentBindIpQO> pageParams){
        return service.findPage(pageParams);
    }

}
