package xy.server.mes.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.mes.entity.model.qo.MesPubSubInfoQO;
import xy.server.mes.entity.model.ro.MesPubSubInfoRO;
import xy.server.mes.entity.model.vo.MesPubSubInfoVO;
import xy.server.mes.service.IMesPubSubInfoService;

import java.util.List;


/**
 * @apiNote  controller
 * <AUTHOR>
 * @since 2024-06-27
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "")
@RestController
@RequestMapping("/mes/mes-pub-sub-info")
@SystemClassLog(code = "MesPubSubInfoController")
public class MesPubSubInfoController {
    private final IMesPubSubInfoService service;

                @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增")
    public Boolean createOne(@RequestBody @Validated MesPubSubInfoRO ro){
        return service.create(ro);
    }

    @DeleteMapping("/delete/{mesPubSubInfoGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除")
    public Boolean delete(@PathVariable("mesPubSubInfoGuid")String mesPubSubInfoGuid){
        return service.delete(mesPubSubInfoGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除")
    public Boolean deleteByBatch(@RequestBody List<String> mesPubSubInfoGuids){
        return service.deleteByBatch(mesPubSubInfoGuids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改")
    public Boolean update(@RequestBody @Validated MesPubSubInfoRO ro){
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    public Boolean saveDate(@RequestBody @Validated InsertOrUpdateList<MesPubSubInfoRO> dataList){
        return service.saveDate(dataList);
    }

    @GetMapping("/getOne/{mesPubSubInfoGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条")
    public MesPubSubInfoVO getOne(@PathVariable("mesPubSubInfoGuid")String mesPubSubInfoGuid){
        return service.getDataById(mesPubSubInfoGuid);
    }

    @GetMapping("/getMqttUrl")
    @ApiOperation(value = "获取mqtt路径")
    @SystemMethodLog(type = "query", description = "获取mqtt路径")
    public String getMqttUrl(){
        return service.getMqttUrl();
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询")
    public List<MesPubSubInfoVO> findList(){
        return service.findList();
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询")
    public IPage<MesPubSubInfoVO> findPage(@RequestBody @Validated PageParams<MesPubSubInfoQO> pageParams){
        return service.findPage(pageParams);
    }

}
