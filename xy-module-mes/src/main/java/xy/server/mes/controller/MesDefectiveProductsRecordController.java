package xy.server.mes.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.mes.entity.model.qo.MesDefectiveProductsRecordQO;
import xy.server.mes.entity.model.ro.MesDefectiveProductsRecordRO;
import xy.server.mes.entity.model.vo.MesDefectiveProductsRecordVO;
import xy.server.mes.service.IMesDefectiveProductsRecordService;

import java.util.List;


/**
 * @apiNote  controller
 * <AUTHOR>
 * @since 2024-09-28
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "")
@RestController
@RequestMapping("/mes/mes-defective-products-record")
@SystemClassLog(code = "MesDefectiveProductsRecordController")
public class MesDefectiveProductsRecordController {
    private final IMesDefectiveProductsRecordService service;

                @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增")
    public Boolean createOne(@RequestBody @Validated MesDefectiveProductsRecordRO ro){
        return service.create(ro);
    }

    @DeleteMapping("/delete/{mesDefectiveProductsGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除")
    public Boolean delete(@PathVariable("mesDefectiveProductsGuid")String mesDefectiveProductsGuid){
        return service.delete(mesDefectiveProductsGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除")
    public Boolean deleteByBatch(@RequestBody List<String> mesDefectiveProductsGuids){
        return service.deleteByBatch(mesDefectiveProductsGuids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改")
    public Boolean update(@RequestBody @Validated MesDefectiveProductsRecordRO ro){
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    public Boolean saveDate(@RequestBody @Validated InsertOrUpdateList<MesDefectiveProductsRecordRO> dataList){
        return service.saveDate(dataList);
    }

    @GetMapping("/getOne/{mesDefectiveProductsGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条")
    public MesDefectiveProductsRecordVO getOne(@PathVariable("mesDefectiveProductsGuid")String mesDefectiveProductsGuid){
        return service.getDataById(mesDefectiveProductsGuid);
    }

    @GetMapping("/getOneByTaskGuid/{taskGuid}")
    @ApiOperation(value = "根据任务id获取不良品数量合计")
    @SystemMethodLog(type = "query", description = "根据任务id获取不良品数量合计")
    public Integer getOneByTaskGuid(@PathVariable("taskGuid")String taskGuid){
        return service.getOneByTaskGuid(taskGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询")
    public List<MesDefectiveProductsRecordVO> findList(@RequestBody @Validated MesDefectiveProductsRecordQO qo){
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询")
    public IPage<MesDefectiveProductsRecordVO> findPage(@RequestBody @Validated PageParams<MesDefectiveProductsRecordQO> pageParams){
        return service.findPage(pageParams);
    }

}
