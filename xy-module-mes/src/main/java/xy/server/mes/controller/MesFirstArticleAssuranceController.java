package xy.server.mes.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.mes.entity.model.qo.MesFirstArticleAssuranceQO;
import xy.server.mes.entity.model.ro.MesFirstArticleAssuranceRO;
import xy.server.mes.entity.model.vo.MesFirstArticleAssuranceVO;
import xy.server.mes.service.IMesFirstArticleAssuranceService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * @apiNote  controller
 * <AUTHOR>
 * @since 2024-11-05
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "")
@RestController
@RequestMapping("/mes/mes-first-article-assurance")
@SystemClassLog(code = "MesFirstArticleAssuranceController")
public class MesFirstArticleAssuranceController {
    private final IMesFirstArticleAssuranceService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增")
    public Boolean createOne(@RequestBody @Validated MesFirstArticleAssuranceRO ro){
        return service.create(ro);
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除")
    public Boolean delete(@PathVariable("id")String id){
        return service.delete(id);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除")
    public Boolean deleteByBatch(@RequestBody List<String> ids){
        return service.deleteByBatch(ids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改")
    public Boolean update(@RequestBody @Validated MesFirstArticleAssuranceRO ro){
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    public Boolean saveDate(@RequestBody @Validated InsertOrUpdateList<MesFirstArticleAssuranceRO> dataList, HttpServletRequest request){
        String tenantGuid = request.getHeader("tenantGuid");
        return service.saveDate(dataList,tenantGuid);
    }

    @GetMapping("/getOne/{id}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条")
    public MesFirstArticleAssuranceVO getOne(@PathVariable("id")String id){
        return service.getDataById(id);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询")
    public List<MesFirstArticleAssuranceVO> findList(@RequestBody @Validated MesFirstArticleAssuranceQO qo){
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询")
    public IPage<MesFirstArticleAssuranceVO> findPage(@RequestBody @Validated PageParams<MesFirstArticleAssuranceQO> pageParams){
        return service.findPage(pageParams);
    }

    @GetMapping("/getOneByTaskGuid/{tasksGuid}")
    @ApiOperation(value = "根据任务id查询这个任务最新录入的首件确认记录是否合格")
    @SystemMethodLog(type = "query", description = "根据任务id查询这个任务最新录入的首件确认记录是否合格")
    public MesFirstArticleAssuranceVO getOneByTaskGuid(@PathVariable("tasksGuid")String tasksGuid){
        return service.getOneByTaskGuid(tasksGuid);
    }

}
