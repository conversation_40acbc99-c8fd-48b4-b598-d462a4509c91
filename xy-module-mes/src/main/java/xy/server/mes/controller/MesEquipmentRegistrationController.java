package xy.server.mes.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.mes.entity.model.qo.MesEquipmentRegistrationQO;
import xy.server.mes.entity.model.ro.MesEquipmentRegistrationRO;
import xy.server.mes.entity.model.vo.MesEquipmentRegistrationVO;
import xy.server.mes.service.IMesEquipmentRegistrationService;

import java.util.List;


/**
 * @apiNote  controller
 * <AUTHOR>
 * @since 2024-06-21
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "")
@RestController
@RequestMapping("/mes/mes-equipment-registration")
@SystemClassLog(code = "MesEquipmentRegistrationController")
public class MesEquipmentRegistrationController {
    private final IMesEquipmentRegistrationService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增")
    public Boolean createOne(@RequestBody @Validated MesEquipmentRegistrationRO ro){
        return service.create(ro);
    }

    @DeleteMapping("/delete/{mesEquipmentRegistrationGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除")
    public Boolean delete(@PathVariable("mesEquipmentRegistrationGuid")String mesEquipmentRegistrationGuid){
        return service.delete(mesEquipmentRegistrationGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除")
    public Boolean deleteByBatch(@RequestBody List<String> mesEquipmentRegistrationGuids){
        return service.deleteByBatch(mesEquipmentRegistrationGuids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改")
    public Boolean update(@RequestBody @Validated MesEquipmentRegistrationRO ro){
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    public Boolean saveDate(@RequestBody @Validated InsertOrUpdateList<MesEquipmentRegistrationRO> dataList){
        return service.saveDate(dataList);
    }

    @GetMapping("/getOne/{mesEquipmentRegistrationGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条")
    public MesEquipmentRegistrationVO getOne(@PathVariable("mesEquipmentRegistrationGuid")String mesEquipmentRegistrationGuid){
        return service.getDataById(mesEquipmentRegistrationGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询")
    public List<MesEquipmentRegistrationVO> findList(@RequestBody @Validated MesEquipmentRegistrationQO qo){
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询")
    public IPage<MesEquipmentRegistrationVO> findPage(@RequestBody @Validated PageParams<MesEquipmentRegistrationQO> pageParams){
        return service.findPage(pageParams);
    }

}
