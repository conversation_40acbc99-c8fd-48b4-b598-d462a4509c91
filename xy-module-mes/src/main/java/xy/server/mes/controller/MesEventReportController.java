package xy.server.mes.controller;

import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import xy.server.mes.entity.MesEventReport;
import xy.server.mes.service.IMesEventReportService;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "MES生产事件报表接口")
@RestController
@RequestMapping("/mes/mes-event-report")
@SystemClassLog(code = "MesEventReportController")
public class MesEventReportController {

    private final IMesEventReportService service;

    @PostMapping("/findReportData")
    @ApiOperation(value = "报表数据查询")
    @SystemMethodLog(type = "query", description = "列表查询")
    public List<MesEventReport> findReportData(@RequestBody MesEventReport qo){
        return service.findReportData(qo);
    }

    @PostMapping("/findReportDataForTime")
    @ApiOperation(value = "报表数据根据时间查询")
    @SystemMethodLog(type = "query", description = "报表数据根据时间查询")
    public List<MesEventReport> findReportDataForTime(@RequestBody MesEventReport qo){
        return service.findReportDataForTime(qo);
    }

    @PostMapping("/findStatisticalData")
    @ApiOperation(value = "绩效报表数据根据时间查询")
    @SystemMethodLog(type = "query", description = "绩效报表数据根据时间查询")
    public List<Map<String,Object>> findStatisticalData(@RequestBody MesEventReport qo){
        return service.findStatisticalData(qo);
    }

    @PostMapping("/findMonthReportData")
    @ApiOperation(value = "生产数据月报表")
    @SystemMethodLog(type = "query", description = "生产数据月报表")
    public List<Map<String,Object>> findMonthReportData(@RequestBody MesEventReport qo){
        return service.findMonthReportData(qo);
    }

    @PostMapping("/findReportDataByMonth")
    @ApiOperation(value = "生产数据月报表")
    @SystemMethodLog(type = "query", description = "生产数据月报表任务")
    public List<MesEventReport> findReportDataByMonth(@RequestBody MesEventReport qo){
        return service.findReportDataByMonth(qo);
    }


    @PostMapping("/findProductionStandardReport")
    @ApiOperation(value = "生产达标统计报表")
    @SystemMethodLog(type = "query", description = "生产达标统计报表")
    public List<MesEventReport> findProductionStandardReport(@RequestBody MesEventReport qo){
        return service.findProductionStandardReport(qo);
    }

    @PostMapping("/findProductionBackPerformanceReport")
    @ApiOperation(value = "后道生产绩效统计表")
    @SystemMethodLog(type = "query", description = "后道生产绩效统计表")
    public List<MesEventReport> findProductionBackPerformanceReport(@RequestBody MesEventReport qo){
        return service.findProductionBackPerformanceReport(qo);
    }

    @GetMapping("/getTaskTimeByTasksGuid/{tasksGuid}")
    @ApiOperation(value = "根据任务id获取设备调机，暂停时间")
    @SystemMethodLog(type = "query", description = "根据任务id获取设备调机，暂停时间")
    public List<Map<String,Object>> getTaskTimeByTasksGuid(@PathVariable("tasksGuid")String tasksGuid){
        return service.getTaskTimeByTasksGuid(tasksGuid);
    }



    @PostMapping("/findReportDataForTimeToFsyj")
    @ApiOperation(value = "报表数据根据时间查询")
    @SystemMethodLog(type = "query", description = "报表数据根据时间查询")
    public List<Map<String, Object>> findReportDataForTimeToFsyj(@RequestBody MesEventReport qo){
        return service.findReportDataForTimeToFsyj(qo);
    }


}
