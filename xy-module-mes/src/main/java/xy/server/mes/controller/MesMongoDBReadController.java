package xy.server.mes.controller;

import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xy.server.mes.entity.model.qo.MesForewarningQO;
import xy.server.mes.entity.model.ro.MesRealTimeDataRO;
import xy.server.mes.entity.model.vo.MongoDBMesPrimitiveDataVO;
import xy.server.mes.entity.model.vo.MongoDBMesRealTimeDataVO;
import xy.server.mes.service.IMongoDBReadService;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "")
@RestController
@RequestMapping("/mes/mongodb-read-data")
@SystemClassLog(code = "MesMongoDBReadController")
public class MesMongoDBReadController {

    private final IMongoDBReadService service;

    @PostMapping("/read-real-time-data")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询")
    public List<MongoDBMesRealTimeDataVO> readRealTimeData(@RequestBody @Validated MesRealTimeDataRO qo) {
        String startTime = qo.getStartTime();
        String endTime = qo.getEndTime();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime ldStartTime = LocalDateTime.parse(startTime, formatter);
        LocalDateTime ldEndTime = LocalDateTime.parse(endTime, formatter);
        return service.readAndProcessRealTimeData(ldStartTime, ldEndTime);
    }

    @PostMapping("/read-primitive-data")
    @ApiOperation(value = "原始数据查询")
    @SystemMethodLog(type = "query", description = "原始数据查询")
    public List<MongoDBMesPrimitiveDataVO> readPrimitiveData(@RequestBody @Validated MesRealTimeDataRO qo) {
        String startTime = qo.getStartTime();
        String endTime = qo.getEndTime();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime ldStartTime = LocalDateTime.parse(startTime, formatter);
        LocalDateTime ldEndTime = LocalDateTime.parse(endTime, formatter);

        return service.readAndProcessPrimitiveData(ldStartTime, ldEndTime);
    }

}
