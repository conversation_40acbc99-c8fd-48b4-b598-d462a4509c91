package xy.server.mes.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.mes.entity.model.qo.MesEquipmentRegistrationStatusQO;
import xy.server.mes.entity.model.ro.MesEquipmentRegistrationStatusRO;
import xy.server.mes.entity.model.vo.MesEquipmentRegistrationStatusVO;
import xy.server.mes.service.IMesEquipmentRegistrationStatusService;

import java.util.List;


/**
 * @apiNote  controller
 * <AUTHOR>
 * @since 2024-06-25
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "")
@RestController
@RequestMapping("/mes/mes-equipment-registration-status")
@SystemClassLog(code = "MesEquipmentRegistrationStatusController")
public class MesEquipmentRegistrationStatusController {

    @Autowired
    private final IMesEquipmentRegistrationStatusService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增")
    public Boolean createOne(@RequestBody @Validated MesEquipmentRegistrationStatusRO ro){
        return service.create(ro);
    }

    @DeleteMapping("/delete/{mesEquipmentRegistrationStatusGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除")
    public Boolean delete(@PathVariable("mesEquipmentRegistrationStatusGuid")String mesEquipmentRegistrationStatusGuid){
        return service.delete(mesEquipmentRegistrationStatusGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除")
    public Boolean deleteByBatch(@RequestBody List<String> mesEquipmentRegistrationStatusGuids){
        return service.deleteByBatch(mesEquipmentRegistrationStatusGuids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改")
    public Boolean update(@RequestBody @Validated MesEquipmentRegistrationStatusRO ro){
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    public Boolean saveDate(@RequestBody @Validated InsertOrUpdateList<MesEquipmentRegistrationStatusRO> dataList){
        return service.saveDate(dataList);
    }

    @GetMapping("/getOne/{mesEquipmentRegistrationStatusGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条")
    public MesEquipmentRegistrationStatusVO getOne(@PathVariable("mesEquipmentRegistrationStatusGuid")String mesEquipmentRegistrationStatusGuid){
        return service.getDataById(mesEquipmentRegistrationStatusGuid);
    }

    @GetMapping("/getOneByEquipmentGuid/{equipmentGuid}")
    @ApiOperation(value = "根据设备guid单条查询")
    @SystemMethodLog(type = "query", description = "根据设备guid单条查询")
    public MesEquipmentRegistrationStatusVO getOneByEquipmentGuid(@PathVariable("equipmentGuid")String equipmentGuid){
        return service.getDataByEquipmentGuid(equipmentGuid);
    }

    @GetMapping("/updateByEquipmentGuid/{equipmentGuid}/{bool}")
    @ApiOperation(value = "根据设备guid修改上机状态")
    @SystemMethodLog(type = "update", description = "根据设备guid修改上机状态")
    public boolean updateByEquipmentGuid(@PathVariable("equipmentGuid")String equipmentGuid,
                                            @PathVariable("bool")boolean bool){
        return service.updateByEquipmentGuid(equipmentGuid,bool);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询")
    public List<MesEquipmentRegistrationStatusVO> findList(@RequestBody @Validated MesEquipmentRegistrationStatusQO qo){
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询")
    public IPage<MesEquipmentRegistrationStatusVO> findPage(@RequestBody @Validated PageParams<MesEquipmentRegistrationStatusQO> pageParams){
        return service.findPage(pageParams);
    }

}
