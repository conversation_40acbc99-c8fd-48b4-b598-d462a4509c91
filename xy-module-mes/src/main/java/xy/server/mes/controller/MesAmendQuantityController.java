package xy.server.mes.controller;

import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import xy.server.mes.entity.MesAmendQuantity;
import xy.server.mes.service.IMesAmendQuantityService;
import xy.server.mes.service.IMesProductionPlanRecordService;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "mes点击交出时更新完成数量与损耗数量")
@RestController
@RequestMapping("/mes/mes-amend-quantity")
@SystemClassLog(code = "MesAmendQuantity")
public class MesAmendQuantityController {

    private final IMesAmendQuantityService service;

    private final IMesProductionPlanRecordService iMesProductionPlanRecordService;

    @PostMapping("/amendQuantity")
    @ApiOperation(value = "mes点击交出时更新完成数量与损耗数量")
    @SystemMethodLog(type = "insert", description = "mes点击交出时更新完成数量与损耗数量")
    public Boolean amendQuantity(@RequestBody MesAmendQuantity mesAmendQuantity){
        return service.amendQuantity(mesAmendQuantity);
    }

    @DeleteMapping("/deleteByTaskGuid/{taskGuid}")
    @ApiOperation(value = "點擊重置時刪除任務記錄表中的任務數據")
    @SystemMethodLog(type = "deleteByTaskGuid", description = "點擊重置時刪除任務記錄表中的任務數據")
    public Boolean deleteByTaskGuid(@PathVariable("taskGuid")String taskGuid){
        return iMesProductionPlanRecordService.deleteByTaskGuid(taskGuid);
    }
}
