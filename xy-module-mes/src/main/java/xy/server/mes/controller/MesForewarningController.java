package xy.server.mes.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.mes.entity.model.qo.MesForewarningQO;
import xy.server.mes.entity.model.ro.MesForewarningRO;
import xy.server.mes.entity.model.vo.MesForewarningVO;
import xy.server.mes.service.IMesForewarningService;

import java.util.List;


/**
 * @apiNote  controller
 * <AUTHOR>
 * @since 2024-06-13
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "")
@RestController
@RequestMapping("/mes/mes-forewarning")
@SystemClassLog(code = "MesForewarningController")
public class MesForewarningController {
    private final IMesForewarningService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增")
    public Boolean createOne(@RequestBody @Validated MesForewarningRO ro){
        return service.create(ro);
    }

    @DeleteMapping("/delete/{mesForewarningGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除")
    public Boolean delete(@PathVariable("mesForewarningGuid")String mesForewarningGuid){
        return service.delete(mesForewarningGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除")
    public Boolean deleteByBatch(@RequestBody List<String> mesForewarningGuids){
        return service.deleteByBatch(mesForewarningGuids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改")
    public Boolean update(@RequestBody @Validated MesForewarningRO ro){
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    public Boolean saveData(@RequestBody @Validated InsertOrUpdateList<MesForewarningRO> dataList){
        return service.saveData(dataList);
    }

    @GetMapping("/getOne/{mesForewarningGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条")
    public MesForewarningVO getOne(@PathVariable("mesForewarningGuid")String mesForewarningGuid){
        return service.getDataById(mesForewarningGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询")
    public List<MesForewarningVO> findList(@RequestBody @Validated MesForewarningQO qo){
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询")
    public IPage<MesForewarningVO> findPage(@RequestBody @Validated PageParams<MesForewarningQO> pageParams){
        return service.findPage(pageParams);
    }

}
