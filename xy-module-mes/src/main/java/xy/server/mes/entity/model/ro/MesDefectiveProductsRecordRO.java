package xy.server.mes.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* <p>
* RO
* </p>
*
* <AUTHOR>
* @since 2024-09-28
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesDefectiveProductsRecordRO对象", description = "")
public class MesDefectiveProductsRecordRO extends BaseEntity {

        @ApiModelProperty(value = "id主键")
    private String mesDefectiveProductsGuid;

        @ApiModelProperty(value = "任务主键")
    private String taskGuid;

        @ApiModelProperty(value = "良品数")
    private Integer goodCount;

        @ApiModelProperty(value = "不良品数")
    private Integer badCount;

        @ApiModelProperty(value = "设备guid")
    private String equipmentGuid;

        @ApiModelProperty(value = "设备名称")
    private String equipmentName;

        @ApiModelProperty(value = "不良品缺陷")
    private String defectiveProductsDefect;

        @ApiModelProperty(value = "不良品缺陷描述")
    private String defectiveProductsDefectDesc;

        @ApiModelProperty(value = "是否IPQC巡查（false否true是）")
    private Boolean inspectionOrNot;

        @ApiModelProperty(value = "工号")
    private String jobNumber;

}
