package xy.server.mes.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesRealTimeDataRO对象", description = "")
public class MesRealTimeDataRO {

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private String startTime ;

    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "结束不能为空")
    private String endTime ;
}
