package xy.server.mes.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-14
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "mes_equipment_bind_ip")
public class MesEquipmentBindIp extends BaseEntity implements Serializable{

private static final long serialVersionUID=1L;
    /**
    * PK
    */
    @TableId("equipment_bind_ip_guid")
    private String equipmentBindIpGuid;
    /**
    * ip地址
    */
    @TableField("ip")
    private String ip;
    /**
    * 设备guid
    */
    @TableField("equipment_guid")
    private String equipmentGuid;
    /**
    * 设备编码
    */
    @TableField("equipment_code")
    private String equipmentCode;
    /**
    * 设备名称
    */
    @TableField("equipment_name")
    private String equipmentName;
    /**
    * 班组guid
    */
    @TableField("team_guid")
    private String teamGuid;
    /**
    * 班组名称
    */
    @TableField("team_name")
    private String teamName;
    /**
    * 逻辑删除
    */
    @TableLogic
    private Boolean deleted;

}
