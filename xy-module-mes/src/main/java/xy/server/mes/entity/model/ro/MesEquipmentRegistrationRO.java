package xy.server.mes.entity.model.ro;

import com.baomidou.mybatisplus.annotation.TableField;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* <p>
* RO
* </p>
*
* <AUTHOR>
* @since 2024-06-21
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesEquipmentRegistrationRO对象", description = "")
public class MesEquipmentRegistrationRO extends BaseEntity {

    @ApiModelProperty(value = "上机登记注册记录主键（PK）")
    private String mesEquipmentRegistrationGuid;

    @ApiModelProperty(value = "多个文件名称，用,号分开保存")
    private String fileName;

    @ApiModelProperty(value = "minio桶名")
    private String bucketName;

    @ApiModelProperty(value = "登记人id")
    private String registrationUserId;

    @ApiModelProperty(value = "登记人名称")
    private String registrationUserName;

    @ApiModelProperty(value = "关联设备guid")
    private String registrationEquipmentGuid;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "反馈")
    private String feedback;

    @ApiModelProperty(value = "时间戳唯一标识符")
    @TableField(exist = false)
    private String rlTimestamp;

    @ApiModelProperty(value = "备注2")
    private String remark2;

    @ApiModelProperty(value = "备注3")
    private String remark3;


}
