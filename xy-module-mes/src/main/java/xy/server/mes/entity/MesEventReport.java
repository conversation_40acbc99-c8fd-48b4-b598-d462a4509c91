package xy.server.mes.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class MesEventReport {

    @ApiModelProperty(value = "班组编码")
    private String teamCode;

    @ApiModelProperty(value = "班组名称")
    private String teamName;

    @ApiModelProperty(value = "班组长")
    private String staffShortName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "工单号纸箱")
    private String workorderNumberBox;

    @ApiModelProperty(value = "工单号纸板")
    private String workorderNumberPaperboard;

    @ApiModelProperty(value = "班次")
    private String shifts;

    @ApiModelProperty(value = "客户名称")
    private String customerShortName;

    @ApiModelProperty(value = "产品名称")
    private String materialName;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "工艺")
    private String productionProcessesTypeName;

    @ApiModelProperty(value = "计划完成数量")
    private Integer planCount;

    @ApiModelProperty(value = "良品数")
    private Integer goodCount;

    @ApiModelProperty(value = "不良品数")
    private Integer badCount;

    @ApiModelProperty(value = "订单数量")
    private Integer orderQuantity;

    @ApiModelProperty(value = "完成数量")
    private Integer completedQuantity;

    @ApiModelProperty(value = "累计数量")
    private Integer totalCompletedQuantity;

    @ApiModelProperty(value = "班次")
    private String shiftsType;

    @ApiModelProperty(value = "班次guid")
    private String productionShiftGuid;

    @ApiModelProperty(value = "班次名称")
    private String productionShiftName;

    @ApiModelProperty(value = "拼接采集字段，需要解析")
    private String rankedData;

    @ApiModelProperty(value = "生产速度")
    private Integer productionSpeed;

    @ApiModelProperty(value = "环境湿度")
    private Integer standardHumidity;

    @ApiModelProperty(value = "环境温度")
    private Integer standardTemp;

    @ApiModelProperty(value = "开机信号")
    private Integer bootTimes;

    @ApiModelProperty(value = "取样次数")
    private Integer samplingFrequency;

    @ApiModelProperty(value = "取样数量")
    private Integer samplingPaperCapacity;

    @ApiModelProperty(value = "润版液ph")
    private Integer standardPh;

    @ApiModelProperty(value = "润版液导电率")
    private BigDecimal standardCondRate;

    @ApiModelProperty(value = "润版液温度")
    private Integer standardPlateTemp;

    @ApiModelProperty(value = "停机次数")
    private Integer runningStopsFrequency;

    @ApiModelProperty(value = "累计停机次数")
    private Integer totalRunningStopsFrequency;

    @ApiModelProperty(value = "运行时间统计")
    private BigDecimal runTimeTotal;

    @ApiModelProperty(value = "累计运行时间统计")
    private BigDecimal totalRunTimeTotal;

//    @ApiModelProperty(value = "运行信号")
//    private Integer productionSpeed;

    @ApiModelProperty(value = "调机耗时/s")
    private String switchingTime;

    @ApiModelProperty(value = "装版耗时/s")
    private String plateSettingTime;

    @ApiModelProperty(value = "暂停耗时/s")
    private String pauseTime;

    @ApiModelProperty(value = "查询日期")
    private LocalDate filterDate;

    @ApiModelProperty(value = "开始日期")
    private String startFilterDate;

    @ApiModelProperty(value = "结束日期")
    private String endFilterDate;

    @ApiModelProperty(value = "交出良品数")
    private Integer productionNonDefectiveQuantity;

    @ApiModelProperty(value = "当班的总工作时长")
    private String durationMinutes;

    @ApiModelProperty(value = "当班总良品数")
    private Integer totalProductionNonDefectiveQuantity;

    @ApiModelProperty(value = "当班平均达标率")
    private String rthRate;

    @ApiModelProperty(value = "标准速度")
    private Integer equipmentParameterValue;

    @ApiModelProperty(value = "当班设备平均速度")
    private String avgSpeed;

    @ApiModelProperty(value = "速度达标率")
    private String speedRatio;

    @ApiModelProperty(value = "超时笔数")
    private Integer overDiffCount;

    @ApiModelProperty(value = "计划达成率")
    private String planRates;

    @ApiModelProperty(value = "缺数笔数")
    private Integer lackDiffCount;

    @ApiModelProperty(value = "缺数率")
    private String lackRates;

    @ApiModelProperty(value = "任务总数")
    private Integer totalTasksCount;

    @ApiModelProperty(value = "总任务完成数量")
    private Integer totalPlannedQuantity;

    @ApiModelProperty(value = "总计划时间")
    private String planDurationMinutes;

    @ApiModelProperty(value = "总实际计划完成时间")
    private String actDurationMinutes;





}
