package xy.server.mes.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
* <p>
* QO
* </p>
*
* <AUTHOR>
* @since 2024-09-28
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesDefectiveProductsRecordQO对象", description = "")
public class MesDefectiveProductsRecordQO {

    @ApiModelProperty(value = "id主键")
private String mesDefectiveProductsGuid;

    @ApiModelProperty(value = "任务主键")
private String taskGuid;

    @ApiModelProperty(value = "良品数")
private Integer goodCount;

    @ApiModelProperty(value = "不良品数")
private Integer badCount;

    @ApiModelProperty(value = "设备guid")
private String equipmentGuid;

    @ApiModelProperty(value = "设备名称")
private String equipmentName;

    @ApiModelProperty(value = "不良品缺陷")
private String defectiveProductsDefect;

    @ApiModelProperty(value = "不良品缺陷描述")
private String defectiveProductsDefectDesc;

    @ApiModelProperty(value = "是否IPQC巡查（false否true是）")
private Boolean inspectionOrNot;

    @ApiModelProperty(value = "工号")
private String jobNumber;

    @ApiModelProperty(value = "创建人guid")
private String creatorGuid;

    @ApiModelProperty(value = "创建人")
private String creator;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
private String lastUpdater;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "租户id")
private String tenantGuid;

}