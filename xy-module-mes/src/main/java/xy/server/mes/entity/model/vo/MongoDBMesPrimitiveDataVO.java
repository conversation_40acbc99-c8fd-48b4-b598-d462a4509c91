package xy.server.mes.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;

/**
 * MongoDB MES 原始数据 VO
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MongoDBMesPrimitiveDataVO对象", description = "MongoDB MES 原始数据 VO")
public class MongoDBMesPrimitiveDataVO {

    @Id
    private String id;

    @ApiModelProperty(value = "订阅主题")
    private String topic;

    @ApiModelProperty(value = "网关")
    private String gatew;

    @ApiModelProperty(value = "网关")
    private String gateidc;

    @ApiModelProperty(value = "时间戳")
    private String time;

    @ApiModelProperty(value = "预留字段给软件使用")
    private String xhxy20w;

    @ApiModelProperty(value = "预留字段给软件使用")
    private String xhxy21w;

    @ApiModelProperty(value = "启用自动清零逻辑信号")
    private String xhxy22w;

    @ApiModelProperty(value = "计数信号")
    private String xhxy01r;

    @ApiModelProperty(value = "开机信号")
    private String xhxy06r;

    @ApiModelProperty(value = "运行信号")
    private String xhxy07r;

    @ApiModelProperty(value = "空转状态信号（=0非空转，=1空转）")
    private String xhxy08r;

    @ApiModelProperty(value = "调机生产状态（=0非调机，=1调机）")
    private String xhxy09r;

    @ApiModelProperty(value = "正式生产状态（=0非正式生产，=1正式生产，有产量运行超过30秒，判断是正式生产）")
    private String xhxy10r;

    @ApiModelProperty(value = "换单指令")
    private String xhxy11w;

    @ApiModelProperty(value = "换班指令")
    private String xhxy12w;

    @ApiModelProperty(value = "手动清零")
    private String xhxy13w;

    @ApiModelProperty(value = "工单请求信号（=0未申请，=1申请）")
    private String xhxy14w;

    @ApiModelProperty(value = "确定工单开始信号（=0未确定工单开始，=1确定工单开始）")
    private String xhxy15w;

    @ApiModelProperty(value = "累计开机次数（换班清零）")
    private String csxy02r;

    @ApiModelProperty(value = "累计运行次数（换单清零）")
    private String csxy03r;

    @ApiModelProperty(value = "累计生产停机次数（运行停机次数一定现有产量再停机，产量还不能少于5张）（累计指令清零）")
    private String csxy04r;

    @ApiModelProperty(value = "累计点动停机次数（设备非正式生产时，经历了有运行到停止的过程）（累计指令清零）")
    private String csxy05r;

    @ApiModelProperty(value = "开机时间累计（换班清零）")
    private String sjxy03r;

    @ApiModelProperty(value = "运行时间累计（换单清零）")
    private String sjxy04r;

    @ApiModelProperty(value = "停机时间累计（累计指令清零）")
    private String sjxy05r;

    @ApiModelProperty(value = "空转时间累计（累计指令清零）")
    private String sjxy06r;

    @ApiModelProperty(value = "生产速度（当生产速度和设备运行速度相等时上传）")
    private String sdxy01r;

    @ApiModelProperty(value = "设备速度（设备转动速度），适用于有组合信号的设备")
    private String sdxy02r;

    @ApiModelProperty(value = "生产产能（换单清零）")
    private String clxy01r;

    @ApiModelProperty(value = "累计产能（（累计指令清零）手动可清零）")
    private String clxy02r;

    @ApiModelProperty(value = "实时时间，与实际实际一致")
    private String sjxy07r;

    @ApiModelProperty(value = "设置预期时间（设置时间等于实际时间自动清零累计数据）")
    private String sjxy08w;

    @ApiModelProperty(value = "下方清零信号的瞬间时间（判断信号执行）")
    private String sjxy09r;

    @ApiModelProperty(value = "硬件调试字段")
    private String M8000;

    @ApiModelProperty(value = "硬件调试字段")
    private String D8068;

    @ApiModelProperty(value = "任务id")
    private String taskid;

    @ApiModelProperty(value = "启用自动清零逻辑")
    private String xhxy30w;

    @ApiModelProperty(value = "设置定时清零时间")
    private String xhxy31w;

    @ApiModelProperty(value = "自动清零执行完成时间")
    private String xhxy32w;

    @ApiModelProperty(value = "全天累计开机次数")
    private String xhxy33w;

    // 能源
    @ApiModelProperty(value = "累计电能")
    private String dnxy01r;

    @ApiModelProperty(value = "A相电压")
    private String dnxy02r;

    @ApiModelProperty(value = "B相电压")
    private String dnxy03r;

    @ApiModelProperty(value = "C相电压")
    private String dnxy04r;

    @ApiModelProperty(value = "A相电流")
    private String dnxy05r;

    @ApiModelProperty(value = "B相电流")
    private String dnxy06r;

    @ApiModelProperty(value = "C相电流")
    private String dnxy07r;

    @ApiModelProperty(value = "互感器比分子")
    private String dnxy08w;

    @ApiModelProperty(value = "互感器比分母比例")
    private String dnxy09w;

    @ApiModelProperty(value = "硬件调试字段")
    private String d30;

    @ApiModelProperty(value = "累计水能/0.01m³")
    private String snxy01r;

    @ApiModelProperty(value = "温度")
    private String wdxy01r;

    @ApiModelProperty(value = "噪音")
    private String zyxy01r;

    @Indexed
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;
}
