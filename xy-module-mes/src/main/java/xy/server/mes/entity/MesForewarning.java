package xy.server.mes.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "mes_forewarning")
public class MesForewarning extends BaseEntity implements Serializable{

    private static final long serialVersionUID=1L;
    /**
    * 预警表主键PK
    */
    @TableId("mes_forewarning_guid")
    private String mesForewarningGuid;
    /**
    * 预警名称
    */
    @TableField("mes_forewarning_name")
    private String mesForewarningName;
    /**
    * 父预警主键
    */
    @TableField("parent_mes_forewarning_guid")
    private String parentMesForewarningGuid;
    /**
    * 预警级别
    */
    @TableField("mes_forewarning_level_name")
    private String mesForewarningLevelName;
    /**
    * 预警最小值
    */
    @TableField("min_value")
    private Integer minValue;
    /**
    * 预警最大值
    */
    @TableField("max_value")
    private Integer maxValue;
    /**
    * 是否百分比计算
    */
    @TableField("is_percent")
    private Boolean isPercent;
    /**
    * 基础值
    */
    @TableField("basic_value")
    private Integer basicValue;
    /**
    * 指定推送微信号
    */
    @TableField("wx_user_id")
    private String wxUserId;
    /**
    * 指定推送erp用户
    */
    @TableField("erp_user_guid")
    private String erpUserGuid;
    /**
    * 逻辑删除
    */
    @TableLogic
    private Boolean deleted;

    // 设备编码
    @TableField("equipment_code")
    private String equipmentCode;


}
