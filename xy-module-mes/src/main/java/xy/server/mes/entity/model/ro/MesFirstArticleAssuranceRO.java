package xy.server.mes.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* <p>
* RO
* </p>
*
* <AUTHOR>
* @since 2024-11-05
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesFirstArticleAssuranceRO对象", description = "")
public class MesFirstArticleAssuranceRO extends BaseEntity {

    @ApiModelProperty(value = "租户id")
    private String tenantGuid;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "是否合格")
    private Boolean qualityInspectionResults;

    @ApiModelProperty(value = "不合格原因")
    private String description;

    @ApiModelProperty(value = "主管id")
    private String supervisorId;

    @ApiModelProperty(value = "主管名称")
    private String supervisorName;

    @ApiModelProperty(value = "IPQC工号")
    private String inspectorGuid;

    @ApiModelProperty(value = "IPQC工号名称")
    private String inspectorName;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "任务主键")
    private String tasksGuid;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "类型")
    private String type;

}
