package xy.server.mes.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
*
* </p>
*
* <AUTHOR>
* @since 2024-06-27
*/
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "mes_pub_sub_info")
public class MesPubSubInfo extends BaseEntity implements Serializable{

    private static final long serialVersionUID=1L;
    /**
    * PK
    */
    @TableId("mes_pub_sub_info_guid")
    private String mesPubSubInfoGuid;
    /**
    * 数采网关
    */
    @TableField("gatew")
    private String gatew;
    /**
    * 数据采集盒名称
    */
    @TableField("box_name")
    private String boxName;
    /**
    * 设备编码
    */
    @TableField("equipment_code")
    private String equipmentCode;
    /**
    * 发布主题
    */
    @TableField("pub_topic")
    private String pubTopic;
    /**
    * 订阅主题
    */
    @TableField("sub_topic")
    private String subTopic;
    /**
    * 逻辑删除
    */
    @TableLogic
    private Boolean deleted;
    /**
     * 切换工单
     */
    @TableField("delivery_order")
    private String deliveryOrder;
    /**
     * 切换班组
     */
    @TableField("change_shifts")
    private String changeShifts;
    /**
     * 备用字段
     */
    @TableField("standby")
    private String standby;

    // 设备guid
    @TableField("equipment_guid")
    private String equipmentGuid;

    // 网关类型
    @TableField("gatew_type")
    private Integer gatewType;


}
