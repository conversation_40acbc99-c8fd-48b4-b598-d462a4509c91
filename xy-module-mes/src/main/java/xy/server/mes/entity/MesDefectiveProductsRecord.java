package xy.server.mes.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-28
 */
@Getter
@Setter
        @Accessors(chain = true)
    @TableName(value = "mes_defective_products_record")
public class MesDefectiveProductsRecord extends BaseEntity implements Serializable{

private static final long serialVersionUID=1L;
        /**
        * id主键
        */
        @TableId("mes_defective_products_guid")
        private String mesDefectiveProductsGuid;
        /**
        * 任务主键
        */
        @TableField("task_guid")
        private String taskGuid;
        /**
        * 良品数
        */
        @TableField("good_count")
        private Integer goodCount;
        /**
        * 不良品数
        */
        @TableField("bad_count")
        private Integer badCount;
        /**
        * 设备guid
        */
        @TableField("equipment_guid")
        private String equipmentGuid;
        /**
        * 设备名称
        */
        @TableField("equipment_name")
        private String equipmentName;
        /**
        * 不良品缺陷
        */
        @TableField("defective_products_defect")
        private String defectiveProductsDefect;
        /**
        * 不良品缺陷描述
        */
        @TableField("defective_products_defect_desc")
        private String defectiveProductsDefectDesc;
        /**
        * 是否IPQC巡查（false否true是）
        */
        @TableField("inspection_or_not")
        private Boolean inspectionOrNot;
        /**
        * 工号
        */
        @TableField("job_number")
        private String jobNumber;
        /**
        * 是否删除
        */
        @TableLogic
        private Boolean deleted;



}
