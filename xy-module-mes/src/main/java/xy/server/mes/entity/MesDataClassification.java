package xy.server.mes.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "mes_data_classification")
public class MesDataClassification extends BaseEntity implements Serializable{

private static final long serialVersionUID=1L;

    /**
    * PK 主键
    */
    @TableId("data_classification_guid")
    private String dataClassificationGuid;

    /**
    * 顺序号(每一个层级有对应的顺序号)
    */
    @TableField("serial_number")
    private Integer serialNumber;

    /**
    * 数据分类名称
    */
    @TableField("data_classification_name")
    private String dataClassificationName;

    /**
    * 备注或描述
    */
    @TableField("description")
    private String description;

    /**
    * 父节点_guid
    */
    @TableField("parent_classification_guid")
    private String parentClassificationGuid;

    /**
    * 数据字段
    */
    @TableField("data_field")
    private String dataField;

    @TableField("gather_data_source")
    private String gatherDataSource;


    /**
    * 逻辑删除
    */
    @TableLogic
    private Boolean deleted;

    /**
     * 是否递增纠正
     */
    @TableField("incremental_correction")
    private Boolean incrementalCorrection;

    /**
     * 单位
     */
    @TableField("collection_unit")
    private String collectionUnit;

}
