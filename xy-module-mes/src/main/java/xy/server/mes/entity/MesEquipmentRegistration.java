package xy.server.mes.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
*
* </p>
*
* <AUTHOR>
* @since 2024-06-21
*/
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "mes_equipment_registration")
public class MesEquipmentRegistration extends BaseEntity implements Serializable{

    private static final long serialVersionUID=1L;
    /**
    * 上机登记注册记录主键（PK）
    */
    @TableId("mes_equipment_registration_guid")
    private String mesEquipmentRegistrationGuid;
    /**
    * 多个文件名称，用,号分开保存
    */
    @TableField("file_name")
    private String fileName;
    /**
    * minio桶名
    */
    @TableField("bucket_name")
    private String bucketName;
    /**
    * 登记人id
    */
    @TableField("registration_user_id")
    private String registrationUserId;
    /**
    * 登记人名称
    */
    @TableField("registration_user_name")
    private String registrationUserName;
    /**
    * 关联设备guid
    */
    @TableField("registration_equipment_guid")
    private String registrationEquipmentGuid;
    /**
    * 设备名称
    */
    @TableField("equipment_name")
    private String equipmentName;
    /**
    * 设备编码
    */
    @TableField("equipment_code")
    private String equipmentCode;
    /**
    * 备注
    */
    @TableField("remark")
    private String remark;
    /**
    * 反馈
    */
    @TableField("feedback")
    private String feedback;

    @TableField(exist = false)
    private String rlTimestamp;

    /**
     * 备注2
     */
    @TableField("remark2")
    private String remark2;

    /**
     * 备注3
     */
    @TableField("remark3")
    private String remark3;

}
