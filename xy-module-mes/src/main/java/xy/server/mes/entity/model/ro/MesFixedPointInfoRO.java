package xy.server.mes.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* <p>
* RO
* </p>
*
* <AUTHOR>
* @since 2024-07-10
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesFixedPointInfoRO对象", description = "")
public class MesFixedPointInfoRO extends BaseEntity {

        @ApiModelProperty(value = "PK")
    private String mesFixedPointGuid;

        @ApiModelProperty(value = "设备名称")
    private String equipmentName;

        @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

        @ApiModelProperty(value = "设备guid")
    private String equipmentGuid;

        @ApiModelProperty(value = "定点位置1")
    private String fixedPointPosition1;

        @ApiModelProperty(value = "定点位置2")
    private String fixedPointPosition2;

        @ApiModelProperty(value = "定点位置3")
    private String fixedPointPosition3;

}
