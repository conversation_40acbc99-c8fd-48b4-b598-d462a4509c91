package xy.server.mes.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* <p>
* RO
* </p>
*
* <AUTHOR>
* @since 2024-06-25
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesEquipmentRegistrationStatusRO对象", description = "")
public class MesEquipmentRegistrationStatusRO extends BaseEntity {

        @ApiModelProperty(value = "PK")
    private String mesEquipmentRegistrationStatusGuid;

        @ApiModelProperty(value = "设备主键")
    private String equipmentGuid;

        @ApiModelProperty(value = "设备名称")
    private String equipmentName;

        @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

        @ApiModelProperty(value = "上机状态 false:否 true:是")
    private Boolean equipmentStatus;

}
