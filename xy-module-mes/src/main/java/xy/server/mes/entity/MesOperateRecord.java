package xy.server.mes.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* <p>
*
* </p>
*
* <AUTHOR>
* @since 2024-11-07
*/
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "mes_operate_record")
public class MesOperateRecord extends BaseEntity implements Serializable{

    private static final long serialVersionUID=1L;

    /**
    * 主键
    */
    @TableId("id")
    private String id;

    /**
    * 状态类型
    */
    @TableField("type")
    private String type;

    /**
    * 开始时间
    */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
    * 结束时间
    */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
    * 任务id
    */
    @TableField("tasks_guid")
    private String tasksGuid;

    /**
    * 设备id
    */
    @TableField("equipment_guid")
    private String equipmentGuid;

    /**
    * 是否删除
    */
    @TableLogic
    private Boolean deleted;
    /**
    * 设备名称
    */
    @TableField("equipment_name")
    private String equipmentName;


    /**
     * 备注
     */
    @TableField("desc")
    private String desc;

    /**
     * 开始时数量
     */
    @TableField("start_num")
    private Integer startNum;

    /**
     * 结束时数量
     */
    @TableField("end_num")
    private Integer endNum;





}
