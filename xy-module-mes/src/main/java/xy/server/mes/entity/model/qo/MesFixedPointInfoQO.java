package xy.server.mes.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
/**
* <p>
* QO
* </p>
*
* <AUTHOR>
* @since 2024-07-10
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesFixedPointInfoQO对象", description = "")
public class MesFixedPointInfoQO{

    @ApiModelProperty(value = "PK")
    private String mesFixedPointGuid;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "设备guid")
    private String equipmentGuid;

    @ApiModelProperty(value = "租户主键")
    private String tenantGuid;

    @ApiModelProperty(value = "创建人guid")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "修改人guid")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "定点位置1")
    private String fixedPointPosition1;

    @ApiModelProperty(value = "定点位置2")
    private String fixedPointPosition2;

    @ApiModelProperty(value = "定点位置3")
    private String fixedPointPosition3;

}
