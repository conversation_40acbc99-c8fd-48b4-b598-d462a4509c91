package xy.server.mes.entity.model.ro;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
* <p>
* RO
* </p>
*
* <AUTHOR>
* @since 2024-11-07
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesOperateRecordRO对象", description = "")
public class MesOperateRecordRO extends BaseEntity {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "状态类型")
    private String type;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "任务id")
    private String tasksGuid;

    @ApiModelProperty(value = "设备id")
    private String equipmentGuid;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "描述")
    private String desc;

    /**
     * 开始时数量
     */
    @ApiModelProperty(value = "开始时数量")
    private Integer startNum;

    /**
     * 结束时数量
     */
    @ApiModelProperty(value = "结束时数量")
    private Integer endNum;

}
