package xy.server.mes.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
*
* </p>
*
* <AUTHOR>
* @since 2024-11-05
*/
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "mes_first_article_assurance")
public class MesFirstArticleAssurance extends BaseEntity implements Serializable{

    private static final long serialVersionUID=1L;
    /**
    * 主键
    */
    @TableId("id")
    private String id;
    /**
    * 是否合格
    */
    @TableField("quality_inspection_results")
    private Boolean qualityInspectionResults;
    /**
    * 不合格原因
    */
    @TableField("description")
    private String description;
    /**
    * 主管id
    */
    @TableField("supervisor_id")
    private String supervisorId;
    /**
    * 主管名称
    */
    @TableField("supervisor_name")
    private String supervisorName;
    /**
    * IPQC工号
    */
    @TableField("inspector_guid")
    private String inspectorGuid;
    /**
    * IPQC工号名称
    */
    @TableField("inspector_name")
    private String inspectorName;
    /**
    * 逻辑删除
    */
    @TableLogic
    private Boolean deleted;

    @TableField("tenant_guid")
    private String tenantGuid;

    @TableField("workorder_number")
    private String workorderNumber;

    @TableField("tasks_guid")
    private String tasksGuid;

    @TableField("product_name")
    private String productName;

    @TableField("type")
    private String type;

}
