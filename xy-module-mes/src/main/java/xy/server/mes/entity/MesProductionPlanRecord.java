package xy.server.mes.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "mes_production_plan_record")
public class MesProductionPlanRecord extends BaseEntity implements Serializable{

    private static final long serialVersionUID=1L;

    @TableField(value = "tenant_guid", fill = FieldFill.INSERT)
    private String tenantGuid;
    /**
     * PK
     */
    @TableId("id")
    private String id;
    /**
     * 任务id
     */
    @TableField("tasks_guid")
    private String tasksGuid;
    /**
     * 设备id
     */
    @TableField("equipment_guid")
    private String equipmentGuid;

    /**
     * 设备编码
     */
    @TableField("equipment_code")
    private String equipmentCode;

    /**
     * 设备名称
     */
    @TableField("equipment_name")
    private String equipmentName;
    /**
     * 班组id
     */
    @TableField("team_guid")
    private String teamGuid;
    /**
     * 班组名称
     */
    @TableField("team_name")
    private String teamName;
    /**
     * 班次id
     */
    @TableField("shift_id")
    private String shiftId;
    /**
     * 班次名称
     */
    @TableField("shift_name")
    private String shiftName;
    /**
     * 开始时间
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    /**
     * 加工总数（计划总数）
     */
    @TableField("plan_count")
    private Integer planCount;
    /**
     * 良品数
     */
    @TableField("good_count")
    private Integer goodCount;
    /**
     * 不良品数
     */
    @TableField("bad_count")
    private Integer badCount;
    /**
     * 产出良品数
     */
    @TableField("output_good_count")
    private Integer outputGoodCount;
    /**
     * 产出不良品数
     */
    @TableField("output_bad_count")
    private Integer outputBadCount;
    /**
     * 生产时长
     */
    @TableField("pduration")
    private String pduration;
    /**
     * 有效生产时长
     */
    @TableField("peffect_duration")
    private String peffectDuration;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;

    /**
     *  当前事件状态
     */
    @TableField(exist = false)
    private String currentStatus;

    /**
     * 完成数量
     */
    @TableField("completed_quantity")
    private Integer completedQuantity;

}
