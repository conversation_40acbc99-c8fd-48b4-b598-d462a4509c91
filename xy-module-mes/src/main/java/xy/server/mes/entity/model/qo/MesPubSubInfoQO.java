package xy.server.mes.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
* <p>
* QO
* </p>
*
* <AUTHOR>
* @since 2024-06-27
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesPubSubInfoQO对象", description = "")
public class MesPubSubInfoQO {

    @ApiModelProperty(value = "PK")
    private String mesPubSubInfoGuid;

    @ApiModelProperty(value = "数采网关")
    private String gatew;

    @ApiModelProperty(value = "数据采集盒名称")
    private String boxName;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "发布主题")
    private String pubTopic;

    @ApiModelProperty(value = "订阅主题")
    private String subTopic;

    @ApiModelProperty(value = "租户id")
    private String tenantGuid;

    @ApiModelProperty(value = "创建人id")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "更新人guid")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "更新人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后一次更新日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "下发指令切换工单")
    private String deliveryOrder;

    @ApiModelProperty(value = "下发指令切换班组")
    private String changeShifts;

    @ApiModelProperty(value = "备用字段")
    private String standby;

    @ApiModelProperty(value = "设备guid")
    private String equipmentGuid;

    @ApiModelProperty(value = "0是数采，1是电能，2是水能，3456...待定")
    private Integer gatewType;

}