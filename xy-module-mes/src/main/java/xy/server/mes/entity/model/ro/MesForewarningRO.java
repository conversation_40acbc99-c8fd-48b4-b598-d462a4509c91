package xy.server.mes.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
* <p>
* RO
* </p>
*
* <AUTHOR>
* @since 2024-06-13
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesForewarningRO对象", description = "")
public class MesForewarningRO extends BaseEntity {

    @ApiModelProperty(value = "预警表主键PK")
    private String mesForewarningGuid;

    @NotNull(message = "预警名称不能为空")
    @ApiModelProperty(value = "预警名称")
    private String mesForewarningName;

    @ApiModelProperty(value = "父预警主键")
    private String parentMesForewarningGuid;

    @ApiModelProperty(value = "预警级别")
    private String mesForewarningLevelName;

    @ApiModelProperty(value = "预警最小值")
    private Integer minValue;

    @ApiModelProperty(value = "预警最大值")
    private Integer maxValue;

    @ApiModelProperty(value = "是否百分比计算")
    private Boolean isPercent;

    @ApiModelProperty(value = "基础值")
    private Integer basicValue;

    @ApiModelProperty(value = "指定推送微信号")
    private String wxUserId;

    @ApiModelProperty(value = "指定推送erp用户")
    private String erpUserGuid;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

}
