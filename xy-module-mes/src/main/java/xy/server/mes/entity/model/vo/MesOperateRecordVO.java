package xy.server.mes.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
* <p>
* VO
* </p>
*
* <AUTHOR>
* @since 2024-11-07
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesOperateRecordVO对象", description = "")
public class MesOperateRecordVO {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "状态类型")
    private String type;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "任务id")
    private String tasksGuid;

    @ApiModelProperty(value = "设备id")
    private String equipmentGuid;

    @ApiModelProperty(value = "创建人id")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "修改人id")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "租户id")
    private String tenantGuid;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "描述")
    private String desc;

    /**
     * 开始时数量
     */
    @ApiModelProperty(value = "开始时数量")
    private Integer startNum;

    /**
     * 结束时数量
     */
    @ApiModelProperty(value = "结束时数量")
    private Integer endNum;

}