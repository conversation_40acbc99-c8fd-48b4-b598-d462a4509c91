package xy.server.mes.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MongoDBMesRealTimeDataVO对象", description = "")
public class MongoDBMesRealTimeDataVO {

    @Id
    private String id;

    @ApiModelProperty(value = "租户ID")
    private String tenantGuid;

    @ApiModelProperty(value = "分类表主键")
    private String dataClassificationGuid;

    @ApiModelProperty(value = "字段名")
    private String fieldName;

    @ApiModelProperty(value = "字段值")
    private String fieldValue;

    @Indexed
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "网关")
    private String gateway;

    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @ApiModelProperty(value = "是否递增纠正")
    private Boolean incrementalCorrection;

    @ApiModelProperty(value = "单位")
    private String collectionUnit;

    @ApiModelProperty(value = "步序")
    private String stepSequence;

    @ApiModelProperty(value = "任务ID")
    private String taskGuid;
}
