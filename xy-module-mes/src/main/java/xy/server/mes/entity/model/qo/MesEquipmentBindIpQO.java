package xy.server.mes.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
* <p>
* QO
* </p>
*
* <AUTHOR>
* @since 2024-08-14
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesEquipmentBindIpQO对象", description = "")
public class MesEquipmentBindIpQO {

    @ApiModelProperty(value = "PK")
    private String equipmentBindIpGuid;

    @ApiModelProperty(value = "ip地址")
    private String ip;

    @ApiModelProperty(value = "设备guid")
    private String equipmentGuid;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "班组guid")
    private String teamGuid;

    @ApiModelProperty(value = "班组名称")
    private String teamName;

    @ApiModelProperty(value = "租户id")
    private String tenantGuid;

    @ApiModelProperty(value = "创建人id")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "更新人guid")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "更新人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后一次更新日期")
    private LocalDateTime lastUpdateDate;

}