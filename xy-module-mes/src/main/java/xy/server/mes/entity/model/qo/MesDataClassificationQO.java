package xy.server.mes.entity.model.qo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
* <p>
* QO
* </p>
*
* <AUTHOR>
* @since 2024-04-22
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesDataClassificationQO对象", description = "")
public class MesDataClassificationQO {

@ApiModelProperty(value = "租户id")
private String tenantGuid;

@ApiModelProperty(value = "PK")
private String dataClassificationGuid;

@ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
private Integer serialNumber;

@ApiModelProperty(value = "数据分类名称")
private String dataClassificationName;

@ApiModelProperty(value = "备注或描述")
private String description;

@ApiModelProperty(value = "父节点_guid")
private String parentClassificationGuid;

@ApiModelProperty(value = "数据字段")
private String dataField;

@ApiModelProperty(value = "采集数据来源")
private String gatherDataSource;


@ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
private String creatorGuid;

@ApiModelProperty(value = "创建人")
private String creator;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ApiModelProperty(value = "创建日期")
private LocalDateTime createDate;

@ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
private String lastUpdaterGuid;

@ApiModelProperty(value = "最后修改人")
private String lastUpdater;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ApiModelProperty(value = "最后修改日期")
private LocalDateTime lastUpdateDate;

@ApiModelProperty(value = "是否递增纠正")
private Boolean incrementalCorrection;

@ApiModelProperty(value = "单位")
private String collectionUnit;

@ApiModelProperty(value = "实时时间")
@TableField(value = "realTime", exist = false) // 标记为非数据库字段
private String realTime;

}