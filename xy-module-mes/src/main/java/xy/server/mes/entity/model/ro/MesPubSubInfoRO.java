package xy.server.mes.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
* <p>
* RO
* </p>
*
* <AUTHOR>
* @since 2024-06-27
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesPubSubInfoRO对象", description = "")
public class MesPubSubInfoRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String mesPubSubInfoGuid;

    @NotNull(message = "数采网关不能为空")
    @ApiModelProperty(value = "数采网关")
    private String gatew;

    @ApiModelProperty(value = "数据采集盒名称")
    private String boxName;

    @NotNull(message = "设备编码不能为空")
    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @NotNull(message = "发布主题不能为空")
    @ApiModelProperty(value = "发布主题")
    private String pubTopic;

    @NotNull(message = "订阅主题不能为空")
    @ApiModelProperty(value = "订阅主题")
    private String subTopic;

    @ApiModelProperty(value = "下发指令切换工单")
    private String deliveryOrder;

    @ApiModelProperty(value = "下发指令切换班组")
    private String changeShifts;

    @ApiModelProperty(value = "备用字段")
    private String standby;

    @ApiModelProperty(value = "设备guid")
    private String equipmentGuid;

    @ApiModelProperty(value = "0是数采，1是电能，2是水能，3456...待定")
    private Integer gatewType;

}
