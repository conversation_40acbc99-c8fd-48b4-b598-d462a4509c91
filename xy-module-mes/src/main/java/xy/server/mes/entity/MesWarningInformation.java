package xy.server.mes.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
@Data
public class MesWarningInformation {

    @ApiModelProperty(value = "租户id")
    private String tenantGuid;

    @ApiModelProperty(value = "调机开始时间")
    private String tjStartTime;

    @ApiModelProperty(value = "调机结束时间")
    private String tjEndTime;

    @ApiModelProperty(value = "较版开始时间")
    private String jbStartTime;

    @ApiModelProperty(value = "较版结束时间")
    private String jbEndTime;

    @ApiModelProperty(value = "暂停时间")
    private String ztTime;

    @ApiModelProperty(value = "恢复时间")
    private String hfTime;

    @ApiModelProperty(value = "任务id")
    private String tasksGuid;


}
