package xy.server.mes.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
* <p>
* QO
* </p>
*
* <AUTHOR>
* @since 2024-11-05
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesFirstArticleAssuranceQO对象", description = "")
public class MesFirstArticleAssuranceQO {

@ApiModelProperty(value = "租户id")
private String tenantGuid;

@ApiModelProperty(value = "主键")
private String id;

@ApiModelProperty(value = "是否合格")
private Boolean qualityInspectionResults;

@ApiModelProperty(value = "不合格原因")
private String description;

@ApiModelProperty(value = "主管id")
private String supervisorId;

@ApiModelProperty(value = "主管名称")
private String supervisorName;

@ApiModelProperty(value = "IPQC工号")
private String inspectorGuid;

@ApiModelProperty(value = "IPQC工号名称")
private String inspectorName;

@ApiModelProperty(value = "创建人id")
private String creatorGuid;

@ApiModelProperty(value = "创建人名称")
private String creator;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ApiModelProperty(value = "创建日期")
private LocalDateTime createDate;

@ApiModelProperty(value = "修改人id")
private String lastUpdaterGuid;

@ApiModelProperty(value = "修改人名称")
private String lastUpdater;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ApiModelProperty(value = "修改日期")
private LocalDateTime lastUpdateDate;

@ApiModelProperty(value = "工单号")
private String workorderNumber;

@ApiModelProperty(value = "任务主键")
private String tasksGuid;

@ApiModelProperty(value = "产品名称")
private String productName;

}