package xy.server.mes.entity.model.qo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
* <p>
* QO
* </p>
*
* <AUTHOR>
* @since 2024-05-27
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesProductionPlanRecordQO对象", description = "")
public class MesProductionPlanRecordQO {

    @ApiModelProperty(value = "租户id")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String id;

    @ApiModelProperty(value = "任务id")
    private String tasksGuid;

    @ApiModelProperty(value = "设备id")
    private String equipmentGuid;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "班组id")
    private String teamGuid;

    @ApiModelProperty(value = "班组名称")
    private String teamName;

    @ApiModelProperty(value = "班次id")
    private String shiftId;

    @ApiModelProperty(value = "班次名称")
    private String shiftName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "加工总数（计划总数）")
    private Integer planCount;

    @ApiModelProperty(value = "良品数")
    private Integer goodCount;

    @ApiModelProperty(value = "不良品数")
    private Integer badCount;

    @ApiModelProperty(value = "产出良品数")
    private Integer outputGoodCount;

    @ApiModelProperty(value = "产出不良品数")
    private Integer outputBadCount;

    @ApiModelProperty(value = "生产时长")
    private String pduration;

    @ApiModelProperty(value = "有效生产时长")
    private String peffectDuration;

    @ApiModelProperty(value = "创建人guid")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "完成数量")
    private Integer completedQuantity;

}