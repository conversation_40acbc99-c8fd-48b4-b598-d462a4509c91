package xy.server.mes.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* <p>
* RO
* </p>
*
* <AUTHOR>
* @since 2024-08-14
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesEquipmentBindIpRO对象", description = "")
public class MesEquipmentBindIpRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String equipmentBindIpGuid;

    @ApiModelProperty(value = "ip地址")
    private String ip;

    @ApiModelProperty(value = "设备guid")
    private String equipmentGuid;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "班组guid")
    private String teamGuid;

    @ApiModelProperty(value = "班组名称")
    private String teamName;

}
