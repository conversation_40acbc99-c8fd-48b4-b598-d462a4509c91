package xy.server.mes.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
*
* </p>
*
* <AUTHOR>
* @since 2024-06-25
*/
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "mes_equipment_registration_status")
public class MesEquipmentRegistrationStatus extends BaseEntity implements Serializable{

    private static final long serialVersionUID=1L;
    /**
    * PK
    */
    @TableId("mes_equipment_registration_status_guid")
    private String mesEquipmentRegistrationStatusGuid;
    /**
    * 设备主键
    */
    @TableField("equipment_guid")
    private String equipmentGuid;
    /**
    * 设备名称
    */
    @TableField("equipment_name")
    private String equipmentName;
    /**
    * 设备编码
    */
    @TableField("equipment_code")
    private String equipmentCode;
    /**
    * 上机状态 false:否 true:是
    */
    @TableField("equipment_status")
    private Boolean equipmentStatus;

}
