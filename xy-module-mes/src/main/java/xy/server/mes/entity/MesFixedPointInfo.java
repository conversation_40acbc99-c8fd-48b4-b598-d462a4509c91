package xy.server.mes.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
*
* </p>
*
* <AUTHOR>
* @since 2024-07-10
*/
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "mes_fixed_point_info")
public class MesFixedPointInfo extends BaseEntity implements Serializable{

    private static final long serialVersionUID=1L;
    /**
    * PK
    */
    @TableId("mes_fixed_point_guid")
    private String mesFixedPointGuid;
    /**
    * 设备名称
    */
    @TableField("equipment_name")
    private String equipmentName;
    /**
    * 设备编码
    */
    @TableField("equipment_code")
    private String equipmentCode;
    /**
    * 设备guid
    */
    @TableField("equipment_guid")
    private String equipmentGuid;
    /**
    * 定点位置1
    */
    @TableField("fixed_point_position1")
    private String fixedPointPosition1;
    /**
    * 定点位置2
    */
    @TableField("fixed_point_position2")
    private String fixedPointPosition2;
    /**
    * 定点位置3
    */
    @TableField("fixed_point_position3")
    private String fixedPointPosition3;



}
