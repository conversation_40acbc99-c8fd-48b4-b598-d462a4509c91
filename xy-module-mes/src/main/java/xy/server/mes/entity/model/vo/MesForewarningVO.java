package xy.server.mes.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
/**
* <p>
* VO
* </p>
*
* <AUTHOR>
* @since 2024-06-13
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesForewarningVO对象", description = "")
public class MesForewarningVO {

    @ApiModelProperty(value = "租户主键")
    private String tenantGuid;

    @ApiModelProperty(value = "预警表主键PK")
    private String mesForewarningGuid;

    @ApiModelProperty(value = "预警名称")
    private String mesForewarningName;

    @ApiModelProperty(value = "父预警主键")
    private String parentMesForewarningGuid;

    @ApiModelProperty(value = "预警级别")
    private String mesForewarningLevelName;

    @ApiModelProperty(value = "预警最小值")
    private Integer minValue;

    @ApiModelProperty(value = "预警最大值")
    private Integer maxValue;

    @ApiModelProperty(value = "是否百分比计算")
    private Boolean isPercent;

    @ApiModelProperty(value = "基础值")
    private Integer basicValue;

    @ApiModelProperty(value = "指定推送微信号")
    private String wxUserId;

    @ApiModelProperty(value = "指定推送erp用户")
    private String erpUserGuid;

    @ApiModelProperty(value = "创建人guid")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "修改人guid")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

}
