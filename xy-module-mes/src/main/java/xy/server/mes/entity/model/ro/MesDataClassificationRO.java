package xy.server.mes.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
* <p>
* RO
* </p>
*
* <AUTHOR>
* @since 2024-04-22
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MesDataClassificationRO对象", description = "")
public class MesDataClassificationRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String dataClassificationGuid;

    @NotNull(message = "顺序号(每一个层级有对应的顺序号)不能为空")
    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @NotNull(message = "数据分类名称不能为空")
    @ApiModelProperty(value = "数据分类名称")
    private String dataClassificationName;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @NotNull(message = "数据字段不能为空")
    @ApiModelProperty(value = "数据字段")
    private String dataField;

    @NotNull(message = "采集数据来源不能为空")
    @ApiModelProperty(value = "采集数据来源")
    private String gatherDataSource;


    @NotNull(message = "逻辑删除")
    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

    @ApiModelProperty(value = "是否递增纠正")
    private Boolean incrementalCorrection;

    @ApiModelProperty(value = "单位")
    private String collectionUnit;

}
