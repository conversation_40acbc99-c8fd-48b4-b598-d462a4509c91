package xy.server.mes.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xunyue.tenant.util.HttpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 微信通知发送类
 *
 * <AUTHOR>
 * @date 2021年3月3日10:20:27
 */
@Component
public class SendUtil {

    @Autowired
    private RedisConnectionFactory connectionFactory;
    // http://weixin.xyerptech.com
    private final String BASE_URL = "http://weixin.xyerptech.com";

    // private final String BASE_URL = "http://127.0.0.1:8088";

    private final String ACCESS_TOKEN_URL = BASE_URL + "/xms/token?grant_type=password&username=xymerp&password=MTIzNDU2cWF6ISEh";

    private final String TEMPLATE_URL = BASE_URL + "/weixin/wechatuserinfo/sendTemplateMessage2";

    private final String TEMPLATE_ID = "GeK96UaxfnYCAZMLlBXwvhKDgzgq2ntYQMIgQLRDTIg";

    private String redisKey = "we_chat_notice_access_token";

    private String noticeQueenKey = "we_chat_notice_queen_key";


    public void initSend() {
        RedisConnection connection = null;
        try {
            HttpUtil.doPostJSON("/mes/yongjiu-data-interaction/analyzeData","");
            JSONObject object = JSON.parseObject(HttpUtil.doPostJSON(ACCESS_TOKEN_URL, ""));
            String access_token = object.getString("access_token");
            connection = connectionFactory.getConnection();
            connection.set(redisKey.getBytes(), access_token.getBytes());
            connection.expire(redisKey.getBytes(), object.getLong("expires_in"));


        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.close();
            }
        }
    }

    /**
     * 微信推送
     *
     * @param json
     * @param frequency 重试次数
     * @return ok timeout error
     */
    public String sendTemplate(String json, int frequency) {
        RedisConnection connection = null;
        try {
            connection = connectionFactory.getConnection();
            if (connection.exists(redisKey.getBytes())) {
                String access_token = new String(connection.get(redisKey.getBytes()), StandardCharsets.UTF_8);
                String url = TEMPLATE_URL + "?access_token=" + access_token;
                String response = HttpUtil.doPostJSON(url, json);
                if (!"".equals(response)) {
                    JSONObject object = JSON.parseObject(response);
                    Integer code = (Integer) object.get("code");
                    if (code == 0 || code == -100 || code == -200) {
                        return "ok";
                    }
                }
            }
            if (frequency > 5) {
                return "timeout";
            }
            frequency += 1;
            initSend();
            return sendTemplate(json, frequency);
        } catch (Exception e) {
            e.printStackTrace();
            return "error";
        } finally {
            if (connection != null) {
                connection.close();
            }
        }
    }

    /**
     * 微信推送，队列形式
     *
     * @param json
     */
    public void sendTemplateQueen(String json) {
        RedisConnection connection = null;
        try {
            connection = connectionFactory.getConnection();
            // 将需要发送的消息存入到Redis列表
            connection.rPush(noticeQueenKey.getBytes(), json.getBytes());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.close();
            }
        }
    }

    /**
     * @param OACustomerID 推送者微信编号
     * @param title        标题
     * @param content      提醒内容
     * @param date         提醒内容
     * @param remark       给对方留言
     * @param urlParam     模板详情跳转携带参数，请补充“?”符号
     * @return JSONObject
     */
    public String getTemplateData(String OACustomerID, String title, String content,
                                  String date, String remark, String urlParam) {
        JSONObject big = new JSONObject();
        big.put("OACustomerID", OACustomerID);
        big.put("templateID", TEMPLATE_ID);
        big.put("url", "");
        big.put("systemID", 4);
        JSONObject small = new JSONObject();
        small.put("first", title);
        small.put("keyword1", content);
        small.put("keyword2", date);
        small.put("remark", remark);
        big.put("DATA", small);
        return big.toJSONString();
    }

}
