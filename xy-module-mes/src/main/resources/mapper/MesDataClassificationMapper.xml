<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.mes.mapper.MesDataClassificationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.mes.entity.model.vo.MesDataClassificationVO">
        <id column="data_classification_guid" property="dataClassificationGuid" />
        <result column="tenant_guid" property="tenantGuid"  />
        <result column="serial_number" property="serialNumber"  />
        <result column="data_classification_name" property="dataClassificationName"  />
        <result column="description" property="description"  />
        <result column="parent_classification_guid" property="parentClassificationGuid"  />
        <result column="data_field" property="dataField"  />
        <result column="gather_data_source" property="gatherDataSource"  />
        <result column="creator_guid" property="creatorGuid"  />
        <result column="creator" property="creator"  />
        <result column="create_date" property="createDate"  />
        <result column="last_updater_guid" property="lastUpdaterGuid"  />
        <result column="last_updater" property="lastUpdater"  />
        <result column="last_update_date" property="lastUpdateDate"  />
        <result column="deleted" property="deleted"  />
        <result column="collection_unit" property="collectionUnit"  />
        <result column="incremental_correction" property="incrementalCorrection"  />
    </resultMap>

    <select id="findPage" resultMap="VoResultMap">
        select * from mes_data_classification
        order by create_date desc
    </select>

    <select id="findMesDataClassification" resultMap="VoResultMap">
        select parent_classification_guid,data_classification_guid,data_classification_name,
               data_field,gather_data_source,collection_unit,incremental_correction,
               description
        from mes_data_classification
        where parent_classification_guid != '' and deleted = false
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        SELECT t1.serial_number,t1.data_classification_name,t1.description,t1.parent_classification_guid,t1.data_field,
        t1.gather_data_source,t1.creator,t1.deleted,t1.last_updater_guid,t1.last_updater,t1.last_update_date,
        t1.data_classification_guid,t1.tenant_guid,t1.creator_guid,t1.create_date
        FROM mes_data_classification t1
        where t1.deleted = false
    </select>

    <select id="findTreeData" resultMap="VoResultMap">
        SELECT t1.*,t2.real_time_data_guid,t2.filed_name,t2.filed_value,t2.create_date,t2.device_id,t2.gatew
        FROM mes_data_classification t1
        left join mes_real_time_data t2 on t1.data_classification_guid = t2.data_classification_guid
        where t1.deleted = false
        order by t1.serial_number asc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select * from mes_data_classification where data_classification_guid = #{guid}
    </select>

    <select id="findMesFiledList" resultMap="VoResultMap">
        select *
        from mes_data_classification
    </select>

</mapper>
