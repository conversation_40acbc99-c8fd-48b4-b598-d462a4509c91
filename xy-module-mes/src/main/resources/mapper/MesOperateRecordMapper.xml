<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.mes.mapper.MesOperateRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.mes.entity.model.vo.MesOperateRecordVO">
        <id column="id" property="id" />
                                                                                                                                                                                                                                                                                                        <result column="type" property="type"  />
        <result column="start_time" property="startTime"  />
        <result column="end_time" property="endTime"  />
        <result column="tasks_guid" property="tasksGuid"  />
        <result column="equipment_guid" property="equipmentGuid"  />
        <result column="creator_guid" property="creatorGuid"  />
        <result column="creator" property="creator"  />
        <result column="create_date" property="createDate"  />
        <result column="last_updater_guid" property="lastUpdaterGuid"  />
        <result column="last_updater" property="lastUpdater"  />
        <result column="last_update_date" property="lastUpdateDate"  />
        <result column="tenant_guid" property="tenantGuid"  />
        <result column="equipment_name" property="equipmentName"  />

        <result column="desc" property="desc"  />
        <result column="start_num" property="startNum"  />
        <result column="end_num" property="endNum"  />

    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select * from mes_operate_record
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select * from mes_operate_record
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select * from mes_operate_record where  id = #{guid}
    </select>

    <select id="getOneOperate" resultMap="VoResultMap">
        select * from mes_operate_record where tasks_guid = #{tasksGuid} and end_time is null order by start_time desc limit 1
    </select>

    <update id="updateByTableId" >
        update mes_operate_record set end_time = #{endTime} where id = #{id}
    </update>
</mapper>
