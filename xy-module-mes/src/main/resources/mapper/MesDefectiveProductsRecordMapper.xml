<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.mes.mapper.MesDefectiveProductsRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.mes.entity.model.vo.MesDefectiveProductsRecordVO">
        <id column="mes_defective_products_guid" property="mesDefectiveProductsGuid" />
        <result column="task_guid" property="taskGuid"  />
        <result column="good_count" property="goodCount"  />
        <result column="bad_count" property="badCount"  />
        <result column="equipment_guid" property="equipmentGuid"  />
        <result column="equipment_name" property="equipmentName"  />
        <result column="defective_products_defect" property="defectiveProductsDefect"  />
        <result column="defective_products_defect_desc" property="defectiveProductsDefectDesc"  />
        <result column="inspection_or_not" property="inspectionOrNot"  />
        <result column="job_number" property="jobNumber"  />
        <result column="creator_guid" property="creatorGuid"  />
        <result column="creator" property="creator"  />
        <result column="create_date" property="createDate"  />
        <result column="last_updater_guid" property="lastUpdaterGuid"  />
        <result column="last_updater" property="lastUpdater"  />
        <result column="last_update_date" property="lastUpdateDate"  />
        <result column="tenant_guid" property="tenantGuid"  />
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select * from mes_defective_products_record
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select * from mes_defective_products_record
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select * from mes_defective_products_record where mes_defective_products_guid = #{guid}
    </select>

    <select id="getOneByTaskGuid" resultType="java.lang.Integer">
        select sum(bad_count) as badCount from mes_defective_products_record where task_guid = #{taskGuid}
    </select>
</mapper>
