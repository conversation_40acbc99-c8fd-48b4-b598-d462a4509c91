<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.mes.mapper.MesFixedPointInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.mes.entity.model.vo.MesFixedPointInfoVO">
        <id column="mes_fixed_point_guid" property="mesFixedPointGuid" />
        <result column="equipment_name" property="equipmentName"  />
        <result column="equipment_code" property="equipmentCode"  />
        <result column="equipment_guid" property="equipmentGuid"  />
        <result column="tenant_guid" property="tenantGuid"  />
        <result column="creator_guid" property="creatorGuid"  />
        <result column="creator" property="creator"  />
        <result column="create_date" property="createDate"  />
        <result column="last_updater_guid" property="lastUpdaterGuid"  />
        <result column="last_updater" property="lastUpdater"  />
        <result column="last_update_date" property="lastUpdateDate"  />
        <result column="fixed_point_position1" property="fixedPointPosition1"  />
        <result column="fixed_point_position2" property="fixedPointPosition2"  />
        <result column="fixed_point_position3" property="fixedPointPosition3"  />
    </resultMap>

    <select id="findPage" resultMap="VoResultMap">
        select * from mes_fixed_point_info
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select * from mes_fixed_point_info
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select * from mes_fixed_point_info where mes_fixed_point_guid = #{guid}
    </select>

    <select id="getDataByEquipmentGuid" resultMap="VoResultMap">
        select * from mes_fixed_point_info where equipment_guid = #{equipmentGuid}
    </select>
</mapper>
