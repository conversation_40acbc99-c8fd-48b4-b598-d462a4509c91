<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.mes.mapper.MesPubSubInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.mes.entity.model.vo.MesPubSubInfoVO">
        <id column="mes_pub_sub_info_guid" property="mesPubSubInfoGuid" />
        <id column="gatew" property="gatew" />
        <result column="box_name" property="boxName"  />
        <result column="equipment_code" property="equipmentCode"  />
        <result column="pub_topic" property="pubTopic"  />
        <result column="sub_topic" property="subTopic"  />
        <result column="tenant_guid" property="tenantGuid"  />
        <result column="creator_guid" property="creatorGuid"  />
        <result column="creator" property="creator"  />
        <result column="create_date" property="createDate"  />
        <result column="last_updater_guid" property="lastUpdaterGuid"  />
        <result column="last_updater" property="lastUpdater"  />
        <result column="last_update_date" property="lastUpdateDate"  />
        <result column="delivery_order" property="deliveryOrder"  />
        <result column="change_shifts" property="changeShifts"  />
        <result column="standby" property="standby"  />
        <result column="equipment_guid" property="equipmentGuid"  />
        <result column="gatew_type" property="gatewType"  />
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select * from mes_pub_sub_info
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select * from mes_pub_sub_info
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select * from mes_pub_sub_info where  mes_pub_sub_info_guid = #{guid}
    </select>
    
    <select id="getMqttUrl" resultType="java.lang.String">
        select mqtt_url from mes_mqtt_info;
    </select>
</mapper>
