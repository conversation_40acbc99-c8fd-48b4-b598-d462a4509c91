<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.mes.mapper.MesEquipmentRegistrationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.mes.entity.model.vo.MesEquipmentRegistrationVO">
        <id column="mes_equipment_registration_guid" property="mesEquipmentRegistrationGuid" />
        <result column="tenant_guid" property="tenantGuid"  />
        <result column="file_name" property="fileName"  />
        <result column="bucket_name" property="bucketName"  />
        <result column="registration_user_id" property="registrationUserId"  />
        <result column="registration_user_name" property="registrationUserName"  />
        <result column="registration_equipment_guid" property="registrationEquipmentGuid"  />
        <result column="equipment_name" property="equipmentName"  />
        <result column="equipment_code" property="equipmentCode"  />
        <result column="creator_guid" property="creatorGuid"  />
        <result column="creator" property="creator"  />
        <result column="create_date" property="createDate"  />
        <result column="last_updater_guid" property="lastUpdaterGuid"  />
        <result column="last_updater" property="lastUpdater"  />
        <result column="last_update_date" property="lastUpdateDate"  />
        <result column="remark" property="remark"  />
        <result column="feedback" property="feedback"  />
        <result column="remark2" property="remark2"  />
        <result column="remark3" property="remark3"  />
    </resultMap>

    <select id="findPage" resultMap="VoResultMap">
        select * from mes_equipment_registration
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select * from mes_equipment_registration
        where creator_guid = #{model.creatorGuid}
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select * from mes_equipment_registration where mes_equipment_registration_guid = #{guid}
    </select>

</mapper>
