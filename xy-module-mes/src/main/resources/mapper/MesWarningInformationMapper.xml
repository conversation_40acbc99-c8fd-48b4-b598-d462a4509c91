<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.mes.mapper.MesWarningInformationMapper">

    <resultMap id="MesWarningDataResultMap" type="xy.server.mes.entity.MesWarningInformation">
        <result column="tenant_guid" property="tenantGuid" />
        <result column="tj_start_time" property="tjStartTime" />
        <result column="tj_end_time" property="tjEndTime" />
        <result column="jb_start_time" property="jbStartTime" />
        <result column="jb_end_time" property="jbEndTime" />
        <result column="pause_date" property="ztTime" />
        <result column="resume_date" property="hfTime" />
        <result column="tasks_guid" property="tasksGuid" />
    </resultMap>

    <select id="getMesForewarningData" resultMap="MesWarningDataResultMap">
        SELECT t1.create_date as tj_start_time,t2.create_date as tj_end_time,
               t3.create_date as jb_start_time,t4.create_date as jb_end_time,
               t5.create_date as pause_date, t6.create_date as resume_date, t0.tasks_guid,
               t0.tenant_guid
        FROM mes_production_plan_record t0
        LEFT JOIN (
            SELECT t1.tasks_guid,t1.create_date FROM erp_basic_mgt_operation_type t0
            LEFT JOIN erp_basic_mgt_operation_records t1 ON t0.operation_type_value = t1.operation_type_guid
            WHERE t0.operation_type_name = '调机开始' and t0.deleted = false
        ) t1 ON t0.tasks_guid = t1.tasks_guid
        LEFT JOIN (
            SELECT t1.tasks_guid,t1.create_date FROM erp_basic_mgt_operation_type t0
            LEFT JOIN erp_basic_mgt_operation_records t1 ON t0.operation_type_value = t1.operation_type_guid
            WHERE t0.operation_type_name = '调机结束' and t0.deleted = false
        ) t2 ON t0.tasks_guid = t2.tasks_guid
        LEFT JOIN (
            SELECT t1.tasks_guid,t1.create_date FROM erp_basic_mgt_operation_type t0
            LEFT JOIN erp_basic_mgt_operation_records t1 ON t0.operation_type_value = t1.operation_type_guid
            WHERE t0.operation_type_name = '较版开始' and t0.deleted = false
        ) t3 ON t0.tasks_guid = t3.tasks_guid
        LEFT JOIN (
            SELECT t1.tasks_guid,t1.create_date FROM erp_basic_mgt_operation_type t0
            LEFT JOIN erp_basic_mgt_operation_records t1 ON t0.operation_type_value = t1.operation_type_guid
            WHERE t0.operation_type_name = '较版结束' and t0.deleted = false
        ) t4 ON t0.tasks_guid = t4.tasks_guid
        LEFT JOIN (
            SELECT t1.tasks_guid,t1.create_date FROM erp_basic_mgt_operation_type t0
            LEFT JOIN erp_basic_mgt_operation_records t1 ON t0.operation_type_value = t1.operation_type_guid
            WHERE t0.operation_type_name = '暂停' and t0.deleted = false
            ORDER BY t1.create_date desc
                LIMIT 1
        ) t5 ON t0.tasks_guid = t5.tasks_guid
        LEFT JOIN (
            SELECT t1.tasks_guid,t1.create_date FROM erp_basic_mgt_operation_type t0
            LEFT JOIN erp_basic_mgt_operation_records t1 ON t0.operation_type_value = t1.operation_type_guid
            WHERE t0.operation_type_name = '恢复生产' and t0.deleted = false
            ORDER BY t1.create_date desc
                LIMIT 1
        ) t6 ON t0.tasks_guid = t6.tasks_guid
        WHERE t0.end_time IS NULL
    </select>
</mapper>
