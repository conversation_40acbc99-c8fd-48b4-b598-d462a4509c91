<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.mes.mapper.MesProductionPlanRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.mes.entity.model.vo.MesProductionPlanRecordVO">
        <id column="id" property="id" />
        <result column="tasks_guid" property="tasksGuid" />
        <result column="equipment_guid" property="equipmentGuid"  />
        <result column="equipment_code" property="equipmentCode"  />
        <result column="equipment_name" property="equipmentName"  />
        <result column="team_guid" property="teamGuid"  />
        <result column="team_name" property="teamName"  />
        <result column="shift_id" property="shiftId"  />
        <result column="shift_name" property="shiftName"  />
        <result column="start_time" property="startTime"  />
        <result column="end_time" property="endTime"  />
        <result column="plan_count" property="planCount"  />
        <result column="good_count" property="goodCount"  />
        <result column="bad_count" property="badCount"  />
        <result column="output_good_count" property="outputGoodCount"  />
        <result column="output_bad_count" property="outputBadCount"  />
        <result column="pduration" property="pduration"  />
        <result column="peffect_duration" property="peffectDuration"  />
        <result column="creator_guid" property="creatorGuid"  />
        <result column="creator" property="creator"  />
        <result column="create_date" property="createDate"  />
        <result column="last_updater_guid" property="lastUpdaterGuid"  />
        <result column="last_updater" property="lastUpdater"  />
        <result column="last_update_date" property="lastUpdateDate"  />
        <result column="completed_quantity" property="completedQuantity"  />
    </resultMap>

    <resultMap id="RoResultMap" type="xy.server.mes.entity.model.ro.MesProductionPlanRecordRO">
        <id column="id" property="id" />
        <result column="tenant_guid" property="tenantGuid"  />
        <result column="tasks_guid" property="tasksGuid" />
        <result column="equipment_guid" property="equipmentGuid"  />
        <result column="equipment_code" property="equipmentCode"  />
        <result column="equipment_name" property="equipmentName"  />
        <result column="team_guid" property="teamGuid"  />
        <result column="team_name" property="teamName"  />
        <result column="shift_id" property="shiftId"  />
        <result column="shift_name" property="shiftName"  />
        <result column="start_time" property="startTime"  />
        <result column="end_time" property="endTime"  />
        <result column="plan_count" property="planCount"  />
        <result column="good_count" property="goodCount"  />
        <result column="bad_count" property="badCount"  />
        <result column="output_good_count" property="outputGoodCount"  />
        <result column="output_bad_count" property="outputBadCount"  />
        <result column="pduration" property="pduration"  />
        <result column="peffect_duration" property="peffectDuration"  />
        <result column="completed_quantity" property="completedQuantity"  />
    </resultMap>

    <select id="findPage" resultMap="VoResultMap">
        select * from mes_production_plan_record
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
         select * from mes_production_plan_record order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select * from mes_production_plan_record where id = #{guid}
    </select>

    <select id="findByTasksGuid" resultMap="RoResultMap">
        select * from mes_production_plan_record where tasks_guid = #{tasksGuid} and deleted = false
        order by create_date desc
    </select>

    <update id="updateByTasksGuid">
        update mes_production_plan_record set end_time = #{model.endTime}  where id = #{model.id}
    </update>

    <update id="updateQuantityByTasksGuid">
        update mes_production_plan_record set good_count = #{goodCount},bad_count = #{badCount}  where tasks_guid = #{tasksGuid}
    </update>

</mapper>
