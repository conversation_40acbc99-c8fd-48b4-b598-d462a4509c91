<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.mes.mapper.MesEquipmentBindIpMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.mes.entity.model.vo.MesEquipmentBindIpVO">
        <id column="equipment_bind_ip_guid" property="equipmentBindIpGuid" />
        <result column="ip" property="ip"  />
        <result column="equipment_guid" property="equipmentGuid"  />
        <result column="equipment_code" property="equipmentCode"  />
        <result column="equipment_name" property="equipmentName"  />
        <result column="team_guid" property="teamGuid"  />
        <result column="team_name" property="teamName"  />
        <result column="tenant_guid" property="tenantGuid"  />
        <result column="creator_guid" property="creatorGuid"  />
        <result column="creator" property="creator"  />
        <result column="create_date" property="createDate"  />
        <result column="last_updater_guid" property="lastUpdaterGuid"  />
        <result column="last_updater" property="lastUpdater"  />
        <result column="last_update_date" property="lastUpdateDate"  />
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select * from mes_equipment_bind_ip
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select * from mes_equipment_bind_ip
        order by create_date desc
    </select>

    <select id="findListByIpAddress" resultMap="VoResultMap">
        select * from mes_equipment_bind_ip where ip = #{model.ip} and deleted = false
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select * from mes_equipment_bind_ip where equipment_bind_ip_guid = #{guid}
    </select>
</mapper>
