<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.mes.mapper.MesEventReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ResultMap" type="xy.server.mes.entity.MesEventReport">
        <result column="team_code" property="teamCode"  />
        <result column="staff_short_name" property="staffShortName"  />
        <result column="equipment_code" property="equipmentCode"  />
        <result column="equipment_name" property="equipmentName"  />
        <result column="workorder_number" property="workorderNumber"  />
        <result column="customer_short_name" property="customerShortName"  />
        <result column="material_name" property="materialName"  />
        <result column="start_time" property="startTime"  />
        <result column="end_time" property="endTime"  />
        <result column="production_processes_type_name" property="productionProcessesTypeName"  />
        <result column="plan_count" property="planCount"  />
        <result column="order_quantity" property="orderQuantity"  />
        <result column="completed_quantity" property="completedQuantity"  />
        <result column="shifts_type" property="shiftsType"  />
        <result column="ranked_data" property="rankedData"  />

        <result column="switching_time" property="switchingTime"  />
        <result column="plate_setting_time" property="plateSettingTime" />
        <result column="pause_time" property="pauseTime"  />


        <result column="team_name" property="teamName"  />
        <result column="production_non_defective_quantity" property="productionNonDefectiveQuantity"  />
        <result column="duration_minutes" property="durationMinutes"  />
        <result column="total_production_non_defective_quantity" property="totalProductionNonDefectiveQuantity"  />
        <result column="rth_rate" property="rthRate"  />
        <result column="equipment_parameter_value" property="equipmentParameterValue"  />

        <result column="avg_speed" property="avgSpeed"  />
        <result column="speed_ratio" property="speedRatio"  />
        <result column="over_diff_count" property="overDiffCount"  />
        <result column="plan_rates" property="planRates"  />
        <result column="lack_diff_count" property="lackDiffCount"  />
        <result column="lack_rates" property="lackRates"  />

        <result column="total_tasks_count" property="totalTasksCount"  />
        <result column="total_planned_quantity" property="totalPlannedQuantity"  />
        <result column="plan_duration_minutes" property="planDurationMinutes"  />
        <result column="act_duration_minutes" property="actDurationMinutes"  />




    </resultMap>

    <select id="findReportData" resultMap="ResultMap">
        SELECT
        COALESCE(t0.equipment_code, '') as equipment_code,
        COALESCE(t0.box_name, '') as equipment_name,
        COALESCE(t1.team_code, '') as team_code,
        COALESCE(t1.staff_short_name, '') as staff_short_name,
        COALESCE(t1.workorder_number, '') as workorder_number,
        COALESCE(t1.customer_short_name, '') as customer_short_name,
        COALESCE(t1.material_name, '') as material_name,
        COALESCE(t1.production_processes_type_name, '') as production_processes_type_name,
        t1.start_time,
        t1.end_time,
        COALESCE(t1.plan_count, 0) as plan_count,
        COALESCE(t1.quantity, 0) as quantity,
        COALESCE(t1.completed_quantity, 0) as completed_quantity,
        t1.good_count,
        t1.bad_count,
        t1.shifts_type,
        t1.switching_time,
        t1.plate_setting_time,
        t1.pause_time,
        t1.ranked_data
        FROM mes_pub_sub_info t0
        LEFT JOIN (
        WITH ranked_data AS (
        SELECT t2.filed_name,
        t2.task_guid,
        t2.filed_value,
        t2.create_date,
        ROW_NUMBER() OVER(PARTITION BY t2.filed_name, t2.task_guid ORDER BY t2.create_date DESC) AS rn
        FROM mes_production_plan_record t1
        LEFT JOIN energy_items_data_hour_record t2 ON t1.tasks_guid = t2.task_guid
        where t2.create_date is not null
        )
        , merged_data AS (
        SELECT task_guid as tasks_guid,
        STRING_AGG(filed_name || ': ' || filed_value, ' | ') AS merged_data,
        MAX(create_date) AS max_create_date
        FROM ranked_data
        WHERE rn = 1
        GROUP BY task_guid
        )

        SELECT
        COALESCE(t2.team_code, '') as team_code,
        COALESCE(t4.staff_short_name, '') as staff_short_name,
        COALESCE(t5.equipment_code, '') as equipment_code,
        COALESCE(t5.equipment_name, '') as equipment_name,
        COALESCE(t6.workorder_number, '') as workorder_number,
        COALESCE(t8.customer_short_name, '') as customer_short_name,
        COALESCE(t9.material_name, '') as material_name,
        COALESCE(t10.production_processes_type_name, '') as production_processes_type_name,
        t1.start_time,
        t1.end_time,
        COALESCE(t1.plan_count, 0) as plan_count,
        COALESCE(t11.quantity, 0) as quantity,
        COALESCE(t1.completed_quantity, 0) as completed_quantity,
        COALESCE(t1.good_count, 0) as good_count,
        COALESCE(t1.bad_count, 0) as bad_count,
        t15.switching_time,
        t16.plate_setting_time,
        t17.pause_time,
        CASE
        WHEN EXTRACT(HOUR FROM t1.start_time) BETWEEN 8 AND 20 THEN '白班'
        ELSE '夜班'
        END AS shifts_type,
        md.merged_data AS ranked_data
        FROM mes_production_plan_record t1
        LEFT JOIN erp_administration_mgt_team t2 ON t1.team_guid = t2.team_guid
        LEFT JOIN erp_administration_mgt_team_members t3 ON t2.team_guid = t3.team_guid AND t3.is_group = TRUE
        LEFT JOIN erp_administration_mgt_staff t4 ON t3.staff_guid = t4.staff_guid
        LEFT JOIN erp_equipment_mgt_equipment t5 ON t1.equipment_guid = t5.equipment_guid
        LEFT JOIN erp_basic_mgt_tasks t6 ON t1.tasks_guid = t6.tasks_guid
        LEFT JOIN erp_production_mgt_workorder t7 ON t6.workorder_number = t7.workorder_number AND t7.deleted = FALSE AND t7.workorder_properties = 15 AND t7.source_guid IS NOT NULL AND t7.current_status != '3'
        LEFT JOIN erp_customer_mgt_customer t8 ON t7.customer_guid = t8.customer_guid
        LEFT JOIN erp_material_mgt_material t9 ON t7.material_guid = t9.material_guid
        LEFT JOIN erp_basic_mgt_production_processes_type t10 ON t6.production_processes_type_guid = t10.production_processes_type_guid
        LEFT JOIN (
        SELECT
        SUM(ebmod.quantity) AS quantity, epmw.workorder_number
        FROM
        erp_production_mgt_workorder epmw
        LEFT JOIN erp_production_mgt_workorder epmw2 ON epmw.source_guid = epmw2.workorder_guid
        LEFT JOIN erp_business_mgt_order_data ebmod ON epmw2.source_guid = ebmod.order_data_guid
        WHERE epmw.workorder_properties = 15 AND epmw.source_guid IS NOT NULL	AND epmw.current_status != '3'
        GROUP BY epmw.workorder_number
        ) t11 ON t11.workorder_number = t7.workorder_number
        LEFT JOIN merged_data md ON md.tasks_guid = t1.tasks_guid
        LEFT JOIN (
        WITH operation_times AS (
        SELECT
        t1.tasks_guid,
        t1.create_date,
        t2.operation_type_value,
        ROW_NUMBER() OVER(PARTITION BY t1.tasks_guid, t2.operation_type_value ORDER BY t1.create_date DESC) as rn
        FROM
        erp_basic_mgt_operation_records t1
        LEFT JOIN
        erp_basic_mgt_operation_type t2
        ON t1.operation_type_guid = t2.operation_type_value
        WHERE t2.operation_type_value IN ('5', '6')
        ),
        min_max_times AS (
        SELECT
        tasks_guid,
        MAX(CASE WHEN operation_type_value = '5' THEN create_date END) as max_start_time,
        MIN(CASE WHEN operation_type_value = '6' THEN create_date END) as min_end_time
        FROM
        operation_times
        WHERE rn = 1 GROUP BY tasks_guid
        )
        SELECT
        tasks_guid,
        TRUNC(COALESCE(EXTRACT(EPOCH FROM (min_end_time - max_start_time)), 0)) as switching_time
        FROM
        min_max_times
        ) t15 ON t1.tasks_guid = t15.tasks_guid
        LEFT JOIN (
        WITH operation_times AS (
        SELECT
        t1.tasks_guid,
        t1.create_date,
        t2.operation_type_value,
        ROW_NUMBER() OVER(PARTITION BY t1.tasks_guid, t2.operation_type_value ORDER BY t1.create_date DESC) as rn
        FROM
        erp_basic_mgt_operation_records t1
        LEFT JOIN
        erp_basic_mgt_operation_type t2
        ON t1.operation_type_guid = t2.operation_type_value
        WHERE t2.operation_type_value IN ('3', '4')
        ),
        min_max_times AS (
        SELECT
        tasks_guid,
        MAX(CASE WHEN operation_type_value = '3' THEN create_date END) as max_start_time,
        MIN(CASE WHEN operation_type_value = '4' THEN create_date END) as min_end_time
        FROM  operation_times
        WHERE  rn = 1 GROUP BY tasks_guid
        )
        SELECT
        tasks_guid,
        TRUNC(COALESCE(EXTRACT(EPOCH FROM (min_end_time - max_start_time)), 0)) as plate_setting_time
        FROM
        min_max_times
        ) t16 ON t1.tasks_guid = t16.tasks_guid
        LEFT JOIN (
        WITH operation_times AS (
        SELECT
        t1.tasks_guid,
        t1.create_date,
        t2.operation_type_value,
        ROW_NUMBER() OVER(PARTITION BY t1.tasks_guid, t2.operation_type_value ORDER BY t1.create_date DESC) as rn
        FROM
        erp_basic_mgt_operation_records t1
        LEFT JOIN
        erp_basic_mgt_operation_type t2
        ON t1.operation_type_guid = t2.operation_type_value
        WHERE t2.operation_type_value IN ('7', '8')
        ),
        min_max_times AS (
        SELECT
        tasks_guid,
        MAX(CASE WHEN operation_type_value = '7' THEN create_date END) as max_start_time,
        MIN(CASE WHEN operation_type_value = '8' THEN create_date END) as min_end_time
        FROM
        operation_times
        WHERE rn = 1  GROUP BY tasks_guid
        )
        SELECT
        tasks_guid,
        TRUNC(COALESCE(EXTRACT(EPOCH FROM (min_end_time - max_start_time)), 0)) as pause_time
        FROM
        min_max_times
        ) t17 ON t1.tasks_guid = t17.tasks_guid
        where 1=1
        <if test="model.startTime != null and model.startTime != '' ">
            and t1.start_time <![CDATA[>=]]> to_timestamp(#{model.startTime}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="model.endTime != null and model.endTime != '' ">
            and t1.start_time <![CDATA[<=]]> to_timestamp(#{model.endTime}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
        and t1.deleted = false
        ) t1 ON t0.equipment_code = t1.equipment_code
        where 1=1

        ORDER BY box_name asc, shifts_type ASC
    </select>

    <select id="findReportDataForTime" resultMap="ResultMap">
        SELECT equipment_name ,equipment_code, ranked_data,
               start_time, end_time,shifts_type
        FROM (
                 WITH ranked_data_1 AS (
                     WITH RankedData AS (
                         SELECT
                             equipment_code,
                             filed_name,
                             create_date,
                             filed_value::numeric AS numeric_filed_value,

                                 ROW_NUMBER() OVER (PARTITION BY filed_name,equipment_code ORDER BY create_date DESC) AS RowLast
                         FROM
                             energy_items_data_hour_record
                         WHERE
                             create_date IS NOT NULL
                           AND DATE_TRUNC('day', create_date) = #{model.filterDate}
                           AND EXTRACT(HOUR FROM create_date) BETWEEN 8 AND 20
                           AND EXTRACT(MINUTE FROM create_date) BETWEEN 0 AND 59
                           AND EXTRACT(SECOND FROM create_date) BETWEEN 0 AND 59
                     )
                     SELECT
                         filed_name,
                         equipment_code,
                         MAX(create_date) AS last_create_date,
                         MAX(numeric_filed_value) AS filed_value
                     FROM
                         RankedData
                     WHERE
                         RowLast = 1
                     GROUP BY
                         filed_name,equipment_code
                     ORDER BY
                         filed_name
                 ),
                      merged_data_1 AS (
                          SELECT
                              equipment_code,
                              STRING_AGG(filed_name || ': ' || filed_value, ' | ') AS merged_data,
                              MAX(filed_value) AS filed_value
                          FROM
                              ranked_data_1
                          GROUP BY
                              equipment_code
                      ),
                      ranked_data_2 AS (
                          WITH RankedData AS (
                              SELECT
                                  equipment_code,
                                  filed_name,
                                  create_date,
                                  filed_value::numeric AS numeric_filed_value,
                                      ROW_NUMBER() OVER (PARTITION BY filed_name,equipment_code ORDER BY create_date ASC) AS RowFirst,
                                      ROW_NUMBER() OVER (PARTITION BY filed_name,equipment_code ORDER BY create_date DESC) AS RowLast
                              FROM
                                  energy_items_data_hour_record
                              WHERE create_date IS NOT NULL
                                AND (DATE_TRUNC('day', create_date) = #{model.filterDate} AND EXTRACT(HOUR FROM create_date) BETWEEN 20 AND 23
                                  OR DATE_TRUNC('day', create_date) = #{model.filterDate} + INTERVAL '1 day' AND EXTRACT(HOUR FROM create_date) BETWEEN 0 AND 7)
                                AND EXTRACT(MINUTE FROM create_date) BETWEEN 0 AND 59
                                AND EXTRACT(SECOND FROM create_date) BETWEEN 0 AND 59
                          )

                          SELECT
                              equipment_code,
                              filed_name,
                              MIN(create_date) AS first_create_date,
                              MAX(create_date) AS last_create_date,
                              MIN(numeric_filed_value) AS first_numeric_filed_value,
                              MAX(numeric_filed_value) AS last_numeric_filed_value,
                              (MAX(numeric_filed_value) - MIN(numeric_filed_value)) AS filed_value
                          FROM
                              RankedData
                          WHERE
                              RowFirst = 1 OR RowLast = 1
                          GROUP BY
                              filed_name,	equipment_code
                          ORDER BY
                              filed_name
                      ),
                      merged_data_2  AS (
                          SELECT equipment_code AS equipment_code,
                                 STRING_AGG(filed_name || ': ' || filed_value, ' | ') AS merged_data,
                                 MIN(first_numeric_filed_value) AS first_numeric_filed_value,
                                 MAX(last_numeric_filed_value) AS last_numeric_filed_value
                          FROM ranked_data_2
                          GROUP BY equipment_code
                      )

                 SELECT t0.box_name AS equipment_name,
                        t0.equipment_code,
                        t1.merged_data AS ranked_data,
                        '08:00' AS start_time,
                        '20:00' AS end_time,
                        '白班' AS shifts_type
                 FROM
                     mes_pub_sub_info t0
                         LEFT JOIN
                     merged_data_1 t1 ON t0.equipment_code = t1.equipment_code

                 UNION ALL

                 SELECT t0.box_name AS equipment_name,
                        t0.equipment_code,
                        t1.merged_data AS ranked_data,
                        '20:00' AS start_time,
                        '08:00' AS end_time,
                        '晚班' AS shifts_type
                 FROM
                     mes_pub_sub_info t0
                         LEFT JOIN
                     merged_data_2 t1 ON t0.equipment_code = t1.equipment_code
             ) t  ORDER BY equipment_name asc ,start_time asc
    </select>

    <select id="findStatisticalData" resultType="java.util.Map">
        SELECT
            t0.*,
            t1.outboundArea,
            t0.team_name,
            t1.production_duration
        FROM
            (
                SELECT
                    t1.team_guid,t1.team_name,
                    ROUND( AVG ( CAST ( t2.avg_filed_value AS NUMERIC ) ) ) AS avg_filed_value,
                    ROUND( MAX ( CAST ( t2.max_filed_value AS NUMERIC ) ) ) AS max_filed_value,
                    ROUND( MIN ( CAST ( t2.min_filed_value AS NUMERIC ) ) ) AS min_filed_value,
                    SUM ( t1.plan_count ) AS total_plan_count,
                    SUM ( t1.good_count ) AS total_good_count
                FROM
                    mes_production_plan_record t1
                        LEFT JOIN (
                        SELECT
                            t1.tasks_guid,
                            ROUND( AVG ( CAST ( t2.filed_value AS NUMERIC ) ) ) AS avg_filed_value,
                            ROUND( MAX ( CAST ( t2.filed_value AS NUMERIC ) ) ) AS max_filed_value,
                            ROUND( MIN ( CAST ( t2.filed_value AS NUMERIC ) ) ) AS min_filed_value
                        FROM
                            mes_production_plan_record t1
                                LEFT JOIN energy_items_data_hour_record t2 ON t1.tasks_guid = t2.task_guid
                        WHERE
                            t2.filed_name LIKE'%生产速度%'
                        GROUP BY
                            t1.tasks_guid
                    ) t2 ON t1.tasks_guid = t2.tasks_guid
                WHERE 1=1
                GROUP BY
                    t1.team_guid ,t1.team_name
            ) t0 LEFT JOIN (
                SELECT SUM-- 	面积
                       ( COALESCE ( production_process_extend.product_area, 0 ) * ebmdrdp.production_non_defective_quantity ) AS outboundArea,
                       eamt.team_guid,
                       ROUND( AVG ( ebmdrdp.production_duration ) ) AS production_duration
                FROM
                    erp_basic_mgt_daily_report_detail ebmdrd
                        LEFT JOIN (
                        SELECT
                            MIN ( team_guid ) team_guid,
                            daily_report_detail_guid
                        FROM erp_basic_mgt_daily_report_members
                        GROUP BY daily_report_detail_guid
                    ) sfllteam ON sfllteam.daily_report_detail_guid = ebmdrd.daily_report_detail_guid
                        LEFT JOIN erp_administration_mgt_team eamt ON eamt.team_guid = sfllteam.team_guid
                        LEFT JOIN erp_basic_mgt_daily_report_detail_production ebmdrdp ON ebmdrdp.daily_report_detail_guid = ebmdrd.daily_report_detail_guid
                        LEFT JOIN erp_basic_mgt_tasks ebmt ON ebmt.tasks_guid = ebmdrd.tasks_guid
                        LEFT JOIN erp_production_mgt_workorder epmw ON epmw.workorder_guid = ebmt.source_guid
                        LEFT JOIN erp_production_mgt_workorder epmw6 ON epmw6.source_guid = epmw.source_guid
                        AND epmw6.workorder_type = 1
                        AND epmw6.workorder_properties = 14 -- 客户
                        LEFT JOIN erp_production_mgt_experience_production_process production_process ON production_process.experience_production_process_guid = epmw6.experience_production_process_guid
                        LEFT JOIN erp_production_mgt_experience_production_process_extend production_process_extend ON production_process_extend.experience_production_process_guid = production_process.experience_production_process_guid
                GROUP BY
                    sfllteam.team_guid,
                    eamt.team_guid
            ) t1 ON t0.team_guid = t1.team_guid
    </select>


    <select id="findMonthReportData_copies" resultType="java.util.Map">
        WITH specified_date AS ( SELECT '2024-08-01' :: DATE AS start_date ),
             current_month AS ( SELECT start_date AS first_day_of_month, DATE_TRUNC( 'month', start_date + INTERVAL '1 month' ) - INTERVAL '1 day' AS last_day_of_month FROM specified_date ),
            days_in_month AS ( SELECT GENERATE_SERIES ( first_day_of_month, last_day_of_month, '1 day' ) AS the_day FROM current_month )
        SELECT
            TO_CHAR( t0.the_day, 'YYYY-MM-DD' ) AS the_day,
            t1.create_date,
            COALESCE ( t1.merged_data, '' ) AS ranked_data
        FROM
            days_in_month t0
                LEFT JOIN (
                WITH RankedValues AS (
                    SELECT
                        equipment_code,
                        filed_name,
                        DATE_TRUNC( 'day', create_date ) AS create_date_day,
                        filed_value :: NUMERIC AS numeric_filed_value,
                            ROW_NUMBER ( ) OVER ( PARTITION BY DATE_TRUNC( 'day', create_date ), filed_name ORDER BY filed_value DESC ) AS RowNum
                    FROM
                        energy_items_data_hour_record
                    WHERE
                        create_date IS NOT NULL
                      AND create_date <![CDATA[>=]]> DATE_TRUNC( 'month', '2024-08-03' :: DATE )

                      AND create_date <![CDATA[<]]>  DATE_TRUNC( 'month', '2024-08-03' :: DATE ) + INTERVAL '1 month'

                    AND equipment_code = 'C105'
            )
        SELECT
            TO_CHAR( create_date_day, 'YYYY-MM-DD' ) AS create_date,
            STRING_AGG ( filed_name || ': ' || numeric_filed_value, ' | ' ) AS merged_data
        FROM RankedValues
        WHERE
            RowNum = 1
        GROUP BY
            create_date_day
        ORDER BY
            create_date_day
            ) t1 ON t0.the_day = t1.create_date :: DATE;-- 转换 t1.create_date 为 date 类型进行比较
    </select>


    <select id="findMonthReportData" resultType="java.util.Map">
        WITH specified_date AS (
            SELECT #{model.filterDate}::DATE AS start_date
        ),
             current_month AS (
                 SELECT
                     start_date AS first_day_of_month,
                     DATE_TRUNC('month', start_date + INTERVAL '1 month') - INTERVAL '1 day' AS last_day_of_month
        FROM specified_date
            ),
            days_in_month AS (
        SELECT GENERATE_SERIES(first_day_of_month, last_day_of_month, '1 day') AS the_day
        FROM current_month
            ),


            filtered_team_members AS (
        SELECT
            t0.team_guid,
            t1.staff_short_name,
            ROW_NUMBER() OVER (PARTITION BY t0.team_guid ORDER BY t0.staff_guid) AS rn
        FROM
            erp_administration_mgt_team_members t0
            LEFT JOIN
            erp_administration_mgt_staff t1 ON t0.staff_guid = t1.staff_guid
        WHERE
            t0.is_group = true
            )

        SELECT
            TO_CHAR(t0.the_day, 'YYYY-MM-DD') AS the_day,
            t1.equipment_code,
            t1.equipment_name,
            t1.team_guid,
            t1.team_name,
            t1.plan_date,
            t1.total_plan_count,
            t1.total_good_count,
            t1.total_bad_count,
            t1.good_rate,
            t1.bad_rate,
            staff_names.staff_short_names as  group_members,
            STRING_AGG(ftm.staff_short_name, ', ') AS group_leader

        FROM
            days_in_month t0
                LEFT JOIN (
                SELECT
                    equipment_code,
                    equipment_name,
                    team_guid,
                    team_name,
                    DATE_TRUNC('day', start_time) AS plan_date,
                    SUM(plan_count) AS total_plan_count,
                    SUM(good_count) AS total_good_count,
                    SUM(bad_count) AS total_bad_count,
                    CASE
                        WHEN SUM(good_count) + SUM(bad_count) > 0 AND SUM(good_count) > 0 THEN
                            ROUND(SUM(good_count) * 1.0 / (SUM(good_count) + SUM(bad_count)), 4)
                        ELSE
                            0
                        END AS good_rate,
                    CASE
                        WHEN SUM(good_count) + SUM(bad_count) > 0 AND SUM(bad_count) > 0 THEN
                            ROUND(SUM(bad_count) * 1.0 / (SUM(good_count) + SUM(bad_count)), 4)
                        ELSE
                            0
                        END AS bad_rate
                FROM
                    mes_production_plan_record
                WHERE
                    equipment_code = #{model.equipmentCode}
                GROUP BY
                    equipment_code,
                    equipment_name,
                    plan_date,
                    team_guid,
                    team_name
            ) t1 ON t0.the_day = t1.plan_date::DATE
        LEFT JOIN (
            SELECT
                team_guid,
                staff_short_name
            FROM
                filtered_team_members
            WHERE
                rn = 1
        ) ftm ON t1.team_guid = ftm.team_guid
            LEFT JOIN (
            SELECT
            team_guid,
            STRING_AGG(staff_short_name, ', ') AS staff_short_names
            FROM
            erp_administration_mgt_team_members t0
            LEFT JOIN
            erp_administration_mgt_staff t1 ON t0.staff_guid = t1.staff_guid
            GROUP BY
            team_guid
            ) staff_names ON t1.team_guid = staff_names.team_guid

        GROUP BY
            t0.the_day,
            t1.equipment_code,
            t1.equipment_name,
            t1.team_guid,
            t1.team_name,
            t1.plan_date,
            t1.total_plan_count,
            t1.total_good_count,
            t1.total_bad_count,
            t1.good_rate,
            staff_names.staff_short_names ,
            t1.bad_rate
        ORDER BY
            t0.the_day;
    </select>


    <select id="findReportDataByMonth" resultMap="ResultMap">
        SELECT
        COALESCE(t0.equipment_code, '') as equipment_code,
        COALESCE(t0.box_name, '') as equipment_name,
        COALESCE(t1.team_code, '') as team_code,
        COALESCE(t1.staff_short_name, '') as staff_short_name,
        COALESCE(t1.workorder_number, '') as workorder_number,
        COALESCE(t1.customer_short_name, '') as customer_short_name,
        COALESCE(t1.material_name, '') as material_name,
        COALESCE(t1.production_processes_type_name, '') as production_processes_type_name,
        t1.start_time,
        t1.end_time,
        COALESCE(t1.plan_count, 0) as plan_count,
        COALESCE(t1.quantity, 0) as quantity,
        COALESCE(t1.completed_quantity, 0) as completed_quantity,
        t1.good_count,
        t1.bad_count,
        t1.shifts_type,
        t1.switching_time,
        t1.plate_setting_time,
        t1.pause_time,
        t1.ranked_data
        FROM mes_pub_sub_info t0
        LEFT JOIN (
        WITH ranked_data AS (
        SELECT t2.filed_name,
        t2.task_guid,
        t2.filed_value,
        t2.create_date,
        ROW_NUMBER() OVER(PARTITION BY t2.filed_name, t2.task_guid ORDER BY t2.create_date DESC) AS rn
        FROM mes_production_plan_record t1
        LEFT JOIN energy_items_data_hour_record t2 ON t1.tasks_guid = t2.task_guid
        where t2.create_date is not null
        )
        , merged_data AS (
        SELECT task_guid as tasks_guid,
        STRING_AGG(filed_name || ': ' || filed_value, ' | ') AS merged_data,
        MAX(create_date) AS max_create_date
        FROM ranked_data
        WHERE rn = 1
        GROUP BY task_guid
        )

        SELECT
        COALESCE(t2.team_code, '') as team_code,
        COALESCE(t4.staff_short_name, '') as staff_short_name,
        COALESCE(t5.equipment_code, '') as equipment_code,
        COALESCE(t5.equipment_name, '') as equipment_name,
        COALESCE(t6.workorder_number, '') as workorder_number,
        COALESCE(t8.customer_short_name, '') as customer_short_name,
        COALESCE(t9.material_name, '') as material_name,
        COALESCE(t10.production_processes_type_name, '') as production_processes_type_name,
        t1.start_time,
        t1.end_time,
        COALESCE(t1.plan_count, 0) as plan_count,
        COALESCE(t11.quantity, 0) as quantity,
        COALESCE(t1.completed_quantity, 0) as completed_quantity,
        COALESCE(t1.good_count, 0) as good_count,
        COALESCE(t1.bad_count, 0) as bad_count,
        t15.switching_time,
        t16.plate_setting_time,
        t17.pause_time,
        CASE
        WHEN EXTRACT(HOUR FROM t1.start_time) BETWEEN 8 AND 20 THEN '白班'
        ELSE '夜班'
        END AS shifts_type,
        md.merged_data AS ranked_data
        FROM mes_production_plan_record t1
        LEFT JOIN erp_administration_mgt_team t2 ON t1.team_guid = t2.team_guid
        LEFT JOIN erp_administration_mgt_team_members t3 ON t2.team_guid = t3.team_guid AND t3.is_group = TRUE
        LEFT JOIN erp_administration_mgt_staff t4 ON t3.staff_guid = t4.staff_guid
        LEFT JOIN erp_equipment_mgt_equipment t5 ON t1.equipment_guid = t5.equipment_guid
        LEFT JOIN erp_basic_mgt_tasks t6 ON t1.tasks_guid = t6.tasks_guid
        LEFT JOIN erp_production_mgt_workorder t7 ON t6.workorder_number = t7.workorder_number AND t7.deleted = FALSE AND t7.workorder_properties = 15 AND t7.source_guid IS NOT NULL AND t7.current_status != '3'
        LEFT JOIN erp_customer_mgt_customer t8 ON t7.customer_guid = t8.customer_guid
        LEFT JOIN erp_material_mgt_material t9 ON t7.material_guid = t9.material_guid
        LEFT JOIN erp_basic_mgt_production_processes_type t10 ON t6.production_processes_type_guid = t10.production_processes_type_guid
        LEFT JOIN (
        SELECT
        SUM(ebmod.quantity) AS quantity, epmw.workorder_number
        FROM
        erp_production_mgt_workorder epmw
        LEFT JOIN erp_production_mgt_workorder epmw2 ON epmw.source_guid = epmw2.workorder_guid
        LEFT JOIN erp_business_mgt_order_data ebmod ON epmw2.source_guid = ebmod.order_data_guid
        WHERE epmw.workorder_properties = 15 AND epmw.source_guid IS NOT NULL	AND epmw.current_status != '3'
        GROUP BY epmw.workorder_number
        ) t11 ON t11.workorder_number = t7.workorder_number
        LEFT JOIN merged_data md ON md.tasks_guid = t1.tasks_guid
        LEFT JOIN (
        WITH operation_times AS (
        SELECT
        t1.tasks_guid,
        t1.create_date,
        t2.operation_type_value,
        ROW_NUMBER() OVER(PARTITION BY t1.tasks_guid, t2.operation_type_value ORDER BY t1.create_date DESC) as rn
        FROM
        erp_basic_mgt_operation_records t1
        LEFT JOIN
        erp_basic_mgt_operation_type t2
        ON t1.operation_type_guid = t2.operation_type_value
        WHERE t2.operation_type_value IN ('5', '6')
        ),
        min_max_times AS (
        SELECT
        tasks_guid,
        MAX(CASE WHEN operation_type_value = '5' THEN create_date END) as max_start_time,
        MIN(CASE WHEN operation_type_value = '6' THEN create_date END) as min_end_time
        FROM
        operation_times
        WHERE rn = 1 GROUP BY tasks_guid
        )
        SELECT
        tasks_guid,
        TRUNC(COALESCE(EXTRACT(EPOCH FROM (min_end_time - max_start_time)), 0)) as switching_time
        FROM
        min_max_times
        ) t15 ON t1.tasks_guid = t15.tasks_guid
        LEFT JOIN (
        WITH operation_times AS (
        SELECT
        t1.tasks_guid,
        t1.create_date,
        t2.operation_type_value,
        ROW_NUMBER() OVER(PARTITION BY t1.tasks_guid, t2.operation_type_value ORDER BY t1.create_date DESC) as rn
        FROM
        erp_basic_mgt_operation_records t1
        LEFT JOIN
        erp_basic_mgt_operation_type t2
        ON t1.operation_type_guid = t2.operation_type_value
        WHERE t2.operation_type_value IN ('3', '4')
        ),
        min_max_times AS (
        SELECT
        tasks_guid,
        MAX(CASE WHEN operation_type_value = '3' THEN create_date END) as max_start_time,
        MIN(CASE WHEN operation_type_value = '4' THEN create_date END) as min_end_time
        FROM  operation_times
        WHERE  rn = 1 GROUP BY tasks_guid
        )
        SELECT
        tasks_guid,
        TRUNC(COALESCE(EXTRACT(EPOCH FROM (min_end_time - max_start_time)), 0)) as plate_setting_time
        FROM
        min_max_times
        ) t16 ON t1.tasks_guid = t16.tasks_guid
        LEFT JOIN (
        WITH operation_times AS (
        SELECT
        t1.tasks_guid,
        t1.create_date,
        t2.operation_type_value,
        ROW_NUMBER() OVER(PARTITION BY t1.tasks_guid, t2.operation_type_value ORDER BY t1.create_date DESC) as rn
        FROM
        erp_basic_mgt_operation_records t1
        LEFT JOIN
        erp_basic_mgt_operation_type t2
        ON t1.operation_type_guid = t2.operation_type_value
        WHERE t2.operation_type_value IN ('7', '8')
        ),
        min_max_times AS (
        SELECT
        tasks_guid,
        MAX(CASE WHEN operation_type_value = '7' THEN create_date END) as max_start_time,
        MIN(CASE WHEN operation_type_value = '8' THEN create_date END) as min_end_time
        FROM
        operation_times
        WHERE rn = 1  GROUP BY tasks_guid
        )
        SELECT
        tasks_guid,
        TRUNC(COALESCE(EXTRACT(EPOCH FROM (min_end_time - max_start_time)), 0)) as pause_time
        FROM
        min_max_times
        ) t17 ON t1.tasks_guid = t17.tasks_guid
        where 1=1
        <if test="model.startTime != null and model.startTime != '' ">
            and t1.start_time::DATE = #{model.startTime}::DATE
        </if>
        and t1.deleted = false
        ) t1 ON t0.equipment_code = t1.equipment_code
        where 1=1
        <if test="model.equipmentCode != null and model.equipmentCode != '' ">
            and t1.equipment_code = #{model.equipmentCode}
        </if>
        ORDER BY box_name asc, shifts_type ASC
    </select>

    <select id="findProductionStandardReport" resultMap="ResultMap">

        -- 交出良品数 = production_non_defective_quantity
        -- 当班的总工作时长 = duration_minutes
        -- 当班总良品数 = total_production_non_defective_quantity
        -- 当班平均达标率（数量） = rth_rate
        -- 标准速度 = equipment_parameter_value
        -- 当班设备平均速度（AH） = avg_speed
        -- 速度达标率
        -- 超时笔数 = over_diff_count
        -- 计划达成率 = plan_rates
        -- 缺数笔数 = lack_diff_count
        -- 缺数率 = lack_rates

        SELECT t3.team_name,t2.* , Round((t2.avg_speed::decimal / NULLIF(CAST(t2.equipment_parameter_value AS decimal), 0)), 2) AS speed_ratio FROM (
        SELECT *,
        COALESCE(Round(total_production_non_defective_quantity / ((sub_act_duration_minutes - total_device_time)/60),2),0) as avg_speed,
        ROUND((less_diff_count::decimal / NULLIF(total_tasks_count,0)),2) as plan_rates ,
        ROUND((lack_diff_count::decimal / NULLIF(total_tasks_count,0)),2) as lack_rates
        FROM (
        WITH ranked_records AS (
        SELECT
        t44.team_guid as team_guid,
        eemepv.equipment_parameter_value,
        eeme.equipment_name,
        ebmor.create_date AS actualStartTime,
        endebmor.create_date AS actualEndTime,
        ebmorjc.production_non_defective_quantity,
        COALESCE(t5.switching_time,0) as switching_time,
        COALESCE(t6.plate_setting_time,0) as plate_setting_time,
        COALESCE(t7.pause_time,0) as pause_time,
        ROUND((EXTRACT(EPOCH FROM (endebmor.create_date - ebmor.create_date)) / 60),2) AS act_duration_minutes,
        ROUND((EXTRACT(EPOCH FROM (tastk.planned_end_time - tastk.planned_start_time)) / 60),2) AS plan_duration_minutes,
        tastk.*,
        ROW_NUMBER() OVER (PARTITION BY t4.team_guid, eeme.equipment_guid ORDER BY ebmor.create_date ASC) AS rn_asc,
        ROW_NUMBER() OVER (PARTITION BY t4.team_guid, eeme.equipment_guid ORDER BY endebmor.create_date DESC) AS rn_desc
        FROM erp_basic_mgt_tasks tastk
        LEFT JOIN erp_equipment_mgt_equipment_parameter_value eemepv ON eemepv.equipment_guid = tastk.equipment_guid
        LEFT JOIN erp_equipment_mgt_equipment_parameter_items eemepi ON eemepi.equipment_parameter_items_guid = eemepv.equipment_parameter_items_guid

        LEFT JOIN erp_basic_mgt_daily_report_detail ebmdr ON ebmdr.tasks_guid = tastk.tasks_guid
        LEFT JOIN (
        SELECT daily_report_detail_guid,max(team_guid) as team_guid FROM erp_basic_mgt_daily_report_members WHERE (team_guid is not null or team_guid != '')   GROUP BY daily_report_detail_guid
        ) t44 ON t44.daily_report_detail_guid = ebmdr.daily_report_detail_guid
        LEFT JOIN (
        SELECT
        max(create_date) create_date,
        tasks_guid
        FROM erp_basic_mgt_operation_records
        WHERE operation_type_guid = '1'
        GROUP BY tasks_guid
        ) ebmor ON ebmor.tasks_guid = tastk.tasks_guid
        LEFT JOIN (
        SELECT
        max(create_date) create_date,
        tasks_guid
        FROM erp_basic_mgt_operation_records
        WHERE operation_type_guid = '2'
        GROUP BY tasks_guid
        ) endebmor ON endebmor.tasks_guid = tastk.tasks_guid
        LEFT JOIN erp_equipment_mgt_equipment eeme ON eeme.equipment_guid = tastk.equipment_guid
        LEFT JOIN mes_production_plan_record t4 ON t4.tasks_guid = tastk.tasks_guid
        LEFT JOIN (
        SELECT
        sum(epmpor.production_non_defective_quantity) production_non_defective_quantity,
        ebmor.tasks_guid,
        sum(epmpor.non_defective_quantity) non_defective_quantity
        FROM erp_basic_mgt_operation_records ebmor
        LEFT JOIN erp_production_mgt_production_operation_records epmpor ON epmpor.operation_records_guid = ebmor.operation_records_guid
        WHERE operation_type_guid = '10'
        GROUP BY ebmor.tasks_guid
        ) ebmorjc ON ebmorjc.tasks_guid = tastk.tasks_guid
        LEFT JOIN (
        WITH operation_times AS (
        SELECT
        t1.tasks_guid,
        t1.create_date,
        t2.operation_type_value,
        ROW_NUMBER() OVER (PARTITION BY t1.tasks_guid, t2.operation_type_value ORDER BY t1.create_date DESC) AS rn
        FROM
        erp_basic_mgt_operation_records t1
        LEFT JOIN erp_basic_mgt_operation_type t2 ON t1.operation_type_guid = t2.operation_type_value
        WHERE
        t2.operation_type_value IN ('5', '6')
        ),
        min_max_times AS (
        SELECT
        tasks_guid,
        MAX(CASE WHEN operation_type_value = '5' THEN create_date END) AS max_start_time,
        MIN(CASE WHEN operation_type_value = '6' THEN create_date END) AS min_end_time
        FROM
        operation_times
        WHERE
        rn = 1
        GROUP BY
        tasks_guid
        )
        SELECT
        tasks_guid,
        max_start_time,
        min_end_time,
        TRUNC(COALESCE(EXTRACT(EPOCH FROM (min_end_time - max_start_time)), 0) / 60) AS switching_time
        FROM
        min_max_times
        ) t5 ON tastk.tasks_guid = t5.tasks_guid
        LEFT JOIN (
        WITH operation_times AS (
        SELECT
        t1.tasks_guid,
        t1.create_date,
        t2.operation_type_value,
        ROW_NUMBER() OVER (PARTITION BY t1.tasks_guid, t2.operation_type_value ORDER BY t1.create_date DESC) AS rn
        FROM
        erp_basic_mgt_operation_records t1
        LEFT JOIN erp_basic_mgt_operation_type t2 ON t1.operation_type_guid = t2.operation_type_value
        WHERE
        t2.operation_type_value IN ('3', '4')
        ),
        min_max_times AS (
        SELECT
        tasks_guid,
        MAX(CASE WHEN operation_type_value = '3' THEN create_date END) AS max_start_time,
        MIN(CASE WHEN operation_type_value = '4' THEN create_date END) AS min_end_time
        FROM
        operation_times
        WHERE
        rn = 1
        GROUP BY
        tasks_guid
        )
        SELECT
        tasks_guid,
        TRUNC(COALESCE(EXTRACT(EPOCH FROM (min_end_time - max_start_time)), 0) / 60) AS plate_setting_time
        FROM
        min_max_times
        ) t6 ON tastk.tasks_guid = t6.tasks_guid
        LEFT JOIN (
        WITH operation_times AS (
        SELECT
        t1.tasks_guid,
        t1.create_date,
        t2.operation_type_value,
        ROW_NUMBER() OVER (PARTITION BY t1.tasks_guid, t2.operation_type_value ORDER BY t1.create_date DESC) AS rn
        FROM
        erp_basic_mgt_operation_records t1
        LEFT JOIN erp_basic_mgt_operation_type t2 ON t1.operation_type_guid = t2.operation_type_value
        WHERE
        t2.operation_type_value IN ('7', '8')
        ),
        min_max_times AS (
        SELECT
        tasks_guid,
        MAX(CASE WHEN operation_type_value = '7' THEN create_date END) AS max_start_time,
        MIN(CASE WHEN operation_type_value = '8' THEN create_date END) AS min_end_time
        FROM
        operation_times
        WHERE
        rn = 1
        GROUP BY
        tasks_guid
        )
        SELECT
        tasks_guid,
        TRUNC(COALESCE(EXTRACT(EPOCH FROM (min_end_time - max_start_time)), 0) / 60) AS pause_time
        FROM
        min_max_times
        ) t7 ON tastk.tasks_guid = t7.tasks_guid
        WHERE
        1 = 1
        AND (
        DATE_TRUNC( 'day',  ebmor.create_date ) = #{model.filterDate} AND EXTRACT ( HOUR FROM  ebmor.create_date ) BETWEEN 8 AND 23
        OR DATE_TRUNC( 'day',  ebmor.create_date ) = #{model.filterDate} + INTERVAL '1 day' AND EXTRACT ( HOUR FROM  ebmor.create_date ) BETWEEN 0
        AND 7
        )
        ),
        first_last_records AS (
        SELECT
        team_guid,
        equipment_parameter_value,
        equipment_guid,
        equipment_name,
        SUM(planned_quantity) as total_planned_quantity,
        SUM(production_non_defective_quantity) as total_production_non_defective_quantity,
        SUM(switching_time) as total_switching_time,
        SUM(plate_setting_time) as total_plate_setting_time,
        SUM(pause_time) as total_pause_time,
        count(tasks_guid) as total_tasks_count,
        MAX(CASE WHEN rn_asc = 1 THEN actualStartTime END) AS first_actualStartTime,
        MAX(CASE WHEN rn_desc = 1 THEN actualEndTime END) AS last_actualEndTime,
        SUM(CASE WHEN act_duration_minutes - plan_duration_minutes <![CDATA[>]]> 30 THEN 1 ELSE 0 END) AS over_diff_count, -- 超时任务数新增统计
        SUM(CASE WHEN act_duration_minutes - plan_duration_minutes <![CDATA[<=]]> 30 THEN 1 ELSE 0 END) AS less_diff_count, -- 按时完成任务数新增统计
        SUM(CASE WHEN production_non_defective_quantity - planned_quantity <![CDATA[<]]> 0 THEN 1 ELSE 0 END) AS lack_diff_count, -- 缺数统计
        SUM(act_duration_minutes) as act_duration_minutes
        FROM ranked_records
        GROUP BY team_guid, equipment_guid, equipment_name,equipment_parameter_value
        )
        SELECT
        team_guid,
        equipment_parameter_value,
        equipment_guid,
        equipment_name,
        first_actualStartTime,
        last_actualEndTime,
        total_planned_quantity,
        total_production_non_defective_quantity,
        total_switching_time,
        total_plate_setting_time,
        total_pause_time,
        total_tasks_count,
        ROUND((total_production_non_defective_quantity / total_planned_quantity), 2) as rth_rate,
        ROUND((EXTRACT(EPOCH FROM (last_actualEndTime - first_actualStartTime)) / 60), 2) AS duration_minutes,
        Round((total_switching_time + total_plate_setting_time + total_pause_time), 2) as total_device_time,
        ROUND((act_duration_minutes/60),2) as act_duration_minutes,
        act_duration_minutes AS sub_act_duration_minutes ,
        over_diff_count,
        less_diff_count,
        lack_diff_count
        FROM first_last_records
        ORDER BY first_actualStartTime ASC
        ) t1
        ) t2
        LEFT JOIN erp_administration_mgt_team t3 ON t3.team_guid = t2.team_guid
        where 1=1
        <if test="model.equipmentName != null and model.equipmentName != '' ">
            and t2.equipment_name LIKE CONCAT('%',#{model.equipmentName},'%')

        </if>
        <if test="model.teamName != null and model.teamName != '' ">
            and t3.team_name LIKE CONCAT('%',#{model.teamName},'%')

        </if>

    </select>

    <select id="findProductionBackPerformanceReport" resultMap="ResultMap">

        -- 交出良品数 = production_non_defective_quantity
        -- 当班的总工作时长 = duration_minutes
        -- 当班总良品数 = total_production_non_defective_quantity
        -- 当班平均达标率（数量） = rth_rate
        -- 标准速度 = equipment_parameter_value
        -- 当班设备平均速度（AH） = avg_speed
        -- 速度达标率	speed_ratio
        -- 超时笔数 = over_diff_count
        -- 计划达成率 = plan_rates
        -- 缺数笔数 = lack_diff_count
        -- 缺数率 = lack_rates

        SELECT
        t3.team_name,
        t2.* ,
        Round( ( t2.avg_speed :: DECIMAL / NULLIF ( CAST ( t2.equipment_parameter_value AS DECIMAL ), 0 ) ), 2 ) AS speed_ratio
        FROM
        (
        SELECT
        *,
        COALESCE ( Round( total_production_non_defective_quantity / ( ( sub_act_duration_minutes - total_device_time ) / 60 ), 2 ), 0 ) AS avg_speed,

        ROUND( ( less_diff_count :: DECIMAL / NULLIF ( total_tasks_count, 0 ) ), 2 ) AS plan_rates,
        ROUND( ( lack_diff_count :: DECIMAL / NULLIF ( total_tasks_count, 0 ) ), 2 ) AS lack_rates
        FROM
        (
        WITH ranked_records AS (
        SELECT
        t44.team_guid,
        eemepv.equipment_parameter_value,
        eeme.equipment_name,
        ebmor.create_date AS actualStartTime,
        endebmor.create_date AS actualEndTime,
        ebmorjc.production_non_defective_quantity,
        COALESCE ( t5.switching_time, 0 ) AS switching_time,
        COALESCE ( t6.plate_setting_time, 0 ) AS plate_setting_time,
        COALESCE ( t7.pause_time, 0 ) AS pause_time,
        ROUND( ( EXTRACT ( EPOCH FROM ( endebmor.create_date - ebmor.create_date ) ) / 60 ), 2 ) AS act_duration_minutes,
        ROUND( ( EXTRACT ( EPOCH FROM ( tastk.planned_end_time - tastk.planned_start_time ) ) / 60 ), 2 ) AS plan_duration_minutes,
        tastk.*,
        ROW_NUMBER ( ) OVER ( PARTITION BY t4.team_guid, eeme.equipment_guid ORDER BY ebmor.create_date ASC ) AS rn_asc,
        ROW_NUMBER ( ) OVER ( PARTITION BY t4.team_guid, eeme.equipment_guid ORDER BY endebmor.create_date DESC ) AS rn_desc
        FROM
        erp_basic_mgt_tasks tastk
        LEFT JOIN erp_equipment_mgt_equipment_parameter_value eemepv ON eemepv.equipment_guid = tastk.equipment_guid
        LEFT JOIN erp_equipment_mgt_equipment_parameter_items eemepi ON eemepi.equipment_parameter_items_guid = eemepv.equipment_parameter_items_guid
        LEFT JOIN erp_basic_mgt_daily_report_detail ebmdr ON ebmdr.tasks_guid = tastk.tasks_guid
        LEFT JOIN ( SELECT daily_report_detail_guid, MAX ( team_guid ) AS team_guid FROM erp_basic_mgt_daily_report_members WHERE ( team_guid IS NOT NULL OR team_guid != '' ) GROUP BY daily_report_detail_guid ) t44 ON t44.daily_report_detail_guid = ebmdr.daily_report_detail_guid
        LEFT JOIN ( SELECT MAX ( create_date ) create_date, tasks_guid FROM erp_basic_mgt_operation_records WHERE operation_type_guid = '1' GROUP BY tasks_guid ) ebmor ON ebmor.tasks_guid = tastk.tasks_guid
        LEFT JOIN ( SELECT MAX ( create_date ) create_date, tasks_guid FROM erp_basic_mgt_operation_records WHERE operation_type_guid = '2' GROUP BY tasks_guid ) endebmor ON endebmor.tasks_guid = tastk.tasks_guid
        LEFT JOIN erp_equipment_mgt_equipment eeme ON eeme.equipment_guid = tastk.equipment_guid
        LEFT JOIN mes_production_plan_record t4 ON t4.tasks_guid = tastk.tasks_guid
        LEFT JOIN (
        SELECT SUM
        ( epmpor.production_non_defective_quantity ) production_non_defective_quantity,
        ebmor.tasks_guid,
        SUM ( epmpor.non_defective_quantity ) non_defective_quantity
        FROM
        erp_basic_mgt_operation_records ebmor
        LEFT JOIN erp_production_mgt_production_operation_records epmpor ON epmpor.operation_records_guid = ebmor.operation_records_guid
        WHERE
        operation_type_guid = '10'
        GROUP BY
        ebmor.tasks_guid
        ) ebmorjc ON ebmorjc.tasks_guid = tastk.tasks_guid
        LEFT JOIN (
        WITH operation_times AS (
        SELECT
        t1.tasks_guid,
        t1.create_date,
        t2.operation_type_value,
        ROW_NUMBER ( ) OVER ( PARTITION BY t1.tasks_guid, t2.operation_type_value ORDER BY t1.create_date DESC ) AS rn
        FROM
        erp_basic_mgt_operation_records t1
        LEFT JOIN erp_basic_mgt_operation_type t2 ON t1.operation_type_guid = t2.operation_type_value
        WHERE
        t2.operation_type_value IN ( '5', '6' )
        ),
        min_max_times AS (
        SELECT
        tasks_guid,
        MAX ( CASE WHEN operation_type_value = '5' THEN create_date END ) AS max_start_time,
        MIN ( CASE WHEN operation_type_value = '6' THEN create_date END ) AS min_end_time
        FROM
        operation_times
        WHERE
        rn = 1
        GROUP BY
        tasks_guid
        ) SELECT
        tasks_guid,
        max_start_time,
        min_end_time,
        TRUNC( COALESCE ( EXTRACT ( EPOCH FROM ( min_end_time - max_start_time ) ), 0 ) / 60 ) AS switching_time
        FROM
        min_max_times
        ) t5 ON tastk.tasks_guid = t5.tasks_guid
        LEFT JOIN (
        WITH operation_times AS (
        SELECT
        t1.tasks_guid,
        t1.create_date,
        t2.operation_type_value,
        ROW_NUMBER ( ) OVER ( PARTITION BY t1.tasks_guid, t2.operation_type_value ORDER BY t1.create_date DESC ) AS rn
        FROM
        erp_basic_mgt_operation_records t1
        LEFT JOIN erp_basic_mgt_operation_type t2 ON t1.operation_type_guid = t2.operation_type_value
        WHERE
        t2.operation_type_value IN ( '3', '4' )
        ),
        min_max_times AS (
        SELECT
        tasks_guid,
        MAX ( CASE WHEN operation_type_value = '3' THEN create_date END ) AS max_start_time,
        MIN ( CASE WHEN operation_type_value = '4' THEN create_date END ) AS min_end_time
        FROM
        operation_times
        WHERE
        rn = 1
        GROUP BY
        tasks_guid
        ) SELECT
        tasks_guid,
        TRUNC( COALESCE ( EXTRACT ( EPOCH FROM ( min_end_time - max_start_time ) ), 0 ) / 60 ) AS plate_setting_time
        FROM
        min_max_times
        ) t6 ON tastk.tasks_guid = t6.tasks_guid
        LEFT JOIN (
        WITH operation_times AS (
        SELECT
        t1.tasks_guid,
        t1.create_date,
        t2.operation_type_value,
        ROW_NUMBER ( ) OVER ( PARTITION BY t1.tasks_guid, t2.operation_type_value ORDER BY t1.create_date DESC ) AS rn
        FROM
        erp_basic_mgt_operation_records t1
        LEFT JOIN erp_basic_mgt_operation_type t2 ON t1.operation_type_guid = t2.operation_type_value
        WHERE
        t2.operation_type_value IN ( '7', '8' )
        ),
        min_max_times AS (
        SELECT
        tasks_guid,
        MAX ( CASE WHEN operation_type_value = '7' THEN create_date END ) AS max_start_time,
        MIN ( CASE WHEN operation_type_value = '8' THEN create_date END ) AS min_end_time
        FROM
        operation_times
        WHERE
        rn = 1
        GROUP BY
        tasks_guid
        ) SELECT
        tasks_guid,
        TRUNC( COALESCE ( EXTRACT ( EPOCH FROM ( min_end_time - max_start_time ) ), 0 ) / 60 ) AS pause_time
        FROM
        min_max_times
        ) t7 ON tastk.tasks_guid = t7.tasks_guid
        WHERE
        1 = 1
        AND (
        (DATE_TRUNC('day', ebmor.create_date) = #{model.startFilterDate}::timestamp AND EXTRACT(HOUR FROM ebmor.create_date) BETWEEN 8 AND 23)
        OR (DATE_TRUNC('day', ebmor.create_date) BETWEEN #{model.startFilterDate}::timestamp + INTERVAL '1 day'  AND #{model.endFilterDate}::timestamp)
        OR (DATE_TRUNC('day', ebmor.create_date) = #{model.endFilterDate}::timestamp + INTERVAL '1 day'  AND EXTRACT(HOUR FROM ebmor.create_date) BETWEEN 0 AND 7)
        )


        ),
        first_last_records AS (
        SELECT
        team_guid,
        equipment_parameter_value,
        equipment_guid,
        equipment_name,
        SUM ( planned_quantity ) AS total_planned_quantity,
        SUM ( production_non_defective_quantity ) AS total_production_non_defective_quantity,
        SUM ( switching_time ) AS total_switching_time,
        SUM ( plate_setting_time ) AS total_plate_setting_time,
        SUM ( pause_time ) AS total_pause_time,
        COUNT ( tasks_guid ) AS total_tasks_count,
        MAX ( CASE WHEN rn_asc = 1 THEN actualStartTime END ) AS first_actualStartTime,
        MAX ( CASE WHEN rn_desc = 1 THEN actualEndTime END ) AS last_actualEndTime,
        SUM(CASE WHEN act_duration_minutes - plan_duration_minutes <![CDATA[>]]> 30 THEN 1 ELSE 0 END) AS over_diff_count, -- 超时任务数新增统计
        SUM(CASE WHEN act_duration_minutes - plan_duration_minutes <![CDATA[<=]]> 30 THEN 1 ELSE 0 END) AS less_diff_count, -- 按时完成任务数新增统计
        SUM(CASE WHEN production_non_defective_quantity - planned_quantity <![CDATA[<]]>  0 THEN 1 ELSE 0 END) AS lack_diff_count, -- 缺数统计
        SUM ( plan_duration_minutes ) AS plan_duration_minutes,
        SUM ( act_duration_minutes ) AS act_duration_minutes
        FROM
        ranked_records
        GROUP BY
        team_guid,
        equipment_guid,
        equipment_name,
        equipment_parameter_value
        ) SELECT
        team_guid,
        equipment_parameter_value,
        equipment_guid,
        equipment_name,
        first_actualStartTime,
        last_actualEndTime,
        total_planned_quantity,
        total_production_non_defective_quantity,
        total_switching_time,
        total_plate_setting_time,
        total_pause_time,
        total_tasks_count,
        ROUND( ( total_production_non_defective_quantity / total_planned_quantity ), 2 ) AS rth_rate,
        ROUND( ( EXTRACT ( EPOCH FROM ( last_actualEndTime - first_actualStartTime ) ) / 60 ), 2 ) AS duration_minutes,
        Round( ( total_switching_time + total_plate_setting_time + total_pause_time ), 2 ) AS total_device_time,
        over_diff_count,
        less_diff_count,
        lack_diff_count,
        ROUND(ABS(plan_duration_minutes / 60), 2) AS plan_duration_minutes,
        ROUND( ( act_duration_minutes / 60 ), 2 ) AS act_duration_minutes ,
        act_duration_minutes as sub_act_duration_minutes
        FROM
        first_last_records
        ORDER BY
        first_actualStartTime ASC
        ) t1
        ) t2
        LEFT JOIN erp_administration_mgt_team t3 ON t3.team_guid = t2.team_guid
        WHERE
        1 =1
        <if test="model.equipmentName != null and model.equipmentName != '' ">
            and t2.equipment_name LIKE CONCAT('%',#{model.equipmentName},'%')

        </if>
        <if test="model.teamName != null and model.teamName != '' ">
            and t3.team_name LIKE CONCAT('%',#{model.teamName},'%')

        </if>


    </select>

    <select id="findReportDataForTimeToFsyj" resultType="java.util.Map">
        SELECT
        staff_names.staff_short_names as  group_members,
        mor.tj_total_minutes,
        mor.tj_counts,
        mor1.zt_total_minutes,
        mor1.zt_counts,
        COALESCE ( t11.non_defective_quantity, 0 ) AS non_defective_quantity,
--         COALESCE( t12.bad_count, 0 ) AS bad_count,
        t1.tasks_guid,
        t1.shift_id,
        t1.shift_name,
        COALESCE( t0.equipment_code, '' ) AS equipment_code,
        COALESCE ( t0.box_name, '' ) AS equipment_name,
        COALESCE ( t1.team_name, '' ) AS team_name,
        COALESCE ( t1.team_code, '' ) AS team_code,
        COALESCE ( t1.staff_short_name, '' ) AS staff_short_name,
        COALESCE ( t1.workorder_number, '' ) AS workorder_number,
        COALESCE ( t1.customer_short_name, '' ) AS customer_short_name,
        COALESCE ( t1.material_name, '' ) AS material_name,
        COALESCE ( t1.production_processes_type_name, '' ) AS production_processes_type_name,
        t1.start_time,
        t1.end_time,
        COALESCE ( t1.plan_count, 0 ) AS plan_count,
        COALESCE ( t1.quantity, 0 ) AS quantity,
        COALESCE ( t1.completed_quantity, 0 ) AS completed_quantity,
        t1.good_count,
        t1.bad_count,
        t1.switching_time,
        t1.plate_setting_time,
        t1.pause_time,
        t1.ranked_data,
        COALESCE ( task_info.upper_procedure_quantity, 0 ) AS upper_procedure_quantity,
        task_info.*
        FROM
        mes_pub_sub_info t0
        LEFT JOIN (
        WITH ranked_data AS (
        SELECT
        t2.filed_name,
        t2.task_guid,
        t2.filed_value,
        t2.create_date,
        ROW_NUMBER ( ) OVER ( PARTITION BY t2.filed_name, t2.task_guid ORDER BY t2.create_date DESC ) AS rn
        FROM
        mes_production_plan_record t1
        LEFT JOIN energy_items_data_hour_record t2 ON t1.tasks_guid = t2.task_guid
        WHERE t2.create_date IS NOT NULL and t1.deleted = false
        ),
        merged_data AS (
        SELECT
        task_guid AS tasks_guid,
        STRING_AGG ( filed_name || ': ' || filed_value, ' | ' ) AS merged_data,
        MAX ( create_date ) AS max_create_date
        FROM
        ranked_data
        WHERE
        rn = 1
        GROUP BY
        task_guid
        )
        SELECT
        t1.tasks_guid,
        t1.shift_id,
        t1.shift_name,
        t1.team_guid,
        COALESCE( t2.team_code, '' ) AS team_code,
        COALESCE( t2.team_name, '' ) AS team_name,
        COALESCE ( t4.staff_short_name, '' ) AS staff_short_name,
        COALESCE ( t5.equipment_code, '' ) AS equipment_code,
        COALESCE ( t5.equipment_name, '' ) AS equipment_name,
        COALESCE ( t6.workorder_number, '' ) AS workorder_number,
        COALESCE ( t8.customer_short_name, '' ) AS customer_short_name,
        COALESCE ( t9.material_name, '' ) AS material_name,
        COALESCE ( t10.production_processes_type_name, '' ) AS production_processes_type_name,
        t1.start_time,
        t1.end_time,
        COALESCE ( t1.plan_count, 0 ) AS plan_count,
        COALESCE ( t11.quantity, 0 ) AS quantity,
        COALESCE ( t1.completed_quantity, 0 ) AS completed_quantity,
        COALESCE ( t1.good_count, 0 ) AS good_count,
        COALESCE ( t1.bad_count, 0 ) AS bad_count,
        t15.switching_time,
        t16.plate_setting_time,
        t17.pause_time,
        md.merged_data AS ranked_data
        FROM
        mes_production_plan_record t1
        LEFT JOIN erp_administration_mgt_team t2 ON t1.team_guid = t2.team_guid
        LEFT JOIN erp_administration_mgt_team_members t3 ON t2.team_guid = t3.team_guid AND t3.is_group =TRUE
        LEFT JOIN erp_administration_mgt_staff t4 ON t3.staff_guid = t4.staff_guid
        LEFT JOIN erp_equipment_mgt_equipment t5 ON t1.equipment_guid = t5.equipment_guid
        LEFT JOIN erp_basic_mgt_tasks t6 ON t1.tasks_guid = t6.tasks_guid
        LEFT JOIN erp_production_mgt_workorder t7 ON t6.workorder_number = t7.workorder_number
        AND t7.deleted = FALSE
        AND t7.workorder_properties = 15
        AND t7.source_guid IS NOT NULL
        AND t7.current_status != '3'
        LEFT JOIN erp_customer_mgt_customer t8 ON t7.customer_guid = t8.customer_guid
        LEFT JOIN erp_material_mgt_material t9 ON t7.material_guid = t9.material_guid
        LEFT JOIN erp_basic_mgt_production_processes_type t10 ON t6.production_processes_type_guid =
        t10.production_processes_type_guid
        LEFT JOIN (
        SELECT SUM
        ( ebmod.quantity ) AS quantity,
        epmw.workorder_number
        FROM
        erp_production_mgt_workorder epmw
        LEFT JOIN erp_production_mgt_workorder epmw2 ON epmw.source_guid = epmw2.workorder_guid
        LEFT JOIN erp_business_mgt_order_data ebmod ON epmw2.source_guid = ebmod.order_data_guid
        WHERE
        epmw.workorder_properties = 15
        AND epmw.source_guid IS NOT NULL
        AND epmw.current_status != '3'
        GROUP BY
        epmw.workorder_number
        ) t11 ON t11.workorder_number = t7.workorder_number
        LEFT JOIN merged_data md ON md.tasks_guid = t1.tasks_guid
        LEFT JOIN (
        WITH operation_times AS (
        SELECT
        t1.tasks_guid,
        t1.create_date,
        t2.operation_type_value,
        ROW_NUMBER ( ) OVER ( PARTITION BY t1.tasks_guid, t2.operation_type_value ORDER BY t1.create_date DESC ) AS rn
        FROM
        erp_basic_mgt_operation_records t1
        LEFT JOIN erp_basic_mgt_operation_type t2 ON t1.operation_type_guid = t2.operation_type_value
        WHERE
        t2.operation_type_value IN ( '5', '6' )
        ),
        min_max_times AS (
        SELECT
        tasks_guid,
        MAX ( CASE WHEN operation_type_value = '5' THEN create_date END ) AS max_start_time,
        MIN ( CASE WHEN operation_type_value = '6' THEN create_date END ) AS min_end_time
        FROM
        operation_times
        WHERE
        rn = 1
        GROUP BY
        tasks_guid
        ) SELECT
        tasks_guid,
        TRUNC( COALESCE ( EXTRACT ( EPOCH FROM ( min_end_time - max_start_time ) ), 0 ) ) AS switching_time
        FROM
        min_max_times
        ) t15 ON t1.tasks_guid = t15.tasks_guid
        LEFT JOIN (
        WITH operation_times AS (
        SELECT
        t1.tasks_guid,
        t1.create_date,
        t2.operation_type_value,
        ROW_NUMBER ( ) OVER ( PARTITION BY t1.tasks_guid, t2.operation_type_value ORDER BY t1.create_date DESC ) AS rn
        FROM
        erp_basic_mgt_operation_records t1
        LEFT JOIN erp_basic_mgt_operation_type t2 ON t1.operation_type_guid = t2.operation_type_value
        WHERE
        t2.operation_type_value IN ( '3', '4' )
        ),
        min_max_times AS (
        SELECT
        tasks_guid,
        MAX ( CASE WHEN operation_type_value = '3' THEN create_date END ) AS max_start_time,
        MIN ( CASE WHEN operation_type_value = '4' THEN create_date END ) AS min_end_time
        FROM
        operation_times
        WHERE
        rn = 1
        GROUP BY
        tasks_guid
        ) SELECT
        tasks_guid,
        TRUNC( COALESCE ( EXTRACT ( EPOCH FROM ( min_end_time - max_start_time ) ), 0 ) ) AS plate_setting_time
        FROM
        min_max_times
        ) t16 ON t1.tasks_guid = t16.tasks_guid
        LEFT JOIN (
        WITH operation_times AS (
        SELECT
        t1.tasks_guid,
        t1.create_date,
        t2.operation_type_value,
        ROW_NUMBER ( ) OVER ( PARTITION BY t1.tasks_guid, t2.operation_type_value ORDER BY t1.create_date DESC ) AS rn
        FROM
        erp_basic_mgt_operation_records t1
        LEFT JOIN erp_basic_mgt_operation_type t2 ON t1.operation_type_guid = t2.operation_type_value
        WHERE
        t2.operation_type_value IN ( '7', '8' )
        ),
        min_max_times AS (
        SELECT
        tasks_guid,
        MAX ( CASE WHEN operation_type_value = '7' THEN create_date END ) AS max_start_time,
        MIN ( CASE WHEN operation_type_value = '8' THEN create_date END ) AS min_end_time
        FROM
        operation_times
        WHERE
        rn = 1
        GROUP BY
        tasks_guid
        ) SELECT
        tasks_guid,
        TRUNC( COALESCE ( EXTRACT ( EPOCH FROM ( min_end_time - max_start_time ) ), 0 ) ) AS pause_time
        FROM
        min_max_times
        ) t17 ON t1.tasks_guid = t17.tasks_guid
        WHERE
        1 = 1
        <if test="model.startTime != null and model.startTime != '' ">
            and t1.start_time <![CDATA[>=]]> to_timestamp(#{model.startTime}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="model.endTime != null and model.endTime != '' ">
            and t1.start_time <![CDATA[<=]]> to_timestamp(#{model.endTime}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
        AND t1.deleted = FALSE
        ) t1 ON t0.equipment_code = t1.equipment_code

        left join (
        WITH RankedTasks AS (
        SELECT
        tastk.tasks_guid,
        ebms.plan_number AS planNumber,
        tastk.planned_start_time AS plannedStartTime,
        tastk.planned_end_time AS plannedEndTime,
        ebmod.external_tracking_number AS externalTrackingNumber,
        ebmod.external_material_code AS externalMaterialCode,
        product_classfication.material_classification_name AS boxPileName,
        epmepp2.experience_production_process_name AS fullPaperName,
        ebmcpur.corrugated_paper_name AS corrugatedPaperUsageRateName,
        get_material_specification_type ( product.material_guid ) AS material_specification,
        product_classfication.search_code AS searchCode,
        ebmppt.production_processes_type_name AS productionProcessesTypeName,
        epmeppe3.color_description AS colorDescription,
        outWorkorder.proportion AS aperture,
        -- 长
        (
        SELECT
        spec_value.specification_value
        FROM
        erp_material_mgt_specification_value spec_value
        LEFT JOIN erp_material_mgt_specification_type spec_type ON spec_type.specification_type_guid =
        spec_value.specification_type_guid
        WHERE
        spec_type.specification_type_guid = 'e4db46c52e0e319acbc8f50510ef0321'
        AND spec_value.material_guid = outWorkorder.material_guid
        ) AS openLength,
        -- 宽
        (
        SELECT
        spec_value.specification_value
        FROM
        erp_material_mgt_specification_value spec_value
        LEFT JOIN erp_material_mgt_specification_type spec_type ON spec_type.specification_type_guid =
        spec_value.specification_type_guid
        WHERE
        spec_type.specification_type_guid = 'a1a8e4972eba7fb28f7d8b9cc406a109'
        AND spec_value.material_guid = outWorkorder.material_guid
        ) AS openWidth,
        tastk.planned_quantity AS plannedQuantity,
        tastk.tasks_state_guid AS tasksStateGuid,
        epmeppe3.board_area AS boardArea,
        epmeppe3.carton_area AS cartonArea,
        eamtm.team_name AS team_name,
        ebmod.quantity AS quantity,
        tastk.planned_output_quantity AS plannedOutputQuantity,
        ebmdrd.description,
        ebmdrd.creator,
        ebmdrd.create_date AS createDate,
        ebmdrd.last_updater AS lastUpdater,
        ebmdrd.last_update_date AS lastUpdateDate,
        workorder.workorder_number AS productionNumber,
        ecmc.customer_short_name AS customerShortName,
        product.material_code AS productCode,
        in_inventoty.quantity AS inInventotyQuantity,
        COALESCE ( t13.quantity, 0 ) AS upper_procedure_quantity,
        ebmo.order_number,
        ROW_NUMBER ( ) OVER ( PARTITION BY tastk.tasks_guid ORDER BY tastk.planned_start_time DESC ) AS rn
        FROM
        erp_basic_mgt_tasks AS tastk
        LEFT JOIN erp_basic_mgt_schedule ebms ON ebms.schedule_guid = tastk.schedule_guid
        LEFT JOIN (
        SELECT SUM
        ( eimied.quantity ) AS quantity,
        ebmor.tasks_guid
        FROM
        erp_basic_mgt_operation_records ebmor
        LEFT JOIN erp_inventory_mgt_inventory_receipt_detail eimied ON eimied.source_guid = ebmor.operation_guid
        GROUP BY
        ebmor.tasks_guid
        ) in_inventoty ON in_inventoty.tasks_guid = tastk.tasks_guid
        LEFT JOIN erp_administration_mgt_team eamtm ON eamtm.team_guid = ebms.team_guid
        LEFT JOIN erp_basic_mgt_daily_report_detail ebmdrd ON ebmdrd.tasks_guid = tastk.tasks_guid
        LEFT JOIN erp_production_mgt_workorder AS workorder ON workorder.workorder_guid = tastk.source_guid
        LEFT JOIN erp_production_mgt_workorder AS outWorkorder ON outWorkorder.workorder_guid =
        workorder.parent_classification_guid
        LEFT JOIN erp_material_mgt_material AS product ON product.material_guid = workorder.product_material_guid
        LEFT JOIN erp_material_mgt_material_classification AS product_classfication ON
        product_classfication.material_classification_guid = product.material_classification_guid
        LEFT JOIN erp_production_mgt_workorder psepmw ON workorder.workorder_number = psepmw.workorder_number
        LEFT JOIN erp_production_mgt_workorder epmw ON workorder.workorder_number = epmw.workorder_number
        LEFT JOIN erp_production_mgt_workorder fepmw ON psepmw.source_guid = fepmw.workorder_guid
        LEFT JOIN erp_business_mgt_order_data ebmod ON fepmw.source_guid = ebmod.order_data_guid
        LEFT JOIN erp_business_mgt_order ebmo ON ebmod.order_guid = ebmo.order_guid
        LEFT JOIN erp_production_mgt_workorder epmw2 ON epmw2.workorder_number = workorder.workorder_number
        LEFT JOIN erp_production_mgt_workorder epmw3 ON epmw2.source_guid = epmw3.workorder_guid
        LEFT JOIN erp_production_mgt_experience_production_process epmepp ON epmepp.experience_production_process_guid =
        epmw3.experience_production_process_guid
        LEFT JOIN erp_production_mgt_experience_production_process_external epmeppe ON
        epmeppe.experience_production_process_guid = epmepp.experience_production_process_guid
        LEFT JOIN erp_production_mgt_experience_production_process_extend epmeppe3 ON
        epmeppe3.experience_production_process_guid = epmepp.experience_production_process_guid
        LEFT JOIN erp_production_mgt_experience_production_process epmepp2 ON epmeppe3.paper_guid =
        epmepp2.experience_production_process_guid
        LEFT JOIN erp_basic_mgt_corrugated_paper_usage_rate ebmcpur ON ebmcpur.corrugated_paper_usage_rate_guid =
        epmeppe3.corrugated_paper_usage_rate_guid
        LEFT JOIN erp_basic_mgt_production_processes_type ebmppt ON workorder.production_processes_type_guid =
        ebmppt.production_processes_type_guid
        LEFT JOIN erp_customer_mgt_customer ecmc ON workorder.customer_guid = ecmc.customer_guid
        LEFT JOIN (
        SELECT
        eomws.thisWorkorderGuid,
        eimirds.quantity
        FROM
        (
        SELECT
        epmwbgx.workorder_guid AS workorder_guid,
        epmw.workorder_guid AS thisWorkorderGuid
        FROM
        erp_production_mgt_workorder epmw
        LEFT JOIN erp_production_mgt_workorder epmwbgx ON epmwbgx.parent_classification_guid =
        epmw.parent_classification_guid
        WHERE
        epmwbgx.serial_number = ( epmw.serial_number - 1 ) UNION ALL
        SELECT
        epmwg.workorder_guid AS workorder_guid,
        MIN ( epmw.workorder_guid ) AS thisWorkorderGuid
        FROM
        erp_production_mgt_workorder epmw
        LEFT JOIN erp_production_mgt_workorder_relationship epmwr ON epmwr.related_workorder_guid =
        epmw.parent_classification_guid
        LEFT JOIN erp_production_mgt_workorder epmwc ON epmwc.workorder_guid = epmwr.workorder_guid
        LEFT JOIN erp_production_mgt_workorder epmwg ON epmwg.workorder_guid = epmwc.parent_classification_guid
        WHERE
        epmwr.serial_number != 0
        AND epmw.serial_number = 1
        GROUP BY
        epmwg.workorder_guid
        ) eomws
        LEFT JOIN (
        SELECT SUM
        ( eimird.quantity ) AS quantity,
        epmw.workorder_guid
        FROM
        erp_production_mgt_workorder epmw
        LEFT JOIN erp_basic_mgt_production_processes_type ebmppt ON ebmppt.production_processes_type_guid =
        epmw.production_processes_type_guid
        LEFT JOIN erp_production_mgt_workorder outepmw ON outepmw.parent_classification_guid = epmw.workorder_guid
        LEFT JOIN erp_material_mgt_material_batch emmmb ON emmmb.material_production_batch_number =
        outepmw.workorder_number
        AND emmmb.material_guid = outepmw.material_guid
        LEFT JOIN erp_inventory_mgt_inventory_receipt_batch_detail eimirbd ON eimirbd.material_batch_guid =
        emmmb.material_batch_guid
        LEFT JOIN erp_inventory_mgt_inventory_receipt_detail eimird ON eimird.inventory_receipt_detail_guid =
        eimirbd.inventory_receipt_detail_guid
        GROUP BY
        epmw.workorder_guid
        ) eimirds ON eimirds.workorder_guid = eomws.workorder_guid
        ) t13 ON t13.thisWorkorderGuid = tastk.source_guid
        WHERE
        psepmw.workorder_properties = 15
        AND epmw.workorder_properties = 13
        AND epmw2.workorder_properties = 15
        AND epmw3.workorder_properties = 14
        AND epmw3.experience_production_process_guid IS NOT NULL
        ) SELECT
        *
        FROM
        RankedTasks
        WHERE
        rn = 1
        ) task_info on task_info.tasks_guid = t1.tasks_guid
        left join (
        SELECT
        type,
        tasks_guid,
        Round(SUM(EXTRACT(EPOCH FROM (end_time - start_time)) / 60),2) AS tj_total_minutes,
        count(tasks_guid) as tj_counts
        FROM
        mes_operate_record
        where type = '调机'
        GROUP BY type, tasks_guid
        ) mor on  mor.tasks_guid = t1.tasks_guid
        left join (
        SELECT
        type,
        tasks_guid,
        Round(SUM(EXTRACT(EPOCH FROM (end_time - start_time)) / 60),2) AS zt_total_minutes,
        count(tasks_guid) as zt_counts
        FROM
        mes_operate_record
        where type = '暂停'
        GROUP BY type, tasks_guid
        ) mor1 on  mor1.tasks_guid = t1.tasks_guid
        LEFT JOIN (
            SELECT
                team_guid,
                STRING_AGG(staff_short_name, ', ') AS staff_short_names
            FROM
                erp_administration_mgt_team_members t0
            LEFT JOIN
                erp_administration_mgt_staff t1 ON t0.staff_guid = t1.staff_guid
            GROUP BY
                team_guid
        ) staff_names ON t1.team_guid = staff_names.team_guid
        LEFT JOIN (
            SELECT
                ebmt.tasks_guid,
                ebmor.non_defective_quantity
            FROM
                erp_basic_mgt_tasks ebmt
            LEFT JOIN (
                SELECT tasks_guid, SUM ( non_defective_quantity ) non_defective_quantity
                FROM erp_basic_mgt_operation_records WHERE operation_type_guid = '10'
                GROUP BY tasks_guid
            ) ebmor ON ebmt.tasks_guid = ebmor.tasks_guid
        ) t11 ON t11.tasks_guid = t1.tasks_guid
        LEFT JOIN (
            SELECT task_guid,sum(bad_count) as bad_count FROM mes_defective_products_record GROUP BY task_guid
        ) t12 ON t12.task_guid = t1.tasks_guid
        WHERE
        1 = 1
        <if test="model.staffShortName != null and model.staffShortName != '' ">
            and t1.staff_short_name like CONCAT('%',#{model.staffShortName},'%')
        </if>
        <if test="model.orderNumber != null and model.orderNumber != '' ">
            and task_info.order_number = #{model.orderNumber}
        </if>
        <if test="model.workorderNumber != null and model.workorderNumber != '' ">
            and t1.workorder_number = #{model.workorderNumber}
        </if>
        <if test="model.productionShiftGuid != null and model.productionShiftGuid != '' ">
            and t1.shift_id = #{model.productionShiftGuid}
        </if>
        and t0.equipment_code = #{model.equipmentCode}
        And t0.gatew_type = 0
        and t1.workorder_number is not null
        ORDER BY
        t0.equipment_code DESC,t1.start_time desc
    </select>


    <select id="getTaskTimeByTasksGuid" resultType="java.util.Map">
        SELECT
            type,
            tasks_guid,
            Round(SUM(EXTRACT(EPOCH FROM (end_time - start_time)) / 60),2) AS total_minutes,
            count(tasks_guid) as counts
        FROM
            mes_operate_record
        WHERE tasks_guid = #{tasksGuid}
        GROUP BY type, tasks_guid
    </select>

</mapper>
