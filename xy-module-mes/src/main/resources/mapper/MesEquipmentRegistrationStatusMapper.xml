<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.mes.mapper.MesEquipmentRegistrationStatusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.mes.entity.model.vo.MesEquipmentRegistrationStatusVO">
        <id column="mes_equipment_registration_status_guid" property="mesEquipmentRegistrationStatusGuid" />
        <result column="equipment_guid" property="equipmentGuid"  />
        <result column="equipment_name" property="equipmentName"  />
        <result column="equipment_code" property="equipmentCode"  />
        <result column="equipment_status" property="equipmentStatus"  />
        <result column="creator_guid" property="creatorGuid"  />
        <result column="creator" property="creator"  />
        <result column="create_date" property="createDate"  />
        <result column="last_updater_guid" property="lastUpdaterGuid"  />
        <result column="last_updater" property="lastUpdater"  />
        <result column="last_update_date" property="lastUpdateDate"  />
    </resultMap>

    <select id="findPage" resultMap="VoResultMap">
        select * from mes_equipment_registration_status
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select * from mes_equipment_registration_status
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select * from mes_equipment_registration_status where mes_equipment_registration_status_guid = #{guid}
    </select>

    <select id="getDataByEquipmentGuid" resultMap="VoResultMap">
        select * from mes_equipment_registration_status where equipment_guid = #{equipmentGuid}
    </select>

    <update id="updateByEquipmentGuid">
        update mes_equipment_registration_status set equipment_status = #{bool} where equipment_guid = #{equipmentGuid}
    </update>


</mapper>
