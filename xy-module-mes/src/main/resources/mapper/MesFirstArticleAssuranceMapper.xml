<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.mes.mapper.MesFirstArticleAssuranceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.mes.entity.model.vo.MesFirstArticleAssuranceVO">
        <id column="id" property="id" />
        <id column="tenant_guid" property="tenantGuid" />                                                                                                                                                                                                                       <result column="quality_inspection_results" property="qualityInspectionResults"  />
        <result column="description" property="description"  />
        <result column="supervisor_id" property="supervisorId"  />
        <result column="supervisor_name" property="supervisorName"  />
        <result column="inspector_guid" property="inspectorGuid"  />
        <result column="inspector_name" property="inspectorName"  />
        <result column="creator_guid" property="creatorGuid"  />
        <result column="creator" property="creator"  />
        <result column="create_date" property="createDate"  />
        <result column="last_updater_guid" property="lastUpdaterGuid"  />
        <result column="last_updater" property="lastUpdater"  />
        <result column="last_update_date" property="lastUpdateDate"  />

        <result column="workorder_number" property="workorderNumber"  />
        <result column="tasks_guid" property="tasksGuid"  />
        <result column="type" property="type"  />
        <result column="product_name" property="productName"  />
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select * from mes_first_article_assurance
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select * from mes_first_article_assurance
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select * from mes_first_article_assurance where  id = #{guid}
    </select>

    <select id="getOneByTaskGuid" resultMap="VoResultMap">
        select * from mes_first_article_assurance where tasks_guid = #{tasksGuid}
        order by create_date desc
        LIMIT 1
    </select>
</mapper>
