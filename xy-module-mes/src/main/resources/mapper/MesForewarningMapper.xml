<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.mes.mapper.MesForewarningMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.mes.entity.model.vo.MesForewarningVO">
        <id column="mes_forewarning_guid" property="mesForewarningGuid" />
        <result column="tenant_guid" property="tenantGuid"  />
        <result column="mes_forewarning_name" property="mesForewarningName"  />
        <result column="parent_mes_forewarning_guid" property="parentMesForewarningGuid"  />
        <result column="mes_forewarning_level_name" property="mesForewarningLevelName"  />
        <result column="min_value" property="minValue"  />
        <result column="max_value" property="maxValue"  />
        <result column="is_percent" property="isPercent"  />
        <result column="basic_value" property="basicValue"  />
        <result column="wx_user_id" property="wxUserId"  />
        <result column="erp_user_guid" property="erpUserGuid"  />
        <result column="creator_guid" property="creatorGuid"  />
        <result column="creator" property="creator"  />
        <result column="create_date" property="createDate"  />
        <result column="last_updater_guid" property="lastUpdaterGuid"  />
        <result column="last_updater" property="lastUpdater"  />
        <result column="last_update_date" property="lastUpdateDate"  />
        <result column="equipment_code" property="equipmentCode"  />
    </resultMap>

    <select id="findPage" resultMap="VoResultMap">
        select * from mes_forewarning
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select * from mes_forewarning
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select * from mes_forewarning where mes_forewarning_guid = #{guid}
    </select>

    <select id="initMesForewarningValue" resultMap="VoResultMap">
        select t1.* from mes_forewarning t1
        where t1.parent_mes_forewarning_guid != ''
        order by t1.create_date desc
    </select>

    <select id="getMesForewarningData" resultType="java.util.Map">
        SELECT t1.create_date as tjks_time,t2.create_date as tjjs_time,
           t3.create_date as jbks_time,t4.create_date as jbjs_time,
           t5.create_date as zt_time, t6.create_date as hf_time, t0.*
        FROM mes_production_plan_record t0
        LEFT JOIN (
            SELECT t1.tasks_guid,t1.create_date FROM erp_basic_mgt_operation_type t0
            LEFT JOIN erp_basic_mgt_operation_records t1 ON t0.operation_type_value = t1.operation_type_guid
            WHERE t0.operation_type_name = '调机开始' and t0.deleted = false
        ) t1 ON t0.tasks_guid = t1.tasks_guid
        LEFT JOIN (
            SELECT t1.tasks_guid,t1.create_date FROM erp_basic_mgt_operation_type t0
            LEFT JOIN erp_basic_mgt_operation_records t1 ON t0.operation_type_value = t1.operation_type_guid
            WHERE t0.operation_type_name = '调机结束' and t0.deleted = false
        ) t2 ON t0.tasks_guid = t2.tasks_guid
        LEFT JOIN (
            SELECT t1.tasks_guid,t1.create_date FROM erp_basic_mgt_operation_type t0
            LEFT JOIN erp_basic_mgt_operation_records t1 ON t0.operation_type_value = t1.operation_type_guid
            WHERE t0.operation_type_name = '较版开始' and t0.deleted = false
        ) t3 ON t0.tasks_guid = t3.tasks_guid
        LEFT JOIN (
            SELECT t1.tasks_guid,t1.create_date FROM erp_basic_mgt_operation_type t0
            LEFT JOIN erp_basic_mgt_operation_records t1 ON t0.operation_type_value = t1.operation_type_guid
            WHERE t0.operation_type_name = '较版结束' and t0.deleted = false
        ) t4 ON t0.tasks_guid = t4.tasks_guid
        LEFT JOIN (
            SELECT t1.tasks_guid,t1.create_date FROM erp_basic_mgt_operation_type t0
            LEFT JOIN erp_basic_mgt_operation_records t1 ON t0.operation_type_value = t1.operation_type_guid
            WHERE t0.operation_type_name = '暂停' and t0.deleted = false
            ORDER BY t1.create_date desc
            LIMIT 1
        ) t5 ON t0.tasks_guid = t5.tasks_guid
        LEFT JOIN (
            SELECT t1.tasks_guid,t1.create_date FROM erp_basic_mgt_operation_type t0
            LEFT JOIN erp_basic_mgt_operation_records t1 ON t0.operation_type_value = t1.operation_type_guid
            WHERE t0.operation_type_name = '恢复生产' and t0.deleted = false
            ORDER BY t1.create_date desc
            LIMIT 1
        ) t6 ON t0.tasks_guid = t6.tasks_guid
        WHERE t0.end_time IS NULL
    </select>

</mapper>
