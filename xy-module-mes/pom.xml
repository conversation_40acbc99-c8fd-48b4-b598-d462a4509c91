<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xy-erp</artifactId>
        <groupId>xy.erp.biz</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>xy-module-mes</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <properties>
        <!--        <maven.compiler.source>8</maven.compiler.source>-->
        <!--        <maven.compiler.target>8</maven.compiler.target>-->
        <!--        <xerces.version>2.11.0</xerces.version>-->
        <!--        <batik.version>1.10</batik.version>-->
        <!--        <commons.io.version>2.11.0</commons.io.version>-->
    </properties>

    <dependencies>
       <dependency>
            <groupId>com.xunyue</groupId>
            <artifactId>xy-erp-config</artifactId>
        </dependency>

        <dependency>
            <groupId>xy.erp.biz</groupId>
            <artifactId>xy-module-basic-data</artifactId>
            <version>1.0.0</version>
        </dependency>
         <dependency>
            <groupId>com.xunyue</groupId>
            <artifactId>xy-erp-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xunyue</groupId>
            <artifactId>xy-erp-tenant</artifactId>
        </dependency>
    </dependencies>

</project>
