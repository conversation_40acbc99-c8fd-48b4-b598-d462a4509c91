package xy.server.policy.i18n;

import com.xunyue.common.i18n.BaseResultErrorCode;

/**
 * <AUTHOR>
 * @Date 2023-05-01
 * <p>
 * 错误代码枚举
 * </p>
 **/
public enum ResultErrorCode implements BaseResultErrorCode {
    POLICY_ENGINE_CODE_OR_NAME_IS_REPEAT("决策编码或名称重复", 1150000001),
    POLICY_ENGINE_IS_NOT_EXIST_OR_DISABLED("决策引擎不存在或已禁用", 1150000002),
    START_NODE_IS_MISSING_OR_NOT_UNIQUE("缺少开始节点或开始节点不唯一", 1150000003),
    END_NODE_IS_MISSING_OR_NOT_UNIQUE("缺少结束节点或结束节点不唯一", 1150000004),
    MESSAGE_CONFIG_ERROR("平台消息配置错误", 1150000005),
    ENCODINGS_OF_THE_SAME_TYPE_CANNOT_BE_REPEATED_FOR_MESSAGE_TEMPLATES("消息模板相同类型的编码不能重复", 1150000006),
    AN_EXECUTING_POLICY_ENGINE_CANNOT_BE_DELETED_OR_EDITED("正在执行的决策引擎不能删除或编辑", 1150000007),
    ;
    private String msg;
    private int code;

    ResultErrorCode(String msg, int code) {
        this.msg = msg;
        this.code = code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public int getCode() {
        return code;
    }
}
