package xy.server.policy.delay.event;

import xy.server.policy.delay.XyDelayDTO;

/**
 * <AUTHOR>
 * @Date 2024-11-27 10:10
 * @apiNote 延迟消息接口定义
 **/
public interface XyDelayEventService {

    /**
     * 延迟消息到期事件
     * @param xyDelayDTO
     */
    default void onXyDelayExpireEvent(XyDelayDTO xyDelayDTO) {}

    /**
     * 重新加载延迟消息队列
     * <p>
     *     比如一开始我们定义业务订单10分钟后超时关闭，已经生成了延迟消息，但是突然改为60分钟后关闭，
     *     我们需要把延迟消息全部清空，从数据库获取相关业务信息在放回延迟消息队列
     * </p>
     */
    default void reloadDelayData(){}

}
