package xy.server.policy.delay;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024-11-30 14:28
 **/
@Data
public class XyDelayDTO {

    /**
     * 业务单据Guid
     */
    private String businessKey;

    /**
     * 类别，用于区分不同业务
     * 例：订单模块：ORDER
     * 注意：最好加上type区分，假如延迟类型是超时：ORDER_1
     */
    private String category;

    /**
     * 租户id
     */
    private String tenantGuid;

    /**
     * 超时 / 倒计时
     */
    private String type;

    /**
     * 触发精度 天/小时/分钟
     */
    private String triggerPrecision;

    /**
     * 基线时间(时间戳)
     */
    private Long baseTime;

    /**
     * 计算时间（秒）+3000 -6000
     */
    private Long calcTime;

    /**
     * 携带的变量信息
     */
    private Map<String, String> variables;
}
