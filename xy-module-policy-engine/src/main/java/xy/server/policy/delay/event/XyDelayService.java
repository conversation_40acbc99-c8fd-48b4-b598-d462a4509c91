package xy.server.policy.delay.event;

import cn.hutool.core.util.ObjUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import xy.server.policy.delay.XyDelayDTO;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024-11-27 10:17
 * <p>
 * 延迟队列事件接收
 * </p>
 **/
@Component
@Slf4j
public class XyDelayService {

    @Resource
    private final Map<String, XyDelayEventService> eventServiceMap = new HashMap<>();

    @EventListener(value = XyDelayEvent.class)
    @Order(1)
    public void broadcastEventListener(XyDelayEvent eventSource) {
        XyDelayDTO delayDTO = eventSource.getXyDelayDTO();
        XyDelayEventService delayEventService = eventServiceMap.get(delayDTO.getCategory());
        if (ObjUtil.isNotNull(delayEventService)) {
            // 通知业务
            delayEventService.onXyDelayExpireEvent(eventSource.getXyDelayDTO());
        }
    }

}
