package xy.server.policy.delay;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xunyue.config.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import xy.server.policy.common.enums.XyDelayTriggerPrecisionEnum;
import xy.server.policy.common.enums.XyDelayTypeEnum;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>
 * 迅越软件延迟消息工具
 * </p>
 *
 * <AUTHOR>
 * @Date 2024-11-30 14:26
 **/
@Component
@Slf4j
public class XyDelayUtil {
    @Resource
    private RedisUtil redisUtil;

    public final static String XY_DELAY_KEY = "xy_delay_key:";
    /**
     * 记录延迟消息
     */
    public static ConcurrentHashMap<String, String> cacheDelay = new ConcurrentHashMap<>();

    /**
     * 创建一个延迟消息
     *
     * @param xyDelayDTO
     */
    public void add(@NotNull XyDelayDTO xyDelayDTO) {
        // key
        String key = XY_DELAY_KEY + xyDelayDTO.getCategory() + ":" + xyDelayDTO.getBusinessKey();
        // 存入到Redis，不设置过期时间！
        String value = JSONObject.toJSONString(xyDelayDTO);
        redisUtil.set(key, value);
        log.info("创建一个延迟消息:{}", xyDelayDTO);
        // 保存进内存里面，方便后续取值
        cacheDelay.put(key, value);
    }

    /**
     * 批量创建一个延迟消息
     *
     * @param xyDelayList
     */
    public void addAll(@NotNull List<XyDelayDTO> xyDelayList) {
        if (CollUtil.isNotEmpty(xyDelayList)) {
            xyDelayList.forEach(this::add);
        }
    }

    /**
     * 批量更新延迟消息
     *
     * @param xyDelayList
     */
    public void updateAll(@NotNull List<XyDelayDTO> xyDelayList) {
        if (CollUtil.isNotEmpty(xyDelayList)) {
            xyDelayList.forEach(this::add);
        }
    }

    /**
     * 更新延迟消息
     *
     * @param xyDelayDTO
     */
    public void update(@NotNull XyDelayDTO xyDelayDTO) {
        add(xyDelayDTO);
    }

    /**
     * 获取未到期的延迟消息
     *
     * @param category    业务类型
     * @param businessKey 业务主键值
     */
    public XyDelayDTO getTaskByKey(String category, String businessKey) {
        String key = getKey(category, businessKey);
        String value = cacheDelay.get(key);
        if (StrUtil.isNotBlank(value)) {
            return JSONObject.parseObject(value, XyDelayDTO.class);
        }
        Object o = redisUtil.get(key);
        return JSONObject.parseObject(o.toString(), XyDelayDTO.class);
    }

    /**
     * 获取未到期的延迟消息
     *
     * @param category 业务类型
     */
    public List<XyDelayDTO> getTaskListByCategory(String category) {
        List<String> taskList = redisUtil.getListByKeys(getKey(category, ""));
        List<XyDelayDTO> result = new ArrayList<>();
        taskList.forEach(v -> result.add(JSONObject.parseObject(v, XyDelayDTO.class)));
        return result;
    }


    /**
     * 移除一个延迟消息
     *
     * @param category    业务类型
     * @param businessKey 业务主键值
     */
    public void remove(String category, String businessKey) {
        String key = getKey(category, businessKey);
        // 删除
        redisUtil.del(key);
        // 删除缓存
        cacheDelay.remove(key);
    }

    /**
     * 按类型移除延迟消息
     *
     * @param category 业务类型
     */
    public void removeByCategory(String category) {
        String keys = getKey(category, "");
        // 删除缓存
        Set<String> keySet = redisUtil.keys(keys + "*");
        keySet.forEach(v -> cacheDelay.remove(v));
        // 删除
        redisUtil.deleteByKeys(keys);
    }

    /**
     * 获取Redis中的等待任务
     */
    public Map<String, String> getTaskMap() {
        return redisUtil.getMapByKeys(XY_DELAY_KEY);
    }

    /**
     * 获取Redis中的等待任务
     *
     * @param category 业务类型
     */
    public Map<String, String> getTaskMapByCategory(String category) {
        return redisUtil.getMapByKeys(XY_DELAY_KEY + category + ":");
    }

    /**
     * 检查是否有任务
     *
     * @param businessKey
     * @return
     */
    public boolean policyEngineHasTask(String category, String businessKey) {
        return redisUtil.hasKey(getKey(category, businessKey));
    }


    /**
     * 获取Key
     *
     * @param category
     * @param businessKey
     * @return
     */
    public String getKey(String category, String businessKey) {
        return XY_DELAY_KEY + category + ":" + businessKey;
    }

    /**
     * 计算延时时间
     *
     * @param xyDelayDTO
     * @return
     */
    public Long calcDelayTime(@NotNull XyDelayDTO xyDelayDTO) {
        if (XyDelayTypeEnum.TIMEOUT.getCode().equals(xyDelayDTO.getType())) {
            // 超时
            return xyDelayDTO.getBaseTime() + xyDelayDTO.getCalcTime();
        } else if (XyDelayTypeEnum.AS_OF.getCode().equals(xyDelayDTO.getType())) {
            // 截止日期前
            return xyDelayDTO.getBaseTime() - xyDelayDTO.getCalcTime();
        }
        return 0L;
    }

    /**
     * 时间转换
     *
     * @param time
     * @param triggerPrecision
     * @return
     */
    public Date convertDate(Long time, String triggerPrecision) {
        if (XyDelayTriggerPrecisionEnum.DAY.getCode().equals(triggerPrecision)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = sdf.format(new Date(time));
            return DateUtil.parse(formattedDate);
        } else if (XyDelayTriggerPrecisionEnum.HOUR.getCode().equals(triggerPrecision)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH");
            String formattedDate = sdf.format(new Date(time));
            return DateUtil.parse(formattedDate);
        } else if (XyDelayTriggerPrecisionEnum.MINUTE.getCode().equals(triggerPrecision)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            String formattedDate = sdf.format(new Date(time));
            return DateUtil.parse(formattedDate);
        }
        return new Date();
    }

    /**
     * 更新计算时间
     *
     * @param category    业务类型
     * @param newCalcTime 新的时间
     */
    public void updateCalcTimeByCategory(String category, Long newCalcTime) {
        List<XyDelayDTO> listByCategory = getTaskListByCategory(category);
        listByCategory.forEach(v -> v.setCalcTime(newCalcTime));
        updateAll(listByCategory);
    }
}
