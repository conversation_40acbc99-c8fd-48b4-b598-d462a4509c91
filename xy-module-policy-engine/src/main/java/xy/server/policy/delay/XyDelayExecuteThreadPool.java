package xy.server.policy.delay;

import com.alibaba.fastjson.JSONObject;
import com.xunyue.common.util.SpringUtils;
import org.springframework.context.ApplicationEventPublisher;
import xy.server.policy.delay.event.XyDelayEvent;

import java.util.Date;

/**
 * <AUTHOR>
 * @data 2024-03-19 16:46
 * @apiNote 决策引擎延时执行队列线程pool
 */

public class XyDelayExecuteThreadPool implements Runnable {

    private XyDelayUtil xyDelayUtil = SpringUtils.getBean(XyDelayUtil.class);

    private ApplicationEventPublisher applicationEventPublisher;

    private String content;

    public XyDelayExecuteThreadPool(String content, ApplicationEventPublisher applicationEventPublisher) {
        this.content = content;
        this.applicationEventPublisher = applicationEventPublisher;
    }

    @Override
    public void run() {
        try {
            XyDelayDTO dataDTO = JSONObject.parseObject(content, XyDelayDTO.class);
            // 获取当前时间
            long currentTimeMillis = System.currentTimeMillis();
            // 获取触发时间，已经按照规则计算好
            Long delayTime = xyDelayUtil.calcDelayTime(dataDTO);
            // 按照触发精度转换时间
            Date currentDate = xyDelayUtil.convertDate(currentTimeMillis, dataDTO.getTriggerPrecision());
            Date triggerDate = xyDelayUtil.convertDate(delayTime, dataDTO.getTriggerPrecision());
            if (currentDate.compareTo(triggerDate) == 0) {
                // 当前时间相等延时执行时间
                // 触发事件
                applicationEventPublisher.publishEvent(new XyDelayEvent(dataDTO));
                // 删除Redis数据
                xyDelayUtil.remove(dataDTO.getCategory(), dataDTO.getBusinessKey());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
