package xy.server.policy.delay;

import cn.hutool.core.collection.CollUtil;
import com.xunyue.config.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;

import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 决策引擎延时执行线程
 *
 * <AUTHOR>
 * @date 2024-03-19
 */
@Slf4j
public class XyDelayExecuteThread extends Thread {
    /**
     * redis工具
     */
    private RedisUtil redisUtil;
    /**
     *
     */
    private static XyDelayExecuteThread xyDelayExecuteThread;
    /**
     * 定义线程池
     */
    private ExecutorService cachedThreadPool = new ThreadPoolExecutor(2, 4, 0, TimeUnit.MINUTES, new LinkedBlockingDeque<>(), r -> new Thread(r, "xyDelayThread"));

    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 单例
     *
     * @param redisUtil
     * @return
     */
    public static XyDelayExecuteThread getInstance(RedisUtil redisUtil, ApplicationEventPublisher applicationEventPublisher) {
        if (xyDelayExecuteThread == null) {
            synchronized (XyDelayExecuteThread.class) {
                if (xyDelayExecuteThread == null) {
                    xyDelayExecuteThread = new XyDelayExecuteThread(redisUtil,applicationEventPublisher);
                }
            }
        }
        return xyDelayExecuteThread;
    }

    public XyDelayExecuteThread(RedisUtil redisUtil, ApplicationEventPublisher applicationEventPublisher) {
        this.redisUtil = redisUtil;
        this.applicationEventPublisher=applicationEventPublisher;
    }

    @Override
    public void run() {
        while (true) {
            try {
                // 一分钟执行一次，系统时间只精确到分钟
                sleep(1000);
                Map<String, String> mapByKeys;
                if (CollUtil.isEmpty(XyDelayUtil.cacheDelay)) {
                    mapByKeys = redisUtil.getMapByKeys(XyDelayUtil.XY_DELAY_KEY);
                    XyDelayUtil.cacheDelay.putAll(mapByKeys);
                } else {
                    mapByKeys = XyDelayUtil.cacheDelay;
                }

                // 如果没有消息队列，挂起线程
                if (CollUtil.isNotEmpty(mapByKeys)) {
                    // 启动线程执行等待任务
                    mapByKeys.forEach((key, value) -> {
                        cachedThreadPool.execute(new XyDelayExecuteThreadPool(value, applicationEventPublisher));
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
