package xy.server.policy.delay.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;
import xy.server.policy.delay.XyDelayDTO;

/**
 * <AUTHOR>
 * @data 2024-11-27
 * @apiNote 延迟消息通知事件
 */
@Getter
@Setter
public class XyDelayEvent extends ApplicationEvent {

    /**
     * 事件实体
     */
    private XyDelayDTO xyDelayDTO;

    public XyDelayEvent(XyDelayDTO xyDelayDTO) {
        super(xyDelayDTO);
        this.xyDelayDTO = xyDelayDTO;
    }
}
