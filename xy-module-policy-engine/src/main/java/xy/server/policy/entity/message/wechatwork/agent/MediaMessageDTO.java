package xy.server.policy.entity.message.wechatwork.agent;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import xy.server.policy.entity.message.base.BaseMessage;

import java.util.List;

/**
 * 图片、文件消息DTO
 *
 * <AUTHOR>
 * @since 2021/4/8/008 21:38
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class MediaMessageDTO extends BaseMessage {
    private static final long serialVersionUID = 7412950115675650317L;

    @ApiModelProperty(value = "接收人分组列表")
    private List<Long> receiverGroupIds;

    @ApiModelProperty(value = "接收人列表")
    private List<String> receiverIds;

    @ApiModelProperty(value = "PartyID列表，非必填，多个接受者用‘|’分隔。当touser为@all时忽略本参数")
    private String toParty;

    @ApiModelProperty(value = "TagID列表，非必填，多个接受者用‘|’分隔。当touser为@all时忽略本参数")
    private String toTag;

    @ApiModelProperty(value = "素材id")
    private String mediaId;

}
