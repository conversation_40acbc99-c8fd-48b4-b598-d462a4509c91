package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "message_platform_config_value")
public class MessagePlatformConfigValue extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId("message_platform_config_value_guid")
    private String messagePlatformConfigValueGuid;
    @TableField("message_platform_config_guid")
    private String messagePlatformConfigGuid;
    /**
     * 参数键
     */
    @TableField("key")
    private String key;
    /**
     * 参数值
     */
    @TableField("value")
    private String value;


}
