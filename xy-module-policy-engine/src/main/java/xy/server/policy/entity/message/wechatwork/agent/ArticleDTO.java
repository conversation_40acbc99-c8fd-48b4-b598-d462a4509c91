package xy.server.policy.entity.message.wechatwork.agent;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 图文消息
 *
 * <AUTHOR>
 * @since 2021/4/8/008 12:15
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ArticleDTO {

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "超过512个字节，超过会自动截断")
    private String description;

    @ApiModelProperty(value = "点击跳转链接")
    private String url;

    @ApiModelProperty(value = "图文消息的图片链接，支持JPG、PNG格式，较好的效果为大图1068*455，小图150*150。")
    private String picUrl;

    @ApiModelProperty(value = "仅在图文数为1条时才生效。 默认为“阅读全文”， 不超过4个文字，超过自动截断。该设置只在企业微信上生效，微工作台（原企业号）上不生效。")
    private String btnText;

}
