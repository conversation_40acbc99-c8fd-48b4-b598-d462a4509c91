package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 决策节点组件属性_延时队列
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_policy_engine_node_meta_delay")
public class ErpPolicyEngineNodeMetaDelay implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 决策_节点_guid
     */
    @TableField("policy_engine_node_guid")
    private String policyEngineNodeGuid;
    /**
     * PK
     */
    @TableId("policy_engine_node_meta_guid")
    private String policyEngineNodeMetaGuid;
    /**
     * 延时类型（expire到期；overdue超期）
     */
    @TableField("delay_type")
    private String delayType;
    /**
     * 决策引擎变量标识
     */
    @TableField("variables_symbol")
    private String variablesSymbol;
    /**
     * 延时时间_天
     */
    @TableField("delay_time_day")
    private Integer delayTimeDay;
    /**
     * 延时时间_时
     */
    @TableField("delay_time_hour")
    private Integer delayTimeHour;
    /**
     * 延时时间_分
     */
    @TableField("delay_time_minute")
    private Integer delayTimeMinute;


}
