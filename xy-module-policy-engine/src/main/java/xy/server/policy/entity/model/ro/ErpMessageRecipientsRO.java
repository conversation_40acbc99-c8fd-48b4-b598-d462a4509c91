package xy.server.policy.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 消息通知收件人RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMessageRecipientsRO对象", description = "消息通知收件人")
public class ErpMessageRecipientsRO {

    @ApiModelProperty(value = "消息通知_guid")
    private String messageGuid;

    @ApiModelProperty(value = "收件人")
    private String userGuid;

    @ApiModelProperty(value = "PK")
    private String messageRecipientsGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "阅读时间")
    private LocalDateTime readTime;

}