package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 决策节点流程
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_policy_engine_node")
public class ErpPolicyEngineNode implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("policy_engine_node_guid")
    private String policyEngineNodeGuid;
    /**
     * 决策流程_guid
     */
    @TableField("policy_engine_guid")
    private String policyEngineGuid;
    /**
     * 节点组件名称
     */
    @TableField("component")
    private String component;
    /**
     * 节点类型
     */
    @TableField("component_type")
    private String componentType;
    /**
     * 节点位置-x
     */
    @TableField("position_x")
    private Integer positionX;
    /**
     * 节点位置-y
     */
    @TableField("position_y")
    private Integer positionY;
    /**
     * 节点描述
     */
    @TableField("description")
    private String description;


}
