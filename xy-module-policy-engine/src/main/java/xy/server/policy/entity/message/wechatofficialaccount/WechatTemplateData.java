package xy.server.policy.entity.message.wechatofficialaccount;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 微信模板Data
 *
 * <AUTHOR>
 * @since 2021/4/10/010 15:35
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class WechatTemplateData {

    private String name;
    private String value;
    @ApiModelProperty(value = "显示颜色")
    private String color;

}
