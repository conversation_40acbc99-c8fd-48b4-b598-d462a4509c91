package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 决策节点组件属性_消息通知
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_policy_engine_node_meta_notify")
public class ErpPolicyEngineNodeMetaNotify implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 决策_节点_guid
     */
    @TableField("policy_engine_node_guid")
    private String policyEngineNodeGuid;
    /**
     * PK
     */
    @TableId("policy_engine_node_meta_guid")
    private String policyEngineNodeMetaGuid;
    /**
     * 消息模板
     */
    @TableField("message_template_guid")
    private String messageTemplateGuid;
    /**
     * 接收人列表
     */
    @TableField("consignee_guids")
    private String consigneeGuids;
    /**
     * 接收角色
     */
    @TableField("roles")
    private String roles;
    /**
     * 发送方式，站内信，公众号，企业微信，钉钉，邮件等
     */
    @TableField("message_types")
    private String messageTypes;
    /**
     * 标题
     */
    @TableField("message_title")
    private String messageTitle;
    /**
     * 消息内容
     */
    @TableField("message_content")
    private String messageContent;
    /**
     * 消息平台guid（多个用,号分隔）
     */
    @TableField("message_platform_guids")
    private String messagePlatformGuids;


}
