package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 消息通知
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_message")
public class ErpMessage implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 租户GUID
     */
    @TableField(value = "tenant_guid", fill = FieldFill.INSERT)
    private String tenantGuid;
    /**
     * 消息通知PK
     */
    @TableId("message_guid")
    private String messageGuid;
    /**
     * 消息通知途径,字典,1、站内，2、微信通知，3、企业微信，4、钉钉，5飞书
     */
    @TableField("message_way")
    private String messageWay;
    /**
     * 消息模板
     */
    @TableField("message_template_guid")
    private String messageTemplateGuid;
    /**
     * 消息通知标题
     */
    @TableField("message_title")
    private String messageTitle;
    /**
     * 消息通知封面
     */
    @TableField("message_cover")
    private String messageCover;
    /**
     * 发送时间
     */
    @TableField("send_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime;
    /**
     * 发送状态，1成功，0失败
     */
    @TableField("status")
    private String status;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;


}
