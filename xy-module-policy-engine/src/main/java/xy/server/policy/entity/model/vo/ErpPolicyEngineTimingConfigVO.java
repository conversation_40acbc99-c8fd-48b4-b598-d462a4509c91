package xy.server.policy.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 定时触发配置VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPolicyEngineTimingConfigVO对象", description = "定时触发配置")
public class ErpPolicyEngineTimingConfigVO implements Serializable {

    @ApiModelProperty(value = "决策_guid")
    private String policyEngineGuid;

    @ApiModelProperty(value = "PK")
    private String erpPolicyEngineTimingConfigGuid;

    @ApiModelProperty(value = "cron表达式")
    private String cronExpression;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间：必须指定")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间：为空不结束")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "定时开关")
    private Boolean status;

    @ApiModelProperty(value = "租户")
    private String tenantGuid;

}
