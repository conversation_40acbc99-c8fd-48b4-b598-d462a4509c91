package xy.server.policy.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 决策执行日志VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPolicyEngineLogsVO对象", description = "决策执行日志")
public class ErpPolicyEngineLogsVO {

    @ApiModelProperty(value = "PK")
    private String policyEngineLogsGuid;

    @ApiModelProperty(value = "决策流程_guid")
    private String policyEngineGuid;

    @ApiModelProperty(value = "决策_节点_guid")
    private String policyEngineNodeGuid;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "操作状态")
    private String operatorStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "操作日期")
    private LocalDateTime operatorDate;

    @ApiModelProperty(value = "操作结果")
    private String operatorResult;

}