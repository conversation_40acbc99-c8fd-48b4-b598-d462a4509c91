package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 决策流程边
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_policy_engine_edge")
public class ErpPolicyEngineEdge implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("policy_engine_edge_guid")
    private String policyEngineEdgeGuid;
    /**
     * 决策流程_guid
     */
    @TableField("policy_engine_guid")
    private String policyEngineGuid;
    /**
     * 来源节点_guid
     */
    @TableField("source_node_guid")
    private String sourceNodeGuid;
    /**
     * 目标节点_guid
     */
    @TableField("target_node_guid")
    private String targetNodeGuid;
    /**
     * 条件表达式
     */
    @TableField("expression")
    private String expression;


}
