package xy.server.policy.entity.message.dingtalk.robot;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import xy.server.policy.entity.message.base.BaseMessage;

import java.util.List;

/**
 * 钉钉群消息link类型DTO
 *
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LinkMessageDTO extends BaseMessage {
    private static final long serialVersionUID = -3289428483627765265L;

    /**
     * 接收人分组列表
     */
    @ApiModelProperty("接收人分组列表")
    private List<Long> receiverGroupIds;

    @ApiModelProperty("是否@所有人")
    private Boolean isAtAll;

    @ApiModelProperty(value = "接收人列表")
    private List<String> receiverIds;

    @ApiModelProperty(value = "消息标题")
    private String title;

    @ApiModelProperty(value = "消息内容。如果太长只会部分展示。")
    private String text;

    @ApiModelProperty(value = "点击消息跳转的URL")
    private String messageUrl;

    @ApiModelProperty(value = "图片URL")
    private String picUrl;

}
