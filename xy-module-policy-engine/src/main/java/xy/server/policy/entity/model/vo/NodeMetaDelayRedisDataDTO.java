package xy.server.policy.entity.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2024-03-18 15:40
 * @apiNote 决策引擎延时队列节点存到redis的数据结构
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NodeMetaDelayRedisDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 决策引擎guid
     */
    private String policyEngineGuid;

    /**
     * 当前节点guid
     */
    private String currentNodeGuid;

    /**
     * 变量池数据
     */
    private Map<String, String> variables;

    /**
     * 延时执行日期
     */
    private Date delayExecutionDate;

    /**
     * 当前任务taskId
     */
    private String taskId;

    /**
     * 等待分钟
     */
    private long offsetMinute;

    /**
     * 当前租户
     */
    private String tenantGuid;
}
