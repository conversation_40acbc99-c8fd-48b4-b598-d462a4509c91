package xy.server.policy.entity.message.wechatwork.agent;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import xy.server.policy.entity.message.base.BaseMessage;

import java.util.List;

/**
 * 视频消息类型DTO
 *
 * <AUTHOR>
 * @since 2021/4/8/008 21:49
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class VideoMessageDTO extends BaseMessage {
    private static final long serialVersionUID = -5830938694539681793L;


    @ApiModelProperty(value = "接收人分组列表")
    private List<Long> receiverGroupIds;

    @ApiModelProperty(value = "接收人列表")
    private List<String> receiverIds;

    @ApiModelProperty(value = "PartyID列表，非必填，多个接受者用‘|’分隔。当touser为@all时忽略本参数")
    private String toParty;

    @ApiModelProperty(value = "TagID列表，非必填，多个接受者用‘|’分隔。当touser为@all时忽略本参数")
    private String toTag;

    @ApiModelProperty(value = "视频素材id")
    private String mediaId;

    @ApiModelProperty(value = "视频消息的标题，不超过128个字节，超过会自动截断")
    private String title;

    @ApiModelProperty(value = "视频消息的描述，不超过512个字节，超过会自动截断")
    private String description;
}
