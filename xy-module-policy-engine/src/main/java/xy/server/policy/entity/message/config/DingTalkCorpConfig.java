package xy.server.policy.entity.message.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 钉钉工作通知配置
 *
 * <AUTHOR>
 * @since 2021/3/16/016 10:46
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DingTalkCorpConfig extends BaseConfig {

    private static final long serialVersionUID = -1382230873208739831L;

    @ApiModelProperty(value = "应用appKey")
    private String appKey;
    @ApiModelProperty(value = "应用Secret")
    private String AppSecret;
    @ApiModelProperty(value = "应用agentId")
    private Integer agentId;

}
