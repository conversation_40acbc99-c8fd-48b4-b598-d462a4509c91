package xy.server.policy.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 消息平台配置表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MessagePlatformConfigQO对象", description = "消息平台配置表")
public class MessagePlatformConfigQO {

    @ApiModelProperty(value = "")
    private String tenantGuid;

    @ApiModelProperty(value = "平台")
    private String platform;

    @ApiModelProperty(value = "显示名称")
    private String messagePlatformConfigName;

    @ApiModelProperty(value = "是否默认")
    private Boolean isDefault;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "关键字列表")
    private List<String> keywords;

}