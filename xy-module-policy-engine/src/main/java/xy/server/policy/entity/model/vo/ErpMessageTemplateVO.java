package xy.server.policy.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 消息通知模板VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMessageTemplateVO对象", description = "消息通知模板")
public class ErpMessageTemplateVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "消息模板PK")
    private String messageTemplateGuid;

    @ApiModelProperty(value = "消息模板编码")
    private String messageTemplateCode;

    @ApiModelProperty(value = "消息模板类型,字典")
    private String messageTemplateType;

    @ApiModelProperty(value = "消息模板标题")
    private String messageTemplateTitle;

    @ApiModelProperty(value = "消息模板封面")
    private String messageTemplateCover;

    @ApiModelProperty(value = "启用状态")
    private Boolean status;

    @ApiModelProperty(value = "模板内容列表")
    private List<ErpMessageTemplateBodyVO> templateBodyList;

}