package xy.server.policy.entity.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PolicyCombineTermDTO {

    @ApiModelProperty(value = "变量标识")
    private String variablesSymbol;

    @ApiModelProperty(value = "审核流ID")
    private String variablesName;

    @ApiModelProperty(value = "表达式类型")
    private String resultType;

    @ApiModelProperty("判断条件")
    private Integer logic;

    @ApiModelProperty("比较值1")
    private String logicValue;

    @ApiModelProperty("比较值2")
    private String logicValue2;
}
