package xy.server.policy.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 决策节点组件属性_SQLRO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPolicyEngineNodeMetaSqlRO对象", description = "决策节点组件属性_SQL")
public class ErpPolicyEngineNodeMetaSqlRO {

    @ApiModelProperty(value = "决策_节点_guid")
    private String policyEngineNodeGuid;

    @ApiModelProperty(value = "PK")
    private String policyEngineNodeMetaGuid;

    @NotNull(message = "sql语句不能为空")
    @ApiModelProperty(value = "sql语句")
    private String sql;

    @NotNull(message = "预期执行结果类型，1:单行，2:集合不能为空")
    @ApiModelProperty(value = "预期执行结果类型，1:单行，2:集合")
    private String resutType;

}