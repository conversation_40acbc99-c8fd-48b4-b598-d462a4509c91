package xy.server.policy.entity.message.base;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.policy.common.enums.MessageType;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 消息推送参数
 *
 * <AUTHOR>
 * @date 2021/2/8 18:30
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessagePushDTO {
    private static final long serialVersionUID = 2732930320545780215L;
    /**
     * 消息参数，键为需要发送的消息类型，值为对应消息类型需要的参数（不同平台可能会需要不同的参数，所以这里不表达具体类型，由不同的实现决定具体结构）
     */
    private Map<MessageType, JSONObject> messageParam = new LinkedHashMap<>();
}
