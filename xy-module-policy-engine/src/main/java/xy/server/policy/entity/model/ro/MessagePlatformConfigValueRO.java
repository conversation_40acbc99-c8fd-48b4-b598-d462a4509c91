package xy.server.policy.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MessagePlatformConfigValueRO对象", description = "")
public class MessagePlatformConfigValueRO extends BaseEntity {

    @ApiModelProperty(value = "")
    private String messagePlatformConfigValueGuid;

    @ApiModelProperty(value = "")
    private String messagePlatformConfigGuid;

    @NotNull(message = "参数键不能为空")
    @ApiModelProperty(value = "参数键")
    private String key;

    @NotNull(message = "参数值不能为空")
    @ApiModelProperty(value = "参数值")
    private String value;

}
