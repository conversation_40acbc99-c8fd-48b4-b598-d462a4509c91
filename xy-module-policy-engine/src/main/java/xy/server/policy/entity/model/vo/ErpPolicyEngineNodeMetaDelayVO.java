package xy.server.policy.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 决策节点组件属性_延时队列VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPolicyEngineNodeMetaDelayVO对象", description = "决策节点组件属性_延时队列")
public class ErpPolicyEngineNodeMetaDelayVO implements Serializable {

    @ApiModelProperty(value = "决策_节点_guid")
    private String policyEngineNodeGuid;

    @ApiModelProperty(value = "PK")
    private String policyEngineNodeMetaGuid;

    @ApiModelProperty(value = "延时类型（expire到期；overdue超期）")
    private String delayType;

    @ApiModelProperty(value = "决策引擎变量标识")
    private String variablesSymbol;

    @ApiModelProperty(value = "延时时间_天")
    private Integer delayTimeDay;

    @ApiModelProperty(value = "延时时间_时")
    private Integer delayTimeHour;

    @ApiModelProperty(value = "延时时间_分")
    private Integer delayTimeMinute;

}
