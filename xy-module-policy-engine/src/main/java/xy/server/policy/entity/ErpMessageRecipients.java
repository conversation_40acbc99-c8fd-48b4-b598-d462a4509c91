package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 消息通知收件人
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_message_recipients")
public class ErpMessageRecipients implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 租户GUID
     */
    @TableField(value = "tenant_guid", fill = FieldFill.INSERT)
    private String tenantGuid;
    /**
     * 消息通知_guid
     */
    @TableField("message_guid")
    private String messageGuid;
    /**
     * PK
     */
    @TableId("message_recipients_guid")
    private String messageRecipientsGuid;
    /**
     * 收件人
     */
    @TableField("user_guid")
    private String userGuid;
    /**
     * 阅读时间
     */
    @TableField("read_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime readTime;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;

}
