package xy.server.policy.entity.message.dingtalk.corp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import xy.server.policy.entity.message.base.BaseMessage;

import java.util.List;

/**
 * 钉钉卡片消息-独立跳转
 *
 * <AUTHOR>
 * @since 2021/2/28/028 21:28
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ActionCardMultiMessageDTO extends BaseMessage {
    private static final long serialVersionUID = -3289428483627765265L;

    @ApiModelProperty(value = "接收人分组列表")
    private List<Long> receiverGroupIds;

    @ApiModelProperty("是否发送给企业全部用户，注意钉钉限制只能发3次全员消息")
    private Boolean toAllUser;

    @ApiModelProperty(value = "接收人列表")
    private List<String> receiverIds;

    @ApiModelProperty("接收人的部门id列表，接收者的部门id列表，多个用,隔开")
    private String deptIdList;

    @ApiModelProperty("标题，最长20个字符。")
    private String title;

    @ApiModelProperty("跳转链接")
    private String actionUrl;

    @ApiModelProperty(value = "按钮排列方式,使用独立跳转ActionCard样式时的按钮排列方式,0竖直排列,1,横向排列")
    private String btnOrientation = "0";

    @ApiModelProperty(value = "消息内容，支持markdown，语法参考标准markdown语法。建议1000个字符以内。")
    private String markdown;

    @ApiModelProperty("按钮设置")
    private List<BtnJsonDTO> btnJsonList;
}
