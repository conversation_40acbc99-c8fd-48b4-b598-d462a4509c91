package xy.server.policy.entity.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-11-28 15:03
 * <p>
 *     预警数据表
 * </p>
 **/
@Data
public class EarlyWarningVO {

    @ApiModelProperty("对应前端模块")
    private String category;

    @ApiModelProperty(value = "接收人")
    private String consigneeGuids;

    @ApiModelProperty(value = "接收角色")
    private String roles;

    @ApiModelProperty(value = "接收部门")
    private String department;

    @ApiModelProperty(value = "等级")
    private String level;

    @ApiModelProperty(value = "预警数量")
    private Integer num;

    @ApiModelProperty(value = "最早预警时间")
    private LocalDateTime earliestDate;

    @ApiModelProperty(value = "最新预警时间")
    private LocalDateTime latestDate;

    @ApiModelProperty(value = "业务Guid")
    private List<String> guidList;

//    @ApiModelProperty(value = "预警等级记录")
//    private List<String> levelList;

}
