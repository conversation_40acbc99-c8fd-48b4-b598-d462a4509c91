package xy.server.policy.entity.message.wechatwork.agent;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import xy.server.policy.entity.message.base.BaseMessage;

import java.util.List;

/**
 * 企业微信Markdown
 *
 * <AUTHOR>
 * @since 2021/4/7/007 17:57
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MarkdownMessageDTO extends BaseMessage {
    private static final long serialVersionUID = 8123659270032033936L;

    @ApiModelProperty(value = "接收人分组列表")
    private List<Long> receiverGroupIds;

    @ApiModelProperty(value = "接收人列表")
    private List<String> receiverIds;

    @ApiModelProperty(value = "PartyID列表，非必填，多个接受者用‘|’分隔。当touser为@all时忽略本参数")
    private String toParty;

    @ApiModelProperty(value = "TagID列表，非必填，多个接受者用‘|’分隔。当touser为@all时忽略本参数")
    private String toTag;

    @ApiModelProperty(value = "请输入Markdown内容...")
    private String content;


}
