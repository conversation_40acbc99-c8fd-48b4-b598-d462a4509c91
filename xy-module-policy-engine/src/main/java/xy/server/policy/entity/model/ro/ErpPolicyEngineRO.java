package xy.server.policy.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 决策表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPolicyEngineRO对象", description = "决策表")
public class ErpPolicyEngineRO {

    @ApiModelProperty(value = "PK")
    private String policyEngineGuid;

    @ApiModelProperty(value = "租户")
    private String tenantGuid;

    @NotBlank(message = "决策编码不能为空")
    @ApiModelProperty(value = "决策引擎编码")
    private String policyEngineCode;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @NotBlank(message = "触发类型，1：定时触发、2：API同步触发，3:API异步触发，4:事件触发不能为空")
    @ApiModelProperty(value = "触发类型，1：定时触发、2：API同步触发，3:API异步触发，4:事件触发")
    private String triggerType;

    @NotBlank(message = "决策名称不能为空")
    @ApiModelProperty(value = "决策名称")
    private String policyEngineName;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "入参JSON")
    private Object paramsJson;

    @NotNull(message = "状态不能为空")
    @ApiModelProperty(value = "状态")
    private Boolean status;

    @ApiModelProperty(value = "x6FlowJson数据")
    private String flowJson;

    @ApiModelProperty(value = "决策分类")
    private String category;

    @Valid
    @ApiModelProperty(value = "定时触发配置Obj")
    private ErpPolicyEngineTimingConfigRO policyEngineTimingConfigObj;

    @NotEmpty
    @Valid
    @ApiModelProperty(value = "决策流程边列表")
    private List<ErpPolicyEngineEdgeRO> policyEngineEdgeList;

    @NotEmpty
    @Valid
    @ApiModelProperty(value = "决策流程节点列表")
    private List<ErpPolicyEngineNodeRO> policyEngineNodeList;

    @Valid
    @ApiModelProperty(value = "决策流程执行变量")
    private List<ErpPolicyEngineVariablesRO> policyEngineVariables;

}