package xy.server.policy.entity.message.wechatwork.robot;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import xy.server.policy.entity.message.base.BaseMessage;

import java.util.List;

/**
 * 图片、文件消息DTO
 *
 * <AUTHOR>
 * @since 2021/4/8/008 21:38
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ImageMessageDTO extends BaseMessage {
    private static final long serialVersionUID = 7412950115675650317L;

    @ApiModelProperty(value = "接收人分组列表")
    private List<Long> receiverGroupIds;

    @ApiModelProperty(value = "接收人列表")
    private List<String> receiverIds;

    @ApiModelProperty(value = "图片内容的base64编码")
    private String base64;

    @ApiModelProperty(value = "图片内容（base64编码前）的md5值")
    private String md5;

}
