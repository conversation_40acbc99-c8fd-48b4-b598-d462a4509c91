package xy.server.policy.entity.message.dingtalk.robot;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 按钮
 *
 * <AUTHOR>
 * @since 2021/4/11/011 14:59
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class BtnJsonDTO {

    @ApiModelProperty("按钮标题")
    private String title;
    @ApiModelProperty("点击按钮触发的URL")
    private String actionURL;

}
