package xy.server.policy.entity.message.wechatofficialaccount;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import xy.server.policy.entity.message.base.BaseMessage;

import java.util.List;

/**
 * 微信公众号模板消息
 *
 * <AUTHOR>
 * @since 2021/4/10/010 15:30
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TemplateMessageDTO extends BaseMessage {
    private static final long serialVersionUID = 2978534939532888543L;

    /**
     * 接收人分组列表
     */
    @ApiModelProperty(value = "接收人分组列表")
    private List<Long> receiverGroupIds;

    /**
     * 接收人列表
     */
    @ApiModelProperty(value = "接收人列表")
    private List<String> receiverIds;

    @ApiModelProperty("公众号模板id")
    private String wechatTemplateId;

    @ApiModelProperty("点击跳转链接")
    private String url;

    @ApiModelProperty("小程序appId")
    private String miniAppId;

    @ApiModelProperty("小程序页面路径")
    private String miniPagePath;

    @ApiModelProperty(value = "模板变量")
    private List<WechatTemplateData> templateDataList;


}
