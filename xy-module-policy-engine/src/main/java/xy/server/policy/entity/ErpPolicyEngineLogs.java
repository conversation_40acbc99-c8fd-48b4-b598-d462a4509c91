package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 决策执行日志
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_policy_engine_logs")
public class ErpPolicyEngineLogs implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("policy_engine_logs_guid")
    private String policyEngineLogsGuid;
    /**
     * 决策流程_guid
     */
    @TableField("policy_engine_guid")
    private String policyEngineGuid;
    /**
     * 决策_节点_guid
     */
    @TableField("policy_engine_node_guid")
    private String policyEngineNodeGuid;
    /**
     * 操作人
     */
    @TableField("operator")
    private String operator;
    /**
     * 操作状态
     */
    @TableField("operator_status")
    private String operatorStatus;
    /**
     * 操作日期
     */
    @TableField("operator_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operatorDate;
    /**
     * 操作结果
     */
    @TableField("operator_result")
    private String operatorResult;


}
