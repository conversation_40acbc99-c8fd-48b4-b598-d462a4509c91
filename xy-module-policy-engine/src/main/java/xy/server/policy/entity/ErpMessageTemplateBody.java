package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 消息通知模板内容
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_message_template_body")
public class ErpMessageTemplateBody implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 租户GUID
     */
    @TableField(value = "tenant_guid", fill = FieldFill.INSERT)
    private String tenantGuid;
    /**
     * 消息模板_guid
     */
    @TableField("message_template_guid")
    private String messageTemplateGuid;
    /**
     * PK
     */
    @TableId("message_template_body_guid")
    private String messageTemplateBodyGuid;
    /**
     * 消息模板内容类型,字典，1、内容项 2、跳转链接
     */
    @TableField("message_template_body_type")
    private String messageTemplateBodyType;
    /**
     * 消息模板label
     */
    @TableField("message_template_body_label")
    private String messageTemplateBodyLabel;
    /**
     * 消息模板内容字段userName
     */
    @TableField("message_template_body_field")
    private String messageTemplateBodyField;
    /**
     * 消息模版内容${userName}
     */
    @TableField("message_template_body_content")
    private String messageTemplateBodyContent;
    /**
     * 启用状态
     */
    @TableField("status")
    private Boolean status;
    /**
     * 排序
     */
    @TableField("serial_number")
    private Integer serialNumber;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;


}
