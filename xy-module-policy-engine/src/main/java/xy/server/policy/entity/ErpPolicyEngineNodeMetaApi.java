package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 决策节点组件属性_API请求
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_policy_engine_node_meta_api", autoResultMap = true)
public class ErpPolicyEngineNodeMetaApi implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 决策_节点_guid
     */
    @TableField("policy_engine_node_guid")
    private String policyEngineNodeGuid;
    /**
     * PK
     */
    @TableId("policy_engine_node_meta_guid")
    private String policyEngineNodeMetaGuid;
    /**
     * 请求地址
     */
    @TableField("url")
    private String url;
    /**
     * 请求方式
     */
    @TableField("method")
    private String method;
    /**
     * 请求头参数
     */
    @TableField(value = "header_param", typeHandler = JsonbTypeHandler.class)
    private Object headerParam;
    /**
     * 请求体参数
     */
    @TableField(value = "body_param", typeHandler = JsonbTypeHandler.class)
    private Object bodyParam;


}
