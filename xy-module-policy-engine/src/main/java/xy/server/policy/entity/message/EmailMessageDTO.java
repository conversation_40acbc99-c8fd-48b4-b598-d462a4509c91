package xy.server.policy.entity.message;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import xy.server.policy.entity.message.base.BaseMessage;

import java.util.List;

/**
 * 邮件消息
 *
 * <AUTHOR>
 * @date 2021/2/8 10:00
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EmailMessageDTO extends BaseMessage {
    private static final long serialVersionUID = 2692273549631779696L;

    @ApiModelProperty(value = "接收人分组列表")
    private List<Long> receiverGroupIds;

    @ApiModelProperty(value = "接收人列表")
    private List<String> receiverIds;

    @ApiModelProperty(value = "请输入邮箱标题...")
    private String title;

    @ApiModelProperty(value = "请输入邮箱内容...")
    private String content;

}
