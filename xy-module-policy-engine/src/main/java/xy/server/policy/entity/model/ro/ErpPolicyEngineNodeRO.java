package xy.server.policy.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 决策节点流程RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPolicyEngineNodeRO对象", description = "决策节点流程")
public class ErpPolicyEngineNodeRO {

    @NotBlank
    @ApiModelProperty(value = "PK")
    private String policyEngineNodeGuid;

    @ApiModelProperty(value = "决策流程_guid")
    private String policyEngineGuid;

    @NotBlank(message = "节点组件名称不能为空")
    @ApiModelProperty(value = "节点组件名称")
    private String component;

    @NotBlank(message = "节点类型不能为空")
    @ApiModelProperty(value = "节点类型")
    private String componentType;

    @NotNull(message = "节点位置-x不能为空")
    @ApiModelProperty(value = "节点位置-x")
    private Integer positionX;

    @NotNull(message = "节点位置-y不能为空")
    @ApiModelProperty(value = "节点位置-y")
    private Integer positionY;

    @ApiModelProperty(value = "节点描述")
    private String description;

    @Valid
    @ApiModelProperty(value = "决策节点组件属性_SQL对象")
    private ErpPolicyEngineNodeMetaSqlRO policyEngineNodeMetaSqlObj;

    @Valid
    @ApiModelProperty(value = "决策节点组件属性_消息通知对象")
    private ErpPolicyEngineNodeMetaNotifyRO policyEngineNodeMetaNotifyObj;

    @Valid
    @ApiModelProperty(value = "决策节点组件属性_事件广播对象")
    private ErpPolicyEngineNodeMetaBroadcastRO policyEngineNodeMetaBroadcastObj;

    @Valid
    @ApiModelProperty(value = "决策节点组件属性_API请求对象")
    private ErpPolicyEngineNodeMetaApiRO policyEngineNodeMetaApiObj;

    @Valid
    @ApiModelProperty(value = "决策节点组件属性_延迟队列对象")
    private ErpPolicyEngineNodeMetaDelayRO policyEngineNodeMetaDelayObj;

}