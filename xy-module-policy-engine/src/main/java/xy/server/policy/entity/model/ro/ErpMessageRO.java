package xy.server.policy.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 消息通知RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMessageRO对象", description = "消息通知")
public class ErpMessageRO {

    @ApiModelProperty(value = "消息通知PK")
    private String messageGuid;

    @NotBlank(message = "消息通知途径,字典,1、站内，2、微信通知，3、企业微信，4、钉钉，5飞书不能为空")
    @ApiModelProperty(value = "消息通知途径,字典,1、站内，2、微信通知，3、企业微信，4、钉钉，5飞书")
    private String messageWay;

    @ApiModelProperty(value = "消息模板")
    private String messageTemplateGuid;

    @NotBlank(message = "消息通知标题不能为空")
    @ApiModelProperty(value = "消息通知标题")
    private String messageTitle;

    @ApiModelProperty(value = "消息通知封面")
    private String messageCover;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "发送时间不能为空")
    @ApiModelProperty(value = "发送时间")
    private LocalDateTime sendTime;

    @ApiModelProperty(value = "发送状态，1成功，0失败")
    private String status;

    @NotEmpty
    @Valid
    @ApiModelProperty(value = "消息内容列表")
    private List<ErpMessageBodyRO> messageBodyList;

    @ApiModelProperty(value = "要删除的内容guids")
    private List<String> delMessageBodyGuids;

    @NotEmpty
    @ApiModelProperty(value = "收件人userGuids")
    private List<String> userGuids;

}