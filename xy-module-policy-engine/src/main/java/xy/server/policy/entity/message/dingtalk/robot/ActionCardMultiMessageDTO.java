package xy.server.policy.entity.message.dingtalk.robot;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import xy.server.policy.entity.message.base.BaseMessage;

import java.util.List;

/**
 * 钉钉群消息-卡片消息-独立跳转类型DTO
 *
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ActionCardMultiMessageDTO extends BaseMessage {
    private static final long serialVersionUID = -3289428483627765265L;

    @ApiModelProperty(value = "接收人分组列表")
    private List<Long> receiverGroupIds;

    @ApiModelProperty(value = "是否@所有人")
    private Boolean isAtAll;

    @ApiModelProperty(value = "接收人列表")
    private List<String> receiverIds;

    @ApiModelProperty(value = "首屏会话透出的展示内容")
    private String title;

    @ApiModelProperty(value = "markdown格式的消息")
    private String text;

    @ApiModelProperty("使用独立跳转ActionCard样式时的按钮排列方式,0:按钮竖直排列,1:按钮横向排列")
    private String btnOrientation = "0";

    @ApiModelProperty("按钮")
    private List<BtnJsonDTO> btns;

}
