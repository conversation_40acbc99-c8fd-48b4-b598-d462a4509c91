package xy.server.policy.entity.message.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 企业微信配置
 *
 * <AUTHOR>
 * @since 2021/3/16/016 10:46
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WechatWorkAgentConfig extends BaseConfig {
    private static final long serialVersionUID = -9206902816158196669L;

    @ApiModelProperty(value = "企业ID 在此页面查看：https://work.weixin.qq.com/wework_admin/frame#profile")
    private String corpId;
    @ApiModelProperty(value = "应用Secret")
    private String secret;
    @ApiModelProperty(value = "应用agentId")
    private Integer agentId;

}
