package xy.server.policy.entity.message.wechatofficialaccount;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import xy.server.policy.entity.message.base.BaseMessage;

import java.util.List;

/**
 * 微信公众号图文消息发送DTO
 *
 * <AUTHOR>
 * @since 2021/4/7/007 17:30
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class NewsMessageDTO extends BaseMessage {
    private static final long serialVersionUID = 7034106110120563906L;

    @ApiModelProperty(value = "接收人分组列表")
    private List<Long> receiverGroupIds;

    @ApiModelProperty(value = "接收人列表")
    private List<String> receiverIds;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "描述，超过512个字节，超过会自动截断")
    private String description;

    @ApiModelProperty(value = "点击跳转链接")
    private String url;

    @ApiModelProperty(value = "图片链接，图文消息的图片链接，支持JPG、PNG格式，较好的效果为大图1068*455，小图150*150。")
    private String picUrl;

}
