package xy.server.policy.entity.message.base;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-01-25 15:46
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BaseMessage implements Serializable {

    private static final long serialVersionUID = 2441017514225328221L;
    /**
     * 配置
     */
    private List<String> configIds;
}
