package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 决策表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_policy_engine", autoResultMap = true)
public class ErpPolicyEngine implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("policy_engine_guid")
    private String policyEngineGuid;

    /**
     * 租户
     */
    @TableField(value = "tenant_guid", fill = FieldFill.INSERT)
    private String tenantGuid;
    /**
     * 决策引擎编码
     */
    @TableField("policy_engine_code")
    private String policyEngineCode;
    /**
     * 顺序号(每一个层级有对应的顺序号)
     */
    @TableField("serial_number")
    private Integer serialNumber;
    /**
     * 触发类型，1：定时触发、2：API同步触发，3:API异步触发，4:事件触发
     */
    @TableField("trigger_type")
    private String triggerType;
    /**
     * 决策名称
     */
    @TableField("policy_engine_name")
    private String policyEngineName;
    /**
     * 备注
     */
    @TableField("description")
    private String description;
    /**
     * 入参JSON
     */
    @TableField(value = "params_json", typeHandler = JsonbTypeHandler.class)
    private Object paramsJson;
    /**
     * 状态
     */
    @TableField("status")
    private Boolean status;

    /**
     * x6FlowJson数据
     */
    @TableField("flow_json")
    private String flowJson;

    /**
     * 决策分类
     */
    @TableField("category")
    private String category;

    /**
     * 创建人GUID
     */
    @TableField(value = "creator_guid", fill = FieldFill.INSERT)
    private String creatorGuid;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建日期
     */
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    private LocalDateTime createDate;

    /**
     * 最后修改人GUID
     */
    @TableField(value = "last_updater_guid", fill = FieldFill.INSERT_UPDATE)
    private String lastUpdaterGuid;

    /**
     * 最后修改人
     */
    @TableField(value = "last_updater", fill = FieldFill.INSERT_UPDATE)
    private String lastUpdater;

    /**
     * 最后修改日期
     */
    @TableField(value = "last_update_date", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastUpdateDate;


}
