package xy.server.policy.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 消息通知模板QO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMessageTemplateQO对象", description = "消息通知模板")
public class ErpMessageTemplateQO {

    @ApiModelProperty(value = "消息模板类型,字典（多选）")
    private List<String> messageTemplateTypes;

    @ApiModelProperty(value = "消息模板标题")
    private String messageTemplateTitle;

    @ApiModelProperty(value = "消息模板封面")
    private String messageTemplateCover;

    @ApiModelProperty(value = "启用状态")
    private Boolean status;

}