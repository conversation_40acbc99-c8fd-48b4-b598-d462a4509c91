package xy.server.policy.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @data 2024-01-15 16:28
 * @apiNote Api请求参数实体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ApiRequestParams对象", description = "Api请求参数实体")
public class ApiRequestParam {

    @ApiModelProperty(value = "参数字段")
    private String key;

    @ApiModelProperty(value = "参数值")
    private String value;
}
