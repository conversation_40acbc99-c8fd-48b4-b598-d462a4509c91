package xy.server.policy.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 消息通知模板内容VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMessageTemplateBodyVO对象", description = "消息通知模板内容")
public class ErpMessageTemplateBodyVO {

    @ApiModelProperty(value = "消息模板_guid")
    private String messageTemplateGuid;

    @ApiModelProperty(value = "PK")
    private String messageTemplateBodyGuid;

    @ApiModelProperty(value = "消息模板内容类型,字典，1、内容项 2、跳转链接")
    private String messageTemplateBodyType;

    @ApiModelProperty(value = "消息模板label")
    private String messageTemplateBodyLabel;

    @ApiModelProperty(value = "消息模板内容字段userName")
    private String messageTemplateBodyField;

    @ApiModelProperty(value = "消息模版内容${userName}")
    private String messageTemplateBodyContent;

    @ApiModelProperty(value = "启用状态")
    private Boolean status;

    @ApiModelProperty(value = "排序")
    private Integer serialNumber;

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

}