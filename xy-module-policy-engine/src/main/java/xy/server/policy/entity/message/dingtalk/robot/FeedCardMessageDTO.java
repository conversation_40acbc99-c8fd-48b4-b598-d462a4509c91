package xy.server.policy.entity.message.dingtalk.robot;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;
import xy.server.policy.entity.message.base.BaseMessage;

import java.util.List;

/**
 * 钉钉群消息FeedCard类型DTO
 *
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class FeedCardMessageDTO extends BaseMessage {
    private static final long serialVersionUID = -3289428483627765265L;

    @ApiModelProperty("接收人分组列表")
    private List<Long> receiverGroupIds;

    @ApiModelProperty("是否@所有人")
    private Boolean isAtAll;

    @ApiModelProperty("接收人列表")
    private List<String> receiverIds;

    @ApiModelProperty("多条信息设置")
    private List<Item> items;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Item {
        @ApiModelProperty("单条信息文本")
        private String title;
        @ApiModelProperty("点击单条信息的链接")
        private String messageURL;
        @ApiModelProperty("单条信息图片的URL")
        private String picURL;
    }

}
