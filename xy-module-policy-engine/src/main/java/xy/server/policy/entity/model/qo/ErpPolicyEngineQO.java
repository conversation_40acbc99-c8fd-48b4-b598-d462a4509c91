package xy.server.policy.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 决策表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPolicyEngineQO对象", description = "决策表")
public class ErpPolicyEngineQO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String policyEngineGuid;

    @ApiModelProperty(value = "决策引擎编码")
    private String policyEngineCode;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "触发类型，1：定时触发、2：API触发同步，3:API触发异步，4:事件触发")
    private String triggerType;

    @ApiModelProperty(value = "决策名称")
    private String policyEngineName;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "入参JSON")
    private String paramsJson;

    @ApiModelProperty(value = "状态")
    private Boolean status;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "关键字列表")
    private List<String> keywords;

    @ApiModelProperty(value = "分类")
    private String category;

}