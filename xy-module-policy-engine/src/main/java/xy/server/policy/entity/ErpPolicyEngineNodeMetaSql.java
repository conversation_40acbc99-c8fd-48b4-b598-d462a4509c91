package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 决策节点组件属性_SQL
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_policy_engine_node_meta_sql")
public class ErpPolicyEngineNodeMetaSql implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 决策_节点_guid
     */
    @TableField("policy_engine_node_guid")
    private String policyEngineNodeGuid;
    /**
     * PK
     */
    @TableId("policy_engine_node_meta_guid")
    private String policyEngineNodeMetaGuid;
    /**
     * sql语句
     */
    @TableField("sql")
    private String sql;
    /**
     * 预期执行结果类型，1:单行，2:集合
     */
    @TableField("resut_type")
    private String resutType;


}
