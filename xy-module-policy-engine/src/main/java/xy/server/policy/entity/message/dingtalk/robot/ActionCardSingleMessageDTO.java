package xy.server.policy.entity.message.dingtalk.robot;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import xy.server.policy.entity.message.base.BaseMessage;

import java.util.List;

/**
 * 钉钉群消息-卡片消息-整体跳转类型DTO
 *
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ActionCardSingleMessageDTO extends BaseMessage {
    private static final long serialVersionUID = -3289428483627765265L;

    @ApiModelProperty(value = "接收人分组列表")
    private List<Long> receiverGroupIds;

    @ApiModelProperty(value = "是否@所有人")
    private Boolean isAtAll;

    @ApiModelProperty(value = "接收人列表")
    private List<String> receiverIds;

    @ApiModelProperty(value = "首屏会话透出的展示内容")
    private String title;

    @ApiModelProperty(value = "markdown格式的消息")
    private String text;

    @ApiModelProperty(value = "单个按钮的标题")
    private String singleTitle;

    @ApiModelProperty(value = "点击singleTitle按钮触发的URL")
    private String singleURL;

}
