package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 消息通知模板
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_message_template")
public class ErpMessageTemplate implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 租户GUID
     */
    @TableField(value = "tenant_guid", fill = FieldFill.INSERT)
    private String tenantGuid;
    /**
     * 消息模板PK
     */
    @TableId("message_template_guid")
    private String messageTemplateGuid;
    /**
     * 消息模板编码
     */
    @TableField("message_template_code")
    private String messageTemplateCode;
    /**
     * 消息模板类型,字典
     */
    @TableField("message_template_type")
    private String messageTemplateType;
    /**
     * 消息模板标题
     */
    @TableField("message_template_title")
    private String messageTemplateTitle;
    /**
     * 消息模板封面
     */
    @TableField("message_template_cover")
    private String messageTemplateCover;
    /**
     * 启用状态
     */
    @TableField("status")
    private Boolean status;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;


}
