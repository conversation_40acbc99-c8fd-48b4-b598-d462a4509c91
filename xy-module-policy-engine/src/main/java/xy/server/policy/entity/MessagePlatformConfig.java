package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 消息平台配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "message_platform_config")
public class MessagePlatformConfig extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId("message_platform_config_guid")
    private String messagePlatformConfigGuid;
    /**
     * 平台
     */
    @TableField("platform")
    private String platform;
    /**
     * 显示名称
     */
    @TableField("message_platform_config_name")
    private String messagePlatformConfigName;
    /**
     * 是否默认
     */
    @TableField("is_default")
    private Boolean isDefault;

    /**
     * 状态
     */
    @TableField("status")
    private Boolean status;


}
