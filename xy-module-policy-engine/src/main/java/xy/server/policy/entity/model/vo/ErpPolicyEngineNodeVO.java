package xy.server.policy.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 决策节点流程VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpPolicyEngineNodeVO对象", description = "决策节点流程")
public class ErpPolicyEngineNodeVO implements Serializable {

    @ApiModelProperty(value = "PK")
    private String policyEngineNodeGuid;

    @ApiModelProperty(value = "决策流程_guid")
    private String policyEngineGuid;

    @ApiModelProperty(value = "节点组件名称")
    private String component;

    @ApiModelProperty(value = "节点类型")
    private String componentType;

    @XyTrans(dictionaryKey = "POLICY_ENGINE_NODE_COMPONENT_TYPE", dictionaryValue = "componentType")
    @ApiModelProperty(value = "节点类型字典值")
    private String componentTypeDictValue;

    @ApiModelProperty(value = "节点位置-x")
    private Integer positionX;

    @ApiModelProperty(value = "节点位置-y")
    private Integer positionY;

    @ApiModelProperty(value = "节点描述")
    private String description;

    @ApiModelProperty(value = "决策节点组件属性_SQL对象")
    private ErpPolicyEngineNodeMetaSqlVO policyEngineNodeMetaSqlObj;

    @ApiModelProperty(value = "决策节点组件属性_消息通知对象")
    private ErpPolicyEngineNodeMetaNotifyVO policyEngineNodeMetaNotifyObj;

    @ApiModelProperty(value = "决策节点组件属性_事件广播对象")
    private ErpPolicyEngineNodeMetaBroadcastVO policyEngineNodeMetaBroadcastObj;

    @ApiModelProperty(value = "决策节点组件属性_API请求对象")
    private ErpPolicyEngineNodeMetaApiVO policyEngineNodeMetaApiObj;

    @ApiModelProperty(value = "决策节点组件属性_延时队列对象")
    private ErpPolicyEngineNodeMetaDelayVO policyEngineNodeMetaDelayObj;

}
