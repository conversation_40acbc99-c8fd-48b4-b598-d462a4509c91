package xy.server.policy.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 决策流程执行变量RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPolicyEngineVariablesRO对象", description = "决策流程执行变量")
public class ErpPolicyEngineVariablesRO {

    @ApiModelProperty(value = "PK")
    private String policyEngineVariablesGuid;

    @ApiModelProperty(value = "决策流程_guid")
    private String policyEngineGuid;

    @ApiModelProperty(value = "决策_节点_guid")
    private String policyEngineNodeGuid;

    @NotBlank(message = "变量类型,1全局，2节点不能为空")
    @ApiModelProperty(value = "变量类型,1全局，2节点")
    private String variablesType;

    @NotBlank(message = "变量名称不能为空")
    @ApiModelProperty(value = "变量名称")
    private String variablesName;

    @ApiModelProperty(value = "变量标识（字段名称）")
    private String variablesSymbol;

    @ApiModelProperty(value = "变量值")
    private String variablesValue;

    @ApiModelProperty(value = "变量执行")
    private String variablesExecute;

    @ApiModelProperty(value = "变量类型")
    private String resultType;

}