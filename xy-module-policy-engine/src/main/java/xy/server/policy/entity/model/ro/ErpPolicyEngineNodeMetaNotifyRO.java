package xy.server.policy.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 决策节点组件属性_消息通知RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPolicyEngineNodeMetaNotifyRO对象", description = "决策节点组件属性_消息通知")
public class ErpPolicyEngineNodeMetaNotifyRO {

    @ApiModelProperty(value = "决策_节点_guid")
    private String policyEngineNodeGuid;

    @ApiModelProperty(value = "PK")
    private String policyEngineNodeMetaGuid;

    @ApiModelProperty(value = "消息模板")
    private String messageTemplateGuid;

    @ApiModelProperty(value = "接收人列表")
    private String consigneeGuids;

    @ApiModelProperty(value = "接收角色")
    private String roles;

    @NotNull(message = "发送方式，站内信，公众号，企业微信，钉钉，邮件等不能为空")
    @ApiModelProperty(value = "发送方式，站内信，公众号，企业微信，钉钉，邮件等")
    private String messageTypes;

    @ApiModelProperty(value = "标题")
    private String messageTitle;

    @ApiModelProperty(value = "消息内容")
    private String messageContent;

    @ApiModelProperty(value = "消息平台guid（多个用,号分隔）")
    private String messagePlatformGuids;

}