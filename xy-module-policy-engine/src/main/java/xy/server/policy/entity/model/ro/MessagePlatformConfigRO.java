package xy.server.policy.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.policy.entity.model.vo.MessagePlatformConfigValueVO;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 消息平台配置表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MessagePlatformConfigRO对象", description = "消息平台配置表")
public class MessagePlatformConfigRO extends BaseEntity {

    @ApiModelProperty(value = "")
    private String messagePlatformConfigGuid;

    @NotNull(message = "平台不能为空")
    @ApiModelProperty(value = "平台")
    private String platform;

    @ApiModelProperty(value = "显示名称")
    private String messagePlatformConfigName;

    @ApiModelProperty(value = "是否默认")
    private Boolean isDefault;

    @ApiModelProperty(value = "状态")
    private Boolean status;

    @ApiModelProperty(value = "配置值列表")
    private List<MessagePlatformConfigValueVO> configValueList;

}
