package xy.server.policy.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <p>
 * 消息通知模板RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMessageTemplateRO对象", description = "消息通知模板")
public class ErpMessageTemplateRO {

    @ApiModelProperty(value = "消息模板PK")
    private String messageTemplateGuid;

    @NotBlank
    @ApiModelProperty(value = "消息模板编码")
    private String messageTemplateCode;

    @NotBlank(message = "消息模板类型,字典不能为空")
    @ApiModelProperty(value = "消息模板类型,字典")
    private String messageTemplateType;

    @NotBlank(message = "消息模板标题不能为空")
    @ApiModelProperty(value = "消息模板标题")
    private String messageTemplateTitle;

    @ApiModelProperty(value = "消息模板封面")
    private String messageTemplateCover;

    @ApiModelProperty(value = "启用状态")
    private Boolean status;

    @NotEmpty
    @Valid
    @ApiModelProperty(value = "模板内容列表")
    private List<ErpMessageTemplateBodyRO> templateBodyList;

    @ApiModelProperty(value = "要删除的模板内容guids")
    private List<String> delTemplateBodyGuids;

}