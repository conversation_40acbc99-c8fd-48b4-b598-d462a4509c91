package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 决策流程执行变量
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_policy_engine_variables")
public class ErpPolicyEngineVariables implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("policy_engine_variables_guid")
    private String policyEngineVariablesGuid;
    /**
     * 决策流程_guid
     */
    @TableField("policy_engine_guid")
    private String policyEngineGuid;
    /**
     * 决策_节点_guid
     */
    @TableField("policy_engine_node_guid")
    private String policyEngineNodeGuid;
    /**
     * 变量类型,1全局，2节点
     */
    @TableField("variables_type")
    private String variablesType;
    /**
     * 变量名称
     */
    @TableField("variables_name")
    private String variablesName;
    /**
     * 变量标识（字段名称）
     */
    @TableField("variables_symbol")
    private String variablesSymbol;
    /**
     * 变量值
     */
    @TableField("variables_value")
    private String variablesValue;
    /**
     * 变量执行
     */
    @TableField("variables_execute")
    private String variablesExecute;

    /**
     * 变量类型
     */
    @TableField("result_type")
    private String resultType;




}
