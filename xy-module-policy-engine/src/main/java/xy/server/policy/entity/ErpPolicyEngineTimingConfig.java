package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 定时触发配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_policy_engine_timing_config")
public class ErpPolicyEngineTimingConfig implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 决策_guid
     */
    @TableField("policy_engine_guid")
    private String policyEngineGuid;
    /**
     * PK
     */
    @TableId("erp_policy_engine_timing_config_guid")
    private String erpPolicyEngineTimingConfigGuid;
    /**
     * cron表达式
     */
    @TableField("cron_expression")
    private String cronExpression;
    /**
     * 开始时间：必须指定
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    /**
     * 结束时间：为空不结束
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    /**
     * 定时开关
     */
    @TableField("status")
    private Boolean status;


}
