package xy.server.policy.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 决策节点组件属性_事件广播RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPolicyEngineNodeMetaBroadcastRO对象", description = "决策节点组件属性_事件广播")
public class ErpPolicyEngineNodeMetaBroadcastRO {

    @ApiModelProperty(value = "决策_节点_guid")
    private String policyEngineNodeGuid;

    @ApiModelProperty(value = "PK")
    private String policyEngineNodeMetaGuid;

    @NotNull(message = "广播名称不能为空")
    @ApiModelProperty(value = "广播名称")
    private String broadcastTopic;

    @NotNull(message = "广播内容不能为空")
    @ApiModelProperty(value = "广播内容")
    private String broadcastContent;

    @ApiModelProperty(value = "接收人")
    private String consigneeGuids;

    @ApiModelProperty(value = "接收角色")
    private String roles;

    @ApiModelProperty(value = "接收部门")
    private String department;

    @ApiModelProperty(value = "广播等级")
    private String broadcastLevel;

    @ApiModelProperty(value = "标记")
    private String flag;

}
