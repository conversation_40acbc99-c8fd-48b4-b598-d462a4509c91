package xy.server.policy.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 消息通知内容VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMessageBodyVO对象", description = "消息通知内容")
public class ErpMessageBodyVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "消息通知_guid")
    private String messageGuid;

    @ApiModelProperty(value = "PK")
    private String messageBodyGuid;

    @ApiModelProperty(value = "消息模板内容类型,字典，1、内容项 2、跳转链接")
    private String messageBodyType;

    @ApiModelProperty(value = "消息模板label")
    private String messageBodyLabel;

    @ApiModelProperty(value = "消息模版内容${userName}")
    private String messageBodyContent;

    @ApiModelProperty(value = "排序")
    private Integer serialNumber;

}