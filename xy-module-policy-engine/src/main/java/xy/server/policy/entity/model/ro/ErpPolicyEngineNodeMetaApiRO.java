package xy.server.policy.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 决策节点组件属性_API请求RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPolicyEngineNodeMetaApiRO对象", description = "决策节点组件属性_API请求")
public class ErpPolicyEngineNodeMetaApiRO {

    @ApiModelProperty(value = "决策_节点_guid")
    private String policyEngineNodeGuid;

    @ApiModelProperty(value = "PK")
    private String policyEngineNodeMetaGuid;

    @NotNull(message = "请求地址不能为空")
    @ApiModelProperty(value = "请求地址")
    private String url;

    @NotNull(message = "请求方式不能为空")
    @ApiModelProperty(value = "请求方式")
    private String method;

    @ApiModelProperty(value = "请求头参数")
    private Object headerParam;

    @ApiModelProperty(value = "请求体参数")
    private Object bodyParam;

}