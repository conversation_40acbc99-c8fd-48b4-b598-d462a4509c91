package xy.server.policy.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 决策表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpPolicyEngineVO对象", description = "决策表")
public class ErpPolicyEngineVO implements Serializable {

    @ApiModelProperty(value = "PK")
    private String policyEngineGuid;

    @ApiModelProperty(value = "租户")
    private String tenantGuid;

    @ApiModelProperty(value = "决策引擎编码")
    private String policyEngineCode;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "触发类型，1：定时触发、2：API同步触发，3:API异步触发，4:事件触发")
    private String triggerType;

    @XyTrans(dictionaryKey = "POLICY_ENGINE_TRIGGER_TYPE", dictionaryValue = "triggerType")
    @ApiModelProperty(value = "触发类型字典值")
    private String triggerTypeDictValue;

    @ApiModelProperty(value = "决策名称")
    private String policyEngineName;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "入参JSON")
    private Object paramsJson;

    @ApiModelProperty(value = "状态")
    private Boolean status;

    @ApiModelProperty(value = "x6FlowJson数据")
    private String flowJson;

    @ApiModelProperty(value = "决策分类")
    private String category;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "定时触发配置Obj")
    private ErpPolicyEngineTimingConfigVO policyEngineTimingConfigObj;

    @ApiModelProperty(value = "决策流程边列表")
    private List<ErpPolicyEngineEdgeVO> policyEngineEdgeList;

    @XyTransCycle
    @ApiModelProperty(value = "决策流程节点列表")
    private List<ErpPolicyEngineNodeVO> policyEngineNodeList;

    @XyTransCycle
    @ApiModelProperty(value = "决策流程执行变量")
    private List<ErpPolicyEngineVariablesVO> policyEngineVariables;

}
