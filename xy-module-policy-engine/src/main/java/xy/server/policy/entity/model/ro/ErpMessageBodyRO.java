package xy.server.policy.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 消息通知内容RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMessageBodyRO对象", description = "消息通知内容")
public class ErpMessageBodyRO {

    @ApiModelProperty(value = "消息通知_guid")
    private String messageGuid;

    @ApiModelProperty(value = "PK")
    private String messageBodyGuid;

    @NotBlank(message = "消息模板内容类型,字典，1、内容项 2、跳转链接不能为空")
    @ApiModelProperty(value = "消息模板内容类型,字典，1、内容项 2、跳转链接")
    private String messageBodyType;

    @NotBlank(message = "消息模板label不能为空")
    @ApiModelProperty(value = "消息模板label")
    private String messageBodyLabel;

    @NotBlank(message = "消息模版内容${userName}不能为空")
    @ApiModelProperty(value = "消息模版内容${userName}")
    private String messageBodyContent;

    @NotNull(message = "排序不能为空")
    @ApiModelProperty(value = "排序")
    private Integer serialNumber;

}