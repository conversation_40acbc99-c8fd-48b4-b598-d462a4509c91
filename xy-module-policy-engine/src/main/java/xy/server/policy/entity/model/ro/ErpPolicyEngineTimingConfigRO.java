package xy.server.policy.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <p>
 * 定时触发配置RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPolicyEngineTimingConfigRO对象", description = "定时触发配置")
public class ErpPolicyEngineTimingConfigRO {

    @ApiModelProperty(value = "决策_guid")
    private String policyEngineGuid;

    @ApiModelProperty(value = "PK")
    private String erpPolicyEngineTimingConfigGuid;

    @NotBlank(message = "cron表达式不能为空")
    @ApiModelProperty(value = "cron表达式")
    private String cronExpression;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //@NotNull(message = "开始时间：必须指定不能为空")
    @ApiModelProperty(value = "开始时间：必须指定")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间：为空不结束")
    private LocalDateTime endTime;

    @NotNull(message = "定时开关不能为空")
    @ApiModelProperty(value = "定时开关")
    private Boolean status;

}