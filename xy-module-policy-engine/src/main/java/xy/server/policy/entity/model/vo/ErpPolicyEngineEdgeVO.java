package xy.server.policy.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 决策流程边VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPolicyEngineEdgeVO对象", description = "决策流程边")
public class ErpPolicyEngineEdgeVO implements Serializable {

    @ApiModelProperty(value = "PK")
    private String policyEngineEdgeGuid;

    @ApiModelProperty(value = "决策流程_guid")
    private String policyEngineGuid;

    @ApiModelProperty(value = "来源节点_guid")
    private String sourceNodeGuid;

    @ApiModelProperty(value = "目标节点_guid")
    private String targetNodeGuid;

    @ApiModelProperty(value = "条件表达式")
    private String expression;

}
