package xy.server.policy.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 决策节点组件属性_事件广播VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPolicyEngineNodeMetaBroadcastVO对象", description = "决策节点组件属性_事件广播")
public class ErpPolicyEngineNodeMetaBroadcastVO implements Serializable {

    @ApiModelProperty(value = "决策_节点_guid")
    private String policyEngineNodeGuid;

    @ApiModelProperty(value = "PK")
    private String policyEngineNodeMetaGuid;

    @ApiModelProperty(value = "广播名称")
    private String broadcastTopic;

    @ApiModelProperty(value = "广播内容")
    private String broadcastContent;

    /**
     * 当前决策流程运算变量
     */
    @JsonIgnore
    private Map<String, String> variables;

    @ApiModelProperty(value = "接收人")
    private String consigneeGuids;

    @ApiModelProperty(value = "接收角色")
    private String roles;

    @ApiModelProperty(value = "接收部门")
    private String department;

    @ApiModelProperty(value = "广播等级")
    private String broadcastLevel;

    @ApiModelProperty(value = "标记")
    private String flag;

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

}
