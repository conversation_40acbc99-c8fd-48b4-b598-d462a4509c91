package xy.server.policy.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
* <p>
* VO
* </p>
*
* <AUTHOR>
* @since 2024-01-26
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "MessagePlatformConfigValueVO对象", description = "")
public class MessagePlatformConfigValueVO {

    @ApiModelProperty(value = "")
    private String tenantGuid;

    @ApiModelProperty(value = "")
    private String messagePlatformConfigValueGuid;

    @ApiModelProperty(value = "")
    private String messagePlatformConfigGuid;

    @ApiModelProperty(value = "参数键")
    private String key;

    @ApiModelProperty(value = "参数值")
    private String value;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

}
