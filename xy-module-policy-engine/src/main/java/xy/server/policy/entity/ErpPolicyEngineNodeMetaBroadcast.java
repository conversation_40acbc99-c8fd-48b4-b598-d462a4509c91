package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 决策节点组件属性_事件广播
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_policy_engine_node_meta_broadcast")
public class ErpPolicyEngineNodeMetaBroadcast implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 决策_节点_guid
     */
    @TableField("policy_engine_node_guid")
    private String policyEngineNodeGuid;
    /**
     * PK
     */
    @TableId("policy_engine_node_meta_guid")
    private String policyEngineNodeMetaGuid;
    /**
     * 广播名称
     */
    @TableField("broadcast_topic")
    private String broadcastTopic;
    /**
     * 广播内容
     */
    @TableField("broadcast_content")
    private String broadcastContent;

    /**
     * 接收人
     */
    @TableField("consignee_guids")
    private String consigneeGuids;

    /**
     * 接收角色
     */
    @TableField("roles")
    private String roles;

    /**
     * 接收部门
     */
    @TableField("department")
    private String department;

    /**
     * 广播等级
     */
    @TableField("broadcast_level")
    private String broadcastLevel;

    /**
     * 标记
     */
    @TableField("flag")
    private String flag;

    /**
     * 扩展字段
     */
    @TableField("to_json")
    private Object to_json;
}
