package xy.server.policy.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 决策流程边RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPolicyEngineEdgeRO对象", description = "决策流程边")
public class ErpPolicyEngineEdgeRO {

    @ApiModelProperty(value = "PK")
    private String policyEngineEdgeGuid;

    @ApiModelProperty(value = "决策流程_guid")
    private String policyEngineGuid;

    @NotBlank
    @ApiModelProperty(value = "来源节点_guid")
    private String sourceNodeGuid;

    @NotBlank
    @ApiModelProperty(value = "目标节点_guid")
    private String targetNodeGuid;

    @ApiModelProperty(value = "条件表达式")
    private String expression;

}