package xy.server.policy.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 消息通知内容
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_message_body")
public class ErpMessageBody implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 租户GUID
     */
    @TableField(value = "tenant_guid", fill = FieldFill.INSERT)
    private String tenantGuid;
    /**
     * 消息通知_guid
     */
    @TableField("message_guid")
    private String messageGuid;
    /**
     * PK
     */
    @TableId("message_body_guid")
    private String messageBodyGuid;
    /**
     * 消息模板内容类型,字典，1、内容项 2、跳转链接
     */
    @TableField("message_body_type")
    private String messageBodyType;
    /**
     * 消息模板label
     */
    @TableField("message_body_label")
    private String messageBodyLabel;
    /**
     * 消息模版内容${userName}
     */
    @TableField("message_body_content")
    private String messageBodyContent;
    /**
     * 排序
     */
    @TableField("serial_number")
    private Integer serialNumber;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;


}
