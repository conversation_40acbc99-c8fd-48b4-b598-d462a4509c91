package xy.server.policy.common.interfaces;

/**
 * <AUTHOR>
 * @date 2024/12/5
 * @description 预警接口
 **/
public interface EarlyWarningInterface {


    /**
     * 预警模块标识
     * @return
     */
    String getCategory();


    /**
     * 业务标识
     * @return
     */
    String getBusinessKey();


    /**
     * flag 预警标识;1-触发预警; 0-取消预警;
     * @return
     */
    String getFlag();

    /**
     * 广播主题
     * @return
     */
    String getTopic();



}
