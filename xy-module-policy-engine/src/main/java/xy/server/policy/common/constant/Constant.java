package xy.server.policy.common.constant;

/**
 * <AUTHOR>
 * @data 2024-03-18 17:43
 * @apiNote 决策引擎公共变量
 */
public interface Constant {
    /**
     * 决策引擎延时执行redis前缀
     */
    String NODE_META_DELAY_REDIS_KEY_PREFIX = "policyEngineDelayQueue_";

    /**
     * 决策引擎延时执行队列key
     */
    String POLICY_ENGINE_DELAY_EXECUTE_KEY = "policy_engine_delay_execute:";

    /**
     * 决策引擎延时执行队列key
     */
    String POLICY_ENGINE_DELAY_TASK_LIST = "policy_engine_delay_task_list:";

    /**
     * 决策引擎延时执行，决策Id:任务Id
     */
    String POLICY_ENGINE_DELAY_EXECUTE_KEY_MAP = "policy_engine:";


    /**
     * 获取决策根据code缓存key
     */
    String POLICY_GET_BY_CODE_CACHE_KEY = "policy_get_by_code_cache_key";
}
