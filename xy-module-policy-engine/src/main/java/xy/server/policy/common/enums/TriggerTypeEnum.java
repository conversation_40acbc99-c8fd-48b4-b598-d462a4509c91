package xy.server.policy.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @data 2024-01-08 14:09
 * @apiNote 决策引擎触发类型枚举
 */
@Getter
@AllArgsConstructor
public enum TriggerTypeEnum {
    TIMING_TRIGGER("1", "定时触发"),
    API_SYNCHRONIZATION_TRIGGER("2", "API同步触发"),
    API_ASYNCHRONOUS_TRIGGER("3", "API异步触发"),
    EVENT_TRIGGER("4", "事件触发"),
    ;

    private String code;

    private String msg;
}
