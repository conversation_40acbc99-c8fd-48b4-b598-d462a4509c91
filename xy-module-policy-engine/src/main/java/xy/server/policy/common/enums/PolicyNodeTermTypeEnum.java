package xy.server.policy.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @data 2023/8/29 11:08
 * @apiNote ActNodeTermVO对象中的type枚举(条件类型)
 */
@Getter
@AllArgsConstructor
public enum PolicyNodeTermTypeEnum {

    /**
     * 数字
     */
    NUMBER("1"),
    /**
     * 字符串
     */
    STRING("3"),
    /**
     * 时间
     */
    DATETIME("2");

    private final String type;
}
