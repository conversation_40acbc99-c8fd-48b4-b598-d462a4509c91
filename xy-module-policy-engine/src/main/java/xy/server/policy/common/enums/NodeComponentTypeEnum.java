package xy.server.policy.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @data 2024-01-08 15:49
 * @apiNote 决策流程节点组件类型枚举
 */
@Getter
@AllArgsConstructor
public enum NodeComponentTypeEnum {
    SQL("sql", "SQL查询"),
    MESSAGE_NOTIFICATION("notice", "消息通知"),
    PROCESS_PUSH_DOWN("flow", "流程下推"),
    API_REQUEST("api", "API请求"),
    BROADCAST("broadcast", "广播"),
    DELAY_EXPIRE("delay_expire", "延时到期"),
    DELAY_OVERDUE("delay_overdue", "延时超期期"),
    START("start", "开始"),
    END("end", "结束"),
    ;

    private String code;

    private String msg;

    public static NodeComponentTypeEnum getEumByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (NodeComponentTypeEnum enumItem : NodeComponentTypeEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem;
            }
        }
        return null;
    }
}
