package xy.server.policy.common.constant;

/**
 * <AUTHOR>
 * @date 2024/12/5
 * @description 预警相关常量
 **/
public interface EarlyWarningConstant {

    /**
     * 预警类型-字段
     */
    String EARLY_WARNING_CATEGORY_KEY = "category";


    /**
     * 预警标识-字段
     * 0-取消预警;1-触发预警;
     */
    String EARLY_WARNING_FLAG_KEY = "flag";

    /**
     * 开启触发预警
     */
    String EARLY_WARNING_FLAG_OPEN = "1";

    /**
     * 开启取消预警
     */
    String EARLY_WARNING_FLAG_CANCEL = "0";

    /**
     * 预警等级-字段
     */
    String EARLY_WARNING_LEVEL_KEY = "level";

    /**
     * 业务单据key字段
     */
    String EARLY_WARNING_BUSINESS_KEY = "businessKey";

    /**
     * 预警策略编码
     */
    String EARLY_WARNING_CODE = "7157709377541502";

    /**
     * 基础广播名称
     */
    String EARLY_WARNING_BASE_TOPIC = "EarlyWarning";
}
