package xy.server.policy.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/5
 * @description 预警枚举
 **/
@Getter
@AllArgsConstructor
public enum EarlyWarningEnum {

    PURCHASE_ORDER_OVERDUE("PURCH_OVERDUE", "采购超期订单"),

    FINISHED_GOODS_DWELLING_OVERDUE("FG_DWELL_OVERDUE", "成品呆滞天数逾期"),

    MATERIAL_DWELLING_OVERDUE("MAT_DWELL_OVERDUE", "物料呆滞天数逾期"),

    MATERIAL_STOCK_BELOW_MIN("MAT_STOCK_MIN", "物料库存低于最小库存"),

    MATERIAL_STOCK_ABOVE_MAX("MAT_STOCK_MAX", "物料库存高于最大库存"),

    FINISHED_GOODS_STOCK_BELOW_MIN("FG_STOCK_MIN", "成品库存低于最小库存"),

    FINISHED_GOODS_STOCK_ABOVE_MAX("FG_STOCK_MAX", "成品库存高于最大库存"),

    INSPECTION_WARNING("INSP_WARN", "巡检预警(超出标准)"),

    ;


    private final String code;
    private final String name;

}
