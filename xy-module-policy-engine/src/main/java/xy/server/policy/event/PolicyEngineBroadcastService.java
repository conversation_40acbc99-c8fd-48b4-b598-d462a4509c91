package xy.server.policy.event;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import xy.server.policy.common.constant.EarlyWarningConstant;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024-11-27 10:17
 * <p>
 *     决策引擎广播事件策略调用处理
 * </p>
 **/
@Component
public class PolicyEngineBroadcastService {

    @Resource
    private final Map<String, PolicyEngineBroadcastEventService> broadcastEventServiceMap = new HashMap<>();

    /**
     * 用户注册事件监听
     */
    @EventListener(value = PolicyEngineBroadcastEvent.class)
    @Order(1)
    public void broadcastEventListener(PolicyEngineBroadcastEvent eventSource) {
        String topic = eventSource.getTopic();
        if (StrUtil.isBlank(topic)) {
            // 如果为空，默认EarlyWarning
            topic = EarlyWarningConstant.EARLY_WARNING_BASE_TOPIC;
        }
        PolicyEngineBroadcastEventService eventService = broadcastEventServiceMap.get(topic);
        if (ObjUtil.isNotNull(eventService)) {
            // 发送广播
            eventService.onReceive(eventSource.getBroadcast());
        }
    }

}
