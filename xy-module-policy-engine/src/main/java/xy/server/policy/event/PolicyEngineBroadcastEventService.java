package xy.server.policy.event;

import xy.server.policy.entity.model.vo.ErpPolicyEngineNodeMetaBroadcastVO;

/**
 * <AUTHOR>
 * @Date 2024-11-27 10:10
 * @apiNote 决策引擎广播策略接口定义
 **/
public interface PolicyEngineBroadcastEventService {

    /**
     * 接受到广播信息
     * @param broadcast
     */
    default void onReceive(ErpPolicyEngineNodeMetaBroadcastVO broadcast) {}

    /**
     * 移除广播信息
     * @param category
     * @param businessKey
     */
    default void removeByBusinessKey(String category,String businessKey) {};

}
