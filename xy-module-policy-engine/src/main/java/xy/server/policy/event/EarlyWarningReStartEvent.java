package xy.server.policy.event;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import xy.server.dto.XyMemberDto;
import xy.server.policy.common.constant.EarlyWarningConstant;
import xy.server.policy.common.enums.EarlyWarningEnum;
import xy.server.policy.common.interfaces.EarlyWarningInterface;
import xy.server.policy.utils.BeanUtils;
import xy.server.policy.utils.SFunction;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2024/12/5
 * @description
 **/
public class EarlyWarningReStartEvent extends HashMap<String , String> implements EarlyWarningInterface {
    private EarlyWarningReStartEvent(){}

    private XyMemberDto memberDto;

    public EarlyWarningReStartEvent(XyMemberDto memberDto , EarlyWarningEnum earlyWarningEnum , String businessKey) {
        put(EarlyWarningConstant.EARLY_WARNING_CATEGORY_KEY, earlyWarningEnum.getCode());
        put(EarlyWarningConstant.EARLY_WARNING_BUSINESS_KEY, businessKey);
        // 后续用
        put(EarlyWarningConstant.EARLY_WARNING_BASE_TOPIC, EarlyWarningConstant.EARLY_WARNING_BASE_TOPIC);
        this.memberDto = memberDto;
    }


    public XyMemberDto getMemberDto() {
        return memberDto;
    }

    /**
     * 根据实体和getter方法put数据
     * @param t 实体对象
     * @param getter 实体某个字段的getter方法
     * @param <T> 实体类型
     * @param <R> 字段类型
     */
    public<T,R> void put(T t , SFunction<T,R> getter)  {
        if (ObjUtil.isEmpty(getter)) {
            return;
        }

            put(BeanUtils.getFieldName(getter), StrUtil.toString(getter.apply(t)));
    }

    @Override
    public String getCategory() {
        return get(EarlyWarningConstant.EARLY_WARNING_CATEGORY_KEY);
    }

    @Override
    public String getBusinessKey() {
        return get(EarlyWarningConstant.EARLY_WARNING_BUSINESS_KEY);
    }

    @Override
    public String getFlag() {
        return get(EarlyWarningConstant.EARLY_WARNING_FLAG_KEY);
    }

    @Override
    public String getTopic() {
        return get(EarlyWarningConstant.EARLY_WARNING_BASE_TOPIC);
    }
}
