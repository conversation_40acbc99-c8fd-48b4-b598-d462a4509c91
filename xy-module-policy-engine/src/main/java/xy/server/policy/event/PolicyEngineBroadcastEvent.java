package xy.server.policy.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;
import xy.server.policy.entity.model.vo.ErpPolicyEngineNodeMetaBroadcastVO;

/**
 * <AUTHOR>
 * @data 2024-11-27
 * @apiNote 决策引擎广播事件
 */
@Getter
@Setter
public class PolicyEngineBroadcastEvent extends ApplicationEvent {

    /**
     * 广播名称
     */
    private String topic;

    /**
     * 广播内容
     */
    private ErpPolicyEngineNodeMetaBroadcastVO broadcast;


    public PolicyEngineBroadcastEvent(String topic, ErpPolicyEngineNodeMetaBroadcastVO broadcast) {
        super(topic);
        this.topic = topic;
        this.broadcast = broadcast;
    }
}
