package xy.server.policy.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.exception.ExpressionRuntimeException;
import com.xunyue.common.util.StringAssembler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.text.StrSubstitutor;
import org.springframework.stereotype.Component;
import xy.server.policy.common.enums.PolicyNodeTermTypeEnum;
import xy.server.policy.entity.model.vo.PolicyCombineTermDTO;
import xy.server.policy.entity.model.vo.PolicyLinkTermVO;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 审核流程条件表达式解析工具
 * </p>
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PolicyTermExpressionUtil {

    /**
     * 执行表达式
     *
     * @param linkTerm
     * @return
     */
    public Boolean execute(PolicyLinkTermVO linkTerm, Map<String, String> variate) {
        // 如果为空默认执行
        if (ObjUtil.isNull(linkTerm)) {
            return true;
        }
        // 如果为空默认执行
        if (CollUtil.isEmpty(linkTerm.getCombineTermList())) {
            return true;
        }
        // 执行组内表达式
        StringBuilder combineTermExpression = new StringBuilder();
        for (int i = 0; i < linkTerm.getCombineTermList().size(); i++) {
            PolicyCombineTermDTO combineTerm = linkTerm.getCombineTermList().get(i);
            // 执行条件
            combineTermExpression.append(executeTerm(combineTerm, variate)).append(linkTerm.getLogic());
        }
        if (combineTermExpression.length() == 0) {
            return false;
        }
        log.info("=====================>表达式：>>>" + combineTermExpression);
        return aviatorEvaluatorExecute(combineTermExpression.substring(0, combineTermExpression.length() - 2));
    }

    /**
     * 执行表达式
     *
     * @param expression
     * @return
     */
    private Boolean aviatorEvaluatorExecute(String expression) {
        Object result;
        try {
            // 执行条件表达式
            result = AviatorEvaluator.execute(expression);
            return (Boolean) result;
        } catch (ExpressionRuntimeException e) {
            log.error("表达式：" + expression + "，执行错误！请检查表达式是否合法！");
            return false;
        }
    }

    /**
     * 执行流程条件
     *
     * @param term
     * @param variate
     * @return
     */
    private Boolean executeTerm(PolicyCombineTermDTO term, Map<String, String> variate) {
        // 获取条件对比值, 解析 ${}占位符号
        String dbValue = variate.get(term.getVariablesSymbol());
        // 解析logicValue
        StrSubstitutor substitutor = new StrSubstitutor(variate);
        term.setLogicValue(substitutor.replace(term.getLogicValue()));

        if (term.getResultType().equals(PolicyNodeTermTypeEnum.NUMBER.getType())) {
            // 如果是数字类型, term.getLogic() 1-5 只有logicValue 6-9 是区间值，有logicValue和logicValue2
            String[] ex = {"${a}>${value}", "${a}<${value}", "${a}==${value}", "${a}>=${value}", "${a}<=${value}", "${a}>${value}&&${a}<${value2}", "${a}>=${value}&&${a}<${value2}", "${a}>${value}&&${a}<=${value2}", "${a}>=${value}&&${a}<=${value2}",};
            // 获取条件表达式
            String logicEx = ex[term.getLogic() - 1];
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("a", dbValue);
            hashMap.put("value", term.getLogicValue());
            hashMap.put("value2", term.getLogicValue2());
            logicEx = StringAssembler.assemble(logicEx, hashMap::get);
            Object result;
            try {
                // 执行条件表达式
                result = AviatorEvaluator.execute(logicEx);
                return (Boolean) result;
            } catch (ExpressionRuntimeException e) {
                log.error("表达式：" + logicEx + "，执行错误！请检查表达式是否合法！");
                return false;
            }
        } else if (term.getResultType().equals(PolicyNodeTermTypeEnum.STRING.getType())) {
            // 如果是字符串类型
            if (term.getLogic().equals(1)) {
                // 等于
                return term.getLogicValue().equals(dbValue);
            } else if (term.getLogic().equals(2)) {
                // 包含
                return term.getLogicValue().contains(dbValue);
            } else if (term.getLogic().equals(3)) {
                // 排除
                return !term.getLogicValue().contains(dbValue);
            }
        } else if (term.getResultType().equals(PolicyNodeTermTypeEnum.DATETIME.getType())) {
            // 如果是时间类型 term.getLogic() 1-3 只有logicValue 4 是区间值，有logicValue和logicValue2
            LocalDate localDate = LocalDate.parse(dbValue);
            LocalDate logicLocalDate = LocalDate.parse(term.getLogicValue());
            if (term.getLogic().equals(1)) {
                // 大于
                return localDate.isAfter(logicLocalDate);
            } else if (term.getLogic().equals(2)) {
                // 小于
                return localDate.isBefore(logicLocalDate);
            } else if (term.getLogic().equals(3)) {
                // 等于
                return localDate.isEqual(logicLocalDate);
            } else if (term.getLogic().equals(4)) {
                // 时间区间
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date date1 = sdf.parse(dbValue);
                    Date date2 = sdf.parse(term.getLogicValue());
                    Date date3 = sdf.parse(term.getLogicValue2());
                    return DateUtil.isIn(date1, date2, date3);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }
}
