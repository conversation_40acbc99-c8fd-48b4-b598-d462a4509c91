package xy.server.policy.handler.dingtalk.robot;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import xy.server.policy.common.enums.MessageType;
import xy.server.policy.entity.message.config.DingTalkRobotConfig;
import xy.server.policy.entity.message.dingtalk.robot.TextMessageDTO;
import xy.server.policy.handler.IMsgSender;

import java.util.List;

/**
 * 钉钉群机器人文本类型消息处理器
 *
 * <AUTHOR>
 **/
@Component
@Slf4j
public class RobotTextMessageHandler extends IMsgSender<TextMessageDTO> {

    /**
     * 消息类型
     */
    public static final MessageType MESSAGE_TYPE = MessageType.DING_TALK_ROBOT_TEXT;

    @Override
    public void handle(TextMessageDTO param) {
        List<DingTalkRobotConfig> configs = platformConfigService.queryConfigOrDefault(param, DingTalkRobotConfig.class, MESSAGE_TYPE.getPlatform());

        for (DingTalkRobotConfig config : configs) {

            try {
                DingTalkClient client = new DefaultDingTalkClient(config.getWebhook());
                OapiRobotSendRequest request = new OapiRobotSendRequest();
                request.setMsgtype("text");
                OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
                text.setContent(param.getContent());
                request.setText(text);
                OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
                at.setIsAtAll(param.getIsAtAll());
//                at.setAtMobiles(RpushUtils.getMobile(receiverUsers));
//                at.setAtUserIds(RpushUtils.getNotMobile(receiverUsers));
                request.setAt(at);
                OapiRobotSendResponse rsp = client.execute(request);
                if (!rsp.isSuccess()) {
                    throw new IllegalStateException(rsp.getBody());
                }
            } catch (Exception e) {
                String eMessage = ExceptionUtil.getMessage(e);
                eMessage = StringUtils.isBlank(eMessage) ? "未知错误" : eMessage;
                log.error(eMessage);
            }
        }
    }

    @Override
    public MessageType messageType() {
        return MESSAGE_TYPE;
    }
}
