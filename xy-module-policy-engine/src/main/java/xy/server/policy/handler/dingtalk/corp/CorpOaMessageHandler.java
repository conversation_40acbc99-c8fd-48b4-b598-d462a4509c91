package xy.server.policy.handler.dingtalk.corp;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import xy.server.policy.common.enums.MessageType;
import xy.server.policy.entity.message.config.DingTalkCorpConfig;
import xy.server.policy.entity.message.dingtalk.corp.OaMessageDTO;
import xy.server.policy.handler.IMsgSender;
import xy.server.policy.utils.SingletonUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 钉钉工作通知文本类型消息处理器
 *
 * <AUTHOR>
 * @since 2021/4/11/011 15:06
 **/
@Component
@Slf4j
public class CorpOaMessageHandler extends IMsgSender<OaMessageDTO> {


    /**
     * 消息类型
     */
    public static final MessageType MESSAGE_TYPE = MessageType.DING_TALK_COPR_OA;


    @Override
    public void handle(OaMessageDTO param) {
        List<DingTalkCorpConfig> configs = platformConfigService.queryConfigOrDefault(param, DingTalkCorpConfig.class, MESSAGE_TYPE.getPlatform());

        for (DingTalkCorpConfig config : configs) {

            DingTalkClient client = SingletonUtil.get("dinging-" + config.getAppKey() + config.getAppSecret(),
                    (SingletonUtil.Factory<DingTalkClient>) () -> new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2"));
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setAgentId(Long.valueOf(config.getAgentId()));
//            request.setUseridList(String.join(",", receiverUsers));
            request.setDeptIdList(param.getDeptIdList());
            request.setToAllUser(param.getToAllUser());

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setOa(new OapiMessageCorpconversationAsyncsendV2Request.OA());
            msg.getOa().setHead(new OapiMessageCorpconversationAsyncsendV2Request.Head());
            msg.getOa().setMessageUrl(param.getMessageUrl());
            msg.getOa().setPcMessageUrl(param.getPcMessageUrl());
            msg.getOa().getHead().setText(param.getHeadText());
            msg.getOa().getHead().setBgcolor(param.getHeadBgColor());
            msg.getOa().setBody(new OapiMessageCorpconversationAsyncsendV2Request.Body());
            msg.getOa().getBody().setContent(param.getContent());
            msg.getOa().getBody().setTitle(param.getBodyTitle());
            msg.getOa().getBody().setAuthor(param.getAuthor());
            msg.getOa().getBody().setFileCount(param.getFileCount());
            msg.getOa().getBody().setImage(param.getImage());
            OapiMessageCorpconversationAsyncsendV2Request.Rich rich = new OapiMessageCorpconversationAsyncsendV2Request.Rich();
            rich.setNum(param.getRichNum());
            rich.setUnit(param.getRichUnit());
            msg.getOa().getBody().setRich(rich);

            List<OaMessageDTO.BodyForm> bodyForms = param.getBodyForms();
            List<OapiMessageCorpconversationAsyncsendV2Request.Form> forms = new ArrayList<>();
            for (OaMessageDTO.BodyForm bodyForm : bodyForms) {
                OapiMessageCorpconversationAsyncsendV2Request.Form form = new OapiMessageCorpconversationAsyncsendV2Request.Form();
                form.setKey(bodyForm.getKey());
                form.setValue(bodyForm.getValue());
                forms.add(form);
            }
            msg.getOa().getBody().setForm(forms);

            msg.getOa().setStatusBar(new OapiMessageCorpconversationAsyncsendV2Request.StatusBar());
            msg.getOa().getStatusBar().setStatusBg(param.getStatusBarStatusBg());
            msg.getOa().getStatusBar().setStatusValue(param.getStatusBarStatusValue());
            msg.setMsgtype("oa");

            request.setMsg(msg);

            try {
                OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, AccessTokenUtils.getAccessToken(config.getAppKey(), config.getAppSecret()));
                if (!rsp.isSuccess()) {
                    throw new IllegalStateException(rsp.getBody());
                }
            } catch (Exception e) {
                String eMessage = ExceptionUtil.getMessage(e);
                eMessage = StringUtils.isBlank(eMessage) ? "未知错误" : eMessage;
                log.error(eMessage);
            }
        }
    }

    @Override
    public MessageType messageType() {
        return MESSAGE_TYPE;
    }
}
