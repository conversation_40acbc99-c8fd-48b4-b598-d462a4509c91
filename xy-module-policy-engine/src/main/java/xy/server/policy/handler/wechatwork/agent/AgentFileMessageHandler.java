package xy.server.policy.handler.wechatwork.agent;

import cn.hutool.core.exceptions.ExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import xy.server.policy.common.enums.MessageType;
import xy.server.policy.entity.message.config.WechatWorkAgentConfig;
import xy.server.policy.entity.message.wechatwork.agent.MediaMessageDTO;
import xy.server.policy.handler.IMsgSender;
import xy.server.policy.utils.SingletonUtil;

import java.util.List;

/**
 * 企业微信-图片消息
 *
 * <AUTHOR>
 * @since 2021/4/8/008 21:36
 **/
@Component
@Slf4j
public class AgentFileMessageHandler extends IMsgSender<MediaMessageDTO> {

    /**
     * 消息类型
     */
    public static final MessageType MESSAGE_TYPE = MessageType.WECHAT_WORK_AGENT_FILE;



    @Override
    public void handle(MediaMessageDTO param) {
        List<WechatWorkAgentConfig> configs = platformConfigService.queryConfigOrDefault(param, WechatWorkAgentConfig.class, MESSAGE_TYPE.getPlatform());
        for (WechatWorkAgentConfig config : configs) {

            WxCpServiceImpl wxCpService = SingletonUtil.get(config.getCorpId() + config.getSecret() + config.getAgentId(), () -> {
                WxCpDefaultConfigImpl cpConfig = new WxCpDefaultConfigImpl();
                cpConfig.setCorpId(config.getCorpId());
                cpConfig.setCorpSecret(config.getSecret());
                cpConfig.setAgentId(config.getAgentId());
                WxCpServiceImpl wxCpService1 = new WxCpServiceImpl();
                wxCpService1.setWxCpConfigStorage(cpConfig);
                return wxCpService1;
            });

            for (String receiverUser : param.getReceiverIds()) {

                WxCpMessage message = WxCpMessage.FILE()
                        .agentId(config.getAgentId()) // 企业号应用ID
                        .toUser(receiverUser)
                        .toParty(param.getToParty())
                        .toTag(param.getToTag())
                        .mediaId(param.getMediaId())
                        .build();
                try {
                    wxCpService.getMessageService().send(message);
                } catch (Exception e) {
                    String eMessage = ExceptionUtil.getMessage(e);
                    eMessage = StringUtils.isBlank(eMessage) ? "未知错误" : eMessage;
                    log.error(eMessage);
                }
            }
        }
    }

    @Override
    public MessageType messageType() {
        return MESSAGE_TYPE;
    }
}
