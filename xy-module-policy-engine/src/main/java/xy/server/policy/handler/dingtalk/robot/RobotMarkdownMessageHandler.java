package xy.server.policy.handler.dingtalk.robot;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import xy.server.policy.common.enums.MessageType;
import xy.server.policy.entity.message.config.DingTalkRobotConfig;
import xy.server.policy.entity.message.dingtalk.robot.MarkdownMessageDTO;
import xy.server.policy.handler.IMsgSender;

import java.util.List;

/**
 * 钉钉群机器人markdown类型消息处理器
 *
 * <AUTHOR>
 **/
@Component
@Slf4j
public class RobotMarkdownMessageHandler extends IMsgSender<MarkdownMessageDTO> {

    /**
     * 消息类型
     */
    public static final MessageType MESSAGE_TYPE = MessageType.DING_TALK_ROBOT_MARKDOWN;

    @Override
    public void handle(MarkdownMessageDTO param) {

        // 1、获取消息平台配置
        List<DingTalkRobotConfig> configs = platformConfigService.queryConfigOrDefault(param, DingTalkRobotConfig.class, MESSAGE_TYPE.getPlatform());

        // 2、发送消息
        for (DingTalkRobotConfig config : configs) {
            // todo 3、角色转成对应钉钉的userIds

            try {
                DingTalkClient client = new DefaultDingTalkClient(config.getWebhook());
                OapiRobotSendRequest request = new OapiRobotSendRequest();
                request.setMsgtype("markdown");
                OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
                markdown.setTitle(param.getTitle());
                markdown.setText(param.getText());
                request.setMarkdown(markdown);
                OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
                at.setIsAtAll(param.getIsAtAll());
//            at.setAtMobiles(RpushUtils.getMobile(receiverUsers));
//            at.setAtUserIds(RpushUtils.getNotMobile(receiverUsers));
                request.setAt(at);
                OapiRobotSendResponse rsp = client.execute(request);
                if (!rsp.isSuccess()) {
                    throw new IllegalStateException(rsp.getBody());
                }
            } catch (Exception e) {
                String eMessage = ExceptionUtil.getMessage(e);
                eMessage = StringUtils.isBlank(eMessage) ? "未知错误" : eMessage;
                log.error(eMessage);
            }


        }

    }

    @Override
    public MessageType messageType() {
        return MESSAGE_TYPE;
    }
}
