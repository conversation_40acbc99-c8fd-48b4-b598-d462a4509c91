package xy.server.policy.handler.dingtalk.corp;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import xy.server.policy.common.enums.MessageType;
import xy.server.policy.entity.message.config.DingTalkCorpConfig;
import xy.server.policy.entity.message.dingtalk.corp.LinkMessageDTO;
import xy.server.policy.handler.IMsgSender;
import xy.server.policy.utils.SingletonUtil;

import java.util.List;

/**
 * 钉钉工作通知文本类型消息处理器
 *
 * <AUTHOR>
 * @since 2021/4/11/011 15:06
 **/
@Component
@Slf4j
public class CorpLinkMessageHandler extends IMsgSender<LinkMessageDTO> {

    /**
     * 消息类型
     */
    public static final MessageType MESSAGE_TYPE = MessageType.DING_TALK_COPR_LINK;

    @Override
    public void handle(LinkMessageDTO param) {
        List<DingTalkCorpConfig> configs = platformConfigService.queryConfigOrDefault(param, DingTalkCorpConfig.class, MESSAGE_TYPE.getPlatform());

        for (DingTalkCorpConfig config : configs) {

            DingTalkClient client = SingletonUtil.get("dinging-" + config.getAppKey() + config.getAppSecret(),
                    (SingletonUtil.Factory<DingTalkClient>) () -> new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2"));
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setAgentId(Long.valueOf(config.getAgentId()));
//            request.setUseridList(String.join(",", receiverUsers));
            request.setDeptIdList(param.getDeptIdList());
            request.setToAllUser(param.getToAllUser());

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setMsgtype("link");
            msg.setLink(new OapiMessageCorpconversationAsyncsendV2Request.Link());
            msg.getLink().setTitle(param.getTitle());
            msg.getLink().setText(param.getText());
            msg.getLink().setMessageUrl(param.getMessageUrl());
            msg.getLink().setPicUrl(param.getPicUrl());
            request.setMsg(msg);

            try {
                OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, AccessTokenUtils.getAccessToken(config.getAppKey(), config.getAppSecret()));
                if (!rsp.isSuccess()) {
                    throw new IllegalStateException(rsp.getBody());
                }
            } catch (Exception e) {
                String eMessage = ExceptionUtil.getMessage(e);
                eMessage = StringUtils.isBlank(eMessage) ? "未知错误" : eMessage;
                log.error(eMessage);
            }
        }
    }

    @Override
    public MessageType messageType() {
        return MESSAGE_TYPE;
    }
}
