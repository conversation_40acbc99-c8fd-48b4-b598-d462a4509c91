package xy.server.policy.handler.dingtalk.corp;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import xy.server.policy.common.enums.MessageType;
import xy.server.policy.entity.message.config.DingTalkCorpConfig;
import xy.server.policy.entity.message.dingtalk.corp.ActionCardMultiMessageDTO;
import xy.server.policy.entity.message.dingtalk.corp.BtnJsonDTO;
import xy.server.policy.handler.IMsgSender;
import xy.server.policy.utils.SingletonUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 钉钉工作通知文本类型消息处理器
 *
 * <AUTHOR>
 * @since 2021/4/11/011 15:06
 **/
@Component
@Slf4j
public class CorpActionCardMultiMessageHandler extends IMsgSender<ActionCardMultiMessageDTO> {

    /**
     * 消息类型
     */
    public static final MessageType MESSAGE_TYPE = MessageType.DING_TALK_COPR_ACTION_CARD_MULTI;

    @Override
    public void handle(ActionCardMultiMessageDTO param) {
        List<DingTalkCorpConfig> configs = platformConfigService.queryConfigOrDefault(param, DingTalkCorpConfig.class, MESSAGE_TYPE.getPlatform());

        for (DingTalkCorpConfig config : configs) {


            DingTalkClient client = SingletonUtil.get("dinging-" + config.getAppKey() + config.getAppSecret(),
                    (SingletonUtil.Factory<DingTalkClient>) () -> new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2"));
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setAgentId(Long.valueOf(config.getAgentId()));
            // request.setUseridList(String.join(",", receiverUsers));
            request.setDeptIdList(param.getDeptIdList());
            request.setToAllUser(param.getToAllUser());

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setActionCard(new OapiMessageCorpconversationAsyncsendV2Request.ActionCard());
            msg.getActionCard().setTitle(param.getTitle());
            msg.getActionCard().setSingleTitle(param.getTitle());
            msg.getActionCard().setMarkdown(param.getMarkdown());
            msg.getActionCard().setBtnOrientation(param.getBtnOrientation());
            List<OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList> btnJsonList = new ArrayList<>();
            for (BtnJsonDTO btnJsonDTO : param.getBtnJsonList()) {
                OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList btnJson = new OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList();
                btnJson.setActionUrl(btnJsonDTO.getActionUrl());
                btnJson.setTitle(btnJsonDTO.getTitle());
                btnJsonList.add(btnJson);
            }
            msg.getActionCard().setBtnJsonList(btnJsonList);
            msg.setMsgtype("action_card");
            request.setMsg(msg);

            try {
                OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, AccessTokenUtils.getAccessToken(config.getAppKey(), config.getAppSecret()));
                if (!rsp.isSuccess()) {
                    throw new IllegalStateException(rsp.getBody());
                }

            } catch (Exception e) {
                String eMessage = ExceptionUtil.getMessage(e);
                eMessage = StringUtils.isBlank(eMessage) ? "未知错误" : eMessage;
                log.error(eMessage);
            }

        }
    }

    @Override
    public MessageType messageType() {
        return MESSAGE_TYPE;
    }
}
