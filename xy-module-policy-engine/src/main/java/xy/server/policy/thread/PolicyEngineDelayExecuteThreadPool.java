package xy.server.policy.thread;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.xunyue.common.util.SpringUtils;
import xy.server.policy.entity.model.vo.NodeMetaDelayRedisDataDTO;
import xy.server.policy.service.IErpPolicyEngineService;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @data 2024-03-19 16:46
 * @apiNote 决策引擎延时执行队列线程pool
 */

public class PolicyEngineDelayExecuteThreadPool implements Runnable {

    @Resource
    private IErpPolicyEngineService iErpPolicyEngineService = SpringUtils.getBean(IErpPolicyEngineService.class);
    @Resource
    private PolicyEngineDelayUtil policyEngineDelayUtil = SpringUtils.getBean(PolicyEngineDelayUtil.class);
    private String content;

    public PolicyEngineDelayExecuteThreadPool(String content) {
        this.content = content;
    }

    @Override
    public void run() {
        try {
            NodeMetaDelayRedisDataDTO dataDTO = JSONObject.parseObject(content, NodeMetaDelayRedisDataDTO.class);
            // 获取当前时间（忽略秒）
            Date currentDate = DateUtil.parse(DateUtil.now(), "yyyy-MM-dd HH:mm");
            if (currentDate.compareTo(dataDTO.getDelayExecutionDate()) == 0) {
                // 当前时间相等延时执行时间，调用决策引擎接口继续执行
                iErpPolicyEngineService.specifyNodeExecute(dataDTO);
                // 删除Key
                policyEngineDelayUtil.removeByTaskId(dataDTO.getTaskId(), dataDTO);
            } else if (currentDate.compareTo(dataDTO.getDelayExecutionDate()) < 0) {
                // 当前时间小于<延时执行时间，不做处理
            } else {
                // 当前时间大于>延时执行时间（已过期）,删除Key
                policyEngineDelayUtil.removeByTaskId(dataDTO.getTaskId(), dataDTO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
