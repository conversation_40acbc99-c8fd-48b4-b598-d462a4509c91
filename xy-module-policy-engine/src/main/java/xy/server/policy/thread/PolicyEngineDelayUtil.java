package xy.server.policy.thread;

import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSONObject;
import com.xunyue.config.util.RedisUtil;
import org.springframework.stereotype.Component;
import xy.server.policy.common.constant.Constant;
import xy.server.policy.entity.model.vo.NodeMetaDelayRedisDataDTO;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

import static xy.server.policy.common.constant.Constant.POLICY_ENGINE_DELAY_TASK_LIST;

/**
 * 决策引擎延时队列工具类
 *
 * <AUTHOR>
 * @date 2024-03-19
 */
@Component
public class PolicyEngineDelayUtil {

    @Resource
    private RedisUtil redisUtil;
    /**
     * 记录延迟消息
     */
    public static ConcurrentHashMap<String, String> cachePolicyEngineDelay = new ConcurrentHashMap<>();
    private final ReentrantLock reentrantLock = new ReentrantLock(true);

    /**
     * 往队列中push值
     * @param dataDTO
     */
    public void push(NodeMetaDelayRedisDataDTO dataDTO) {
        // key
        String key = createKey(dataDTO.getPolicyEngineGuid(), dataDTO.getCurrentNodeGuid(), dataDTO.getTaskId());
        // 存入到Redis
        redisUtil.set(key, JSONObject.toJSONString(dataDTO));
        // 记录任务和决策的关联
        redisUtil.set(dataDTO.getTaskId(), dataDTO.getPolicyEngineGuid() + ":" + dataDTO.getCurrentNodeGuid() + ":");
        // 决策延迟任务列表,
        reentrantLock.lock();
        try {
            List<String> taskList = getEngineTaskList(dataDTO.getPolicyEngineGuid());
            taskList.add(dataDTO.getTaskId());
            redisUtil.set(POLICY_ENGINE_DELAY_TASK_LIST + dataDTO.getPolicyEngineGuid(), JSONObject.toJSONString(taskList));
        } finally {
            reentrantLock.unlock();
        }
        // 记录到内存
        cachePolicyEngineDelay.remove(key);
        cachePolicyEngineDelay.put(key, JSONObject.toJSONString(dataDTO));
    }

    /**
     * 移除一个等待任务
     * @param taskId
     */
    public void removeByTaskId(String taskId, NodeMetaDelayRedisDataDTO dataDTO) {
        if (redisUtil.hasKey(taskId)) {
//            Object o = redisUtil.get(taskId);
            String o = dataDTO.getPolicyEngineGuid()+":"+dataDTO.getCurrentNodeGuid()+":";
            String key = Constant.POLICY_ENGINE_DELAY_EXECUTE_KEY + o + taskId;
            // 删除
            redisUtil.del(key);
//            redisUtil.del(taskId);
            cachePolicyEngineDelay.remove(key);
            String policyEngineGuid = o.toString().split(":")[0];
            removeEngineTaskList(policyEngineGuid, taskId);
        }
    }

    /**
     * 移除一个等待任务
     * @param taskId
     * @param
     */
    public void removeByTaskId(String taskId) {
        if (redisUtil.hasKey(taskId)) {
            Object o = redisUtil.get(taskId);
            String key = Constant.POLICY_ENGINE_DELAY_EXECUTE_KEY + o.toString() + taskId;
            // 删除
            redisUtil.del(key);
            redisUtil.del(taskId);
            cachePolicyEngineDelay.remove(key);
            String policyEngineGuid = o.toString().split(":")[0];
            removeEngineTaskList(policyEngineGuid, taskId);
        }
    }

    /**
     * 根据决策Guid删除
     *
     * @param policyEngineGuid
     */
    public void removeByPolicyEngineGuid(String policyEngineGuid) {
        List<String> taskList = getEngineTaskList(policyEngineGuid);
        taskList.forEach(this::removeByTaskId);
    }

    /**
     * 获取Redis中的等待任务
     */
    public Map<String, String> getTaskMap(){
       return redisUtil.getMapByKeys(Constant.POLICY_ENGINE_DELAY_EXECUTE_KEY);
    }

    /**
     * 检查是否有任务
     * @param policyEngineGuids
     * @return
     */
    public boolean policyEngineHasTask(String policyEngineGuids) {
        return getEngineTaskList(policyEngineGuids).size() > 0;
    }

    /**
     * 获取Key
     *
     * @param policyEngineGuid 预警数据
     * @param currentNodeGuid  当前节点
     * @param taskId           任务
     * @return
     */
    private String createKey(String policyEngineGuid, String currentNodeGuid, String taskId) {
        return Constant.POLICY_ENGINE_DELAY_EXECUTE_KEY + policyEngineGuid + ":" + currentNodeGuid + ":" + taskId;
    }

    /**
     * 获取执行中的延迟任务列表
     *
     * @param policyEngineGuid
     * @return
     */
    public List<String> getEngineTaskList(String policyEngineGuid) {
        Object engineTaskList = redisUtil.get(POLICY_ENGINE_DELAY_TASK_LIST + policyEngineGuid);
        List<String> taskList;
        if (ObjUtil.isNotNull(engineTaskList)) {
            taskList = JSONObject.parseArray(engineTaskList.toString(), String.class);
        } else {
            taskList = new ArrayList<>();
        }
        return taskList;
    }

    /**
     * 移除执行中的延迟任务列表
     *
     * @param policyEngineGuid
     * @param taskId
     */
    public void removeEngineTaskList(String policyEngineGuid, String taskId) {
        reentrantLock.lock();
        try {
            List<String> taskList = getEngineTaskList(policyEngineGuid);
            taskList.remove(taskId);
            if (taskList.size() > 0) {
                redisUtil.set(POLICY_ENGINE_DELAY_TASK_LIST + policyEngineGuid, JSONObject.toJSONString(taskList));
            } else {
                redisUtil.del(POLICY_ENGINE_DELAY_TASK_LIST + policyEngineGuid);
            }
        } finally {
            reentrantLock.unlock();
        }
    }

}
