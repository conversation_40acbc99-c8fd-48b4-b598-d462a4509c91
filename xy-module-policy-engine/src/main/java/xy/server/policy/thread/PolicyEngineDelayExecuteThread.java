package xy.server.policy.thread;

import cn.hutool.core.collection.CollUtil;
import com.xunyue.config.util.RedisUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import xy.server.policy.common.constant.Constant;

import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 决策引擎延时执行线程
 *
 * <AUTHOR>
 * @date 2024-03-19
 */
public class PolicyEngineDelayExecuteThread extends Thread {
    private static final Logger log = LogManager.getLogger(PolicyEngineDelayExecuteThread.class);
    /**
     * redis工具
     */
    private RedisUtil redisUtil;
    /**
     *
     */
    private static PolicyEngineDelayExecuteThread policyEngineDelayExecuteThread;
    /**
     * 定义线程池
     */
    private ExecutorService cachedThreadPool = new ThreadPoolExecutor(2, 4, 0, TimeUnit.MINUTES, new LinkedBlockingDeque<>(), r -> new Thread(r, "xyCrmSendThread"));

    /**
     * 单例
     *
     * @param redisUtil
     * @return
     */
    public static PolicyEngineDelayExecuteThread getInstance(RedisUtil redisUtil) {
        if (policyEngineDelayExecuteThread == null) {
            synchronized (PolicyEngineDelayExecuteThread.class) {
                if (policyEngineDelayExecuteThread == null) {
                    policyEngineDelayExecuteThread = new PolicyEngineDelayExecuteThread(redisUtil);
                }
            }
        }
        return policyEngineDelayExecuteThread;
    }

    public PolicyEngineDelayExecuteThread(RedisUtil redisUtil) {
        this.redisUtil = redisUtil;
    }

    @Override
    public void run() {
        while (true) {
            try {
                sleep(1000);
                if (CollUtil.isEmpty(PolicyEngineDelayUtil.cachePolicyEngineDelay)) {
                    Map<String, String> mapByKeys = redisUtil.getMapByKeys(Constant.POLICY_ENGINE_DELAY_EXECUTE_KEY);
                    PolicyEngineDelayUtil.cachePolicyEngineDelay.putAll(mapByKeys);
                }
                // 启动线程执行等待任务
                PolicyEngineDelayUtil.cachePolicyEngineDelay.forEach((key, value) -> {
                    cachedThreadPool.execute(new PolicyEngineDelayExecuteThreadPool(value));
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
