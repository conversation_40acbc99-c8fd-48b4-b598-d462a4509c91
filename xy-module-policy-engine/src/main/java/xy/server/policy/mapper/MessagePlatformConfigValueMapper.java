package xy.server.policy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.policy.entity.MessagePlatformConfigValue;
import xy.server.policy.entity.model.vo.MessagePlatformConfigValueVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Mapper
public interface MessagePlatformConfigValueMapper extends BaseMapper<MessagePlatformConfigValue> {

    /**
     * 列表查询
     *
     * @param messagePlatformConfigGuid
     * @return
     */
    List<MessagePlatformConfigValueVO> getByMessagePlatformConfigGuid(@Param("messagePlatformConfigGuid") String messagePlatformConfigGuid);

    /**
     * 删除
     * @param messagePlatformConfigGuid
     * @return
     */
    Integer deleteByPlatformConfigGuid(@Param("messagePlatformConfigGuid") String messagePlatformConfigGuid);

}
