package xy.server.policy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.policy.entity.ErpPolicyEngineEdge;
import xy.server.policy.entity.model.vo.ErpPolicyEngineEdgeVO;

import java.util.List;

/**
 * <p>
 * 决策流程边 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Mapper
public interface ErpPolicyEngineEdgeMapper extends BaseMapper<ErpPolicyEngineEdge> {

    /**
     * 根据决策引擎查询列表
     * @param policyEngineGuid
     * @return
     */
    List<ErpPolicyEngineEdgeVO> findListByPolicyEngineGuid(@Param("policyEngineGuid") String policyEngineGuid);
}
