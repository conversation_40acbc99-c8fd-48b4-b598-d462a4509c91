package xy.server.policy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.policy.entity.ErpMessageTemplate;
import xy.server.policy.entity.model.qo.ErpMessageTemplateQO;
import xy.server.policy.entity.model.vo.ErpMessageTemplateVO;

import java.util.List;

/**
 * <p>
 * 消息通知模板 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Mapper
public interface ErpMessageTemplateMapper extends BaseMapper<ErpMessageTemplate> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpMessageTemplateVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<ErpMessageTemplateVO> findList(@Param("model") ErpMessageTemplateQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<ErpMessageTemplateVO> findPage(@Param("page") IPage<ErpMessageTemplateVO> page, @Param("model") ErpMessageTemplateQO model);

    /**
     * 根据消息模板编码和类型获取一条数据
     * @param messageTemplateCode
     * @param messageTemplateType
     * @return
     */
    ErpMessageTemplateVO getOneByCodeAndType(@Param("messageTemplateCode") String messageTemplateCode, @Param("messageTemplateType") String messageTemplateType);
}
