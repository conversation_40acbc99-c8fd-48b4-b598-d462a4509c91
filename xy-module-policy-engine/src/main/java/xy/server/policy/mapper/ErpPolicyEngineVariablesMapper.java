package xy.server.policy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.policy.entity.ErpPolicyEngineVariables;
import xy.server.policy.entity.model.vo.ErpPolicyEngineVariablesVO;

import java.util.List;

/**
 * <p>
 * 决策流程执行变量 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-18
 */
@Mapper
public interface ErpPolicyEngineVariablesMapper extends BaseMapper<ErpPolicyEngineVariables> {
    /**
     * 根据决策引擎查询列表
     * @param policyEngineGuid
     * @return
     */
    List<ErpPolicyEngineVariablesVO> findListByPolicyEngineGuid(@Param("policyEngineGuid") String policyEngineGuid);

    /**
     * 删除
     * @param policyEngineGuid
     * @return
     */
    Integer deleteByPolicyEngineGuid(@Param("policyEngineGuid")String policyEngineGuid);

    /**
     * 根据决策引擎Code获取变量
     * @param policyEngineCode
     * @return
     */
    List<ErpPolicyEngineVariablesVO> getVariablesByCode(@Param("policyEngineCode") String policyEngineCode);
}
