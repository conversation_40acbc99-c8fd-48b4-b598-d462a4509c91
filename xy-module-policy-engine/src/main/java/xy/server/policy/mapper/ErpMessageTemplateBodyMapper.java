package xy.server.policy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.policy.entity.ErpMessageTemplateBody;
import xy.server.policy.entity.model.vo.ErpMessageTemplateBodyVO;

import java.util.List;

/**
 * <p>
 * 消息通知模板内容 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Mapper
public interface ErpMessageTemplateBodyMapper extends BaseMapper<ErpMessageTemplateBody> {

    /**
     * 根据主表guid查询数据列表
     *
     * @param messageTemplateGuid
     * @return
     */
    List<ErpMessageTemplateBodyVO> findListByMessageTemplateGuid(@Param("messageTemplateGuid") String messageTemplateGuid);

}
