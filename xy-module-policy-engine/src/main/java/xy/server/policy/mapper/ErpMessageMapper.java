package xy.server.policy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.policy.entity.ErpMessage;
import xy.server.policy.entity.model.qo.ErpMessageQO;
import xy.server.policy.entity.model.vo.ErpMessageVO;

import java.util.List;

/**
 * <p>
 * 消息通知 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Mapper
public interface ErpMessageMapper extends BaseMapper<ErpMessage> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpMessageVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<ErpMessageVO> findList(@Param("model") ErpMessageQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<ErpMessageVO> findPage(@Param("page") IPage<ErpMessageVO> page, @Param("model") ErpMessageQO model);
}
