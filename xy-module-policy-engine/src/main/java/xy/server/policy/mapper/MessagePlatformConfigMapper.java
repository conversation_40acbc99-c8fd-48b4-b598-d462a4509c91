package xy.server.policy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import xy.server.policy.entity.MessagePlatformConfig;
import xy.server.policy.entity.model.qo.MessagePlatformConfigQO;
import xy.server.policy.entity.model.vo.MessagePlatformConfigVO;

import java.util.List;

/**
 * <p>
 * 消息平台配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Mapper
public interface MessagePlatformConfigMapper extends BaseMapper<MessagePlatformConfig> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    MessagePlatformConfigVO getDataByGuid(@Param("guid") String guid);

    /**
     * 删除数据
     * @param guid
     * @return
     */
    Boolean deleteByGuid(@Param("guid") String guid);

    /**
     * 获取配置列表
     * @param guids
     * @return
     */
    List<MessagePlatformConfigVO> getByGuids(@Param("list") List<String> guids);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<MessagePlatformConfigVO> findList(@Param("model") MessagePlatformConfigQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<MessagePlatformConfigVO> findPage(@Param("page") IPage<MessagePlatformConfigVO> page, @Param("model") MessagePlatformConfigQO model);

    /**
     * 根据平台获取
     * @param platform
     * @return
     */
    MessagePlatformConfigVO getByPlatform(@Param("platform") String platform);

    /**
     * 根据平台获取列表
     * @param platform
     * @return
     */
    List<MessagePlatformConfigVO> findListByPlatform(@Param("platform") String platform);

    /**
     * 获取第一条消息平台
     * @return
     */
    @Select("select platform from message_platform_config where status is true and is_default is true limit 1")
    String getFirstMessagePlatform();

}
