package xy.server.policy.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import xy.server.policy.entity.ErpPolicyEngine;
import xy.server.policy.entity.model.qo.ErpPolicyEngineQO;
import xy.server.policy.entity.model.vo.ErpPolicyEngineVO;

import java.util.List;

/**
 * <p>
 * 决策表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Mapper
public interface ErpPolicyEngineMapper extends BaseMapper<ErpPolicyEngine> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpPolicyEngineVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<ErpPolicyEngineVO> findList(@Param("model") ErpPolicyEngineQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<ErpPolicyEngineVO> findPage(@Param("page") IPage<ErpPolicyEngineVO> page, @Param("model") ErpPolicyEngineQO model);

    /**
     * 根据决策引擎编码查询数据
     * @param policyEngineCode
     * @return
     */
    ErpPolicyEngineVO getDataByPolicyEngineCode(@Param("policyEngineCode") String policyEngineCode);

    /**
     * 执行SQL
     * @param sql
     * @return
     */
    @Select("${sql}")
    List<JSONObject> sqlExecution(@Param("sql") String sql);
}
