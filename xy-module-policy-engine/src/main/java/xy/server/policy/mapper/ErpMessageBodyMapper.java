package xy.server.policy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.policy.entity.ErpMessageBody;
import xy.server.policy.entity.model.vo.ErpMessageBodyVO;

import java.util.List;

/**
 * <p>
 * 消息通知内容 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Mapper
public interface ErpMessageBodyMapper extends BaseMapper<ErpMessageBody> {

    /**
     * 根据messageGuid查询列表
     * @param messageGuid
     * @return
     */
    List<ErpMessageBodyVO> findListByMessageGuid(@Param("messageGuid") String messageGuid);
}
