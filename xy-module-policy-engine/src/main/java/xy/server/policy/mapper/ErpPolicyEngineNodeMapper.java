package xy.server.policy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.policy.entity.ErpPolicyEngineNode;
import xy.server.policy.entity.model.vo.ErpPolicyEngineNodeVO;

import java.util.List;

/**
 * <p>
 * 决策节点流程 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Mapper
public interface ErpPolicyEngineNodeMapper extends BaseMapper<ErpPolicyEngineNode> {
    /**
     * 根据决策引擎查询列表
     * @param policyEngineGuid
     * @return
     */
    List<ErpPolicyEngineNodeVO> findListByPolicyEngineGuid(@Param("policyEngineGuid") String policyEngineGuid);
}
