package xy.server.policy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.policy.entity.ErpPolicyEngineNodeMetaDelay;
import xy.server.policy.entity.model.vo.ErpPolicyEngineNodeMetaDelayVO;

/**
 * <p>
 * 决策节点组件属性_延时队列 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Mapper
public interface ErpPolicyEngineNodeMetaDelayMapper extends BaseMapper<ErpPolicyEngineNodeMetaDelay> {

    /**
     * 根据决策引擎节点GUID查询数据
     * @param policyEngineNodeGuid
     * @return
     */
    ErpPolicyEngineNodeMetaDelayVO getDataByPolicyEngineNodeGuid(@Param("policyEngineNodeGuid") String policyEngineNodeGuid);
}
