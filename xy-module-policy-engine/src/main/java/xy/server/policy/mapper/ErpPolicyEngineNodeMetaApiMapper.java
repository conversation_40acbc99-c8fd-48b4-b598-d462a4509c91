package xy.server.policy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.policy.entity.ErpPolicyEngineNodeMetaApi;
import xy.server.policy.entity.model.vo.ErpPolicyEngineNodeMetaApiVO;

/**
 * <p>
 * 决策节点组件属性_API请求 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Mapper
public interface ErpPolicyEngineNodeMetaApiMapper extends BaseMapper<ErpPolicyEngineNodeMetaApi> {
    /**
     * 根据决策引擎节点GUID查询数据
     * @param policyEngineNodeGuid
     * @return
     */
    ErpPolicyEngineNodeMetaApiVO getDataByPolicyEngineNodeGuid(@Param("policyEngineNodeGuid") String policyEngineNodeGuid);
}
