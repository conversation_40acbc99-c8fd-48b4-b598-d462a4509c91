package xy.server.policy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.policy.entity.ErpMessageRecipients;
import xy.server.policy.entity.model.vo.ErpMessageRecipientsVO;

import java.util.List;

/**
 * <p>
 * 消息通知收件人 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Mapper
public interface ErpMessageRecipientsMapper extends BaseMapper<ErpMessageRecipients> {
    /**
     * 根据messageGuid查询列表
     * @param messageGuid
     * @return
     */
    List<ErpMessageRecipientsVO> findListByMessageGuid(@Param("messageGuid") String messageGuid);
}
