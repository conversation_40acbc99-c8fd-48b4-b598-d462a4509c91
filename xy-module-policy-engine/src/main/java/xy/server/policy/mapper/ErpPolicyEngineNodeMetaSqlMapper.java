package xy.server.policy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.policy.entity.ErpPolicyEngineNodeMetaSql;
import xy.server.policy.entity.model.vo.ErpPolicyEngineNodeMetaSqlVO;

/**
 * <p>
 * 决策节点组件属性_SQL Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Mapper
public interface ErpPolicyEngineNodeMetaSqlMapper extends BaseMapper<ErpPolicyEngineNodeMetaSql> {

    /**
     * 根据决策引擎节点GUID查询数据
     * @param policyEngineNodeGuid
     * @return
     */
    ErpPolicyEngineNodeMetaSqlVO getDataByPolicyEngineNodeGuid(@Param("policyEngineNodeGuid") String policyEngineNodeGuid);
}
