package xy.server.policy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.policy.entity.ErpPolicyEngineNodeMetaBroadcast;
import xy.server.policy.entity.model.vo.ErpPolicyEngineNodeMetaBroadcastVO;

/**
 * <p>
 * 决策节点组件属性_事件广播 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Mapper
public interface ErpPolicyEngineNodeMetaBroadcastMapper extends BaseMapper<ErpPolicyEngineNodeMetaBroadcast> {

    /**
     * 根据决策引擎节点GUID查询数据
     * @param policyEngineNodeGuid
     * @return
     */
    ErpPolicyEngineNodeMetaBroadcastVO getDataByPolicyEngineNodeGuid(@Param("policyEngineNodeGuid") String policyEngineNodeGuid);
}
