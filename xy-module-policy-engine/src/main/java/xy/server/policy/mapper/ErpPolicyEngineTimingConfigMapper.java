package xy.server.policy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.policy.entity.ErpPolicyEngineTimingConfig;
import xy.server.policy.entity.model.vo.ErpPolicyEngineTimingConfigVO;

import java.util.List;

/**
 * <p>
 * 定时触发配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Mapper
public interface ErpPolicyEngineTimingConfigMapper extends BaseMapper<ErpPolicyEngineTimingConfig> {

    /**
     * 根据决策引擎GUID查询数据
     * @param policyEngineGuid
     * @return
     */
    ErpPolicyEngineTimingConfigVO getDataByPolicyEngineGuid(@Param("policyEngineGuid") String policyEngineGuid);

    /**
     * 获取所有需要执行的定时任务列表
     * @return
     */
    List<ErpPolicyEngineTimingConfigVO> selectScheduledTaskList();
}
