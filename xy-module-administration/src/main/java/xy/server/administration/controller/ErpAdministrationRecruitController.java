package xy.server.administration.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.trans.annotation.XyTransMethod;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.administration.entity.model.qo.ErpAdministrationRecruitQO;
import xy.server.administration.entity.model.ro.ErpAdministrationRecruitRO;
import xy.server.administration.entity.model.vo.ErpAdministrationRecruitVO;
import xy.server.administration.service.IErpAdministrationRecruitService;

import java.util.List;


/**
 * <AUTHOR>
 * @apiNote 工单表 controller
 * @since 2024-04-26
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "招聘申请")
@RestController
@RequestMapping("/administration/erp-production-mgt-workorder")
@SystemClassLog(code = "ErpAdministrationRecruitController")
public class ErpAdministrationRecruitController {
    private final IErpAdministrationRecruitService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增工单表")
    public Boolean createOne(@RequestBody @Validated ErpAdministrationRecruitRO ro) {
        return service.create(ro);
    }

    @DeleteMapping("/delete/{workorderGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除工单表")
    public Boolean delete(@PathVariable("workorderGuid") String workorderGuid) {
        return service.delete(workorderGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除工单表")
    public Boolean deleteByBatch(@RequestBody List<String> workorderGuids) {
        return service.deleteByBatch(workorderGuids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改工单表")
    public Boolean update(@RequestBody @Validated ErpAdministrationRecruitRO ro) {
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    public Boolean saveDate(@RequestBody @Validated InsertOrUpdateList<ErpAdministrationRecruitRO> dataList) {
        return service.saveDate(dataList);
    }

    @GetMapping("/getOne/{workorderGuid}")
    @ApiOperation(value = "单条查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "查询单条工单表")
    public ErpAdministrationRecruitVO getOne(@PathVariable("workorderGuid") String workorderGuid) {
        return service.getDataById(workorderGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "列表查询工单表")
    public List<ErpAdministrationRecruitVO> findList(@RequestBody @Validated ErpAdministrationRecruitQO qo) {
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "分页查询工单表")
    public IPage<ErpAdministrationRecruitVO> findPage(@RequestBody @Validated PageParams<ErpAdministrationRecruitQO> pageParams) {
        return service.findPage(pageParams);
    }

}
