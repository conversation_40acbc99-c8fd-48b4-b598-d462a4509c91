package xy.server.administration.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.EasyExcelUtils;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtPostQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtPostRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtPostVO;
import xy.server.administration.service.IErpAdministrationMgtPostService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * 岗位管理 controller
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "岗位管理")
@RestController
@RequestMapping("/administration/erp-administration-mgt-post")
@SystemClassLog(code = "ErpAdministrationMgtPostController")
public class ErpAdministrationMgtPostController {
    private final IErpAdministrationMgtPostService service;
    private final EasyExcelUtils easyExcelUtils;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增岗位管理")
    public Boolean createOne(@RequestBody @Validated ErpAdministrationMgtPostRO ro, HttpServletRequest request){
        ro.setTenantGuid(request.getHeader("tenantGuid"));
        return service.create(ro);
    }

    @DeleteMapping("/delete/{postGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除岗位管理")
    public Boolean delete(@PathVariable("postGuid")String postGuid){
        return service.delete(postGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "批量删除岗位管理")
    public Boolean deleteByBatch(@RequestBody List<String> ids){
        return service.removeBatchByIds(ids);
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改/停用")
    @SystemMethodLog(type = "modify", description = "修改岗位管理")
    public Boolean update(@RequestBody @Validated ErpAdministrationMgtPostRO ro){
        return service.update(ro);
    }

    @GetMapping("/{postGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条岗位管理")
    public ErpAdministrationMgtPostVO getOne(@PathVariable("postGuid")String postGuid){
        return service.getById(postGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询岗位管理")
    public List<ErpAdministrationMgtPostVO> findList(@RequestBody @Validated ErpAdministrationMgtPostQO qo, HttpServletRequest request){
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        return service.findList(qo, tenantGuid);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询岗位管理")
    public IPage<ErpAdministrationMgtPostVO> findPage(@RequestBody @Validated PageParams<ErpAdministrationMgtPostQO> pageParams, HttpServletRequest request){
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        pageParams.setUserId(userGuid);
        pageParams.setTenantGuid(tenantGuid);
        return service.findPage(pageParams);
    }

    @PostMapping("/reordered")
    @ApiOperation(value = "重新排序")
    @SystemMethodLog(type = "modify", description = "重新排序")
    public Boolean reordered(@RequestBody @Validated List<SortEntityDto> entityDtoList) {
        return service.reordered(entityDtoList);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存（新增或者更新）")
    @SystemMethodLog(type = "modify", description = "保存（新增或者更新）")
    public Boolean saveData(@RequestBody @Validated InsertOrUpdateList<ErpAdministrationMgtPostRO> saveData, HttpServletRequest request) {
        String tenantGuid = request.getHeader("tenantGuid");
        return service.saveData(saveData, tenantGuid);
    }

    @GetMapping("/downloadTemplate")
    @ApiOperation(value = "下载模版")
    public void downloadTemplate(HttpServletResponse response) {
        easyExcelUtils.downloadTemplate(ErpAdministrationMgtPostRO.class, response);
    }

    @PostMapping("/importExcel")
    @ApiOperation(value = "Excel导入")
    public Boolean importExcel(@RequestPart("file") MultipartFile file, @RequestParam("isErrorResume") boolean isErrorResume) {
        return easyExcelUtils.analyzeExcel(file, ErpAdministrationMgtPostRO.class, service, isErrorResume);
    }

}
