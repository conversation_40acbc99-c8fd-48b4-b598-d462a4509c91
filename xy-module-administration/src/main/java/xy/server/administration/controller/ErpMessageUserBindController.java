package xy.server.administration.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.administration.entity.model.qo.ErpMessageUserBindQO;
import xy.server.administration.entity.model.ro.ErpMessageUserBindRO;
import xy.server.administration.entity.model.vo.ErpMessageUserBindVO;
import xy.server.administration.service.IErpMessageUserBindService;

import java.util.List;


/**
 * <AUTHOR>
 * @apiNote controller
 * @since 2024-03-14
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "")
@RestController
@RequestMapping("/administration/erp-message-user-bind")
@SystemClassLog(code = "ErpMessageUserBindController")
public class ErpMessageUserBindController {
    private final IErpMessageUserBindService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增")
    public Boolean createOne(@RequestBody @Validated ErpMessageUserBindRO ro) {
        return service.create(ro);
    }

    @DeleteMapping("/delete/{messageUserBindGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除")
    public Boolean delete(@PathVariable("messageUserBindGuid") String messageUserBindGuid) {
        return service.delete(messageUserBindGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除")
    public Boolean deleteByBatch(@RequestBody List<String> messageUserBindGuids) {
        return service.deleteByBatch(messageUserBindGuids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改")
    public Boolean update(@RequestBody @Validated ErpMessageUserBindRO ro) {
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    public Boolean saveDate(@RequestBody @Validated InsertOrUpdateList<ErpMessageUserBindRO> dataList) {
        return service.saveDate(dataList);
    }

    @GetMapping("/getOne/{messageUserBindGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条")
    public ErpMessageUserBindVO getOne(@PathVariable("messageUserBindGuid") String messageUserBindGuid) {
        return service.getDataById(messageUserBindGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询")
    public List<ErpMessageUserBindVO> findList(@RequestBody @Validated ErpMessageUserBindQO qo) {
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询")
    public IPage<ErpMessageUserBindVO> findPage(@RequestBody @Validated PageParams<ErpMessageUserBindQO> pageParams) {
        return service.findPage(pageParams);
    }

}
