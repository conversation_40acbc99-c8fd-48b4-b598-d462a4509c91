package xy.server.administration.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.EasyExcelUtils;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtDepartmentQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtDepartmentRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtDepartmentVO;
import xy.server.administration.entity.model.vo.ErpDepartmentMgtStaffVO;
import xy.server.administration.service.IErpAdministrationMgtDepartmentService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * 部门表 controller
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "部门表")
@RestController
@RequestMapping("/administration/erp-administration-mgt-department")
@SystemClassLog(code = "ErpAdministrationMgtDepartmentController")
public class ErpAdministrationMgtDepartmentController {
    private final IErpAdministrationMgtDepartmentService service;
    private final EasyExcelUtils easyExcelUtils;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增部门表")
//    biaom = "aa", ziduan = "bb", isxg = "ss", name = "#user",clazz = ErpAdministrationMgtDepartmentRO.class, type = TypeEnum.NATIVE)
    public Boolean createOne(@RequestBody ErpAdministrationMgtDepartmentRO ro, HttpServletRequest request){
        ro.setTenantGuid(request.getHeader("tenantGuid"));
        return service.create(ro);
    }

    @DeleteMapping("/delete/{departmentGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除部门表")
    public Boolean delete(@PathVariable("departmentGuid") String departmentGuid) {
        return service.delete(departmentGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "批量删除部门表")
    public Boolean deleteByBatch(@RequestBody List<String> ids) {
        return service.removeBatchByIdss(ids);
    }

    @PostMapping("/upDeTeState/{departmentGuid}")
    @ApiOperation(value = "停用")
    @SystemMethodLog(type = "modify", description = "停用")
    public Boolean upDeTeState(@PathVariable("departmentGuid") String departmentGuid) {
        return service.upDeTeState(departmentGuid);
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改部门表")
    public Boolean update(@RequestBody @Validated ErpAdministrationMgtDepartmentRO ro){
        return service.update(ro);
    }

    @GetMapping("/{departmentGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条部门表")
    public ErpAdministrationMgtDepartmentVO getOne(@PathVariable("departmentGuid") String departmentGuid) {
        return service.getById(departmentGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询部门表")
    public List<ErpAdministrationMgtDepartmentVO> findList(@RequestBody @Validated ErpAdministrationMgtDepartmentQO qo, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        return service.findList(qo, tenantGuid);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询部门表")
    public IPage<ErpAdministrationMgtDepartmentVO> findPage(@RequestBody @Validated PageParams<ErpAdministrationMgtDepartmentQO> pageParams, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        pageParams.setUserId(userGuid);
        pageParams.setTenantGuid(tenantGuid);
        return service.findPage(pageParams);
    }

    @PostMapping("/reordered")
    @ApiOperation(value = "重新排序")
    @SystemMethodLog(type = "modify", description = "重新排序")
    public Boolean reordered(@RequestBody @Validated List<SortEntityDto> entityDtoList) {
        return service.reordered(entityDtoList);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存（新增或者更新）")
    @SystemMethodLog(type = "modify", description = "保存（新增或者更新）")
    public Boolean saveData(@RequestBody @Validated InsertOrUpdateList<ErpAdministrationMgtDepartmentRO> saveData, HttpServletRequest request) {
        String tenantGuid = request.getHeader("tenantGuid");
        return service.saveData(saveData, tenantGuid);
    }

    @GetMapping("/department/{departmentGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条部门表")
    public List<ErpDepartmentMgtStaffVO> getStaffList(@PathVariable("departmentGuid") String departmentGuid) {
        return service.getStaffList(departmentGuid);
    }

    @GetMapping("/downloadTemplate")
    @ApiOperation(value = "下载模版")
    public void downloadTemplate(HttpServletResponse response) {
        easyExcelUtils.downloadTemplate(ErpAdministrationMgtDepartmentRO.class, response);
    }

    @PostMapping("/importExcel")
    @ApiOperation(value = "Excel导入")
    public Boolean importExcel(@RequestPart("file") MultipartFile file, @RequestParam("isErrorResume") boolean isErrorResume) {
        return easyExcelUtils.analyzeExcel(file, ErpAdministrationMgtDepartmentRO.class, service, isErrorResume);
    }

}
