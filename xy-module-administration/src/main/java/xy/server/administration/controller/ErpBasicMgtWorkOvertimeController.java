package xy.server.administration.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.tenant.history.SystemFieldHistory;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.administration.entity.ErpBasicMgtWorkOvertime;
import xy.server.administration.entity.model.qo.ErpBasicMgtWorkOvertimeQO;
import xy.server.administration.entity.model.ro.ErpBasicMgtWorkOvertimeRO;
import xy.server.administration.entity.model.vo.ErpBasicMgtWorkOvertimeVO;
import xy.server.administration.service.IErpBasicMgtWorkOvertimeService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * <p>
 * 加班安排 controller
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "加班安排")
@RestController
@RequestMapping("/administration/erp-basic-mgt-work-overtime")
@SystemClassLog(code = "ErpBasicMgtWorkOvertimeController")
public class ErpBasicMgtWorkOvertimeController {
    private final IErpBasicMgtWorkOvertimeService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增加班安排")
    public Boolean createOne(@RequestBody @Validated ErpBasicMgtWorkOvertimeRO ro, HttpServletRequest request) {
        ro.setTenantGuid(request.getHeader("tenantGuid"));
        return service.create(ro);
    }

    @DeleteMapping("/delete/{workOvertimeGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除加班安排")
    public Boolean delete(@PathVariable("workOvertimeGuid") String workOvertimeGuid) {
        return service.delete(workOvertimeGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "批量删除加班安排")
    public Boolean deleteByBatch(@RequestBody List<String> ids) {
        return service.removeBatchById(ids);
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改加班安排")
    public Boolean update(@RequestBody @Validated ErpBasicMgtWorkOvertimeRO ro) {
        return service.update(ro);
    }

    @GetMapping("/{workOvertimeGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条加班安排")
    public ErpBasicMgtWorkOvertimeVO getOne(@PathVariable("workOvertimeGuid") String workOvertimeGuid) {
        return service.getById(workOvertimeGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询加班安排")
    public List<ErpBasicMgtWorkOvertimeVO> findList(@RequestBody @Validated ErpBasicMgtWorkOvertimeQO qo, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        return service.findList(qo, tenantGuid);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询加班安排")
    public IPage<ErpBasicMgtWorkOvertimeVO> findPage(@RequestBody @Validated PageParams<ErpBasicMgtWorkOvertimeQO> pageParams, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        return service.findPage(pageParams);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存(新增或更新)")
    @SystemMethodLog(type = "save", description = "保存(新增或更新)")
    @SystemFieldHistory(targetEntity = ErpBasicMgtWorkOvertime.class)
    public Boolean saveData(@RequestBody @Validated InsertOrUpdateList<ErpBasicMgtWorkOvertimeRO> saveData, HttpServletRequest request) {
        String tenantGuid = request.getHeader("tenantGuid");
        return service.saveDate(saveData, tenantGuid);
    }

    @GetMapping("/stop/{workOvertimeGuid}")
    @ApiOperation(value = "停用/启用")
    @SystemMethodLog(type = "modify", description = "停用/启用")
    public Boolean stopState(@PathVariable("workOvertimeGuid") String workOvertimeGuid) {
        return service.stopState(workOvertimeGuid);
    }
}
