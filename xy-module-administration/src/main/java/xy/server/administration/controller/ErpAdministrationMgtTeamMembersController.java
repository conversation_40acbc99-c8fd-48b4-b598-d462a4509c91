package xy.server.administration.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtTeamMembersQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtTeamMembersRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtTeamMembersVO;
import xy.server.administration.service.IErpAdministrationMgtTeamMembersService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * <p>
 * 班组组员列表 controller
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "班组组员列表")
@RestController
@RequestMapping("/administration/erp-administration-mgt-team-members")
@SystemClassLog(code = "ErpAdministrationMgtTeamMembersController")
@ApiIgnore
public class ErpAdministrationMgtTeamMembersController {
    private final IErpAdministrationMgtTeamMembersService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增班组组员列表")
    public Boolean createOne(@RequestBody @Validated ErpAdministrationMgtTeamMembersRO ro, HttpServletRequest request) {
        ro.setTenantGuid(request.getHeader("tenantGuid"));
        return service.create(ro);
    }

    @DeleteMapping("/delete/{teamMembersGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除班组组员列表")
    public Boolean delete(@PathVariable("teamMembersGuid") String teamMembersGuid) {
        return service.delete(teamMembersGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "批量删除班组组员列表")
    public Boolean deleteByBatch(@RequestBody List<Integer> ids) {
        return service.removeBatchByIds(ids);
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改班组组员列表")
    public Boolean update(@RequestBody @Validated ErpAdministrationMgtTeamMembersRO ro) {
        return service.update(ro);
    }

    @GetMapping("/{teamMembersGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条班组组员列表")
    public ErpAdministrationMgtTeamMembersVO getOne(@PathVariable("teamMembersGuid") String teamMembersGuid) {
        return service.getById(teamMembersGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询班组组员列表")
    public List<ErpAdministrationMgtTeamMembersVO> findList(@RequestBody @Validated ErpAdministrationMgtTeamMembersQO qo, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        return service.findList(qo, tenantGuid);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询班组组员列表")
    public IPage<ErpAdministrationMgtTeamMembersVO> findPage(@RequestBody @Validated PageParams<ErpAdministrationMgtTeamMembersQO> pageParams, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        pageParams.setUserId(userGuid);
        pageParams.setTenantGuid(tenantGuid);
        return service.findPage(pageParams);
    }

    @PostMapping("/reordered")
    @ApiOperation(value = "重新排序")
    @SystemMethodLog(type = "modify", description = "重新排序")
    public Boolean reordered(@RequestBody @Validated List<SortEntityDto> entityDtoList) {
        return service.reordered(entityDtoList);
    }

}
