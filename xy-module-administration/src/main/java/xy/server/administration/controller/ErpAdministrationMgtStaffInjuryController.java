package xy.server.administration.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffInjuryQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffInjuryRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffInjuryVO;
import xy.server.administration.service.IErpAdministrationMgtStaffInjuryService;

import java.util.List;


/**
 * <AUTHOR>
 * @apiNote 工伤记录表 controller
 * @since 2024-10-22
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "工伤记录表")
@RestController
@RequestMapping("/administration/erp-administration-mgt-staff-injury")
@SystemClassLog(code = "ErpAdministrationMgtStaffInjuryController")
public class ErpAdministrationMgtStaffInjuryController {
    private final IErpAdministrationMgtStaffInjuryService service;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增工伤记录表")
    public Boolean createOne(@RequestBody @Validated ErpAdministrationMgtStaffInjuryRO ro) {
        return service.create(ro);
    }

    @DeleteMapping("/delete/{staffInjuryGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除工伤记录表")
    public Boolean delete(@PathVariable("staffInjuryGuid") String staffInjuryGuid) {
        return service.delete(staffInjuryGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除工伤记录表")
    public Boolean deleteByBatch(@RequestBody List<String> staffInjuryGuids) {
        return service.deleteByBatch(staffInjuryGuids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改工伤记录表")
    public Boolean update(@RequestBody @Validated ErpAdministrationMgtStaffInjuryRO ro) {
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    public Boolean saveDate(@RequestBody @Validated InsertOrUpdateList<ErpAdministrationMgtStaffInjuryRO> dataList) {
        return service.saveDate(dataList);
    }

    @GetMapping("/getOne/{staffInjuryGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条工伤记录表")
    public ErpAdministrationMgtStaffInjuryVO getOne(@PathVariable("staffInjuryGuid") String staffInjuryGuid) {
        return service.getDataById(staffInjuryGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询工伤记录表")
    public List<ErpAdministrationMgtStaffInjuryVO> findList(@RequestBody @Validated ErpAdministrationMgtStaffInjuryQO qo) {
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询工伤记录表")
    public IPage<ErpAdministrationMgtStaffInjuryVO> findPage(@RequestBody @Validated PageParams<ErpAdministrationMgtStaffInjuryQO> pageParams) {
        return service.findPage(pageParams);
    }

}
