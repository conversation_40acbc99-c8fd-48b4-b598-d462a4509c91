package xy.server.administration.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffMealQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffMealRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffMealVO;
import xy.server.administration.service.IErpAdministrationMgtStaffMealService;

import java.util.List;


/**
 * @apiNote 报餐记录表 controller
 * <AUTHOR>
 * @since 2024-10-22
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "报餐记录表")
@RestController
@RequestMapping("/administration/erp-administration-mgt-staff-meal")
@SystemClassLog(code = "ErpAdministrationMgtStaffMealController")
public class ErpAdministrationMgtStaffMealController {
    private final IErpAdministrationMgtStaffMealService service;

                @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增报餐记录表")
    public Boolean createOne(@RequestBody @Validated ErpAdministrationMgtStaffMealRO ro){
        return service.create(ro);
    }

    @DeleteMapping("/delete/{staffMealGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除报餐记录表")
    public Boolean delete(@PathVariable("staffMealGuid")String staffMealGuid){
        return service.delete(staffMealGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除报餐记录表")
    public Boolean deleteByBatch(@RequestBody List<String> staffMealGuids){
        return service.deleteByBatch(staffMealGuids);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改报餐记录表")
    public Boolean update(@RequestBody @Validated ErpAdministrationMgtStaffMealRO ro){
        return service.update(ro);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据（新增或编辑）")
    @SystemMethodLog(type = "modify", description = "保存数据（新增或编辑）")
    public Boolean saveDate(@RequestBody @Validated InsertOrUpdateList<ErpAdministrationMgtStaffMealRO> dataList){
        return service.saveDate(dataList);
    }

    @GetMapping("/getOne/{staffMealGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条报餐记录表")
    public ErpAdministrationMgtStaffMealVO getOne(@PathVariable("staffMealGuid")String staffMealGuid){
        return service.getDataById(staffMealGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询报餐记录表")
    public List<ErpAdministrationMgtStaffMealVO> findList(@RequestBody @Validated ErpAdministrationMgtStaffMealQO qo){
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询报餐记录表")
    public IPage<ErpAdministrationMgtStaffMealVO> findPage(@RequestBody @Validated PageParams<ErpAdministrationMgtStaffMealQO> pageParams){
        return service.findPage(pageParams);
    }

}
