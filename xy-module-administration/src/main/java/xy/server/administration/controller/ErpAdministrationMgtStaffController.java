package xy.server.administration.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.tenant.history.SystemFieldHistory;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.trans.annotation.XyTransMethod;
import com.xunyue.config.util.EasyExcelUtils;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import xy.server.administration.entity.ErpAdministrationMgtStaff;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffQO;
import xy.server.administration.entity.model.qo.ErpAuditorQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffRO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffResignationRO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffUserRo;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtDepartmentAndStaffVO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStafSVO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffVO;
import xy.server.administration.entity.model.vo.ErpAuditorVo;
import xy.server.administration.service.IErpAdministrationMgtStaffService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 员工列表 controller
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "员工列表")
@RestController
@RequestMapping("/administration/erp-administration-mgt-staff")
@SystemClassLog(code = "ErpAdministrationMgtStaffController")
public class ErpAdministrationMgtStaffController {
    private final IErpAdministrationMgtStaffService service;
    private final EasyExcelUtils easyExcelUtils;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增员工列表")
    public Boolean createOne(@RequestBody @Validated ErpAdministrationMgtStaffRO ro, HttpServletRequest request) throws ParseException {
        ro.setTenantGuid(request.getHeader("tenantGuid"));
        return service.create(ro);
    }

    @DeleteMapping("/delete/{staffGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除员工列表")
    public Boolean delete(@PathVariable("staffGuid") String staffGuid) {
        return service.delete(staffGuid);
    }

    @PostMapping("/resignation")
    @ApiOperation(value = "离职(type 1:离职 2恢复)")
    @SystemMethodLog(type = "delete", description = "离职")
    public Boolean resignation(@RequestBody ErpAdministrationMgtStaffResignationRO ro) {
        return service.resignation(ro);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "批量删除员工列表")
    public Boolean deleteByBatch(@RequestBody List<String> ids) {
        return service.removeBatchByIdss(ids);
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改")
    @SystemMethodLog(type = "modify", description = "修改员工列表")
    public Boolean update(@RequestBody @Validated ErpAdministrationMgtStaffRO ro) {
        return service.update(ro);
    }


    @PutMapping("/updateuserGuid")
    @ApiOperation(value = "同步用户信息")
    @SystemMethodLog(type = "modify", description = "修改员工列表")
    public Boolean updateuserGuid(@RequestBody  List<ErpAdministrationMgtStaffUserRo> ro) {
        return service.updateuserGuid(ro);
    }

    @PostMapping("/noUserAvailableList")
    @ApiOperation(value = "没有关系用户的员工列表")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "列表查询员工列表")
    public List<ErpAdministrationMgtStaffVO> noUserAvailableList(@RequestBody @Validated ErpAdministrationMgtStaffQO qo, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        return service.noUserAvailableList(qo, tenantGuid);
    }


    @GetMapping("/{staffGuid}")
    @ApiOperation(value = "单条查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "查询单条员工列表")
    public ErpAdministrationMgtStaffVO getOne(@PathVariable("staffGuid") String staffGuid) {
        return service.getById(staffGuid);
    }

    @GetMapping("")
    @ApiOperation(value = "过去当前员工登录信息")
    @SystemMethodLog(type = "query", description = "过去当前员工登录信息")
    public ErpAdministrationMgtStafSVO getStaff(HttpServletRequest request) {
        String loginAccount = request.getHeader("loginAccount");
        return service.getStaff(loginAccount);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "列表查询员工列表")
    public List<ErpAdministrationMgtStaffVO> findList(@RequestBody @Validated ErpAdministrationMgtStaffQO qo, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        return service.findList(qo, tenantGuid);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "分页查询员工列表")
    public IPage<ErpAdministrationMgtStaffVO> findPage(@RequestBody @Validated PageParams<ErpAdministrationMgtStaffQO> pageParams, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        pageParams.setUserId(userGuid);
        pageParams.setTenantGuid(tenantGuid);
        return service.findPage(pageParams);
    }

//    @PostMapping("/reordered")
//    @ApiOperation(value = "重新排序")
//    @SystemMethodLog(type = "modify", description = "重新排序")
//    public Boolean reordered(@RequestBody @Validated List<SortEntityDto> entityDtoList) {
//        return service.reordered(entityDtoList);
//    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存（新增或者更新）")
    @SystemFieldHistory(targetEntity = ErpAdministrationMgtStaff.class)
    @SystemMethodLog(type = "modify", description = "保存（新增或者更新）")
    public Boolean saveData(@RequestBody @Validated InsertOrUpdateList<ErpAdministrationMgtStaffRO> saveData, HttpServletRequest request) {
        String tenantGuid = request.getHeader("tenantGuid");
        return service.saveData(saveData, tenantGuid);
    }

    @GetMapping("/downloadTemplate")
    @ApiOperation(value = "下载模版")
    public void downloadTemplate(HttpServletResponse response) {
        easyExcelUtils.downloadTemplate(ErpAdministrationMgtStaffRO.class, response);
    }

    @PostMapping("/importExcel")
    @ApiOperation(value = "Excel导入")
    public Boolean importExcel(@RequestPart("file") MultipartFile file, @RequestParam("isErrorResume") boolean isErrorResume) {
        return easyExcelUtils.analyzeExcel(file, ErpAdministrationMgtStaffRO.class, service, isErrorResume);
    }

    @PostMapping("/findStaffAndDepartmentList")
    @ApiOperation(value = "获取部门及部门下的员工")
    @XyTransMethod
    @SystemMethodLog(type = "query", description = "获取部门及部门下的员工")
    public List<ErpAdministrationMgtDepartmentAndStaffVO> findStaffAndDepartmentList(@RequestBody @Validated ErpAdministrationMgtStaffQO qo) {
        return service.findStaffAndDepartmentList(qo);
    }

    @PostMapping("/findListAuditor")
    @ApiOperation(value = "根据审核流id获取审核人数据")
    @SystemMethodLog(type = "query", description = "根据审核流id获取审核人数据")
    public Map<String, ErpAuditorVo> findStaffAndDepartmentList(@RequestBody @Validated List<String> list) {
        return service.mapNewAuditMan(list);
    }
    @PostMapping("/selectAuditorByKey")
    @ApiOperation(value = "根据审核人查询审核流数据")
    @SystemMethodLog(type = "query", description = "根据审核人查询审核流数据")
    public List<ErpAuditorVo> selectAuditorByKey(@RequestBody @Validated ErpAuditorQO qo) {
        return service.selectAuditorByKey(qo);
    }

}
