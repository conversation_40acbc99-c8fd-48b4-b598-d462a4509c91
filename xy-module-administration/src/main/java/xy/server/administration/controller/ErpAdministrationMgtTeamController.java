package xy.server.administration.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.excel.EasyExcelDataHandleService;
import com.xunyue.config.util.EasyExcelUtils;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtTeamQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtTeamMembersRO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtTeamRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtTeamVO;
import xy.server.administration.service.IErpAdministrationMgtTeamMembersService;
import xy.server.administration.service.IErpAdministrationMgtTeamService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * 班组管理 controller
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "班组管理")
@RestController
@RequestMapping("/administration/erp-administration-mgt-team")
@SystemClassLog(code = "ErpAdministrationMgtTeamController")
public class ErpAdministrationMgtTeamController {
    private final IErpAdministrationMgtTeamService service;
    private final IErpAdministrationMgtTeamMembersService iErpAdministrationMgtTeamMembersService;
    private final EasyExcelUtils easyExcelUtils;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @SystemMethodLog(type = "insert", description = "新增班组管理")
    public Boolean createOne(@RequestBody @Validated ErpAdministrationMgtTeamRO ro, HttpServletRequest request) {
        ro.setTenantGuid(request.getHeader("tenantGuid"));
        return service.create(ro);
    }

    @DeleteMapping("/delete/{teamGuid}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除班组管理")
    public Boolean delete(@PathVariable("teamGuid") String teamGuid) {
        return service.delete(teamGuid);
    }

    @PostMapping("/deleteByBatch")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "批量删除班组管理")
    public Boolean deleteByBatch(@RequestBody List<String> ids) {
        return service.deleteByBatch(ids);
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改/停用/恢复")
    @SystemMethodLog(type = "modify", description = "修改班组管理")
    public Boolean update(@RequestBody @Validated ErpAdministrationMgtTeamRO ro) {
        return service.update(ro);
    }

    @GetMapping("/{teamGuid}")
    @ApiOperation(value = "单条查询")
    @SystemMethodLog(type = "query", description = "查询单条班组管理")
    public ErpAdministrationMgtTeamVO getOne(@PathVariable("teamGuid") String teamGuid) {
        return service.getById(teamGuid);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询班组管理")
    public List<ErpAdministrationMgtTeamVO> findList(@RequestBody @Validated ErpAdministrationMgtTeamQO qo, HttpServletRequest request) {
        String tenantGuid = request.getHeader("tenantGuid");
        return service.findList(qo, tenantGuid);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询班组管理")
    public IPage<ErpAdministrationMgtTeamVO> findPage(@RequestBody @Validated PageParams<ErpAdministrationMgtTeamQO> pageParams, HttpServletRequest request) {
        String userGuid = request.getHeader("userGuid");
        String tenantGuid = request.getHeader("tenantGuid");
        pageParams.setUserId(userGuid);
        pageParams.setTenantGuid(tenantGuid);
        return service.findPage(pageParams);
    }

    @PostMapping("/reordered")
    @ApiOperation(value = "重新排序")
    @SystemMethodLog(type = "modify", description = "重新排序")
    public Boolean reordered(@RequestBody @Validated List<SortEntityDto> entityDtoList) {
        return service.reordered(entityDtoList);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存（新增或者更新）")
    @SystemMethodLog(type = "modify", description = "保存（新增或者更新）")
    public Boolean saveData(@RequestBody @Validated InsertOrUpdateList<ErpAdministrationMgtTeamRO> saveData, HttpServletRequest request) {
        String tenantGuid = request.getHeader("tenantGuid");
        return service.saveData(saveData, tenantGuid);
    }

    @GetMapping("/downloadTemplate")
    @ApiOperation(value = "下载模版")
    public void downloadTemplate(HttpServletResponse response) {
        Class[] classes = new Class[] {ErpAdministrationMgtTeamRO.class, ErpAdministrationMgtTeamMembersRO.class};
        easyExcelUtils.downloadTemplate(classes, response);
    }

    @PostMapping("/importExcel")
    @ApiOperation(value = "Excel导入")
    public Boolean importExcel(@RequestPart("file") MultipartFile file, @RequestParam("isErrorResume") boolean isErrorResume) {
        Class[] classes = new Class[] {ErpAdministrationMgtTeamRO.class, ErpAdministrationMgtTeamMembersRO.class};
        EasyExcelDataHandleService[] easyExcelDataHandleServices = new EasyExcelDataHandleService[] {
                service, iErpAdministrationMgtTeamMembersService
        };
        return easyExcelUtils.analyzeExcel(file, classes, easyExcelDataHandleServices, isErrorResume);
    }


}
