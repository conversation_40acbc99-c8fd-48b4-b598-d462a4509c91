package xy.server.administration.i18n;

import com.xunyue.common.i18n.BaseResultErrorCode;

/**
 * <AUTHOR>
 * @Date 2023-07-14 17:48
 * <p>
 * 错误代码枚举
 * </p>
 **/
public enum ResultErrorCode implements BaseResultErrorCode {

    NO_REPETITION_ADMINISTRATION_DEPARTMENT("同一租户下部门名称不能重复", 1040000000),
    NO_REPETITION_ADMINISTRATION_DEPARTMENTD("已被使用不能进行删除或者编辑操作，只能进行停用", 1040000001),
    NO_REPETITION_ADMINISTRATION_STAFFNAME("同一租户下员工名称不能重复", 1040000002),
    NO_REPETITION_ADMINISTRATION_STAFFDATELE("已绑定客户资料的员工不能进行删除操作，只能进行离职", 1040000003),
    NO_REPETITION_ADMINISTRATION_ZHUMI("同一租户下班组名称不能重复", 1040000004),
    NO_REPETITION_ADMINISTRATION_CHENY("同一班组下员工名称不能重复", 1040000005),
    NO_REPETITION_ADMINISTRATION_GANWEI("同一租户下岗位名称不能重复", 1040000006),
    NO_REPETITION_BASIC_WORK_OVERTIME("加班名称不能重复", 1040000007),
    NO_REPETITION_BASIC_WORK_OVERLAP("选择的时间，已有加班安排，请选择其他时间", 1040000008),
    NO_REPETITION_BASIC_TIME_EXPIRES("时间已经过期", 1040000009),
    NO_REPETITION_STARTDATE_IS_NOT_OVER_ENDADTE("加班开始时间不能大于加班结束时间", 1040000010),
    NO_REPETITION_BASIC_NO_OVERTIME("未加班", 1040000011),
    NO_REPETITION_BASIC_BE_WORKING_OVERTIME("加班中", 1040000012),
    NO_REPETITION_BASIC_HAVE_WORKED_OVERTIME("已加班", 1040000013),
    NO_REPETITION_DATA_NOT_EXIST("数据不存在", 1040000014),
    NO_REPETITION_ADMINISTRATION_DEPT("同一部门名称不能重复", 1040000015);

    private String msg;
    private int code;

    ResultErrorCode(String msg, int code) {
        this.msg = msg;
        this.code = code;

    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }


}
