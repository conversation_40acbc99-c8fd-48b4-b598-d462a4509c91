package xy.server.administration.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <p>
 * 入住记录表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffCheckInRO对象", description = "入住记录表")
public class ErpAdministrationMgtStaffCheckInRO extends BaseEntity {

    @ApiModelProperty(value = "入住单号")
    private String staffCheckInNumber;

    @ApiModelProperty(value = "入住类型（字典：1正常入住，2变更位置）")
    private String checkInType;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "PK")
    private String staffCheckInGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "单据日期（可变更的）不能为空")
    @ApiModelProperty(value = "单据日期（可变更的）")
    private LocalDateTime receiptDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "退房日期")
    private LocalDateTime checkOutDate;

    @ApiModelProperty(value = "入住原因")
    private String reason;

    @ApiModelProperty(value = "入住位置")
    private String warehouseSpaceGuid;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "员工guid")
    private String staffGuid;

    @ApiModelProperty(value = "")
    private Object toJson;

}
