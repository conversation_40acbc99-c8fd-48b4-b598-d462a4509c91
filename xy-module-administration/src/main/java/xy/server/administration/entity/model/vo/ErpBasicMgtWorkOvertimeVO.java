package xy.server.administration.entity.model.vo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 加班安排VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtWorkOvertimeVO对象", description = "加班安排")
public class ErpBasicMgtWorkOvertimeVO {

    @ApiModelProperty(value = "租户GUID")
    private String tenantGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "PK")
    private String workOvertimeGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "加班名称")
    private String workOvertimeName;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "周模板GUID")
    private String weeklyTemplateGuid;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "逻辑删除")
    @TableLogic(value = "false", delval = "true")
    private Boolean deleted;

    @ApiModelProperty(value = "时长")
    private Double duration;

    @ApiModelProperty(value = "加班状态")
    private String workState;

    @ApiModelProperty(value = "周模板名称")
    private String weeklyTemplateName;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

    @ApiModelProperty(value = "")
    private String equipmentGuid;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;
}
