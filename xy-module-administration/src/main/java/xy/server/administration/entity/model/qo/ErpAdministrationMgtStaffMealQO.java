package xy.server.administration.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 报餐记录表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffMealQO对象", description = "报餐记录表")
public class ErpAdministrationMgtStaffMealQO {

    @ApiModelProperty(value = "报餐单号")
    private String staffMealNumber;

    @ApiModelProperty(value = "报餐类型（字典：1正常报餐，2客户报餐）")
    private List<String> staffMealType;

    @ApiModelProperty(value = "开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束日期")
    private LocalDateTime endTime;

}