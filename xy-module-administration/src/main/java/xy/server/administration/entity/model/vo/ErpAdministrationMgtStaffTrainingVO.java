package xy.server.administration.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 培训记录表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpAdministrationMgtStaffTrainingVO对象", description = "培训记录表")
public class ErpAdministrationMgtStaffTrainingVO {

    @ApiModelProperty(value = "培训单号")
    private String trainingNumber;

    @ApiModelProperty(value = "租户id")
    private String tenantGuid;

    @ApiModelProperty(value = "培训类型（字典：1常规培训）")
    private String trainingType;

    @XyTrans(dictionaryKey = "TRAINING_TYPE",dictionaryValue = "trainingType")
    @ApiModelProperty(value = "培训类型（字典：1常规培训）")
    private String trainingTypeName;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "PK")
    private String staffTrainingGuid;

    @ApiModelProperty(value = "员工guid")
    private String staffGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期（可变更的）")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "培训标题")
    private String trainingTitle;

    @ApiModelProperty(value = "培训内容")
    private String trainingContent;

    @ApiModelProperty(value = "培训版本")
    private String trainingVersion;

    @ApiModelProperty(value = "培训状态(字典：1有，2无)")
    private String trainingStatus;

    @XyTrans(dictionaryKey = "TRAINING_STATUS",dictionaryValue = "trainingStatus")
    @ApiModelProperty(value = "培训状态(字典：1有，2无)")
    private String trainingStatusName;

    @ApiModelProperty(value = "培训成绩")
    private String trainingResults;

    @ApiModelProperty(value = "培训地点")
    private String trainingLocation;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "培训开始时间")
    private LocalDateTime trainingStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "培训结束时间")
    private LocalDateTime trainingEndTime;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "被培训的员工")
    private List<ErpAdministrationMgtTrainingStaffVO> trainingStaffROList;

    @ApiModelProperty(value = "")
    private Object toJson;

    @ApiModelProperty(value = "培训主讲员")
    private String trainer;

}
