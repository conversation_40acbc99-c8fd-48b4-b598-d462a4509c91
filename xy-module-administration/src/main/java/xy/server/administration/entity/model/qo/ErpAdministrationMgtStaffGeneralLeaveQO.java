package xy.server.administration.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
        /**
* <p>
* 员工请假记录表QO
* </p>
*
* <AUTHOR>
* @since 2024-10-22
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffGeneralLeaveQO对象", description = "员工请假记录表")
public class ErpAdministrationMgtStaffGeneralLeaveQO{

                @ApiModelProperty(value = "请假记录单号")
    private String generalLeaveNumber;

            @ApiModelProperty(value = "租户id")
    private String tenantGuid;

                @ApiModelProperty(value = "请假类型（字典：1事假）")
    private String generalLeaveType;

                @ApiModelProperty(value = "备注或描述")
    private String description;

            @ApiModelProperty(value = "创建人GUID(取用户表UserGUID)")
    private String creatorGuid;

                @ApiModelProperty(value = "创建人")
    private String creator;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

            @ApiModelProperty(value = "最后修改人GUID(取用户表UserGUID)")
    private String lastUpdaterGuid;

                @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

            @ApiModelProperty(value = "PK")
    private String staffGeneralLeaveGuid;

            @ApiModelProperty(value = "员工id")
    private String staffGuid;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

            @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

            @ApiModelProperty(value = "状态")
    private String auditStatus;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

            @ApiModelProperty(value = "扩展字段")
    private Object toJson;

}
