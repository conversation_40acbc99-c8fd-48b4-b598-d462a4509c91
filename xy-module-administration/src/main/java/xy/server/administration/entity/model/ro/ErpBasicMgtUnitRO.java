package xy.server.administration.entity.model.ro;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtUnitURO对象", description = "")
public class ErpBasicMgtUnitRO extends BaseEntity {

    @ApiModelProperty(value = "租户Guid ")
    private String tenantGuid;

    @ApiModelProperty(value = "")
    private String unitGuid;

    //@NotNull(message = "顺序号不能为空")
    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    //@NotNull(message = "单位名称不能为空")
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    //@NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    //@NotNull(message = "已被使用(0未使用，1已使用)不能为空")
    @ApiModelProperty(value = "已被使用(0未使用，1已使用)")
    private Boolean isUsed;

    //@NotNull(message = "状态(0停用，1启用)不能为空")
    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "排序方式")
    private Integer operaType;//1上移 2下移


    //@NotNull(message = "逻辑删除不能为空")
    @TableLogic(value = "false", delval = "true")
    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

}
