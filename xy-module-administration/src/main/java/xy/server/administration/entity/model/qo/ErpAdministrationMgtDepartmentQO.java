package xy.server.administration.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 部门表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtDepartmentQO对象", description = "部门表")
public class ErpAdministrationMgtDepartmentQO {

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "false查全部，true只查启用的")
    private Boolean type;
}