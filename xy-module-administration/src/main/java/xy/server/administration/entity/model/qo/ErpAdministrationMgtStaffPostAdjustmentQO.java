package xy.server.administration.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 调岗记录表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffPostAdjustmentQO对象", description = "调岗记录表")
public class ErpAdministrationMgtStaffPostAdjustmentQO {

    @ApiModelProperty(value = "调岗单号")
    private String postAdjustmentNumber;

    @ApiModelProperty(value = "调岗类型（字典：1人事变动）")
    private List<String> postAdjustmentType;

    @ApiModelProperty(value = "员工")
    private String staffName;

    @ApiModelProperty(value = "开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束日期")
    private LocalDateTime endTime;

}