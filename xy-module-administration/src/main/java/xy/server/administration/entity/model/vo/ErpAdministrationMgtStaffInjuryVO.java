package xy.server.administration.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 工伤记录表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpAdministrationMgtStaffInjuryVO对象", description = "工伤记录表")
public class ErpAdministrationMgtStaffInjuryVO {

    @ApiModelProperty(value = "工伤单号")
    private String injuryNumber;

    @ApiModelProperty(value = "租户id")
    private String tenantGuid;

    @ApiModelProperty(value = "工伤类型（字典：1一般工伤，2职业病工伤，3交通事故工伤，4求助伤残）")
    private String injuryType;

    @XyTrans(dictionaryKey = "INJURY_TYPE",dictionaryValue = "injuryType")
    @ApiModelProperty(value = "工伤类型（字典：1一般工伤，2职业病工伤，3交通事故工伤，4求助伤残）")
    private String injuryTypeName;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "PK")
    private String staffInjuryGuid;

    @ApiModelProperty(value = "员工guid")
    private String staffGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期（可变更的）")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "医疗机构")
    private String medicalInstitutions;

    @ApiModelProperty(value = "医疗费用")
    private BigDecimal medicalExpenses;

    @ApiModelProperty(value = "公司支付费用")
    private BigDecimal companyPaymentFees;

    @ApiModelProperty(value = "个人支付费用")
    private BigDecimal personalPaymentFees;

    @ApiModelProperty(value = "赔偿费")
    private BigDecimal compensationFees;

    @ApiModelProperty(value = "责任描述")
    private String responsibilityDescription;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结案日期")
    private LocalDateTime closeDate;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "员工name")
    private String staffName;

    @ApiModelProperty(value = "")
    private Object toJson;

    @ApiModelProperty(value = "图片")
    private List<ErpAdministrationMgtStaffRecordsFileVO> recordsFileROS;

}
