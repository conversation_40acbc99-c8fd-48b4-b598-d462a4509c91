package xy.server.administration.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStafSVO", description = "用户信息")
public class ErpAdministrationMgtStafSVO {

    @ApiModelProperty(value = "用户名称名字(简称)")
    private String staffFullName;

    @ApiModelProperty(value = "用户账号")
    private String loginAccount;

}
