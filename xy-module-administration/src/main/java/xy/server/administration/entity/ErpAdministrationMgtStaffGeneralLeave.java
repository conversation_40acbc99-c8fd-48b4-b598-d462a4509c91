package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 员工请假记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Getter
@Setter
        @Accessors(chain = true)
    @TableName(value = "erp_administration_mgt_staff_general_leave", autoResultMap = true)
public class ErpAdministrationMgtStaffGeneralLeave extends BaseEntity implements Serializable{

private static final long serialVersionUID=1L;
                                /**
         * 请假记录单号
         */
                                        @TableField("general_leave_number")
                                                                        private String generalLeaveNumber;
                                                /**
         * 请假类型（字典：1事假）
         */
                                        @TableField("general_leave_type")
                                                                        private String generalLeaveType;
                                    /**
         * 备注或描述
         */
                                        @TableField("description")
                                                                        private String description;
                                                                                                            /**
         * 逻辑删除
         */
                                        @TableLogic
                                                                        private Boolean deleted;
                                            /**
         * PK
         */
                                                    @TableId("staff_general_leave_guid")
                                                                                    private String staffGeneralLeaveGuid;
                                    /**
         * 员工id
         */
                                        @TableField("staff_guid")
                                                                        private String staffGuid;
                                    /**
         * 开始时间
         */
                                        @TableField("start_time")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime startTime;
                                    /**
         * 结束时间
         */
                                        @TableField("end_time")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime endTime;
                                    /**
         * 流程实例ID
         */
                                        @TableField("process_instance_id")
                                                                        private String processInstanceId;
                                    /**
         * 状态
         */
                                        @TableField("audit_status")
                                                                        private String auditStatus;
                                    /**
         * 审核时间
         */
                                        @TableField("audit_date")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime auditDate;
                                    /**
         * 扩展字段
         */
                                        @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
                                                                        private Object toJson;



        }
