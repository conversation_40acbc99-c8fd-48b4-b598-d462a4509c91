package xy.server.administration.entity.model.ro;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelTemplate;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 班组管理RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
@XyExcelTemplate("班组资料模板")
@ApiModel(value = "ErpAdministrationMgtTeamURO对象", description = "班组管理")
public class ErpAdministrationMgtTeamRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String teamGuid;

    @NotNull(message = "班组编码不能为空")
    @ExcelProperty("班组编码")
    @ApiModelProperty(value = "班组编码")
    private String teamCode;


    @ApiModelProperty(value = "是否自动编码0:否1：是")
    private String isCode;

    @NotNull(message = "班组名称不能为空")
    @ExcelProperty("班组名称")
    @ApiModelProperty(value = "班组名称")
    private String teamName;


    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @ExcelProperty(value = "备注")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ExcelProperty("部门名称")
    @ApiModelProperty(value = "部门_guid")
    private String departmentGuid;

    @ApiModelProperty(value = "已被使用(0未使用，1已使用)")
    private Boolean isUsed;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

    @ApiModelProperty(value = "班组组员列表")
    List<ErpAdministrationMgtTeamMembersRO> teamMembersROS;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;
}
