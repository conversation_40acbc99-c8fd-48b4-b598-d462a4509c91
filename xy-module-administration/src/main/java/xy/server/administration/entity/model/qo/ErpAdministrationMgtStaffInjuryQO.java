package xy.server.administration.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 工伤记录表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffInjuryQO对象", description = "工伤记录表")
public class ErpAdministrationMgtStaffInjuryQO {

    @ApiModelProperty(value = "工伤单号")
    private String injuryNumber;

    @ApiModelProperty(value = "工伤类型（字典：1一般工伤，2职业病工伤，3交通事故工伤，4求助伤残）")
    private List<String> injuryType;

    @ApiModelProperty(value = "员工")
    private String staffName;

    @ApiModelProperty(value = "开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束日期")
    private LocalDateTime endTime;

}