package xy.server.administration.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 奖罚记录表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffRewardsPunishmentsQO对象", description = "奖罚记录表")
public class ErpAdministrationMgtStaffRewardsPunishmentsQO {

    @ApiModelProperty(value = "奖罚单号")
    private String rewardsPunishmentsNumber;

    @ApiModelProperty(value = "奖罚类型（字典：1常规奖励，2常规惩罚）")
    private List<String> rewardsPunishmentsType;

    @ApiModelProperty(value = "员工")
    private String staffName;

    @ApiModelProperty(value = "开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束日期")
    private LocalDateTime endTime;

}