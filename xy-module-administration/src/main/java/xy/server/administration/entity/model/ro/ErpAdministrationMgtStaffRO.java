package xy.server.administration.entity.model.ro;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.xunyue.config.excel.annotation.XyExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelTemplate;
import com.xunyue.config.excel.converter.XyExcelConverter;
import com.xunyue.config.excel.enums.ExcelPropertyTypeEnum;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 员工列表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
@XyExcelTemplate("员工资料模版")
@ApiModel(value = "ErpAdministrationMgtStaffURO对象", description = "员工列表")
public class ErpAdministrationMgtStaffRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String staffGuid;

    @NotBlank(message = "员工名字(简称)不能为空")
    @ExcelProperty("姓名")
    @ApiModelProperty(value = "员工名字(简称)")
    private String staffShortName;

    @NotBlank(message = "员工工号不能为空")
    @ExcelProperty("工号")
    @ApiModelProperty(value = "员工工号")
    private String staffCode;

    @NotBlank
    @ExcelProperty("部门名称")
    @ApiModelProperty(value = "部门_guid")
    private String departmentGuid;

    @NotBlank
    @ExcelProperty("岗位名称")
    @ApiModelProperty(value = "岗位_guid")
    private String postGuid;

    @NotNull
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("身份证")
    @ApiModelProperty(value = "身份证")
    private String idcard;


    @NotNull
    @XyExcelProperty(type = ExcelPropertyTypeEnum.DICTIONARY, dictKey = "GENDER")
    @ExcelProperty(value = "性别", converter = XyExcelConverter.class)
    @ApiModelProperty(value = "性别(1男，2女，3中性别)")
    private Integer gender;


    @XyExcelProperty(type = ExcelPropertyTypeEnum.DATE, format = "yyyy-MM-dd")
    @ExcelProperty(value = "出身日期（yyyy-MM-dd）", converter = XyExcelConverter.class)
    @ApiModelProperty(value = "出生日期")
    private LocalDateTime dateOfBirth;

    @NotNull
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("开户银行")
    @ApiModelProperty(value = "开户银行")
    private String openingBank;

    @NotNull
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("电话")
    @ApiModelProperty(value = "电话")
    private String telephone;

    @NotNull
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("银行帐号")
    @ApiModelProperty(value = "银行帐号")
    private String bankAccount;

    @NotNull
    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("手机")
    @ApiModelProperty(value = "手机")
    private String mobilephone;

    @ExcelProperty("居住地详细地址")
    @ApiModelProperty(value = "居住地详细地址")
    private String residentialAddress;

    @ApiModelProperty(value = "居住地地址行政区域_guid")
    private String residentialAddressAdministrativeAreaGuid;

    @ExcelProperty("出生地详细地址")
    @ApiModelProperty(value = "出生地详细地址")
    private String birthAddress;

    @ApiModelProperty(value = "出生地详细地址行政区域_guid")
    private String birthAddressAdministrativeAreaGuid;

    @ExcelProperty("紧急联系人")
    @ApiModelProperty(value = "紧急联系人")
    private String emergencyContact;

    @ExcelProperty("紧急联系电话")
    @ApiModelProperty(value = "紧急联系电话")
    private String emergencyContactPhoneNumber;


    @ExcelProperty("紧急联系人关系")
    @ApiModelProperty(value = "紧急联系人关系(父子，母子，亲戚等)")
    private String emergencyContactRelationship;

    @XyExcelProperty(type = ExcelPropertyTypeEnum.DICTIONARY, dictKey = "MARITAL_STATUS", isUseDefaultValue = true)
    @ExcelProperty(value = "婚姻状况（未婚；已婚；离异；丧偶）", converter = XyExcelConverter.class)
    @ApiModelProperty(value = "婚姻状况(0未婚，1已婚)")
    private Integer maritalStatus;

    @XyExcelProperty(type = ExcelPropertyTypeEnum.DATE, format = "yyyy-MM-dd")
    @ExcelProperty(value = "入职日期（yyyy-MM-dd）", converter = XyExcelConverter.class)
    @ApiModelProperty(value = "入职日期")
    private LocalDateTime dateOfEmployment;


    @ExcelProperty("学历(小学，初中，高中，大专，本科，硕士，博士)")
    @ApiModelProperty(value = "学历(小学，初中，高中，大专，本科，硕士，博士)")
    private String educationalBackground;

    @ExcelProperty("毕业院校")
    @ApiModelProperty(value = "毕业院校")
    private String graduationInstitution;

    @XyExcelProperty(type = ExcelPropertyTypeEnum.DATE, format = "yyyy-MM-dd")
    @ExcelProperty(value = "毕业日期（yyyy-MM-dd）", converter = XyExcelConverter.class)
    @ApiModelProperty(value = "毕业日期")
    private LocalDateTime graduationDate;

    @ExcelProperty("政治面貌(群众，党员，团员)")
    @ApiModelProperty(value = "政治面貌(群众，党员)")
    private String politicalLandscape;


    @XyExcelProperty(type = ExcelPropertyTypeEnum.DICTIONARY, dictKey = "YES_OR_NO", isUseDefaultValue = true, boolDefaultValue = false)
    @ExcelProperty(value = "是否临时工", converter = XyExcelConverter.class)
    @ApiModelProperty(value = "是否临时工(0否，1是)")
    private Boolean isTemporaryWork;

    @ExcelProperty("QQ号码")
    @ApiModelProperty(value = "QQ号码")
    private String qqNumber;

    @ExcelProperty("微信号码")
    @ApiModelProperty(value = "微信号码")
    private String wechatNumber;

    @ExcelProperty("邮件")
    @ApiModelProperty(value = "邮件")
    private String email;

    @ApiModelProperty(value = "员工全名")
    private String staffFullName;

    @ExcelProperty("备注")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @XyExcelProperty(type = ExcelPropertyTypeEnum.DICTIONARY, dictKey = "YES_OR_NO", isUseDefaultValue = true)
    @ExcelProperty(value = "是否在职", converter = XyExcelConverter.class)
    @ApiModelProperty(value = "在职情况(1在职，0离职)")
    private Boolean isOnTheJob;

    @XyExcelProperty(type = ExcelPropertyTypeEnum.DATE, format = "yyyy-MM-dd")
    @ExcelProperty(value = "离职日期（yyyy-MM-dd）", converter = XyExcelConverter.class)
    @ApiModelProperty(value = "离职日期")
    private LocalDateTime dateOfResignation;


    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

    @ApiModelProperty(value = "用户guid")
    private String userGuid;

    /**
     * 籍贯
     */
    private String placeOfOrigin;
    /**
     * 民族
     */
    private String ethnicity;
    /**
     * 身高
     */
    private BigDecimal height;
    /**
     * 体重
     */
    private BigDecimal weight;
    /**
     * 籍贯详细地址
     */
    private String residentialDistrict;
    private List<ErpAdministrationMgtStaffFileRO> fileList;

}
