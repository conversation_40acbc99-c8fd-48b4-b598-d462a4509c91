package xy.server.administration.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 工序类型属性RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtProductionProcessesTypeAttributeURO对象", description = "工序类型属性")
public class ErpBasicMgtProductionProcessesTypeAttributeRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String productionProcessesTypeAttributeGuid;

    @NotNull(message = "1普通属性，2分拆属性，3复合属性，4包装属性，5制版属性，6成品属性不能为空")
    @ApiModelProperty(value = "1普通属性，2分拆属性，3复合属性，4包装属性，5制版属性，6成品属性")
    private Integer productionProcessesTypeAttributeValue;

    @NotNull(message = "1普通属性，2分拆属性，3复合属性，4包装属性，5制版属性，6成品属性不能为空")
    @ApiModelProperty(value = "1普通属性，2分拆属性，3复合属性，4包装属性，5制版属性，6成品属性")
    private String productionProcessesTypeAttributeName;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @NotNull(message = "逻辑删除不能为空")
    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

}
