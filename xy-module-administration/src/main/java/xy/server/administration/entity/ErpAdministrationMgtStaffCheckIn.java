package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 入住记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Getter
@Setter
        @Accessors(chain = true)
    @TableName(value = "erp_administration_mgt_staff_check_in", autoResultMap = true)
public class ErpAdministrationMgtStaffCheckIn extends BaseEntity implements Serializable{

private static final long serialVersionUID=1L;
                                /**
         * 入住单号
         */
                                        @TableField("staff_check_in_number")
                                                                        private String staffCheckInNumber;
                                                /**
         * 入住类型（字典：1正常入住，2变更位置）
         */
                                        @TableField("check_in_type")
                                                                        private String checkInType;
                                    /**
         * 备注或描述
         */
                                        @TableField("description")
                                                                        private String description;
                                                                                                            /**
         * 逻辑删除
         */
                                        @TableLogic
                                                                        private Boolean deleted;
                                            /**
         * PK
         */
                                                    @TableId("staff_check_in_guid")
                                                                                    private String staffCheckInGuid;
                                    /**
         * 单据日期（可变更的）
         */
                                        @TableField("receipt_date")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime receiptDate;
                                    /**
         * 退房日期
         */
                                        @TableField("check_out_date")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime checkOutDate;
                                    /**
         * 入住原因
         */
                                        @TableField("reason")
                                                                        private String reason;
                                    /**
         * 入住位置
         */
                                        @TableField("warehouse_space_guid")
                                                                        private String warehouseSpaceGuid;
                                    /**
         * 流程实例ID
         */
                                        @TableField("process_instance_id")
                                                                        private String processInstanceId;
                                    /**
         * 状态
         */
                                        @TableField("audit_status")
                                                                        private String auditStatus;
                                    /**
         * 审核时间
         */
                                        @TableField("audit_date")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime auditDate;
                                    /**
         * 员工guid
         */
                                        @TableField("staff_guid")
                                                                        private String staffGuid;
                                                            @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
                                                                        private Object toJson;



        }
