package xy.server.administration.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <p>
 * 报餐记录表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffMealRO对象", description = "报餐记录表")
public class ErpAdministrationMgtStaffMealRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String staffMealGuid;

    @ApiModelProperty(value = "培训单号")
    private String staffMealNumber;

    @ApiModelProperty(value = "报餐员工，用,拼接")
    private String staffGuid;

    @ApiModelProperty(value = "报餐类型（字典：1正常报餐，2客户报餐）")
    private String staffMealType;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "单据日期（可变更的）不能为空")
    @ApiModelProperty(value = "单据日期（可变更的）")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "就餐人数")
    private Integer numberOfMeal;

}
