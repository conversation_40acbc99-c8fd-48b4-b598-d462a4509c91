package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 班组管理
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_administration_mgt_team",autoResultMap = true)
public class ErpAdministrationMgtTeam extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("team_guid")
    private String teamGuid;
    /**
     * 班组编码
     */
    @TableField("team_code")
    private String teamCode;
    /**
     * 班组名称
     */
    @TableField("team_name")
    private String teamName;
    /**
     * 顺序号
     */
    @TableField("serial_number")
    private Integer serialNumber;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 部门名称
     */
    @TableField("department_guid")
    private String departmentGuid;
    /**
     * 已被使用(0未使用，1已使用)
     */
    @TableField("is_used")
    private Boolean isUsed;
    /**
     * 状态(0停用，1启用)
     */
    @TableField("state")
    private Boolean state;
    /**
     * 最后修改人_guid(取用户表User_guid)
     */
    @TableField(" last_updater_guid")
    private String lastUpdaterGuid;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;

    @TableField(value = "to_json",typeHandler  = JsonbTypeHandler.class)
    private Object toJson;


}
