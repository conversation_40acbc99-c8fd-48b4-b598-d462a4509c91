package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 工单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-26
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_production_mgt_workorder", autoResultMap = true)
public class ErpAdministrationRecruit extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("workorder_guid")
    private String workorderGuid;
    /**
     * 工单号
     */
    @TableField("workorder_number")
    private String workorderNumber;
    /**
     * 顺序号(每一个层级有对应的顺序号)
     */
    @TableField("serial_number")
    private Integer serialNumber;
    /**
     * 父节点_guid
     */
    @TableField("parent_classification_guid")
    private String parentClassificationGuid;
    /**
     * 打印次数
     */
    @TableField("print_times")
    private Integer printTimes;
    /**
     * 单据日期
     */
    @TableField("receipt_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptDate;
    /**
     * 工单类型_guid
     */
    @TableField("workorder_type_guid")
    private String workorderTypeGuid;
    /**
     * 来源guid
     */
    @TableField("source_guid")
    private String sourceGuid;
    /**
     * 来源值
     */
    @TableField("source_value")
    private Integer sourceValue;
    /**
     * 工单状态(完成、故障、未开始)
     */
    @TableField("workorder_state")
    private String workorderState;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 流程实例id
     */
    @TableField("process_instance_id")
    private String processInstanceId;
    /**
     * 审核状态
     */
    @TableField("audit_status")
    private String auditStatus;
    /**
     * 审核时间
     */
    @TableField("audit_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditDate;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 工单属性(1工单，2工序工单，3采购工单，4外发工单，5车间工单，6作业工单，维修工单，保养工单)
     */
    @TableField("workorder_properties")
    private Integer workorderProperties;
    /**
     * 紧急程度(非常紧急，紧急，一般)
     */
    @TableField("is_urgent")
    private String isUrgent;
    /**
     * FSC声明_guid
     */
    @TableField("fsc_declaration_guid")
    private String fscDeclarationGuid;
    /**
     * 客户/供应商guid
     */
    @TableField("customer_guid")
    private String customerGuid;
    /**
     * 物料分类(成品)
     */
    @TableField("material_guid")
    private String materialGuid;
    /**
     * 工艺类型guid
     */
    @TableField("production_processes_type_guid")
    private String productionProcessesTypeGuid;
    /**
     * 占比、比例(根据工单属性:1工单占比数，2是工序工单占工单占比，工序工单产出比)
     */
    @TableField("proportion")
    private BigDecimal proportion;
    /**
     * 工艺描述
     */
    @TableField("production_processes_description")
    private String productionProcessesDescription;
    /**
     * 数量(根据工单属性:1工单成品数，2工序投入数量，3采购申请数量，4外发申请数)
     */
    @TableField("quantity")
    private BigDecimal quantity;
    /**
     * 使用库存数量
     */
    @TableField("using_inventory_quantity")
    private BigDecimal usingInventoryQuantity;
    /**
     * 总数量
     */
    @TableField("total_quantity")
    private BigDecimal totalQuantity;
    /**
     * 单位（(根据工单属性:1工单成品单位，2工序加工单位）
     */
    @TableField("unit_guid")
    private String unitGuid;
    /**
     * 规格长(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @TableField("specifications_length")
    private BigDecimal specificationsLength;
    /**
     * 规格宽(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @TableField("specifications_width")
    private BigDecimal specificationsWidth;
    /**
     * 规格高(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @TableField("specifications_height")
    private BigDecimal specificationsHeight;
    /**
     * 要求交货期
     */
    @TableField("required_delivery_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime requiredDeliveryTime;
    /**
     * 1合并生产，2合版生产
     */
    @TableField("production_mode")
    private String productionMode;
    /**
     * 产品物料id
     */
    @TableField("product_material_guid")
    private String productMaterialGuid;
    /**
     * 工艺流
     */
    @TableField("process_flowchart")
    private String processFlowchart;
    /**
     * 扩展字段
     */
    @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
    private Object toJson;
    /**
     * 是否成品
     */
    @TableField("is_product")
    private Boolean isProduct;
    /**
     * 模数
     */
    @TableField("modulus")
    private BigDecimal modulus;
    /**
     * 工单类型（1-父工单，2-子工单）
     */
    @TableField("workorder_type")
    private Integer workorderType;
    /**
     * 本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)
     */
    @TableField("current_status")
    private String currentStatus;


}
