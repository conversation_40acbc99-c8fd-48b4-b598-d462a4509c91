package xy.server.administration.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
* <p>
* 培训员工扩展表RO
* </p>
*
* <AUTHOR>
* @since 2024-10-22
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtTrainingStaffRO对象", description = "培训员工扩展表")
public class ErpAdministrationMgtTrainingStaffRO extends BaseEntity {

        @ApiModelProperty(value = "PK")
    private String trainingStaffGuid;

        @NotNull(message = "备注或描述不能为空")
        @ApiModelProperty(value = "备注或描述")
    private String description;

        @ApiModelProperty(value = "员工guid")
    private String staffGuid;

        @ApiModelProperty(value = "培训记录guid")
    private String staffTrainingGuid;

        @NotNull(message = "培训成绩不能为空")
        @ApiModelProperty(value = "培训成绩")
    private String trainingResults;

        @ApiModelProperty(value = "扩展字段")
    private Object toJson;

}
