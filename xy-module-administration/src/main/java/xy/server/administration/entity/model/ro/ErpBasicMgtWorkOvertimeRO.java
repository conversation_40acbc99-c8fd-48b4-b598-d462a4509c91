package xy.server.administration.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <p>
 * 加班安排RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtWorkOvertimeURO对象", description = "加班安排")
public class ErpBasicMgtWorkOvertimeRO extends BaseEntity {

    //@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "PK")
    private String workOvertimeGuid;

    //@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotBlank(message = "加班名称不能为空")

    @ApiModelProperty(value = "加班名称")
    private String workOvertimeName;

    @NotNull(message = "开始时间不能为空")
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startDate;

    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endDate;

    @NotNull(message = "状态(0停用，1启用)不能为空")
    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "周模板GUID")
    private String weeklyTemplateGuid;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "时长")
    private Double duration;

    @ApiModelProperty(value = "加班状态")
    private String workState;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

    @ApiModelProperty(value = "")
    private String equipmentGuid;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;
}
