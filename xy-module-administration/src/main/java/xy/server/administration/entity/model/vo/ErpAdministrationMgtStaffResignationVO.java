package xy.server.administration.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
* <p>
* 员工离职扩展表VO
* </p>
*
* <AUTHOR>
* @since 2023-12-11
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpAdministrationMgtStaffResignationVO对象", description = "员工离职扩展表")
public class ErpAdministrationMgtStaffResignationVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "员工_guid")
    private String staffGuid;

    @ApiModelProperty(value = "PK")
    private String staffResignationGuid;

    @ApiModelProperty(value = "离职原因")
    private String resignationReason;

    @XyTrans(dictionaryKey = "RERSONS_FOR_LEAVING",dictionaryValue = "resignationReason")
    @ApiModelProperty(value = "离职原因")
    private String resignationReasonName;

    @ApiModelProperty(value = "部分负责人意见")
    private String partialLeaderOpinion;

    @ApiModelProperty(value = "行政人员的意见")
    private String administrationOpinion;

    @ApiModelProperty(value = "是否离职")
    private Boolean isOnTheJob;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "离职日期")
    private LocalDateTime dateOfResignation;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "离职单号")
    private String resignationNumber;

    @ApiModelProperty(value = "离职类型（字典：1终止劳动合同）")
    private String resignationType;

    @XyTrans(dictionaryKey = "TURNOVER_TYPE",dictionaryValue = "resignationType")
    @ApiModelProperty(value = "离职类型（字典：1终止劳动合同）")
    private String resignationTypeName;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "员工name")
    private String staffName;

    @ApiModelProperty(value = "岗位名称")
    private String postName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "入职日期")
    private LocalDateTime dateOfEmployment;

    @ApiModelProperty(value = "在职天数")
    private Integer employmentDays;

    @ApiModelProperty(value = "离职原因说明")
    private String resignationReasonDescription;

    @ApiModelProperty(value = "备注或者描述")
    private String description;

    @ApiModelProperty(value = "申请日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applicationDate;

    @ApiModelProperty(value = "最后工作日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastWorkDate;

    @ApiModelProperty(value = "薪资结算日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payrollSettlementDate;

    @ApiModelProperty(value = "离职性质(辞退，自离，解除劳务合同，终止劳务合同)")
    private String natureOfResignation;

    @ApiModelProperty(value = "员工工号")
    private String staffCode;
}
