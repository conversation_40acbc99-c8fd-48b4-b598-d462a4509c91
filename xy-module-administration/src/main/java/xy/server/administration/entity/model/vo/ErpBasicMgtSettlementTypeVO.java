package xy.server.administration.entity.model.vo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtSettlementTypeVO对象", description = "")
public class ErpBasicMgtSettlementTypeVO {

    @ApiModelProperty(value = "租户Guid")
    private String tenantGuid;

    @ApiModelProperty(value = "")
    private String settlementTypeGuid;

    @ApiModelProperty(value = "结算类型名称")
    private String settlementTypeName;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点Guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "已被使用(0未使用，1已使用)")
    private Boolean isUsed;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "创建人Guid(取用户表UserGuid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人Guid(取用户表UserGuid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "逻辑删除")
    @TableLogic(value = "false", delval = "true")
    private Boolean deleted;

    @ApiModelProperty(value = "子类")
    private List<ErpBasicMgtSettlementTypeVO> children;


}
