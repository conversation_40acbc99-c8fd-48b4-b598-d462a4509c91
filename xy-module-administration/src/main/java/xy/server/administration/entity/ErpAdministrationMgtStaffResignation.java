package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 员工离职扩展表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_administration_mgt_staff_resignation")
public class ErpAdministrationMgtStaffResignation extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 员工_guid
     */
    @TableField("staff_guid")
    private String staffGuid;
    /**
     * PK
     */
    @TableId("staff_resignation_guid")
    private String staffResignationGuid;
    /**
     * 离职原因
     */
    @TableField("resignation_reason")
    private String resignationReason;
    /**
     * 部分负责人意见
     */
    @TableField("partial_leader_opinion")
    private String partialLeaderOpinion;
    /**
     * 行政人员的意见
     */
    @TableField("administration_opinion")
    private String administrationOpinion;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 是否离职
     */
    @TableField("is_on_the_job")
    private Boolean isOnTheJob;
    /**
     * 离职日期
     */
    @TableField("date_of_resignation")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dateOfResignation;
    /**
     * 流程实例ID
     */
    @TableField("process_instance_id")
    private String processInstanceId;
    /**
     * 状态
     */
    @TableField("audit_status")
    private String auditStatus;
    /**
     * 审核时间
     */
    @TableField("audit_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditDate;
    /**
     * 扩展字段
     */
    @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
    private Object toJson;
    /**
     * 离职单号
     */
    @TableField("resignation_number")
    private String resignationNumber;
    /**
     * 离职类型（字典：1终止劳动合同）
     */
    @TableField("resignation_type")
    private String resignationType;
    /**
     * 离职原因说明
     */
    @TableField("resignation_reason_description")
    private String resignationReasonDescription;
    /**
     * 备注或者描述
     */
    @TableField("description")
    private String description;
    /**
     * 离职性质(辞退，自离，解除劳务合同，终止劳务合同)
     */
    @TableField("nature_of_resignation")
    private String natureOfResignation;
    /**
     * 申请日期
     */
    @TableField("application_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applicationDate;
    /**
     * 最后工作日期
     */
    @TableField("last_work_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastWorkDate;
    /**
     * 薪资结算日期
     */
    @TableField("payroll_settlement_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payrollSettlementDate;


}
