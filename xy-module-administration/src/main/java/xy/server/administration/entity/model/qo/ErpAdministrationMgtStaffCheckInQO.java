package xy.server.administration.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
        /**
* <p>
* 入住记录表QO
* </p>
*
* <AUTHOR>
* @since 2024-10-22
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffCheckInQO对象", description = "入住记录表")
public class ErpAdministrationMgtStaffCheckInQO{

            @ApiModelProperty(value = "入住单号")
            private String staffCheckInNumber;

            @ApiModelProperty(value = "入住类型（字典：1正常入住，2变更位置）")
            private List<String> checkInType;

            @ApiModelProperty(value = "员工")
            private String staffName;

            @ApiModelProperty(value = "开始日期")
            private LocalDateTime startTime;

            @ApiModelProperty(value = "结束日期")
            private LocalDateTime endTime;

}
