package xy.server.administration.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 奖罚记录表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpAdministrationMgtStaffRewardsPunishmentsVO对象", description = "奖罚记录表")
public class ErpAdministrationMgtStaffRewardsPunishmentsVO {

    @ApiModelProperty(value = "奖罚单号")
    private String rewardsPunishmentsNumber;

    @ApiModelProperty(value = "租户id")
    private String tenantGuid;

    @ApiModelProperty(value = "奖罚类型（字典：1常规奖励，2常规惩罚）")
    private String rewardsPunishmentsType;

    @XyTrans(dictionaryKey = "REWARD_AND_PUNISHMENT_TYPE",dictionaryValue = "rewardsPunishmentsType")
    @ApiModelProperty(value = "奖罚类型（字典：1常规奖励，2常规惩罚）")
    private String rewardsPunishmentsTypeName;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "PK")
    private String staffRewardsPunishmentsGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期（可变更的）")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "奖罚原因")
    private String reason;

    @ApiModelProperty(value = "奖罚金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "员工name")
    private String staffName;

    @ApiModelProperty(value = "PK")
    private String staffGuid;

    @ApiModelProperty(value = "")
    private Object toJson;

}
