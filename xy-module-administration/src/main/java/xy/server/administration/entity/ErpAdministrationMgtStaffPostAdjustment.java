package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 调岗记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_administration_mgt_staff_post_adjustment", autoResultMap = true)
public class ErpAdministrationMgtStaffPostAdjustment extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 调岗单号
     */
    @TableField("post_adjustment_number")
    private String postAdjustmentNumber;
    /**
     * 调岗类型（字典：1人事变动）
     */
    @TableField("post_adjustment_type")
    private String postAdjustmentType;
    /**
     * 原岗位
     */
    @TableField("original_post_guid")
    private String originalPostGuid;
    /**
     * 新岗位
     */
    @TableField("new_post_guid")
    private String newPostGuid;
    /**
     * 旧部门
     */
    @TableField("original_department_guid")
    private String originalDepartmentGuid;
    /**
     * 新部门
     */
    @TableField("new_department_guid")
    private String newDepartmentGuid;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * PK
     */
    @TableId("staff_post_adjustment_guid")
    private String staffPostAdjustmentGuid;
    /**
     * 单据日期（可变更的）
     */
    @TableField("receipt_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptDate;
    /**
     * 生效日期
     */
    @TableField("effective_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectiveDate;
    /**
     * 调岗原因
     */
    @TableField("reason")
    private String reason;
    /**
     * 流程实例ID
     */
    @TableField("process_instance_id")
    private String processInstanceId;
    /**
     * 状态
     */
    @TableField("audit_status")
    private String auditStatus;
    /**
     * 审核时间
     */
    @TableField("audit_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditDate;
    /**
     * 员工guid
     */
    @TableField("staff_guid")
    private String staffGuid;
    /**
     * 扩展字段
     */
    @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
    private Object toJson;


}
