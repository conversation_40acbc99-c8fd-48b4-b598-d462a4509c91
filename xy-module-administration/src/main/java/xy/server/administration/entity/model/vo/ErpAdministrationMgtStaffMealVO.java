package xy.server.administration.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 报餐记录表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpAdministrationMgtStaffMealVO对象", description = "报餐记录表")
public class ErpAdministrationMgtStaffMealVO {

    @ApiModelProperty(value = "PK")
    private String staffMealGuid;

    @ApiModelProperty(value = "培训单号")
    private String staffMealNumber;

    @ApiModelProperty(value = "租户id")
    private String tenantGuid;

    @ApiModelProperty(value = "报餐员工，用,拼接")
    private String staffGuid;

    @ApiModelProperty(value = "报餐类型（字典：1正常报餐，2客户报餐）")
    private String staffMealType;

    @XyTrans(dictionaryKey = "STAFF_MEAL_TYPE",dictionaryValue = "staffMealType")
    @ApiModelProperty(value = "报餐类型（字典：1正常报餐，2客户报餐）")
    private String staffMealTypeName;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人GUID(取用户表UserGUID)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人GUID(取用户表UserGUID)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期（可变更的）")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "就餐人数")
    private Integer numberOfMeal;

    @ApiModelProperty(value = "报餐员工集合")
    private List<ErpAdministrationMgtStaffVO> staffVOS;

}
