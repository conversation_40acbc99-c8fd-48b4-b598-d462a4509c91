package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 离职记录统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Getter
@Setter
        @Accessors(chain = true)
    @TableName(value = "erp_administration_mgt_staff_on_job_statistics")
public class ErpAdministrationMgtStaffOnJobStatistics extends BaseEntity implements Serializable{

private static final long serialVersionUID=1L;
                                                    /**
         * PK
         */
                                                    @TableId("staff_on_job_statistics_guid")
                                                                                    private String staffOnJobStatisticsGuid;
                                    /**
         * 离职人数
         */
                                        @TableField("is_not_on_job")
                                                                        private Integer isNotOnJob;
                                    /**
         * 在职人数
         */
                                        @TableField("is_on_job")
                                                                        private Integer isOnJob;
                                    /**
         * 月份
         */
                                        @TableField("this_date")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime thisDate;
                                    /**
         * 离职率
         */
                                        @TableField("resignation_rate")
                                                                        private BigDecimal resignationRate;
                                                                                                                                    @TableLogic
                                                                        private Boolean deleted;



        }
