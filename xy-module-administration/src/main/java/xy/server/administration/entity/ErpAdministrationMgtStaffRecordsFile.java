package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 任务文件表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Getter
@Setter
        @Accessors(chain = true)
    @TableName(value = "erp_administration_mgt_staff_records_file")
public class ErpAdministrationMgtStaffRecordsFile extends BaseEntity implements Serializable{

private static final long serialVersionUID=1L;
                                            /**
         * 来源_guid
         */
                                        @TableField("source_guid")
                                                                        private String sourceGuid;
                                            /**
         * PK
         */
                                                    @TableId("staff_records_file_guid")
                                                                                    private String staffRecordsFileGuid;
                                    /**
         * 文件类型(1图片，2文件 )
         */
                                        @TableField("file_type")
                                                                        private Integer fileType;
                                    /**
         * 来源值1离职,2调岗,3奖罚,4培训,5工伤,6入住,7报餐
         */
                                        @TableField("source_value")
                                                                        private Integer sourceValue;
                                    /**
         * 文件表_guid
         */
                                        @TableField("file_guid")
                                                                        private String fileGuid;
                                    /**
         * 备注或描述
         */
                                        @TableField("description")
                                                                        private String description;
                                                                                                            /**
         * 逻辑删除
         */
                                        @TableLogic
                                                                        private Boolean deleted;



        }
