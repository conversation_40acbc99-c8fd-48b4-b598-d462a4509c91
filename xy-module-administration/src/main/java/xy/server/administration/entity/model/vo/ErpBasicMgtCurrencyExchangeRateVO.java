package xy.server.administration.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 币种汇率表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtCurrencyExchangeRateVO对象", description = "币种汇率表")
public class ErpBasicMgtCurrencyExchangeRateVO {

    @ApiModelProperty(value = "租户GUID")
    private String tenantGuid;

    @ApiModelProperty(value = "")
    private String currencyExchangeRateGuid;

    @ApiModelProperty(value = "CurrencyGUID")
    private String currencyGuid;

    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "有效开始时间")
    private Date effectiveStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "有效结束时间")
    private Date effectiveEndTime;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "已被使用(0未使用，1已使用)")
    private Boolean isUsed;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "创建人GUID(取用户表UserGUID)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人GUID(取用户表UserGUID)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

}