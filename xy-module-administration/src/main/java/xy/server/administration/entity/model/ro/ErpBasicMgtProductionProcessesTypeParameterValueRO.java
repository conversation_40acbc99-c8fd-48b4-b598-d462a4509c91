package xy.server.administration.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 工序类型参数值RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtProductionProcessesTypeParameterValueURO对象", description = "工序类型参数值")
public class ErpBasicMgtProductionProcessesTypeParameterValueRO extends BaseEntity {

    @ApiModelProperty(value = "")
    private String productionProcessesTypeParameterValueGuid;

    @NotNull(message = "工序类型参数值不能为空")
    @ApiModelProperty(value = "工序类型参数值")
    private String productionProcessesTypeParameterValue;

    @ApiModelProperty(value = "工序类型参数项 GUID")
    private String productionProcessesTypeParameterItemsGuid;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @NotNull(message = "状态(0停用，1启用)不能为空")
    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @NotNull(message = "逻辑删除不能为空")
    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

}
