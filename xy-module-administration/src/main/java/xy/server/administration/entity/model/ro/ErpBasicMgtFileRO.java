package xy.server.administration.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 文件管理表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtFileURO对象", description = "文件管理表")
public class ErpBasicMgtFileRO extends BaseEntity {

    @ApiModelProperty(value = "")
    private String fileGuid;

    @NotNull(message = "文件名称不能为空")
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "ERP_FileMGT_Folder GUID")
    private String folderGuid;

    @NotNull(message = "文件路径不能为空")
    @ApiModelProperty(value = "文件路径")
    private String path;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @NotNull(message = "逻辑删除不能为空")
    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

}
