package xy.server.administration.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* <p>
* 离职记录统计表RO
* </p>
*
* <AUTHOR>
* @since 2024-10-22
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffOnJobStatisticsRO对象", description = "离职记录统计表")
public class ErpAdministrationMgtStaffOnJobStatisticsRO extends BaseEntity {

        @ApiModelProperty(value = "PK")
    private String staffOnJobStatisticsGuid;

        @NotNull(message = "离职人数不能为空")
        @ApiModelProperty(value = "离职人数")
    private Integer isNotOnJob;

        @NotNull(message = "在职人数不能为空")
        @ApiModelProperty(value = "在职人数")
    private Integer isOnJob;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @NotNull(message = "月份不能为空")
        @ApiModelProperty(value = "月份")
    private LocalDateTime thisDate;

        @NotNull(message = "离职率不能为空")
        @ApiModelProperty(value = "离职率")
    private BigDecimal resignationRate;

}
