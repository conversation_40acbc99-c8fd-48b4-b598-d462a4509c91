package xy.server.administration.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtProductionProcessesTypeURO对象", description = "工序类型")
public class ErpBasicMgtProductionProcessesTypeListRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String productionProcessesTypeGuid;

    @ApiModelProperty(value = "工序类型名称")
    private String productionProcessesTypeName;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点GUID")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "ERP_BasicMGT_ProductionProcessesTypeAttribute GUID	工序类型属性GUID")
    private String productionProcessesTypeAttributeGuid;

    @ApiModelProperty(value = "已被使用(0未使用，1已使用)")
    private Boolean isUsed;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "默认生产排程")
    private Boolean isDefaultProductionScheduling;

    @ApiModelProperty(value = "默认外发")
    private Boolean isDefaultOutsource;

    //    @NotNull(message = "逻辑删除不能为空")
    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;
}
