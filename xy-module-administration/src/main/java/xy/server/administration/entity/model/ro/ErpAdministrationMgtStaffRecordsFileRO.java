package xy.server.administration.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
* <p>
* 任务文件表RO
* </p>
*
* <AUTHOR>
* @since 2024-10-22
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffRecordsFileRO对象", description = "任务文件表")
public class ErpAdministrationMgtStaffRecordsFileRO extends BaseEntity {

        @ApiModelProperty(value = "来源_guid")
    private String sourceGuid;

        @ApiModelProperty(value = "PK")
    private String staffRecordsFileGuid;

        @NotNull(message = "文件类型(1图片，2文件 )不能为空")
        @ApiModelProperty(value = "文件类型(1图片，2文件 )")
    private Integer fileType;

        @NotNull(message = "来源值1离职,2调岗,3奖罚,4培训,5工伤,6入住,7报餐不能为空")
        @ApiModelProperty(value = "来源值1离职,2调岗,3奖罚,4培训,5工伤,6入住,7报餐")
    private Integer sourceValue;

        @ApiModelProperty(value = "文件表_guid")
    private String fileGuid;

        @NotNull(message = "备注或描述不能为空")
        @ApiModelProperty(value = "备注或描述")
    private String description;

}
