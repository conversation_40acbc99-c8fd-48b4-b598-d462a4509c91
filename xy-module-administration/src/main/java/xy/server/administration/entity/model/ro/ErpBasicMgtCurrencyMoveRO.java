package xy.server.administration.entity.model.ro;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ErpBasicMgtCurrencyMoveRO {


    @NotNull(message = "币种guid不能为空")
    @ApiModelProperty(value = "币种guid")
    private String currencyGuid;

    @NotNull(message = "移动类型")
    @ApiModelProperty(value = "移动类型0-上移 1-下移")
    private int moveType;
}
