package xy.server.administration.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
        /**
* <p>
* 员工离职扩展表QO
* </p>
*
* <AUTHOR>
* @since 2023-12-11
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffResignationQO对象", description = "员工离职扩展表")
public class ErpAdministrationMgtStaffResignationQO{

            @ApiModelProperty(value = "工伤单号")
            private String resignationNumber;

            @ApiModelProperty(value = "离职类型（字典：1终止劳动合同）")
            private List<String> resignationType;

            @ApiModelProperty(value = "员工")
            private String staffName;

            @ApiModelProperty(value = "开始日期")
            private LocalDateTime startTime;

            @ApiModelProperty(value = "结束日期")
            private LocalDateTime endTime;

}
