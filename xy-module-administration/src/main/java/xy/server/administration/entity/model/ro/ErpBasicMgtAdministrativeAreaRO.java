package xy.server.administration.entity.model.ro;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtAdministrativeAreaURO对象", description = "")
public class ErpBasicMgtAdministrativeAreaRO extends BaseEntity {

    @ApiModelProperty(value = "租户Guid")
    private String tenantGuid;

    @ApiModelProperty(value = "")
    private String administrativeAreaGuid;

    // @NotNull(message = "行政区域名称不能为空")
    @ApiModelProperty(value = "行政区域名称")
    private String administrativeAreaName;

    // @NotNull(message = "层级不能为空")
    @ApiModelProperty(value = "层级")
    private String level;

    // @NotNull(message = "邮政编码不能为空")
    @ApiModelProperty(value = "邮政编码")
    private String postalCode;

    //@NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    // @NotNull(message = "顺序号(每一个层级有对应的顺序号)不能为空")
    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点Guid")
    private String parentClassificationGuid;

    // @NotNull(message = "已被使用(0未使用，1已使用)不能为空")
    @ApiModelProperty(value = "已被使用(0未使用，1已使用)")
    private Boolean isUsed;

    // @NotNull(message = "状态(0停用，1启用)不能为空")
    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    // @NotNull(message = "逻辑删除不能为空")
    @TableLogic(value = "false", delval = "true")
    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

}
