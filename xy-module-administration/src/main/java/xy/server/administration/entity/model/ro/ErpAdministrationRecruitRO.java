package xy.server.administration.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 工单表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpProductionMgtWorkorderRO对象", description = "工单表")
public class ErpAdministrationRecruitRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "工单属性(1工单，2工序工单，3采购工单，4外发工单，5车间工单，6作业工单，维修工单，保养工单)")
    private Integer workorderProperties;

    @ApiModelProperty(value = "紧急程度(非常紧急，紧急，一般)")
    private String isUrgent;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String currentStatus;

    @ApiModelProperty(value = "年龄要求")
    private String ageRequirements;

    @ApiModelProperty(value = "技能要求")
    private String skillRequirements;

    @ApiModelProperty(value = "性别要求")
    private String sexRequirements;

    @ApiModelProperty(value = "工作经验要求")
    private String workExperienceRequirements;

    @ApiModelProperty(value = "文化程度要求")
    private String educationalLevelRequirements;

    @ApiModelProperty(value = "岗位")
    private String postGuid;

    @ApiModelProperty(value = "部门")
    private String departmentGuid;

    @ApiModelProperty(value = "PK")
    private String staffRecruitGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "招聘开始时间")
    private LocalDateTime recruitStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "招聘结束时间")
    private LocalDateTime recruitEndTime;

    @ApiModelProperty(value = "招聘原因")
    private String recruitReason;

    @ApiModelProperty(value = "招聘类型")
    private String recruitType;

    @ApiModelProperty(value = "招聘人数")
    private BigDecimal quantity;

}
