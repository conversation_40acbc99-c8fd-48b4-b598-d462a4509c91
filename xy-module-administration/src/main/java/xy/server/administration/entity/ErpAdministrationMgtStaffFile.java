package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 员工文件表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_administration_mgt_staff_file")
public class ErpAdministrationMgtStaffFile extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 员工guid
     */
    @TableField("staff_guid")
    private String staffGuid;
    /**
     * PK
     */
    @TableId("staff_file_guid")
    private String staffFileGuid;
    /**
     * 文件类型(1图片，2文件，3流程图 )
     */
    @TableField("file_type")
    private Integer fileType;
    /**
     * 文件表_guid
     */
    @TableField("file_guid")
    private String fileGuid;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;


}
