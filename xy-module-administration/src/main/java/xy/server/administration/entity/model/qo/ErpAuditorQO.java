package xy.server.administration.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ErpAuditorQO {

    @ApiModelProperty(value = "审核人名称")
    private String name;

    private List<String> procInstIdS;

    private String clazz;


}