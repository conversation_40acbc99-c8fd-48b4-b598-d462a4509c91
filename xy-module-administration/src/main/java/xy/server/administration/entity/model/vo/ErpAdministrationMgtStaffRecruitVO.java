package xy.server.administration.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
* <p>
* 招聘申请VO
* </p>
*
* <AUTHOR>
* @since 2024-10-22
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffRecruitVO对象", description = "招聘申请")
public class ErpAdministrationMgtStaffRecruitVO {

    @ApiModelProperty(value = "租户id")
    private String tenantGuid;

    @ApiModelProperty(value = "年龄要求")
    private String ageRequirements;

    @ApiModelProperty(value = "技能要求")
    private String skillRequirements;

    @ApiModelProperty(value = "性别要求")
    private String sexRequirements;

    @ApiModelProperty(value = "工作经验要求")
    private String workExperienceRequirements;

    @ApiModelProperty(value = "文化程度要求")
    private String educationalLevelRequirements;

    @ApiModelProperty(value = "岗位")
    private String postGuid;

    @ApiModelProperty(value = "部门")
    private String departmentGuid;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人GUID(取用户表UserGUID)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人GUID(取用户表UserGUID)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "工单guid")
    private String workorderGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "PK")
    private String staffRecruitGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "招聘开始时间")
    private LocalDateTime recruitStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "招聘结束时间")
    private LocalDateTime recruitEndTime;

    @ApiModelProperty(value = "招聘原因")
    private String recruitReason;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "1，全职，2兼职")
    private String recruitType;

}
