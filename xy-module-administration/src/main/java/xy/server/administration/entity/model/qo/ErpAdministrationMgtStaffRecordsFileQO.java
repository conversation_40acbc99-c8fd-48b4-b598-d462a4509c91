package xy.server.administration.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
        /**
* <p>
* 任务文件表QO
* </p>
*
* <AUTHOR>
* @since 2024-10-22
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffRecordsFileQO对象", description = "任务文件表")
public class ErpAdministrationMgtStaffRecordsFileQO{

            @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

            @ApiModelProperty(value = "来源_guid")
    private String sourceGuid;

            @ApiModelProperty(value = "PK")
    private String staffRecordsFileGuid;

                @ApiModelProperty(value = "文件类型(1图片，2文件 )")
    private Integer fileType;

                @ApiModelProperty(value = "来源值1离职,2调岗,3奖罚,4培训,5工伤,6入住,7报餐")
    private Integer sourceValue;

            @ApiModelProperty(value = "文件表_guid")
    private String fileGuid;

                @ApiModelProperty(value = "备注或描述")
    private String description;

            @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

                @ApiModelProperty(value = "创建人")
    private String creator;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

            @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

                @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

}
