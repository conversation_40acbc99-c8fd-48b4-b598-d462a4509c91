package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 工伤记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Getter
@Setter
        @Accessors(chain = true)
    @TableName(value = "erp_administration_mgt_staff_injury", autoResultMap = true)
public class ErpAdministrationMgtStaffInjury extends BaseEntity implements Serializable{

private static final long serialVersionUID=1L;
                                /**
         * 工伤单号
         */
                                        @TableField("injury_number")
                                                                        private String injuryNumber;
                                                /**
         * 工伤类型（字典：1一般工伤，2职业病工伤，3交通事故工伤，4求助伤残）
         */
                                        @TableField("injury_type")
                                                                        private String injuryType;
                                    /**
         * 备注或描述
         */
                                        @TableField("description")
                                                                        private String description;
                                                                                                            /**
         * 逻辑删除
         */
                                        @TableLogic
                                                                        private Boolean deleted;
                                            /**
         * PK
         */
                                                    @TableId("staff_injury_guid")
                                                                                    private String staffInjuryGuid;
                                    /**
         * 员工guid
         */
                                        @TableField("staff_guid")
                                                                        private String staffGuid;
                                    /**
         * 单据日期（可变更的）
         */
                                        @TableField("receipt_date")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime receiptDate;
                                    /**
         * 医疗机构
         */
                                        @TableField("medical_institutions")
                                                                        private String medicalInstitutions;
                                    /**
         * 医疗费用
         */
                                        @TableField("medical_expenses")
                                                                        private BigDecimal medicalExpenses;
                                    /**
         * 公司支付费用
         */
                                        @TableField("company_payment_fees")
                                                                        private BigDecimal companyPaymentFees;
                                    /**
         * 个人支付费用
         */
                                        @TableField("personal_payment_fees")
                                                                        private BigDecimal personalPaymentFees;
                                    /**
         * 赔偿费
         */
                                        @TableField("compensation_fees")
                                                                        private BigDecimal compensationFees;
                                    /**
         * 责任描述
         */
                                        @TableField("responsibility_description")
                                                                        private String responsibilityDescription;
                                    /**
         * 结案日期
         */
                                        @TableField("close_date")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime closeDate;
                                    /**
         * 流程实例ID
         */
                                        @TableField("process_instance_id")
                                                                        private String processInstanceId;
                                    /**
         * 状态
         */
                                        @TableField("audit_status")
                                                                        private String auditStatus;
                                    /**
         * 审核时间
         */
                                        @TableField("audit_date")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime auditDate;
                                    /**
         * 扩展字段
         */
                                        @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
                                                                        private Object toJson;



        }
