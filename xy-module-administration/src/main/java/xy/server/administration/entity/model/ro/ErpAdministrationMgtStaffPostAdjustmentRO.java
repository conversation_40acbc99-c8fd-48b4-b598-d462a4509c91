package xy.server.administration.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <p>
 * 调岗记录表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffPostAdjustmentRO对象", description = "调岗记录表")
public class ErpAdministrationMgtStaffPostAdjustmentRO extends BaseEntity {

    @ApiModelProperty(value = "调岗单号")
    private String postAdjustmentNumber;

    @ApiModelProperty(value = "调岗类型（字典：1人事变动）")
    private String postAdjustmentType;

    @ApiModelProperty(value = "原岗位")
    private String originalPostGuid;

    @ApiModelProperty(value = "新岗位")
    private String newPostGuid;

    @ApiModelProperty(value = "旧部门")
    private String originalDepartmentGuid;

    @ApiModelProperty(value = "新部门")
    private String newDepartmentGuid;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "PK")
    private String staffPostAdjustmentGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "单据日期（可变更的）不能为空")
    @ApiModelProperty(value = "单据日期（可变更的）")
    private LocalDateTime receiptDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生效日期")
    private LocalDateTime effectiveDate;

    @ApiModelProperty(value = "调岗原因")
    private String reason;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "员工guid")
    private String staffGuid;

    @ApiModelProperty(value = "")
    private Object toJson;

}
