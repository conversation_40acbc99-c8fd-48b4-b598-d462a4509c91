package xy.server.administration.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 员工列表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpAdministrationMgtStaffVO对象", description = "员工列表")
public class ErpAdministrationMgtStaffVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String staffGuid;

    @ApiModelProperty(value = "员工名字(简称)")
    private String staffShortName;

    @ApiModelProperty(value = "员工工号")
    private String staffCode;

    @ApiModelProperty(value = "部门_guid")
    private String departmentGuid;

    @ApiModelProperty(value = "岗位_guid")
    private String postGuid;

    @ApiModelProperty(value = "身份证")
    private String idcard;

    @ApiModelProperty(value = "性别(1男，2女，3中性别)")
    private Integer gender;

    @ApiModelProperty(value = "出生日期")
    private LocalDateTime dateOfBirth;

    @ApiModelProperty(value = "开户银行")
    private String openingBank;

    @ApiModelProperty(value = "电话")
    private String telephone;

    @ApiModelProperty(value = "银行帐号")
    private String bankAccount;

    @ApiModelProperty(value = "手机")
    private String mobilephone;

    @ApiModelProperty(value = "居住地详细地址")
    private String residentialAddress;

    @ApiModelProperty(value = "居住地地址行政区域_guid")
    private String residentialAddressAdministrativeAreaGuid;

    @ApiModelProperty(value = "出生地详细地址")
    private String birthAddress;

    @ApiModelProperty(value = "出生地详细地址行政区域_guid")
    private String birthAddressAdministrativeAreaGuid;

    @ApiModelProperty(value = "紧急联系人")
    private String emergencyContact;

    @ApiModelProperty(value = "紧急联系电话")
    private String emergencyContactPhoneNumber;

    @ApiModelProperty(value = "紧急联系人关系(父子，母子，亲戚等)")
    private String emergencyContactRelationship;

    @ApiModelProperty(value = "婚姻状况(0未婚，1已婚)")
    private Integer maritalStatus;

    @XyTrans(dictionaryKey = "MARITAL_STATUS", dictionaryValue = "maritalStatus")
    @ApiModelProperty(value = "婚姻状况字典值")
    private String maritalStatusDictValue;

    @ApiModelProperty(value = "入职日期")
    private LocalDateTime dateOfEmployment;

    @ApiModelProperty(value = "学历(小学，初中，高中，大专，本科，硕士，博士)")
    private String educationalBackground;

    @ApiModelProperty(value = "毕业院校")
    private String graduationInstitution;

    @ApiModelProperty(value = "毕业日期")
    private LocalDateTime graduationDate;

    @ApiModelProperty(value = "政治面貌(群众，党员)")
    private String politicalLandscape;

    @ApiModelProperty(value = "是否临时工(0否，1是)")
    private Boolean isTemporaryWork;

    @ApiModelProperty(value = "QQ号码")
    private String qqNumber;

    @ApiModelProperty(value = "微信号码")
    private String wechatNumber;

    @ApiModelProperty(value = "邮件")
    private String email;

    @ApiModelProperty(value = "员工全名")
    private String staffFullName;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "在职情况(1在职，0离职)")
    private Boolean isOnTheJob;

    @ApiModelProperty(value = "离职日期")
    private LocalDateTime dateOfResignation;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "岗位名称")
    private String postName;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

    @ApiModelProperty(value = "离职原因")
    private String resignationReason;

    @XyTrans(dictionaryKey = "RERSONS_FOR_LEAVING",dictionaryValue = "resignationReason")
    @ApiModelProperty(value = "离职原因")
    private String resignationReasonReal;

    @ApiModelProperty(value = "部分负责人意见")
    private String partialLeaderOpinion;

    @ApiModelProperty(value = "行政人员的意见")
    private String administrationOpinion;

    @ApiModelProperty(value = "离职或者恢复")
    private Boolean type;

    @ApiModelProperty(value = "员工id")
    private String userGuid;

    @ApiModelProperty(value = "订单id")
    private String orderGuid;

    @ApiModelProperty(value = "跟进类型")
    private String orderFollowUpStaffType;

    @ApiModelProperty(value = "团队id")
    private String teamGuid;

    @ApiModelProperty(value = "团队名称")
    private String teamName;

    @ApiModelProperty(value = "是否为组长")
    private Boolean isGroup;

    /**
     * 籍贯
     */
    private String placeOfOrigin;
    /**
     * 民族
     */
    private String ethnicity;
    /**
     * 身高
     */
    private BigDecimal height;
    /**
     * 体重
     */
    private BigDecimal weight;
    /**
     * 籍贯详细地址
     */
    private String residentialDistrict;

    private List<ErpAdministrationMgtStaffFileVO> fileList;
}
