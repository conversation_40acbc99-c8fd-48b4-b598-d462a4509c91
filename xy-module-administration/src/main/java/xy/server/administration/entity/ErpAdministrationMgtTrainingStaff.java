package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 培训员工扩展表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Getter
@Setter
        @Accessors(chain = true)
    @TableName(value = "erp_administration_mgt_training_staff", autoResultMap = true)
public class ErpAdministrationMgtTrainingStaff extends BaseEntity implements Serializable{

private static final long serialVersionUID=1L;
                                                    /**
         * PK
         */
                                                    @TableId("training_staff_guid")
                                                                                    private String trainingStaffGuid;
                                    /**
         * 备注或描述
         */
                                        @TableField("description")
                                                                        private String description;
                                    /**
         * 员工guid
         */
                                        @TableField("staff_guid")
                                                                        private String staffGuid;
                                    /**
         * 培训记录guid
         */
                                        @TableField("staff_training_guid")
                                                                        private String staffTrainingGuid;
                                    /**
         * 培训成绩
         */
                                        @TableField("training_results")
                                                                        private String trainingResults;
                                                                                                            /**
         * 逻辑删除
         */
                                        @TableLogic
                                                                        private Boolean deleted;
                                    /**
         * 扩展字段
         */
                                        @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
                                                                        private Object toJson;



        }
