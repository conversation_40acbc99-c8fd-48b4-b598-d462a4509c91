package xy.server.administration.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 发票类型税率表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtInvoiceTypeTaxRateURO对象", description = "发票类型税率表")
public class ErpBasicMgtInvoiceTypeTaxRateRO extends BaseEntity {

    @ApiModelProperty(value = "")
    private String invoiceTypeTaxRateGuid;

    @NotNull(message = "税率不能为空")
    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "InvoiceType GUID")
    private String invoiceTypeGuid;

    //    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "有效开始时间不能为空")
    @ApiModelProperty(value = "有效开始时间")
    private Date effectiveStartTime;

    //    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "有效结束时间不能为空")
    @ApiModelProperty(value = "有效结束时间")
    private Date effectiveEndTime;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @NotNull(message = "已被使用(0未使用，1已使用)不能为空")
    @ApiModelProperty(value = "已被使用(0未使用，1已使用)")
    private Boolean isUsed;

    @NotNull(message = "状态(0停用，1启用)不能为空")
    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @NotNull(message = "逻辑删除不能为空")
    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

}
