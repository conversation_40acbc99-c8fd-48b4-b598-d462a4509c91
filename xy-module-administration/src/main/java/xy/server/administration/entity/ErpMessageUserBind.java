package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-14
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_message_user_bind")
public class ErpMessageUserBind extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("message_user_bind_guid")
    private String messageUserBindGuid;
    /**
     * 员工Guid
     */
    @TableField("staff_guid")
    private String staffGuid;
    /**
     * 消息平台
     */
    @TableField("platform")
    private String platform;
    /**
     * 接收人，比如微信公众号就绑定openID之类的
     */
    @TableField("receiver_id")
    private String receiverId;


}
