package xy.server.administration.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
* <p>
* 订单跟进人员VO
* </p>
*
* <AUTHOR>
* @since 2023-10-12
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBusinessMgtOrderFollowUpStaffVO对象", description = "订单跟进人员")
public class ErpBusinessMgtOrderFollowUpStaffVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String orderFollowUpStaffGuid;

    @ApiModelProperty(value = "跟进人员类型(1跟单员2业务员)")
    private Integer orderFollowUpStaffType;

    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "订单表_guid")
    private String orderGuid;

    @ApiModelProperty(value = "业务员名字(取员工资料_guid)")
    private String staffGuid;

    @ApiModelProperty(value = "业务员名字")
    private String staffName;

    @ApiModelProperty(value = "是否默认")
    private Boolean isDefault;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "")
    private Object toJson;

}
