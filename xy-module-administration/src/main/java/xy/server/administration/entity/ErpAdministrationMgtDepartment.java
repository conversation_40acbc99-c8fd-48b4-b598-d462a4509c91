package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 部门表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@TableName(value = "erp_administration_mgt_department",autoResultMap  = true)
public class ErpAdministrationMgtDepartment extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("department_guid")
    private String departmentGuid;
    /**
     * 部门名称
     */
    @TableField("department_name")
    private String departmentName;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 顺序号(每一个层级有对应的顺序号)
     */
    @TableField("serial_number")
    private Integer serialNumber;
    /**
     * 父节点_guid
     */
    @TableField("parent_classification_guid")
    private String parentClassificationGuid;
    /**
     * 已被使用(0未使用，1已使用)
     */
    @TableField("is_used")
    private Boolean isUsed;
    /**
     * 状态(0停用，1启用)
     */
    @TableField("state")
    private Boolean state;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;

    @TableField(value = "to_json",typeHandler  = JsonbTypeHandler.class)
    private Object toJson;


}
