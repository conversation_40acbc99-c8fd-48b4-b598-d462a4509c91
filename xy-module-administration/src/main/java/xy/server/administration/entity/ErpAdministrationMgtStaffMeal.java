package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 报餐记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Getter
@Setter
        @Accessors(chain = true)
    @TableName(value = "erp_administration_mgt_staff_meal", autoResultMap = true)
public class ErpAdministrationMgtStaffMeal extends BaseEntity implements Serializable{

private static final long serialVersionUID=1L;
                                        /**
         * PK
         */
                                                    @TableId("staff_meal_guid")
                                                                                    private String staffMealGuid;
                                    /**
         * 报餐单号
         */
                                        @TableField("staff_meal_number")
                                                                        private String staffMealNumber;
                                    /**
         * 报餐员工，用,拼接
         */
                                        @TableField("staff_guid")
                                                                        private String staffGuid;
                                                /**
         * 报餐类型（字典：1正常报餐，2客户报餐）
         */
                                        @TableField("staff_meal_type")
                                                                        private String staffMealType;
                                    /**
         * 备注或描述
         */
                                        @TableField("description")
                                                                        private String description;
                                                                                                            /**
         * 逻辑删除
         */
                                        @TableLogic
                                                                        private Boolean deleted;
                                    /**
         * 单据日期（可变更的）
         */
                                        @TableField("receipt_date")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime receiptDate;
                                    /**
         * 流程实例ID
         */
                                        @TableField("process_instance_id")
                                                                        private String processInstanceId;
                                    /**
         * 状态
         */
                                        @TableField("audit_status")
                                                                        private String auditStatus;
                                    /**
         * 审核时间
         */
                                        @TableField("audit_date")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime auditDate;
                                    /**
         * 扩展字段
         */
                                        @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
                                                                        private Object toJson;
                                    /**
         * 就餐人数
         */
                                        @TableField("number_of_meal")
                                                                        private Integer numberOfMeal;



        }
