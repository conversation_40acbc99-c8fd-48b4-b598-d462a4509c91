package xy.server.administration.entity.model.ro;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelTemplate;
import com.xunyue.config.excel.converter.XyExcelConverter;
import com.xunyue.config.excel.enums.ExcelPropertyTypeEnum;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 班组组员列表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
@XyExcelTemplate("班组成员模板")
@ApiModel(value = "ErpAdministrationMgtTeamMembersURO对象", description = "班组组员列表")
public class ErpAdministrationMgtTeamMembersRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String teamMembersGuid;

    @NotBlank
    @ExcelProperty("班组编码")
    @ApiModelProperty(value = "班组 _guid")
    private String teamGuid;

    @NotBlank
    @ExcelProperty("员工工号")
    @ApiModelProperty(value = "员工_guid")
    private String staffGuid;

    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @ExcelProperty(value = "备注")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ExcelProperty("级别")
    @ApiModelProperty(value = "级别")
    private Integer level;

    @NotNull(message = "日报表是否默认选中不能为空")
    @XyExcelProperty(type = ExcelPropertyTypeEnum.DICTIONARY, dictKey = "YES_OR_NO", isUseDefaultValue = true, boolDefaultValue = false)
    @ExcelProperty(value = "日报表是否默认选中", converter = XyExcelConverter.class)
    @ApiModelProperty(value = "日报表是否默认选中")
    private Boolean isDailyReportDefaultChecked;

    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

    /**
     * 是否组长
     */
    @XyExcelProperty(type = ExcelPropertyTypeEnum.DICTIONARY, dictKey = "YES_OR_NO", isUseDefaultValue = true, boolDefaultValue = false)
    @ExcelProperty(value = "是否组长", converter = XyExcelConverter.class)
    @ApiModelProperty(value = "是否组长")
    private Boolean isGroup;

    @ApiModelProperty(value = "是否班长")
    private Boolean isMonitor;

    /**
     * 部门id
     */
    @NotNull(message = "部门id不能为空")
    @XyExcelProperty(isUseDefaultValue = true)
    @ApiModelProperty(value = "部门id")
    private String departmentGuid;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

}
