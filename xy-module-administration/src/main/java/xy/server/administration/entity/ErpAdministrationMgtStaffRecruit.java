package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 招聘申请
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Getter
@Setter
        @Accessors(chain = true)
    @TableName(value = "erp_administration_mgt_staff_recruit", autoResultMap = true)
public class ErpAdministrationMgtStaffRecruit extends BaseEntity implements Serializable{

private static final long serialVersionUID=1L;
                                            /**
         * 年龄要求
         */
                                        @TableField("age_requirements")
                                                                        private String ageRequirements;
                                    /**
         * 技能要求
         */
                                        @TableField("skill_requirements")
                                                                        private String skillRequirements;
                                    /**
         * 性别要求
         */
                                        @TableField("sex_requirements")
                                                                        private String sexRequirements;
                                    /**
         * 工作经验要求
         */
                                        @TableField("work_experience_requirements")
                                                                        private String workExperienceRequirements;
                                    /**
         * 文化程度要求
         */
                                        @TableField("educational_level_requirements")
                                                                        private String educationalLevelRequirements;
                                    /**
         * 岗位
         */
                                        @TableField("post_guid")
                                                                        private String postGuid;
                                    /**
         * 部门
         */
                                        @TableField("department_guid")
                                                                        private String departmentGuid;
                                    /**
         * 备注或描述
         */
                                        @TableField("description")
                                                                        private String description;
                                                                                    /**
         * 工单guid
         */
                                        @TableField("workorder_guid")
                                                                        private String workorderGuid;
                                                            /**
         * 逻辑删除
         */
                                        @TableLogic
                                                                        private Boolean deleted;
                                            /**
         * PK
         */
                                                    @TableId("staff_recruit_guid")
                                                                                    private String staffRecruitGuid;
                                    /**
         * 招聘开始时间
         */
                                        @TableField("recruit_start_time")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime recruitStartTime;
                                    /**
         * 招聘结束时间
         */
                                        @TableField("recruit_end_time")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime recruitEndTime;
                                    /**
         * 招聘原因
         */
                                        @TableField("recruit_reason")
                                                                        private String recruitReason;
                                    /**
         * 扩展字段
         */
                                        @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
                                                                        private Object toJson;
                                    /**
         * 1，全职，2兼职
         */
                                        @TableField("recruit_type")
                                                                        private String recruitType;



        }
