package xy.server.administration.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <p>
 * 调岗记录表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpAdministrationMgtStaffPostAdjustmentVO对象", description = "调岗记录表")
public class ErpAdministrationMgtStaffPostAdjustmentVO {

    @ApiModelProperty(value = "调岗单号")
    private String postAdjustmentNumber;

    @ApiModelProperty(value = "租户id")
    private String tenantGuid;

    @ApiModelProperty(value = "调岗类型（字典：1人事变动）")
    private String postAdjustmentType;

    @XyTrans(dictionaryKey = "TRANSFER_TYPE",dictionaryValue = "postAdjustmentType")
    @ApiModelProperty(value = "调岗类型（字典：1人事变动）")
    private String postAdjustmentTypeName;

    @ApiModelProperty(value = "原岗位")
    private String originalPostGuid;

    @ApiModelProperty(value = "原岗位")
    private String originalPostName;

    @ApiModelProperty(value = "新岗位")
    private String newPostGuid;

    @ApiModelProperty(value = "新岗位")
    private String newPostName;

    @ApiModelProperty(value = "旧部门")
    private String originalDepartmentGuid;

    @ApiModelProperty(value = "旧部门")
    private String originalDepartmentName;

    @ApiModelProperty(value = "新部门")
    private String newDepartmentGuid;

    @ApiModelProperty(value = "新部门")
    private String newDepartmentName;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人GUID(取用户表UserGUID)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人GUID(取用户表UserGUID)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "PK")
    private String staffPostAdjustmentGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期（可变更的）")
    private LocalDateTime receiptDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生效日期")
    private LocalDateTime effectiveDate;

    @ApiModelProperty(value = "调岗原因")
    private String reason;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "员工guid")
    private String staffGuid;

    @ApiModelProperty(value = "员工name")
    private String staffName;

    @ApiModelProperty(value = "")
    private Object toJson;

}
