package xy.server.administration.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 员工列表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffVO对象", description = "员工列表")
public class ErpDepartmentMgtStaffVO {

    @ApiModelProperty(value = "员工id")
    private String staffGuid;

    @ApiModelProperty(value = "员工名字(简称)")
    private String staffShortName;

    @ApiModelProperty(value = "员工全名")
    private String staffFullName;

    @ApiModelProperty(value = "部门id")
    private String departmentGuid;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;
}
