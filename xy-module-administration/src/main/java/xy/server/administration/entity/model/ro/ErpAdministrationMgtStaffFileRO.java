package xy.server.administration.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
* <p>
* 员工文件表RO
* </p>
*
* <AUTHOR>
* @since 2024-10-22
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffFileRO对象", description = "员工文件表")
public class ErpAdministrationMgtStaffFileRO extends BaseEntity {

        @ApiModelProperty(value = "员工guid")
    private String staffGuid;

        @ApiModelProperty(value = "PK")
    private String staffFileGuid;

        @NotNull(message = "文件类型(1图片，2文件，3流程图 )不能为空")
        @ApiModelProperty(value = "文件类型(1图片，2文件，3流程图 )")
    private Integer fileType;

        @ApiModelProperty(value = "文件表_guid")
    private String fileGuid;

        @ApiModelProperty(value = "备注或描述")
    private String description;

}
