package xy.server.administration.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 工序类型属性VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtProductionProcessesTypeAttributeVO对象", description = "工序类型属性")
public class ErpBasicMgtProductionProcessesTypeAttributeVO {

    @ApiModelProperty(value = "PK")
    private String productionProcessesTypeAttributeGuid;

    @ApiModelProperty(value = "1普通属性，2分拆属性，3复合属性，4包装属性，5制版属性，6成品属性")
    private Integer productionProcessesTypeAttributeValue;

    @ApiModelProperty(value = "1普通属性，2分拆属性，3复合属性，4包装属性，5制版属性，6成品属性")
    private String productionProcessesTypeAttributeName;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人GUID(取用户表UserGUID)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人GUID(取用户表UserGUID)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

}