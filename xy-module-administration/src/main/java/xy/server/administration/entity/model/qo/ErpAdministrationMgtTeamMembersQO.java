package xy.server.administration.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 班组组员列表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtTeamMembersQO对象", description = "班组组员列表")
public class ErpAdministrationMgtTeamMembersQO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String teamMembersGuid;

    @ApiModelProperty(value = "班组 _guid")
    private String teamGuid;

    @ApiModelProperty(value = "员工_guid")
    private String staffGuid;


    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;


    @ApiModelProperty(value = "备注或描述")
    private String description;


    @ApiModelProperty(value = "级别")
    private Integer level;


    @ApiModelProperty(value = "日报表是否默认选中")
    private Boolean isDailyReportDefaultChecked;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;


    @ApiModelProperty(value = "创建人")
    private String creator;


    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;


    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;


    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;


    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

    @ApiModelProperty(value = "是否组长")
    private Boolean isGroup;

    @ApiModelProperty(value = "部门id")
    private String departmentGuid;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

}
