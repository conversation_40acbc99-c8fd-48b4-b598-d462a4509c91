package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 班组组员列表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_administration_mgt_team_members",autoResultMap = true)
public class ErpAdministrationMgtTeamMembers extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("team_members_guid")
    private String teamMembersGuid;
    /**
     * 班组 _guid
     */
    @TableField("team_guid")
    private String teamGuid;
    /**
     * 员工_guid
     */
    @TableField("staff_guid")
    private String staffGuid;
    /**
     * 顺序号
     */
    @TableField("serial_number")
    private Integer serialNumber;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 级别
     */
    @TableField("level")
    private Integer level;
    /**
     * 日报表是否默认选中
     */
    @TableField("is_daily_report_default_checked")
    private Boolean isDailyReportDefaultChecked;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 是否组长
     */
    @TableField("is_group")
    private Boolean isGroup;

    /**
     * 是否班长
     */
    @TableField("is_monitor")
    private Boolean isMonitor;

    /**
     * 部门id
     */
    @TableField("department_guid")
    private String departmentGuid;

    @TableField(value = "to_json",typeHandler  = JsonbTypeHandler.class)
    private Object toJson;



}
