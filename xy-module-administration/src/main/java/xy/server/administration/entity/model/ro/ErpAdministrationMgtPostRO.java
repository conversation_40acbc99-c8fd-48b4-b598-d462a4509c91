package xy.server.administration.entity.model.ro;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelTemplate;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 岗位管理RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
@XyExcelTemplate("岗位资料模版")
@ApiModel(value = "ErpAdministrationMgtPostURO对象", description = "岗位管理")
public class ErpAdministrationMgtPostRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String postGuid;

    @NotNull(message = "岗位名称不能为空")
    @ExcelProperty("岗位名称")
    @ApiModelProperty(value = "岗位名称")
    private String postName;

    @ApiModelProperty(value = "岗位编码")
    private String postCode;

    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("部门名称")
    @ApiModelProperty(value = "部门_guid")
    private String departmentGuid;

    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("备注")
    @ApiModelProperty(value = "备注或描述")
    private String description;


    @ApiModelProperty(value = "已被使用(0未使用，1已使用)")
    private Boolean isUsed;


    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "删除标志")
    private Boolean deleted;

    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

}
