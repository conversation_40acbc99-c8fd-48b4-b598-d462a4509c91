package xy.server.administration.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <p>
 * 员工离职扩展表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffResignationRO对象", description = "员工离职扩展表")
public class ErpAdministrationMgtStaffResignationRO extends BaseEntity {

    @ApiModelProperty(value = "员工_guid")
    private String staffGuid;

    @ApiModelProperty(value = "PK")
    private String staffResignationGuid;

    @ApiModelProperty(value = "离职原因")
    private String resignationReason;

    @ApiModelProperty(value = "部分负责人意见")
    private String partialLeaderOpinion;

    @ApiModelProperty(value = "行政人员的意见")
    private String administrationOpinion;

    @ApiModelProperty(value = "离职或者恢复")
    private Boolean type;

    @NotNull(message = "离职日期(不能为空)")
    @ApiModelProperty(value = "离职日期")
    private LocalDateTime dateOfResignation;

    @ApiModelProperty(value = "是否离职")
    private Boolean isOnTheJob;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "离职单号")
    private String resignationNumber;

    @ApiModelProperty(value = "离职类型（字典：1终止劳动合同）")
    private String resignationType;

    @ApiModelProperty(value = "离职原因说明")
    private String resignationReasonDescription;

    @ApiModelProperty(value = "备注或者描述")
    private String description;

    @ApiModelProperty(value = "申请日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applicationDate;

    @ApiModelProperty(value = "最后工作日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastWorkDate;

    @ApiModelProperty(value = "薪资结算日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payrollSettlementDate;

    @ApiModelProperty(value = "离职性质(辞退，自离，解除劳务合同，终止劳务合同)")
    private String natureOfResignation;

}
