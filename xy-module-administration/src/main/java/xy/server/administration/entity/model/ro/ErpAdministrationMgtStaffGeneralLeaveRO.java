package xy.server.administration.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <p>
 * 员工请假记录表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffGeneralLeaveRO对象", description = "员工请假记录表")
public class ErpAdministrationMgtStaffGeneralLeaveRO extends BaseEntity {

    @ApiModelProperty(value = "请假记录单号")
    private String generalLeaveNumber;

    @NotNull(message = "请假类型（字典：1事假）不能为空")
    @ApiModelProperty(value = "请假类型（字典：1事假）")
    private String generalLeaveType;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "PK")
    private String staffGeneralLeaveGuid;

    @ApiModelProperty(value = "员工id")
    private String staffGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

}
