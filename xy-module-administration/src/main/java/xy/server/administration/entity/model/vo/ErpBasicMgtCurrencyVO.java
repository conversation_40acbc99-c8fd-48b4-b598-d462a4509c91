package xy.server.administration.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 币种VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtCurrencyVO对象", description = "币种")
public class ErpBasicMgtCurrencyVO {

    @ApiModelProperty(value = "")
    private String tenantGuid;

    @ApiModelProperty(value = "")
    private String currencyGuid;

    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    @ApiModelProperty(value = "币种符号")
    private String currencySign;

    @ApiModelProperty(value = "是否本币(只允许一个)0-否 1-是")
    private Boolean isLocalCurrency;

    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "已被使用(0未使用，1已使用)")
    private Boolean isUsed;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "创建人GUID")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人GUID")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "逻辑删除")
    private boolean deleted;

}