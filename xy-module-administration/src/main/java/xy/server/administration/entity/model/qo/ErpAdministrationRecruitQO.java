package xy.server.administration.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 工单表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpProductionMgtWorkorderQO对象", description = "工单表")
public class ErpAdministrationRecruitQO {

    @ApiModelProperty(value = "招聘申请单号")
    private String workorderNumber;

    @ApiModelProperty(value = "招聘类型")
    private List<String> recruitType;

    @ApiModelProperty(value = "岗位")
    private String postName;

    @ApiModelProperty(value = "部门")
    private String departmentName;

    @ApiModelProperty(value = "申请开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "申请结束日期")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "招聘开始日期")
    private LocalDateTime recruitStartTime;

    @ApiModelProperty(value = "招聘结束日期")
    private LocalDateTime recruitEndTime;

}