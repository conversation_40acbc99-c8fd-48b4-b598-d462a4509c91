package xy.server.administration.entity.model.ro;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelTemplate;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 部门表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
@XyExcelTemplate("部门资料模版")
@ApiModel(value = "ErpAdministrationMgtDepartmentURO对象", description = "部门表")
public class ErpAdministrationMgtDepartmentRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String departmentGuid;

    @NotBlank(message = "部门名称不能为空")
    @ExcelProperty("部门名称")
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @XyExcelProperty(isUseDefaultValue = true)
    @ExcelProperty("上级部门名称")
    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ExcelProperty("备注")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "已被使用(0未使用，1已使用)")
    private Boolean isUsed;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

}
