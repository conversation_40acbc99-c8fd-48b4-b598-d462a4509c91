package xy.server.administration.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 工序类型参数值VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtProductionProcessesTypeParameterValueVO对象", description = "工序类型参数值")
public class ErpBasicMgtProductionProcessesTypeParameterValueVO {

    @ApiModelProperty(value = "租户GUID")
    private String tenantGuid;

    @ApiModelProperty(value = "")
    private String productionProcessesTypeParameterValueGuid;

    @ApiModelProperty(value = "工序类型参数值")
    private String productionProcessesTypeParameterValue;

    @ApiModelProperty(value = "工序类型参数项 GUID")
    private String productionProcessesTypeParameterItemsGuid;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "创建人GUID(取用户表UserGUID)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人GUID(取用户表UserGUID)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

}