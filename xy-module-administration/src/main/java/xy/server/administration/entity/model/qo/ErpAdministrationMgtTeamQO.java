package xy.server.administration.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 班组管理QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtTeamQO对象", description = "班组管理")
public class ErpAdministrationMgtTeamQO {

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "false查全部，true只查启用的")
    private Boolean type;
}
