package xy.server.administration.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
* <p>
* 招聘申请RO
* </p>
*
* <AUTHOR>
* @since 2024-10-22
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffRecruitRO对象", description = "招聘申请")
public class ErpAdministrationMgtStaffRecruitRO extends BaseEntity {

        @ApiModelProperty(value = "年龄要求")
    private String ageRequirements;

        @ApiModelProperty(value = "技能要求")
    private String skillRequirements;

        @ApiModelProperty(value = "性别要求")
    private String sexRequirements;

        @ApiModelProperty(value = "工作经验要求")
    private String workExperienceRequirements;

        @ApiModelProperty(value = "文化程度要求")
    private String educationalLevelRequirements;

        @ApiModelProperty(value = "岗位")
    private String postGuid;

        @ApiModelProperty(value = "部门")
    private String departmentGuid;

        @NotNull(message = "备注或描述不能为空")
        @ApiModelProperty(value = "备注或描述")
    private String description;

        @ApiModelProperty(value = "工单guid")
    private String workorderGuid;

        @ApiModelProperty(value = "PK")
    private String staffRecruitGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @NotNull(message = "招聘开始时间不能为空")
        @ApiModelProperty(value = "招聘开始时间")
    private LocalDateTime recruitStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @NotNull(message = "招聘结束时间不能为空")
        @ApiModelProperty(value = "招聘结束时间")
    private LocalDateTime recruitEndTime;

        @ApiModelProperty(value = "招聘原因")
    private String recruitReason;

        @ApiModelProperty(value = "扩展字段")
    private Object toJson;

        @ApiModelProperty(value = "1，全职，2兼职")
    private String recruitType;

}
