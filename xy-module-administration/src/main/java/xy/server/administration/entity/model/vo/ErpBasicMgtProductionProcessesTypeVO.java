package xy.server.administration.entity.model.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 工序类型VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtProductionProcessesTypeVO对象", description = "工序类型")
public class ErpBasicMgtProductionProcessesTypeVO {

    @ApiModelProperty(value = "租户GUID")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String productionProcessesTypeGuid;

    @ApiModelProperty(value = "工序类型名称")
    private String productionProcessesTypeName;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点GUID")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "ERP_BasicMGT_ProductionProcessesTypeAttribute GUID	工序类型属性GUID")
    private String productionProcessesTypeAttributeGuid;

    @ApiModelProperty(value = "已被使用(0未使用，1已使用)")
    private Boolean isUsed;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "默认生产排程")
    private Boolean isDefaultProductionScheduling;

    @ApiModelProperty(value = "默认外发")
    private Boolean isDefaultOutsource;

    @ApiModelProperty(value = "创建人GUID(取用户表UserGUID)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人GUID(取用户表UserGUID)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

    private List<ErpBasicMgtProductionProcessesTypeVO> children;
}
