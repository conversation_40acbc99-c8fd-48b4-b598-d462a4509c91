package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 员工列表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@TableName(value = "erp_administration_mgt_staff",autoResultMap = true)
public class ErpAdministrationMgtStaff extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("staff_guid")
    private String staffGuid;
    /**
     * 员工名字(简称)
     */
    @TableField("staff_short_name")
    private String staffShortName;
    /**
     * 员工工号
     */
    @TableField("staff_code")
    private String staffCode;
    /**
     * 部门_guid
     */
    @TableField("department_guid")
    private String departmentGuid;
    /**
     * 岗位_guid
     */
    @TableField("post_guid")
    private String postGuid;
    /**
     * 身份证
     */
    @TableField("idcard")
    private String idcard;
    /**
     * 性别(1男，2女，3中性别)
     */
    @TableField("gender")
    private Integer gender;
    /**
     * 出生日期
     */
    @TableField("date_of_birth")
    private LocalDateTime dateOfBirth;
    /**
     * 开户银行
     */
    @TableField("opening_bank")
    private String openingBank;
    /**
     * 电话
     */
    @TableField("telephone")
    private String telephone;
    /**
     * 银行帐号
     */
    @TableField("bank_account")
    private String bankAccount;
    /**
     * 手机
     */
    @TableField("mobilephone")
    private String mobilephone;
    /**
     * 居住地详细地址
     */
    @TableField("residential_address")
    private String residentialAddress;
    /**
     * 居住地地址行政区域_guid
     */
    @TableField("residential_address_administrative_area_guid")
    private String residentialAddressAdministrativeAreaGuid;
    /**
     * 出生地详细地址
     */
    @TableField("birth_address")
    private String birthAddress;
    /**
     * 出生地详细地址行政区域_guid
     */
    @TableField("birth_address_administrative_area_guid")
    private String birthAddressAdministrativeAreaGuid;
    /**
     * 紧急联系人
     */
    @TableField("emergency_contact")
    private String emergencyContact;
    /**
     * 紧急联系电话
     */
    @TableField("emergency_contact_phone_number")
    private String emergencyContactPhoneNumber;
    /**
     * 紧急联系人关系(父子，母子，亲戚等)
     */
    @TableField("emergency_contact_relationship")
    private String emergencyContactRelationship;
    /**
     * 婚姻状况(0未婚，1已婚)
     */
    @TableField("marital_status")
    private Integer maritalStatus;
    /**
     * 入职日期
     */
    @TableField("date_of_employment")
    private LocalDateTime dateOfEmployment;
    /**
     * 学历(小学，初中，高中，大专，本科，硕士，博士)
     */
    @TableField("educational_background")
    private String educationalBackground;
    /**
     * 毕业院校
     */
    @TableField("graduation_institution")
    private String graduationInstitution;
    /**
     * 毕业日期
     */
    @TableField("graduation_date")
    private LocalDateTime graduationDate;
    /**
     * 政治面貌(群众，党员)
     */
    @TableField("political_landscape")
    private String politicalLandscape;
    /**
     * 是否临时工(0否，1是)
     */
    @TableField("is_temporary_work")
    private Boolean isTemporaryWork;
    /**
     * QQ号码
     */
    @TableField("qq_number")
    private String qqNumber;
    /**
     * 微信号码
     */
    @TableField("wechat_number")
    private String wechatNumber;
    /**
     * 邮件
     */
    @TableField("email")
    private String email;
    /**
     * 员工全名
     */
    @TableField("staff_full_name")
    private String staffFullName;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 在职情况(1在职，0离职)
     */
    @TableField("is_on_the_job")
    private Boolean isOnTheJob;
    /**
     * 离职日期
     */
    @TableField("date_of_resignation")
    private LocalDateTime dateOfResignation;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;

    @TableField(value = "to_json",typeHandler  = JsonbTypeHandler.class)
    private Object toJson;
    /**
     * 用户id
     */
    @TableField("user_guid")
    private String userGuid;
    /**
     * 籍贯
     */
    @TableField("place_of_origin")
    private String placeOfOrigin;
    /**
     * 民族
     */
    @TableField("ethnicity")
    private String ethnicity;
    /**
     * 身高
     */
    @TableField("height")
    private BigDecimal height;
    /**
     * 体重
     */
    @TableField("weight")
    private BigDecimal weight;
    /**
     * 籍贯区域
     */
    @TableField("residential_district")
    private String residentialDistrict;


}
