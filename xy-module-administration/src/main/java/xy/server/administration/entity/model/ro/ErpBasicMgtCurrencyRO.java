package xy.server.administration.entity.model.ro;


import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 币种RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtCurrencyURO对象", description = "币种")
public class ErpBasicMgtCurrencyRO extends BaseEntity {

    @ApiModelProperty(value = "")
    private String currencyGuid;

    @NotNull(message = "币种名称不能为空")
    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    @NotNull(message = "币种符号不能为空")
    @ApiModelProperty(value = "币种符号")
    private String currencySign;

    @NotNull(message = "是否本币(只允许一个)0-否 1-是不能为空")
    @ApiModelProperty(value = "是否本币(只允许一个)0-否 1-是")
    private Boolean isLocalCurrency;

    @NotNull(message = "顺序号不能为空")
    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @NotNull(message = "备注不能为空")
    @ApiModelProperty(value = "备注")
    private String description;

    //    @NotNull(message = "已被使用(0未使用，1已使用)不能为空")
    @ApiModelProperty(value = "已被使用(0未使用，1已使用)")
    private Boolean isUsed;

    //    @NotNull(message = "状态(0停用，1启用)不能为空")
    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

}
