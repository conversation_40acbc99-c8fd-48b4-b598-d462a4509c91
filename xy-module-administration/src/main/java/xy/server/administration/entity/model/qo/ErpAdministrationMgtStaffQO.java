package xy.server.administration.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 员工列表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffQO对象", description = "员工列表")
public class ErpAdministrationMgtStaffQO {

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "客户/供应商guid")
    private List<String> customerOrSupplierIds;

    @ApiModelProperty(value = "月份")
    private List<String> mothThis;

    @ApiModelProperty(value = "部门id")
    private String departmentGuid;
}
