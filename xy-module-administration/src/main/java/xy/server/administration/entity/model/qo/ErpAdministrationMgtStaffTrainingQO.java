package xy.server.administration.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 培训记录表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffTrainingQO对象", description = "培训记录表")
public class ErpAdministrationMgtStaffTrainingQO {

    @ApiModelProperty(value = "培训单号")
    private String trainingNumber;

    @ApiModelProperty(value = "培训类型（字典：1常规培训）")
    private List<String> trainingType;

    @ApiModelProperty(value = "开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束日期")
    private LocalDateTime endTime;

}