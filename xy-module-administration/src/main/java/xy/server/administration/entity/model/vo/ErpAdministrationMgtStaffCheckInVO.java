package xy.server.administration.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <p>
 * 入住记录表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpAdministrationMgtStaffCheckInVO对象", description = "入住记录表")
public class ErpAdministrationMgtStaffCheckInVO {

    @ApiModelProperty(value = "入住单号")
    private String staffCheckInNumber;

    @ApiModelProperty(value = "租户id")
    private String tenantGuid;

    @ApiModelProperty(value = "入住类型（字典：1正常入住，2变更位置）")
    private String checkInType;

    @XyTrans(dictionaryKey = "CHECK_IN_TYPE",dictionaryValue = "checkInType")
    @ApiModelProperty(value = "入住类型（字典：1正常入住，2变更位置）")
    private String checkInTypeName;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人GUID(取用户表UserGUID)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人GUID(取用户表UserGUID)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "PK")
    private String staffCheckInGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期（可变更的）")
    private LocalDateTime receiptDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "退房日期")
    private LocalDateTime checkOutDate;

    @ApiModelProperty(value = "入住原因")
    private String reason;

    @ApiModelProperty(value = "入住位置")
    private String warehouseSpaceGuid;

    @ApiModelProperty(value = "入住位置")
    private String warehouseSpaceName;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "员工guid")
    private String staffGuid;

    @ApiModelProperty(value = "")
    private Object toJson;

    @ApiModelProperty(value = "员工name")
    private String staffName;

}
