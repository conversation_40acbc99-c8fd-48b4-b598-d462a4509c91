package xy.server.administration.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 培训记录表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffTrainingRO对象", description = "培训记录表")
public class ErpAdministrationMgtStaffTrainingRO extends BaseEntity {

    @ApiModelProperty(value = "培训单号")
    private String trainingNumber;

    @ApiModelProperty(value = "培训类型（字典：1常规培训）")
    private String trainingType;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "PK")
    private String staffTrainingGuid;

    @ApiModelProperty(value = "员工guid")
    private String staffGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "单据日期（可变更的）不能为空")
    @ApiModelProperty(value = "单据日期（可变更的）")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "培训标题")
    private String trainingTitle;

    @ApiModelProperty(value = "培训内容")
    private String trainingContent;

    @ApiModelProperty(value = "培训版本")
    private String trainingVersion;

    @ApiModelProperty(value = "培训状态(字典：1有，2无)")
    private String trainingStatus;

    @ApiModelProperty(value = "培训成绩")
    private String trainingResults;

    @ApiModelProperty(value = "培训地点")
    private String trainingLocation;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "培训开始时间")
    private LocalDateTime trainingStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "培训结束时间")
    private LocalDateTime trainingEndTime;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "被培训的员工")
    private List<ErpAdministrationMgtTrainingStaffRO> trainingStaffROList;

    @ApiModelProperty(value = "")
    private Object toJson;

    @ApiModelProperty(value = "培训主讲员")
    private String trainer;

}
