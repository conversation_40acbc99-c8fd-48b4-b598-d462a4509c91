package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 加班安排
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_basic_mgt_work_overtime", autoResultMap = true)
public class ErpBasicMgtWorkOvertime extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("work_overtime_guid")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String workOvertimeGuid;
    /**
     * 加班名称
     */
    @TableField("work_overtime_name")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String workOvertimeName;
    /**
     * 开始时间
     */
    @TableField("start_date")
    private LocalDateTime startDate;
    /**
     * 结束时间
     */
    @TableField("end_date")
    private LocalDateTime endDate;
    /**
     * 状态(0停用，1启用)
     */
    @TableField("state")
    private Boolean state;
    /**
     * 周模板GUID
     */
    @TableField("weekly_template_guid")
    private String weeklyTemplateGuid;
    /**
     * 流程实例ID
     */
    @TableField("process_instance_id")
    private String processInstanceId;
    /**
     * 审核状态
     */
    @TableField("audit_status")
    private String auditStatus;
    /**
     * 审核时间
     */
    @TableField("audit_date")
    private LocalDateTime auditDate;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 备用字段
     */
    @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
    private Object toJson;

    @ApiModelProperty(value = "")
    private String equipmentGuid;

}
