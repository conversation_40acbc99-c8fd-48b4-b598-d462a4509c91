package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 奖罚记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Getter
@Setter
        @Accessors(chain = true)
    @TableName(value = "erp_administration_mgt_staff_rewards_punishments", autoResultMap = true)
public class ErpAdministrationMgtStaffRewardsPunishments extends BaseEntity implements Serializable{

private static final long serialVersionUID=1L;
                                /**
         * 奖罚单号
         */
                                        @TableField("rewards_punishments_number")
                                                                        private String rewardsPunishmentsNumber;
                                                /**
         * 奖罚类型（字典：1常规奖励，2常规惩罚）
         */
                                        @TableField("rewards_punishments_type")
                                                                        private String rewardsPunishmentsType;
                                    /**
         * 备注或描述
         */
                                        @TableField("description")
                                                                        private String description;
                                                                                                            /**
         * 逻辑删除
         */
                                        @TableLogic
                                                                        private Boolean deleted;
                                            /**
         * PK
         */
                                                    @TableId("staff_rewards_punishments_guid")
                                                                                    private String staffRewardsPunishmentsGuid;
                                    /**
         * 单据日期（可变更的）
         */
                                        @TableField("receipt_date")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime receiptDate;
                                    /**
         * 奖罚原因
         */
                                        @TableField("reason")
                                                                        private String reason;
                                    /**
         * 奖罚金额
         */
                                        @TableField("total_amount")
                                                                        private BigDecimal totalAmount;
                                    /**
         * 流程实例ID
         */
                                        @TableField("process_instance_id")
                                                                        private String processInstanceId;
                                    /**
         * 状态
         */
                                        @TableField("audit_status")
                                                                        private String auditStatus;
                                    /**
         * 审核时间
         */
                                        @TableField("audit_date")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime auditDate;
                                    /**
         * 员工guid
         */
                                        @TableField("staff_guid")
                                                                        private String staffGuid;
                                    /**
         * 扩展字段
         */
                                        @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
                                                                        private Object toJson;



        }
