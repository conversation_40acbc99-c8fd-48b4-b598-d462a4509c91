package xy.server.administration.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 奖罚记录表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtStaffRewardsPunishmentsRO对象", description = "奖罚记录表")
public class ErpAdministrationMgtStaffRewardsPunishmentsRO extends BaseEntity {

    @ApiModelProperty(value = "奖罚单号")
    private String rewardsPunishmentsNumber;

    @NotNull(message = "奖罚类型（字典：1常规奖励，2常规惩罚）不能为空")
    @ApiModelProperty(value = "奖罚类型（字典：1常规奖励，2常规惩罚）")
    private String rewardsPunishmentsType;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "PK")
    private String staffRewardsPunishmentsGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "单据日期（可变更的）不能为空")
    @ApiModelProperty(value = "单据日期（可变更的）")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "奖罚原因")
    private String reason;

    @ApiModelProperty(value = "奖罚金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "流程实例ID ")
    private String processInstanceId;

    @ApiModelProperty(value = "状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "")
    private Object toJson;

    @ApiModelProperty(value = "PK")
    private String staffGuid;

}
