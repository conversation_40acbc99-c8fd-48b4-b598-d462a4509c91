package xy.server.administration.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 工序类型参数项RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtProductionProcessesTypeParameterItemsURO对象", description = "工序类型参数项")
public class ErpBasicMgtProductionProcessesTypeParameterItemsRO extends BaseEntity {

    @ApiModelProperty(value = "")
    private String productionProcessesTypeParameterItemsGuid;

    @NotNull(message = "工序参数项不能为空")
    @ApiModelProperty(value = "工序参数项")
    private String productionProcessesTypeParameterItems;

    @NotNull(message = "工序参数类型(字符，数值)不能为空")
    @ApiModelProperty(value = "工序参数类型(字符，数值)")
    private String valueType;

    @ApiModelProperty(value = "已被使用(0未使用，1已使用)")
    private boolean isUsed;

    @ApiModelProperty(value = "工序类型 GUID")
    private String productionProcessesTypeGuid;

    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @NotNull(message = "状态(0停用，1启用)不能为空")
    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @NotNull(message = "逻辑删除不能为空")
    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;

}
