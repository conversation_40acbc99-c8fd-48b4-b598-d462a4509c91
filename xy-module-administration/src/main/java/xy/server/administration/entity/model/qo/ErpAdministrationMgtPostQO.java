package xy.server.administration.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 岗位管理QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpAdministrationMgtPostQO对象", description = "岗位管理")
public class ErpAdministrationMgtPostQO {

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "岗位编码")
    private String postCode;

    @ApiModelProperty(value = "false查全部，true只查启用的")
    private Boolean type;
}
