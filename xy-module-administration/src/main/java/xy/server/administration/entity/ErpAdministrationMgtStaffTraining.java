package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 培训记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Getter
@Setter
        @Accessors(chain = true)
    @TableName(value = "erp_administration_mgt_staff_training", autoResultMap = true)
public class ErpAdministrationMgtStaffTraining extends BaseEntity implements Serializable{

private static final long serialVersionUID=1L;
                                /**
         * 培训单号
         */
                                        @TableField("training_number")
                                                                        private String trainingNumber;
                                                /**
         * 培训类型（字典：1常规培训）
         */
                                        @TableField("training_type")
                                                                        private String trainingType;
                                    /**
         * 备注或描述
         */
                                        @TableField("description")
                                                                        private String description;
                                                                                                            /**
         * 逻辑删除
         */
                                        @TableLogic
                                                                        private Boolean deleted;
                                            /**
         * PK
         */
                                                    @TableId("staff_training_guid")
                                                                                    private String staffTrainingGuid;
                                    /**
         * 单据日期（可变更的）
         */
                                        @TableField("receipt_date")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime receiptDate;
                                    /**
         * 培训标题
         */
                                        @TableField("training_title")
                                                                        private String trainingTitle;
                                    /**
         * 培训内容
         */
                                        @TableField("training_content")
                                                                        private String trainingContent;
                                    /**
         * 培训版本
         */
                                        @TableField("training_version")
                                                                        private String trainingVersion;
                                    /**
         * 培训状态(字典：1有，2无)
         */
                                        @TableField("training_status")
                                                                        private String trainingStatus;
                                    /**
         * 培训地点
         */
                                        @TableField("training_location")
                                                                        private String trainingLocation;
                                    /**
         * 培训开始时间
         */
                                        @TableField("training_start_time")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime trainingStartTime;
                                    /**
         * 培训结束时间
         */
                                        @TableField("training_end_time")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime trainingEndTime;
                                    /**
         * 流程实例ID
         */
                                        @TableField("process_instance_id")
                                                                        private String processInstanceId;
                                    /**
         * 状态
         */
                                        @TableField("audit_status")
                                                                        private String auditStatus;
                                    /**
         * 审核时间
         */
                                        @TableField("audit_date")
                                                                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime auditDate;
                                    /**
         * 扩展字段
         */
                                        @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
                                                                        private Object toJson;
                                    /**
         * 主讲人
         */
                                        @TableField("trainer")
                                                                        private String trainer;



        }
