package xy.server.administration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 岗位管理
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("erp_administration_mgt_post")
@NoArgsConstructor
public class ErpAdministrationMgtPost extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("post_guid")
    private String postGuid;
    /**
     * 岗位名称
     */
    @TableField("post_name")
    private String postName;

    /**
     * 岗位编码
     */
    @TableField("post_code")
    private String postCode;
    /**
     * 部门_guid
     */
    @TableField("department_guid")
    private String departmentGuid;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 已被使用(0未使用，1已使用)
     */
    @TableField("is_used")
    private Boolean isUsed;
    /**
     * 状态(0停用，1启用)
     */
    @TableField("state")
    private Boolean state;
    /**
     * 删除标志
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 顺序号
     */
    @TableField("serial_number")
    private Integer serialNumber;

    @TableField(value = "to_json",typeHandler  = JsonbTypeHandler.class)
    private Object toJson;


}
