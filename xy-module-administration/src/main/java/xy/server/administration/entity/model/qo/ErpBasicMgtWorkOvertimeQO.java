package xy.server.administration.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 加班安排QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBasicMgtWorkOvertimeQO对象", description = "加班安排")
public class ErpBasicMgtWorkOvertimeQO {

    @ApiModelProperty(value = "租户GUID")
    private String tenantGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "PK")
    private String workOvertimeGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "加班名称")
    private String workOvertimeName;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "周模板GUID")
    private String weeklyTemplateGuid;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "时长")
    private Double duration;

    @ApiModelProperty(value = "加班状态")
    private String workState;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    private List<String> keywords;

    @ApiModelProperty(value = "周模板名称")
    private String weeklyTemplateName;

    @ApiModelProperty(value = "")
    private String equipmentGuid;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "审核状态")
    private List<String> auditStatus;

    @ApiModelProperty(value = "false查全部，true只查启用的")
    private Boolean type;

    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;
}
