package xy.server.administration.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMessageUserBindRO对象", description = "")
public class ErpMessageUserBindRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String messageUserBindGuid;

    @ApiModelProperty(value = "员工Guid")
    private String staffGuid;

    @ApiModelProperty(value = "消息平台")
    private String platform;

    @ApiModelProperty(value = "接收人，比如微信公众号就绑定openID之类的")
    private String receiverId;

}
