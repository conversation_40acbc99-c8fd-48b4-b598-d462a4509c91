package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtStaffOnJobStatistics;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffOnJobStatisticsQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffOnJobStatisticsVO;

import java.util.List;

/**
 * <p>
 * 离职记录统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Mapper
public interface ErpAdministrationMgtStaffOnJobStatisticsMapper extends BaseMapper<ErpAdministrationMgtStaffOnJobStatistics> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    ErpAdministrationMgtStaffOnJobStatisticsVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtStaffOnJobStatisticsVO> findList(@Param("model") ErpAdministrationMgtStaffOnJobStatisticsQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<ErpAdministrationMgtStaffOnJobStatisticsVO> findPage(@Param("page") IPage<ErpAdministrationMgtStaffOnJobStatisticsVO> page,@Param("model") ErpAdministrationMgtStaffOnJobStatisticsQO model);
}
