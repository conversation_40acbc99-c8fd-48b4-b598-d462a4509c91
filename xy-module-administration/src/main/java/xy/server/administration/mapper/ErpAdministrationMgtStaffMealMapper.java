package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtStaffMeal;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffMealQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffMealVO;

import java.util.List;

/**
 * <p>
 * 报餐记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Mapper
public interface ErpAdministrationMgtStaffMealMapper extends BaseMapper<ErpAdministrationMgtStaffMeal> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    ErpAdministrationMgtStaffMealVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtStaffMealVO> findList(@Param("qo") ErpAdministrationMgtStaffMealQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<ErpAdministrationMgtStaffMealVO> findPage(@Param("page") IPage<ErpAdministrationMgtStaffMealVO> page,@Param("qo") ErpAdministrationMgtStaffMealQO model);
}
