package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtStaffFile;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffFileVO;

import java.util.List;

/**
 * <p>
 * 员工文件表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Mapper
public interface ErpAdministrationMgtStaffFileMapper extends BaseMapper<ErpAdministrationMgtStaffFile> {

    List<ErpAdministrationMgtStaffFileVO> getList(@Param("guid") String guid);

    /**
     * 根据员工id获取文件信息
     * @param collect1
     * @return
     */
    List<ErpAdministrationMgtStaffFileVO> getFileList(@Param("collect1") List<String> collect1);
}
