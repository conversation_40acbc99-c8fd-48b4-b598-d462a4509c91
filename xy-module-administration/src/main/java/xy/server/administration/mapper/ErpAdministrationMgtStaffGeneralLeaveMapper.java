package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtStaffGeneralLeave;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffGeneralLeaveQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffGeneralLeaveVO;

import java.util.List;

/**
 * <p>
 * 员工请假记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Mapper
public interface ErpAdministrationMgtStaffGeneralLeaveMapper extends BaseMapper<ErpAdministrationMgtStaffGeneralLeave> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    ErpAdministrationMgtStaffGeneralLeaveVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtStaffGeneralLeaveVO> findList(@Param("qo") ErpAdministrationMgtStaffGeneralLeaveQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<ErpAdministrationMgtStaffGeneralLeaveVO> findPage(@Param("page") IPage<ErpAdministrationMgtStaffGeneralLeaveVO> page,@Param("model") ErpAdministrationMgtStaffGeneralLeaveQO model);
}
