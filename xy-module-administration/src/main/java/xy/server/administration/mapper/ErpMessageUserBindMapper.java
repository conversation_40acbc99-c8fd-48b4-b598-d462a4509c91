package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpMessageUserBind;
import xy.server.administration.entity.model.qo.ErpMessageUserBindQO;
import xy.server.administration.entity.model.vo.ErpMessageUserBindVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-14
 */
@Mapper
public interface ErpMessageUserBindMapper extends BaseMapper<ErpMessageUserBind> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpMessageUserBindVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<ErpMessageUserBindVO> findList(@Param("model") ErpMessageUserBindQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<ErpMessageUserBindVO> findPage(@Param("page") IPage<ErpMessageUserBindVO> page, @Param("model") ErpMessageUserBindQO model);
}
