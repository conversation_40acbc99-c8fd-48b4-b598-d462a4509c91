package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtStaffRewardsPunishments;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffRewardsPunishmentsQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffRewardsPunishmentsVO;

import java.util.List;

/**
 * <p>
 * 奖罚记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Mapper
public interface ErpAdministrationMgtStaffRewardsPunishmentsMapper extends BaseMapper<ErpAdministrationMgtStaffRewardsPunishments> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    ErpAdministrationMgtStaffRewardsPunishmentsVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
        List<ErpAdministrationMgtStaffRewardsPunishmentsVO> findList(@Param("qo") ErpAdministrationMgtStaffRewardsPunishmentsQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<ErpAdministrationMgtStaffRewardsPunishmentsVO> findPage(@Param("page") IPage<ErpAdministrationMgtStaffRewardsPunishmentsVO> page,@Param("qo") ErpAdministrationMgtStaffRewardsPunishmentsQO model);
}
