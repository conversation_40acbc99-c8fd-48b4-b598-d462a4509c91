package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtStaffTraining;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffTrainingQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffTrainingVO;

import java.util.List;

/**
 * <p>
 * 培训记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Mapper
public interface ErpAdministrationMgtStaffTrainingMapper extends BaseMapper<ErpAdministrationMgtStaffTraining> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    ErpAdministrationMgtStaffTrainingVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtStaffTrainingVO> findList(@Param("qo") ErpAdministrationMgtStaffTrainingQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<ErpAdministrationMgtStaffTrainingVO> findPage(@Param("page") IPage<ErpAdministrationMgtStaffTrainingVO> page,@Param("qo") ErpAdministrationMgtStaffTrainingQO model);
}
