package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtTeam;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtTeamQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtTeamMembersVO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtTeamVO;

import java.util.List;

/**
 * <p>
 * 班组管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Mapper
public interface ErpAdministrationMgtTeamMapper extends BaseMapper<ErpAdministrationMgtTeam> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpAdministrationMgtTeamVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtTeamVO> findList(@Param("model") ErpAdministrationMgtTeamQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<ErpAdministrationMgtTeamVO> findPage(@Param("page") IPage<ErpAdministrationMgtTeamVO> page, @Param("model") ErpAdministrationMgtTeamQO model);

    /**
     * 根据班组guid获取员工
     * @param teamGuid
     * @return
     */
    List<ErpAdministrationMgtTeamMembersVO> getByParent(@Param("teamGuid") String teamGuid);
}
