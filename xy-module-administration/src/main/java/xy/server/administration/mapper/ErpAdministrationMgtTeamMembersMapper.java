package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtTeamMembers;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtTeamMembersQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtTeamMembersVO;

import java.util.List;

/**
 * <p>
 * 班组组员列表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Mapper
public interface ErpAdministrationMgtTeamMembersMapper extends BaseMapper<ErpAdministrationMgtTeamMembers> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpAdministrationMgtTeamMembersVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtTeamMembersVO> findList(@Param("model") ErpAdministrationMgtTeamMembersQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<ErpAdministrationMgtTeamMembersVO> findPage(@Param("page") IPage<ErpAdministrationMgtTeamMembersVO> page, @Param("model") ErpAdministrationMgtTeamMembersQO model);

    int deletes(@Param("teamGuid") String teamGuid);
}
