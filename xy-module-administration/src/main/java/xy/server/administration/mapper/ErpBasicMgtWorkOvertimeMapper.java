package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xunyue.config.dataScope.DataScope;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpBasicMgtWorkOvertime;
import xy.server.administration.entity.model.qo.ErpBasicMgtWorkOvertimeQO;
import xy.server.administration.entity.model.vo.ErpBasicMgtWorkOvertimeVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 加班安排 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Mapper
public interface ErpBasicMgtWorkOvertimeMapper extends BaseMapper<ErpBasicMgtWorkOvertime> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpBasicMgtWorkOvertimeVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    @DataScope(component = "work-overtime")
    List<ErpBasicMgtWorkOvertimeVO> findList(@Param("qo") ErpBasicMgtWorkOvertimeQO qo);

    /**
     * 分页查询
     *
     * @param
     * @param model
     * @return
     */
    @DataScope(component = "work-overtime")
    List<ErpBasicMgtWorkOvertimeVO> findPage(@Param("model") ErpBasicMgtWorkOvertimeQO model);

    List<ErpBasicMgtWorkOvertimeVO> selectLists(@Param("date1") LocalDateTime date1, @Param("equipmentGuid") String equipmentGuid);
}
