package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtStaffPostAdjustment;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffPostAdjustmentQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffPostAdjustmentVO;

import java.util.List;

/**
 * <p>
 * 调岗记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Mapper
public interface ErpAdministrationMgtStaffPostAdjustmentMapper extends BaseMapper<ErpAdministrationMgtStaffPostAdjustment> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    ErpAdministrationMgtStaffPostAdjustmentVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtStaffPostAdjustmentVO> findList(@Param("qo") ErpAdministrationMgtStaffPostAdjustmentQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<ErpAdministrationMgtStaffPostAdjustmentVO> findPage(@Param("page") IPage<ErpAdministrationMgtStaffPostAdjustmentVO> page,@Param("qo") ErpAdministrationMgtStaffPostAdjustmentQO model);
}
