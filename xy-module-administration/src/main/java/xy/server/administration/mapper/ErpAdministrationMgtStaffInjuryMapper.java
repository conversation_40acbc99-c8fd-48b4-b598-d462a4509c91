package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtStaffInjury;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffInjuryQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffInjuryVO;

import java.util.List;

/**
 * <p>
 * 工伤记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Mapper
public interface ErpAdministrationMgtStaffInjuryMapper extends BaseMapper<ErpAdministrationMgtStaffInjury> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    ErpAdministrationMgtStaffInjuryVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtStaffInjuryVO> findList(@Param("qo") ErpAdministrationMgtStaffInjuryQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<ErpAdministrationMgtStaffInjuryVO> findPage(@Param("page") IPage<ErpAdministrationMgtStaffInjuryVO> page,@Param("qo") ErpAdministrationMgtStaffInjuryQO model);
}
