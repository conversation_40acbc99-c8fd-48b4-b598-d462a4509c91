package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationRecruit;
import xy.server.administration.entity.model.qo.ErpAdministrationRecruitQO;
import xy.server.administration.entity.model.vo.ErpAdministrationRecruitVO;

import java.util.List;

/**
 * <p>
 * 工单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-26
 */
@Mapper
public interface ErpAdministrationRecruitMapper extends BaseMapper<ErpAdministrationRecruit> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    ErpAdministrationRecruitVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpAdministrationRecruitVO> findList(@Param("qo") ErpAdministrationRecruitQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<ErpAdministrationRecruitVO> findPage(@Param("page") IPage<ErpAdministrationRecruitVO> page, @Param("qo") ErpAdministrationRecruitQO model);
}
