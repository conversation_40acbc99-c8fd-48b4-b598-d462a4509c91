package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtDepartment;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtDepartmentQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtDepartmentVO;
import xy.server.administration.entity.model.vo.ErpDepartmentMgtStaffVO;

import java.util.List;

/**
 * <p>
 * 部门表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Mapper
public interface ErpAdministrationMgtDepartmentMapper extends BaseMapper<ErpAdministrationMgtDepartment> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpAdministrationMgtDepartmentVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtDepartmentVO> findList(@Param("model") ErpAdministrationMgtDepartmentQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<ErpAdministrationMgtDepartmentVO> findPage(@Param("page") IPage<ErpAdministrationMgtDepartmentVO> page, @Param("model") ErpAdministrationMgtDepartmentQO model);

    List<ErpAdministrationMgtDepartment> getListByParentClassificationGuid(@Param("departmentGuid") String departmentGuid);

    List<ErpDepartmentMgtStaffVO> getStaffList(@Param("departmentGuid") String departmentGuid);
}
