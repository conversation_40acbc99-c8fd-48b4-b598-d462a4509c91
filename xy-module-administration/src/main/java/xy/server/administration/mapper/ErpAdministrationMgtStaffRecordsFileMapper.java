package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtStaffRecordsFile;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffRecordsFileVO;

import java.util.List;

/**
 * <p>
 * 任务文件表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Mapper
public interface ErpAdministrationMgtStaffRecordsFileMapper extends BaseMapper<ErpAdministrationMgtStaffRecordsFile> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    ErpAdministrationMgtStaffRecordsFileVO getDataByGuid(@Param("guid") String guid);
    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    List<ErpAdministrationMgtStaffRecordsFileVO> getList(@Param("guid") String guid);
}
