package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtStaff;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffQO;
import xy.server.administration.entity.model.qo.ErpAuditorQO;
import xy.server.administration.entity.model.vo.*;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 员工列表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Mapper
public interface ErpAdministrationMgtStaffMapper extends BaseMapper<ErpAdministrationMgtStaff> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpAdministrationMgtStaffVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtStaffVO> findList(@Param("model") ErpAdministrationMgtStaffQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<ErpAdministrationMgtStaffVO> findPage(@Param("page") IPage<ErpAdministrationMgtStaffVO> page, @Param("model") ErpAdministrationMgtStaffQO model);

    Integer getUsedCustomer(@Param("staffGuid") String staffGuid);

    Boolean updateCustomer(@Param("staffGuid") String staffGuid);

    List<ErpAdministrationMgtStaffVO> noUserAvailableList(ErpAdministrationMgtStaffQO qo);

    Boolean updateDateOfBirth(@Param("staffGuid") String staffGuid, @Param("dateOfBirth") Object dateOfBirth);

    /**获取部门及部门下的员工
     *
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtDepartmentAndStaffVO> findStaffAndDepartmentList(@Param("qo") ErpAdministrationMgtStaffQO qo);

    ErpAdministrationMgtStaffOnJobStatisticsVO getOnJob(@Param("now") LocalDate localDate);

    /**
     * 批量获取审核人列表
     * 入参数，请先把businessKey和procInstId用_拼接起来
     * @param businessKey_procInstId
     * @return
     */
    List<ErpAuditorVo> getAuditorList(@Param("ids") List<String> businessKey_procInstId);


    /**
     * 根据订单查询跟单员及业务员(批量)
     *
     * @param orderGuids
     * @return
     */
    List<ErpAdministrationMgtStaffVO> selectMerchandiserOrSalesman(@Param("orderGuids") List<String> orderGuids);

    /**
     * 根据团队查询团队成员
     * @param teamGuids
     * @return
     */
    List<ErpAdministrationMgtStaffVO> listTeamStaffByTeamGuids(@Param("teamGuids") List<String> teamGuids);


    List<ErpAuditorVo> selectAuditMan(@Param("procInstIds") List<String> procInstIds);

    List<ErpAuditorVo> mapNewAuditMan(@Param("procInstIds") List<String> procInstIds);

    /**
     * 根据审核人查询审核流数据
     *
     * @param qo
     * @return
     */
    List<ErpAuditorVo> selectAuditorByKey(@Param("qo") ErpAuditorQO qo);
}
