package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtStaffResignation;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffResignationQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffResignationVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 员工离职扩展表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Mapper
public interface ErpAdministrationMgtStaffResignationMapper extends BaseMapper<ErpAdministrationMgtStaffResignation> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    ErpAdministrationMgtStaffResignationVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtStaffResignationVO> findList(@Param("qo") ErpAdministrationMgtStaffResignationQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<ErpAdministrationMgtStaffResignationVO> findPage(@Param("page") IPage<ErpAdministrationMgtStaffResignationVO> page,@Param("qo") ErpAdministrationMgtStaffResignationQO model);

    void updateStaffResignationStatus(@Param("staffGuid") String staffGuid, @Param("dateOfResignation") LocalDateTime dateOfResignation, @Param("state") boolean b);
}
