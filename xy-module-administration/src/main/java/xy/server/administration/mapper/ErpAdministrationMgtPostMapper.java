package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtPost;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtPostQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtPostVO;

import java.util.List;

/**
 * <p>
 * 岗位管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Mapper
public interface ErpAdministrationMgtPostMapper extends BaseMapper<ErpAdministrationMgtPost> {

        /**
         * 根据GUID获取
         * @param guid
         * @return
         */
    ErpAdministrationMgtPostVO getDataByGuid(@Param("guid") String guid);

        /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtPostVO> findList(@Param("model") ErpAdministrationMgtPostQO qo);

        /**
       * 分页查询
       * @param page
       * @param model
       * @return
       */
    IPage<ErpAdministrationMgtPostVO> findPage(@Param("page") IPage<ErpAdministrationMgtPostVO> page,@Param("model") ErpAdministrationMgtPostQO model);
        }
