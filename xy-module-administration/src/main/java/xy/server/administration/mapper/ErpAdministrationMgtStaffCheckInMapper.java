package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtStaffCheckIn;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffCheckInQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffCheckInVO;

import java.util.List;

/**
 * <p>
 * 入住记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Mapper
public interface ErpAdministrationMgtStaffCheckInMapper extends BaseMapper<ErpAdministrationMgtStaffCheckIn> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    ErpAdministrationMgtStaffCheckInVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtStaffCheckInVO> findList(@Param("qo") ErpAdministrationMgtStaffCheckInQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<ErpAdministrationMgtStaffCheckInVO> findPage(@Param("page") IPage<ErpAdministrationMgtStaffCheckInVO> page,@Param("qo") ErpAdministrationMgtStaffCheckInQO model);
}
