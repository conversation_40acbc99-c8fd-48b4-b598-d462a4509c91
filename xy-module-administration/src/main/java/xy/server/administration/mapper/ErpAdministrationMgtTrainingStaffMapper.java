package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtTrainingStaff;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtTrainingStaffQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtTrainingStaffVO;

import java.util.List;

/**
 * <p>
 * 培训员工扩展表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Mapper
public interface ErpAdministrationMgtTrainingStaffMapper extends BaseMapper<ErpAdministrationMgtTrainingStaff> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    ErpAdministrationMgtTrainingStaffVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtTrainingStaffVO> findList(@Param("model") ErpAdministrationMgtTrainingStaffQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<ErpAdministrationMgtTrainingStaffVO> findPage(@Param("page") IPage<ErpAdministrationMgtTrainingStaffVO> page,@Param("model") ErpAdministrationMgtTrainingStaffQO model);

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    List<ErpAdministrationMgtTrainingStaffVO> getList(@Param("guid") String guid);
}
