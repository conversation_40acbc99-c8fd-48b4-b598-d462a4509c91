package xy.server.administration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.administration.entity.ErpAdministrationMgtStaffRecruit;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffRecruitQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffRecruitVO;

import java.util.List;

/**
 * <p>
 * 招聘申请 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Mapper
public interface ErpAdministrationMgtStaffRecruitMapper extends BaseMapper<ErpAdministrationMgtStaffRecruit> {

    /**
     * 根据GUID获取
     * @param guid
     * @return
     */
    ErpAdministrationMgtStaffRecruitVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtStaffRecruitVO> findList(@Param("qo") ErpAdministrationMgtStaffRecruitQO qo);

    /**
    * 分页查询
    * @param page
    * @param model
    * @return
    */
    IPage<ErpAdministrationMgtStaffRecruitVO> findPage(@Param("page") IPage<ErpAdministrationMgtStaffRecruitVO> page,@Param("model") ErpAdministrationMgtStaffRecruitQO model);
}
