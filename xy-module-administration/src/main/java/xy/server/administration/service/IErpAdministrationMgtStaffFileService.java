package xy.server.administration.service;

import com.baomidou.mybatisplus.extension.service.IService;
import xy.server.administration.entity.ErpAdministrationMgtStaffFile;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffFileVO;

import java.util.List;

/**
 * @apiNote 员工文件表 服务类
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface IErpAdministrationMgtStaffFileService extends IService<ErpAdministrationMgtStaffFile> {
    /**
     * 根据员工id获取文件信息
     * @param collect1
     * @return
     */
    List<ErpAdministrationMgtStaffFileVO> getFileList(List<String> collect1);
}
