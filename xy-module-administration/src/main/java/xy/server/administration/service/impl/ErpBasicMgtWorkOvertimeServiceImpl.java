package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.i18n.BaseResultErrorCodeImpl;
import com.xunyue.common.util.UsedUtils;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.activiti.common.enums.BusinessStatusEnum;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.administration.entity.ErpBasicMgtWorkOvertime;
import xy.server.administration.entity.model.qo.ErpBasicMgtWorkOvertimeQO;
import xy.server.administration.entity.model.ro.ErpBasicMgtWorkOvertimeRO;
import xy.server.administration.entity.model.vo.ErpBasicMgtWorkOvertimeVO;
import xy.server.administration.i18n.ResultErrorCode;
import xy.server.administration.mapper.ErpBasicMgtWorkOvertimeMapper;
import xy.server.administration.service.IErpBasicMgtWorkOvertimeService;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 加班安排 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service("WORK_OVERTIME.WORK_OVERTIME")
public class ErpBasicMgtWorkOvertimeServiceImpl extends ServiceImpl<ErpBasicMgtWorkOvertimeMapper, ErpBasicMgtWorkOvertime> implements IErpBasicMgtWorkOvertimeService, ActEventStrategyService {

    @Autowired
    private UsedUtils usedUtils;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpBasicMgtWorkOvertimeRO ro) {
        saveOrUpdateVerify(ro);
        //判断加班开始时间不能大于加班结束时间
        if (ro.getStartDate().compareTo(ro.getEndDate()) > 0) {
            throw new FlowException(ResultErrorCode.NO_REPETITION_STARTDATE_IS_NOT_OVER_ENDADTE.getMsg(), ResultErrorCode.NO_REPETITION_STARTDATE_IS_NOT_OVER_ENDADTE.getCode());
        }
        //判断加班开始时间和加班结束时间是否小于当前时间
        LocalDateTime currentDateTime = LocalDateTime.now();
        if (currentDateTime.compareTo(ro.getEndDate()) > 0 || currentDateTime.compareTo(ro.getStartDate()) > 0) {
            throw new FlowException(ResultErrorCode.NO_REPETITION_BASIC_TIME_EXPIRES.getMsg(), ResultErrorCode.NO_REPETITION_BASIC_TIME_EXPIRES.getCode());
        }

        LambdaQueryWrapper<ErpBasicMgtWorkOvertime> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpBasicMgtWorkOvertime::getTenantGuid, ro.getTenantGuid())
                .eq(ErpBasicMgtWorkOvertime::getDeleted, false);
        List<ErpBasicMgtWorkOvertime> erpBasicMgtWorkOvertimes = baseMapper.selectList(wrapper);
        if (!erpBasicMgtWorkOvertimes.isEmpty() && erpBasicMgtWorkOvertimes.size() != 0) {
            //判断加班时间是否有重叠
            if (isOverlapLocalTime(erpBasicMgtWorkOvertimes, ro.getStartDate(), ro.getEndDate(), ro.getWorkOvertimeGuid())) {
                throw new FlowException(ResultErrorCode.NO_REPETITION_BASIC_WORK_OVERLAP.getMsg(), ResultErrorCode.NO_REPETITION_BASIC_WORK_OVERLAP.getCode());
            }

        }
        ErpBasicMgtWorkOvertime entity = new ErpBasicMgtWorkOvertime();
        BeanUtil.copyProperties(ro, entity);
        //增加被使用
        if (super.save(entity)) {
            // 处理相关表已被使用字段
            this.changeIsUsed(entity, null, false);
            // 启动审核流程
            iProcessInstanceService.start(WorkflowKeyEnum.WORK_OVERTIME, entity.getWorkOvertimeGuid());
            return true;
        }
        return false;
    }

    /**
     * 判断加班时间是否有重叠
     *
     * @param erpBasicMgtWorkOvertimes
     * @param startDate
     * @param endDate
     * @param id
     * @return
     */
    private boolean isOverlapLocalTime(List<ErpBasicMgtWorkOvertime> erpBasicMgtWorkOvertimes, LocalDateTime startDate, LocalDateTime endDate, String id) {
        for (ErpBasicMgtWorkOvertime erpBasicMgtWorkOvertime : erpBasicMgtWorkOvertimes) {
            if (startDate.compareTo(erpBasicMgtWorkOvertime.getEndDate()) <= 0 && endDate.compareTo(erpBasicMgtWorkOvertime.getStartDate()) >= 0
                    && !erpBasicMgtWorkOvertime.getWorkOvertimeGuid().equals(id)) {
                return true;
            }
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String workOvertimeGuid) {
        ErpBasicMgtWorkOvertime entity = baseMapper.selectById(workOvertimeGuid);
        if (!entity.getAuditStatus().equals(BusinessStatusEnum.DRAFT.getStatus())){
            throw new FlowException(BaseResultErrorCodeImpl.AUDIT_STATE_NOT_DRAFT);
        }
        //减少被使用
        if (super.removeById(workOvertimeGuid)) {
            // 处理相关表已被使用字段
            this.changeIsUsed(entity, null, true);

            // 删除流程相关数据
            iProcessInstanceService.deleteProcessAndHisInst(workOvertimeGuid, WorkflowKeyEnum.WORK_OVERTIME);
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpBasicMgtWorkOvertimeRO ro) {
        ErpBasicMgtWorkOvertime oldEntity = baseMapper.selectById(ro.getWorkOvertimeGuid());
        saveOrUpdateVerify(ro);
        //判断加班开始时间不能大于加班结束时间
        if (ro.getStartDate().compareTo(ro.getEndDate()) > 0) {
            throw new FlowException(ResultErrorCode.NO_REPETITION_STARTDATE_IS_NOT_OVER_ENDADTE.getMsg(), ResultErrorCode.NO_REPETITION_STARTDATE_IS_NOT_OVER_ENDADTE.getCode());
        }
        //判断加班开始时间和加班结束时间是否小于当前时间
        LocalDateTime currentDateTime = LocalDateTime.now();
        if (currentDateTime.compareTo(ro.getEndDate()) > 0) {
            throw new FlowException(ResultErrorCode.NO_REPETITION_BASIC_TIME_EXPIRES.getMsg(), ResultErrorCode.NO_REPETITION_BASIC_TIME_EXPIRES.getCode());
        }


        LambdaQueryWrapper<ErpBasicMgtWorkOvertime> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ErpBasicMgtWorkOvertime::getTenantGuid, ro.getTenantGuid())
                .eq(ErpBasicMgtWorkOvertime::getDeleted, false)
                .ne(ro.getWorkOvertimeGuid() != null, ErpBasicMgtWorkOvertime::getWorkOvertimeGuid, ro.getWorkOvertimeGuid());
        List<ErpBasicMgtWorkOvertime> erpBasicMgtWorkOvertimes = baseMapper.selectList(queryWrapper);
        if (!erpBasicMgtWorkOvertimes.isEmpty() && erpBasicMgtWorkOvertimes.size() != 0) {
            //判断加班时间是否有重叠
            if (isOverlapLocalTime(erpBasicMgtWorkOvertimes, ro.getStartDate(), ro.getEndDate(), ro.getWorkOvertimeGuid())) {
                throw new FlowException(ResultErrorCode.NO_REPETITION_BASIC_WORK_OVERLAP.getMsg(), ResultErrorCode.NO_REPETITION_BASIC_WORK_OVERLAP.getCode());
            }

        }
        ErpBasicMgtWorkOvertime entity = new ErpBasicMgtWorkOvertime();
        BeanUtil.copyProperties(ro, entity);
        if (super.updateById(entity)) {
            // 处理相关表已被使用字段
            this.changeIsUsed(entity, oldEntity, false);
            return true;
        }
        return false;
    }

    @Override
    public ErpBasicMgtWorkOvertimeVO getById(String workOvertimeGuid) {
        ErpBasicMgtWorkOvertimeVO erpBasicMgtWorkOvertimeVO = baseMapper.getDataByGuid(workOvertimeGuid);
        //计算加班时长
        duration(erpBasicMgtWorkOvertimeVO);
        //判断加班状态
        workState(erpBasicMgtWorkOvertimeVO);
        return erpBasicMgtWorkOvertimeVO;
    }

    @Override
    public List<ErpBasicMgtWorkOvertimeVO> findList(ErpBasicMgtWorkOvertimeQO qo, String tenantGuid) {

        qo.setTenantGuid(tenantGuid);
        if (!StringUtils.isEmpty(qo.getKeyword())) {
            qo.setKeywords(Arrays.asList(qo.getKeyword().split(" ")));
        }
        List<ErpBasicMgtWorkOvertimeVO> erpBasicMgtWorkOvertimeVOList = baseMapper.findList(qo);
        for (ErpBasicMgtWorkOvertimeVO erpBasicMgtWorkOvertimeVO : erpBasicMgtWorkOvertimeVOList) {
            //计算加班时长
            duration(erpBasicMgtWorkOvertimeVO);
            //判断加班状态
            workState(erpBasicMgtWorkOvertimeVO);
        }
        return erpBasicMgtWorkOvertimeVOList;
    }

    @Override
    public IPage<ErpBasicMgtWorkOvertimeVO> findPage(PageParams<ErpBasicMgtWorkOvertimeQO> pageParams) {
        IPage<ErpBasicMgtWorkOvertimeVO> page = pageParams.buildPage();
        ErpBasicMgtWorkOvertimeQO model = pageParams.getModel();
        model.setTenantGuid(pageParams.getTenantGuid());

        //关键字+空格+关键字模糊查询
        if (StringUtils.isNotEmpty(model.getKeyword())) {
            List<String> params = Arrays.asList(model.getKeyword().split(" "));
            if (CollUtil.isNotEmpty(params)) {
                model.setKeywords(params);
            }
        }
        List<ErpBasicMgtWorkOvertimeVO> records = baseMapper.findPage(model);
        for (ErpBasicMgtWorkOvertimeVO erpBasicMgtWorkOvertimeVO : records) {
            //计算加班时长
            duration(erpBasicMgtWorkOvertimeVO);
            //判断加班状态
            workState(erpBasicMgtWorkOvertimeVO);
        }
        page.setRecords(records);
        page.setTotal(records.size());
        return page;
    }

    /**
     * 计算时长
     *
     * @param erpBasicMgtWorkOvertimeVO
     */
    private void duration(ErpBasicMgtWorkOvertimeVO erpBasicMgtWorkOvertimeVO) {
        double startTimes = erpBasicMgtWorkOvertimeVO.getStartDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        double endTimes = erpBasicMgtWorkOvertimeVO.getEndDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        double count1 = (endTimes - startTimes) / 1000 / 60 / 60;
        DecimalFormat decimalFormat = new DecimalFormat("0.0");
        // 格式化数字，保留一位小数
        Double duration = Double.valueOf(decimalFormat.format(count1));
        erpBasicMgtWorkOvertimeVO.setDuration(duration);
    }

    /**
     * 判断加班状态
     *
     * @param erpBasicMgtWorkOvertimeVO
     */
    private void workState(ErpBasicMgtWorkOvertimeVO erpBasicMgtWorkOvertimeVO) {
        LocalDateTime startDate = erpBasicMgtWorkOvertimeVO.getStartDate();
        LocalDateTime endDate = erpBasicMgtWorkOvertimeVO.getEndDate();
        LocalDateTime currentDateTime = LocalDateTime.now();
        if (currentDateTime.compareTo(startDate) < 0) {
            erpBasicMgtWorkOvertimeVO.setWorkState(ResultErrorCode.NO_REPETITION_BASIC_NO_OVERTIME.getMsg());
        }
        if (currentDateTime.compareTo(startDate) > 0 && currentDateTime.compareTo(endDate) < 0) {
            erpBasicMgtWorkOvertimeVO.setWorkState(ResultErrorCode.NO_REPETITION_BASIC_BE_WORKING_OVERTIME.getMsg());
        }
        if (currentDateTime.compareTo(endDate) > 0) {
            erpBasicMgtWorkOvertimeVO.setWorkState(ResultErrorCode.NO_REPETITION_BASIC_HAVE_WORKED_OVERTIME.getMsg());
        }
    }

    /**
     * 停用/启用
     *
     * @param workOvertimeGuid
     * @return
     */
    @Override
    public Boolean stopState(String workOvertimeGuid) {
        ErpBasicMgtWorkOvertime erpBasicMgtWorkOvertime = baseMapper.selectById(workOvertimeGuid);
        if (Objects.isNull(erpBasicMgtWorkOvertime)) {
            throw new FlowException(ResultErrorCode.NO_REPETITION_DATA_NOT_EXIST.getMsg(), ResultErrorCode.NO_REPETITION_DATA_NOT_EXIST.getCode());
        }
        if (!erpBasicMgtWorkOvertime.getState()) {
            erpBasicMgtWorkOvertime.setState(true);
            updateById(erpBasicMgtWorkOvertime);
            return true;
        }
        erpBasicMgtWorkOvertime.setState(Boolean.FALSE);
        updateById(erpBasicMgtWorkOvertime);
        return true;
    }

    /**
     * 保存
     *
     * @param saveData
     * @param tenantGuid
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveDate(InsertOrUpdateList<ErpBasicMgtWorkOvertimeRO> saveData, String tenantGuid) {
        if (CollUtil.isNotEmpty(saveData.getInsertList())) {
            saveData.getInsertList().forEach(e -> {
                e.setTenantGuid(tenantGuid);
                this.create(e);
            });
        }

        if (CollUtil.isNotEmpty(saveData.getUpdateList())) {
            saveData.getUpdateList().forEach(e -> {
                e.setTenantGuid(tenantGuid);
                this.update(e);
            });
        }
        return true;
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @Override
    public Boolean removeBatchById(List<String> ids) {
        for (String id : ids) {
            ErpBasicMgtWorkOvertime erpBasicMgtWorkOvertime = baseMapper.selectById(id);
            if (!erpBasicMgtWorkOvertime.getAuditStatus().equals(BusinessStatusEnum.DRAFT.getStatus())){
                throw new FlowException(BaseResultErrorCodeImpl.AUDIT_STATE_NOT_DRAFT);
            }
            LocalDateTime endDate = erpBasicMgtWorkOvertime.getEndDate();
            LocalDateTime currentDateTime = LocalDateTime.now();
            if (currentDateTime.compareTo(endDate) > 0) {
                throw new FlowException(ResultErrorCode.NO_REPETITION_BASIC_TIME_EXPIRES.getMsg(), ResultErrorCode.NO_REPETITION_BASIC_TIME_EXPIRES.getCode());
            }
        }
        for (String id : ids) {
            ErpBasicMgtWorkOvertime erpBasicMgtWorkOvertime = baseMapper.selectById(id);
            //减少被使用
            this.changeIsUsed(erpBasicMgtWorkOvertime, null, true);
        }
        return removeByIds(ids);
    }

    @Override
    public List<ErpBasicMgtWorkOvertimeVO> selectList(LocalDateTime date1,String equipmentGuid) {
        return baseMapper.selectLists(date1,equipmentGuid);
    }

    /**
     * 判断名称是否重复
     *
     * @param ro
     * @return
     */
    private Boolean saveOrUpdateVerify(ErpBasicMgtWorkOvertimeRO ro) {
        // 判断名称不能重复
        LambdaQueryWrapper<ErpBasicMgtWorkOvertime> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ErpBasicMgtWorkOvertime::getTenantGuid, ro.getTenantGuid())
                .eq(ErpBasicMgtWorkOvertime::getWorkOvertimeName, ro.getWorkOvertimeName())
                .ne(ro.getWorkOvertimeGuid() != null, ErpBasicMgtWorkOvertime::getWorkOvertimeGuid, ro.getWorkOvertimeGuid());
        if (baseMapper.exists(wrapper)) {
            throw new FlowException(ResultErrorCode.NO_REPETITION_BASIC_WORK_OVERTIME.getMsg(), ResultErrorCode.NO_REPETITION_BASIC_WORK_OVERTIME.getCode());
        }
        return true;
    }

    /**
     * 处理相关表已被删除数据
     *
     * @param entity
     * @param oldEntity
     * @param isDel
     */
    public void changeIsUsed(ErpBasicMgtWorkOvertime entity, ErpBasicMgtWorkOvertime oldEntity, boolean isDel) {
        if (isDel) {
            usedUtils.isUsed("erp_basic_mgt_weekly_template", entity.getWeeklyTemplateGuid(), "weekly_template_guid");

        } else {
            if (oldEntity != null) {
                usedUtils.isUsed("erp_basic_mgt_weekly_template", entity.getWeeklyTemplateGuid(), oldEntity.getWorkOvertimeGuid(), "weekly_template_guid");

            } else {
                usedUtils.isUsed("erp_basic_mgt_weekly_template", entity.getWeeklyTemplateGuid(), null, "weekly_template_guid");

            }
        }
    }

}
