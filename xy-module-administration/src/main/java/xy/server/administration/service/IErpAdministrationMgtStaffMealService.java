package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtStaffMeal;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffMealQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffMealRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffMealVO;

import java.util.List;

/**
 * @apiNote 报餐记录表 服务类
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface IErpAdministrationMgtStaffMealService extends IService<ErpAdministrationMgtStaffMeal> {
                boolean create(ErpAdministrationMgtStaffMealRO ro);

    boolean delete(String staffMealGuid);

    boolean deleteByBatch(List<String> staffMealGuids);

    boolean update(ErpAdministrationMgtStaffMealRO ro);

    boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffMealRO> dataList);

    ErpAdministrationMgtStaffMealVO getDataById(String staffMealGuid);

    List<ErpAdministrationMgtStaffMealVO> findList(ErpAdministrationMgtStaffMealQO qo);

    IPage<ErpAdministrationMgtStaffMealVO> findPage(PageParams<ErpAdministrationMgtStaffMealQO> pageParams);
}
