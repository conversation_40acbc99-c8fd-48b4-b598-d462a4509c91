package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtStaffRewardsPunishments;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffRewardsPunishmentsQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffRewardsPunishmentsRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffRewardsPunishmentsVO;

import java.util.List;

/**
 * @apiNote 奖罚记录表 服务类
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface IErpAdministrationMgtStaffRewardsPunishmentsService extends IService<ErpAdministrationMgtStaffRewardsPunishments> {
                                                            boolean create(ErpAdministrationMgtStaffRewardsPunishmentsRO ro);

    boolean delete(String staffRewardsPunishmentsGuid);

    boolean deleteByBatch(List<String> staffRewardsPunishmentsGuids);

    boolean update(ErpAdministrationMgtStaffRewardsPunishmentsRO ro);

    boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffRewardsPunishmentsRO> dataList);

    ErpAdministrationMgtStaffRewardsPunishmentsVO getDataById(String staffRewardsPunishmentsGuid);

    List<ErpAdministrationMgtStaffRewardsPunishmentsVO> findList(ErpAdministrationMgtStaffRewardsPunishmentsQO qo);

    IPage<ErpAdministrationMgtStaffRewardsPunishmentsVO> findPage(PageParams<ErpAdministrationMgtStaffRewardsPunishmentsQO> pageParams);
}
