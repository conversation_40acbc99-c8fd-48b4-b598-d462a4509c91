package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.enums.FormSourceEnum;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.config.util.PageParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.service.IProcessInstanceService;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.administration.entity.ErpAdministrationMgtStaffCheckIn;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffCheckInQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffCheckInRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffCheckInVO;
import xy.server.administration.mapper.ErpAdministrationMgtStaffCheckInMapper;
import xy.server.administration.service.IErpAdministrationMgtStaffCheckInService;

import java.util.List;


/**
 * <p>
 * 入住记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */

@Service("STAFF_RECORDS.CHECK_IN_RECORDS")
public class ErpAdministrationMgtStaffCheckInServiceImpl extends ServiceImpl<ErpAdministrationMgtStaffCheckInMapper, ErpAdministrationMgtStaffCheckIn> implements IErpAdministrationMgtStaffCheckInService, ActEventStrategyService {

    @Autowired
    private IErpSystemMgtOrderSerialNumberService erpSystemMgtOrderSerialNumberService;
    @Autowired
    private IProcessInstanceService iProcessInstanceService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtStaffCheckInRO ro) {
        ErpAdministrationMgtStaffCheckIn entity = new ErpAdministrationMgtStaffCheckIn();
        ro.setStaffCheckInNumber(erpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.CHECK_IN_RECORDS));
        BeanUtil.copyProperties(ro, entity);
        if (super.save(entity)) {
            iProcessInstanceService.start(WorkflowKeyEnum.CHECK_IN_RECORDS, entity.getStaffCheckInGuid());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String staffCheckInGuid) {
        super.removeById(staffCheckInGuid);
        iProcessInstanceService.deleteProcessAndHisInst(staffCheckInGuid, WorkflowKeyEnum.CHECK_IN_RECORDS);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> staffCheckInGuids) {
        for (String staffCheckInGuid : staffCheckInGuids) {
            super.removeById(staffCheckInGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtStaffCheckInRO ro) {
        ErpAdministrationMgtStaffCheckIn entity = new ErpAdministrationMgtStaffCheckIn();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffCheckInRO> dataList) {
        if (CollectionUtils.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtils.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public ErpAdministrationMgtStaffCheckInVO getDataById(String staffCheckInGuid) {
        return baseMapper.getDataByGuid(staffCheckInGuid);
    }

    @Override
    public List<ErpAdministrationMgtStaffCheckInVO> findList(ErpAdministrationMgtStaffCheckInQO qo) {
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpAdministrationMgtStaffCheckInVO> findPage(PageParams<ErpAdministrationMgtStaffCheckInQO> pageParams) {
        IPage<ErpAdministrationMgtStaffCheckInVO> page = pageParams.buildPage();
        ErpAdministrationMgtStaffCheckInQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }
}
