package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpMessageUserBind;
import xy.server.administration.entity.model.qo.ErpMessageUserBindQO;
import xy.server.administration.entity.model.ro.ErpMessageUserBindRO;
import xy.server.administration.entity.model.vo.ErpMessageUserBindVO;

import java.util.List;

/**
 * <AUTHOR>
 * @apiNote 服务类
 * @since 2024-03-14
 */
public interface IErpMessageUserBindService extends IService<ErpMessageUserBind> {
    boolean create(ErpMessageUserBindRO ro);

    boolean delete(String messageUserBindGuid);

    boolean deleteByBatch(List<String> messageUserBindGuids);

    boolean update(ErpMessageUserBindRO ro);

    boolean saveDate(InsertOrUpdateList<ErpMessageUserBindRO> dataList);

    ErpMessageUserBindVO getDataById(String messageUserBindGuid);

    List<ErpMessageUserBindVO> findList(ErpMessageUserBindQO qo);

    IPage<ErpMessageUserBindVO> findPage(PageParams<ErpMessageUserBindQO> pageParams);
}
