package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.enums.FormSourceEnum;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.config.util.PageParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.service.IProcessInstanceService;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.administration.entity.ErpAdministrationMgtStaffMeal;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffMealQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffMealRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffMealVO;
import xy.server.administration.mapper.ErpAdministrationMgtStaffMealMapper;
import xy.server.administration.service.IErpAdministrationMgtStaffMealService;

import java.util.List;

/**
 * <p>
 * 报餐记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Service("STAFF_RECORDS.MEAL_REPORTING_RECORDS")
public class ErpAdministrationMgtStaffMealServiceImpl extends ServiceImpl<ErpAdministrationMgtStaffMealMapper, ErpAdministrationMgtStaffMeal> implements IErpAdministrationMgtStaffMealService, ActEventStrategyService {

    @Autowired
    private IErpSystemMgtOrderSerialNumberService erpSystemMgtOrderSerialNumberService;
    @Autowired
    private IProcessInstanceService iProcessInstanceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtStaffMealRO ro) {
        ErpAdministrationMgtStaffMeal entity = new ErpAdministrationMgtStaffMeal();
        ro.setStaffMealNumber(erpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.MEAL_REPORTING_RECORDS));
        BeanUtil.copyProperties(ro, entity);
        if (super.save(entity)) {
            iProcessInstanceService.start(WorkflowKeyEnum.MEAL_REPORTING_RECORDS, entity.getStaffMealGuid());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String staffMealGuid) {
        super.removeById(staffMealGuid);
        iProcessInstanceService.deleteProcessAndHisInst(staffMealGuid, WorkflowKeyEnum.MEAL_REPORTING_RECORDS);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> staffMealGuids) {
        for (String staffMealGuid : staffMealGuids) {
            super.removeById(staffMealGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtStaffMealRO ro) {
        ErpAdministrationMgtStaffMeal entity = new ErpAdministrationMgtStaffMeal();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffMealRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public ErpAdministrationMgtStaffMealVO getDataById(String staffMealGuid) {
        return baseMapper.getDataByGuid(staffMealGuid);
    }

    @Override
    public List<ErpAdministrationMgtStaffMealVO> findList(ErpAdministrationMgtStaffMealQO qo) {
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpAdministrationMgtStaffMealVO> findPage(PageParams<ErpAdministrationMgtStaffMealQO> pageParams) {
        IPage<ErpAdministrationMgtStaffMealVO> page = pageParams.buildPage();
        ErpAdministrationMgtStaffMealQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }
}
