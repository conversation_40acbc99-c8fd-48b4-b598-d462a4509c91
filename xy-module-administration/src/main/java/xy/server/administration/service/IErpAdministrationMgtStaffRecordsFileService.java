package xy.server.administration.service;

import com.baomidou.mybatisplus.extension.service.IService;
import xy.server.administration.entity.ErpAdministrationMgtStaffRecordsFile;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffRecordsFileRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffRecordsFileVO;

import java.util.List;

/**
 * <AUTHOR>
 * @apiNote 任务文件表 服务类
 * @since 2024-04-02
 */
public interface IErpAdministrationMgtStaffRecordsFileService extends IService<ErpAdministrationMgtStaffRecordsFile> {
    boolean create(ErpAdministrationMgtStaffRecordsFileRO ro);

    boolean delete(String staffRecordsFileGuid);

    boolean deleteByBatch(List<String> staffRecordsFileGuids);

    boolean update(ErpAdministrationMgtStaffRecordsFileRO ro);

    ErpAdministrationMgtStaffRecordsFileVO getDataById(String staffRecordsFileGuid);
}
