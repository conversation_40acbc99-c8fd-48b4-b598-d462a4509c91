package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.enums.FormSourceEnum;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.config.util.PageParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.service.IProcessInstanceService;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.administration.entity.ErpAdministrationMgtStaffTraining;
import xy.server.administration.entity.ErpAdministrationMgtTrainingStaff;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffTrainingQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffTrainingRO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtTrainingStaffRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffTrainingVO;
import xy.server.administration.mapper.ErpAdministrationMgtStaffTrainingMapper;
import xy.server.administration.service.IErpAdministrationMgtStaffTrainingService;
import xy.server.administration.service.IErpAdministrationMgtTrainingStaffService;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 培训记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Service("STAFF_RECORDS.TRAINING_RECORDS")
public class ErpAdministrationMgtStaffTrainingServiceImpl extends ServiceImpl<ErpAdministrationMgtStaffTrainingMapper, ErpAdministrationMgtStaffTraining> implements IErpAdministrationMgtStaffTrainingService, ActEventStrategyService {

    @Autowired
    private IErpSystemMgtOrderSerialNumberService erpSystemMgtOrderSerialNumberService;
    @Autowired
    private IProcessInstanceService iProcessInstanceService;
    @Autowired
    private IErpAdministrationMgtTrainingStaffService trainingStaffService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtStaffTrainingRO ro) {
        ErpAdministrationMgtStaffTraining entity = new ErpAdministrationMgtStaffTraining();
        ro.setTrainingNumber(erpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.TRAINING_RECORDS));
        BeanUtil.copyProperties(ro, entity);
        if (super.save(entity)) {
            //插入单据图片
            if (CollectionUtils.isNotEmpty(ro.getTrainingStaffROList())){
                List<ErpAdministrationMgtTrainingStaffRO> collect = ro.getTrainingStaffROList().stream().peek(s ->
                        s.setStaffTrainingGuid(entity.getStaffTrainingGuid())
                ).collect(Collectors.toList());
                trainingStaffService.saveBatch(BeanUtil.copyToList(collect, ErpAdministrationMgtTrainingStaff.class));
            }
            // 启动审核流程
            iProcessInstanceService.start(WorkflowKeyEnum.TRAINING_RECORDS, entity.getStaffTrainingGuid());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String staffTrainingGuid) {
        super.removeById(staffTrainingGuid);
        LambdaQueryWrapper<ErpAdministrationMgtTrainingStaff> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpAdministrationMgtTrainingStaff::getStaffTrainingGuid,staffTrainingGuid);
        trainingStaffService.remove(wrapper);
        iProcessInstanceService.deleteProcessAndHisInst(staffTrainingGuid, WorkflowKeyEnum.TRAINING_RECORDS);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> staffTrainingGuids) {
        for (String staffTrainingGuid : staffTrainingGuids) {
            super.removeById(staffTrainingGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtStaffTrainingRO ro) {
        ErpAdministrationMgtStaffTraining entity = new ErpAdministrationMgtStaffTraining();
        BeanUtil.copyProperties(ro, entity);
        if (super.updateById(entity)) {
            LambdaQueryWrapper<ErpAdministrationMgtTrainingStaff> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ErpAdministrationMgtTrainingStaff::getStaffTrainingGuid,ro.getStaffTrainingGuid());
            trainingStaffService.remove(wrapper);
            //插入单据图片
            if (CollectionUtils.isNotEmpty(ro.getTrainingStaffROList())){
                List<ErpAdministrationMgtTrainingStaffRO> collect = ro.getTrainingStaffROList().stream().peek(s -> {
                            s.setStaffTrainingGuid(entity.getStaffTrainingGuid());
                            s.setTrainingStaffGuid("");
                        }
                ).collect(Collectors.toList());
                trainingStaffService.saveBatch(BeanUtil.copyToList(collect, ErpAdministrationMgtTrainingStaff.class));
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffTrainingRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public ErpAdministrationMgtStaffTrainingVO getDataById(String staffTrainingGuid) {
        return baseMapper.getDataByGuid(staffTrainingGuid);
    }

    @Override
    public List<ErpAdministrationMgtStaffTrainingVO> findList(ErpAdministrationMgtStaffTrainingQO qo) {
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpAdministrationMgtStaffTrainingVO> findPage(PageParams<ErpAdministrationMgtStaffTrainingQO> pageParams) {
        IPage<ErpAdministrationMgtStaffTrainingVO> page = pageParams.buildPage();
        ErpAdministrationMgtStaffTrainingQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }
}
