package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.enums.FormSourceEnum;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.config.util.PageParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.service.IProcessInstanceService;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.administration.entity.ErpAdministrationMgtStaffGeneralLeave;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffGeneralLeaveQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffGeneralLeaveRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffGeneralLeaveVO;
import xy.server.administration.mapper.ErpAdministrationMgtStaffGeneralLeaveMapper;
import xy.server.administration.service.IErpAdministrationMgtStaffGeneralLeaveService;

import java.util.List;

/**
 * <p>
 * 员工请假记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Service("STAFF_RECORDS.GENERAL_LEAVE")
public class ErpAdministrationMgtStaffGeneralLeaveServiceImpl extends ServiceImpl<ErpAdministrationMgtStaffGeneralLeaveMapper, ErpAdministrationMgtStaffGeneralLeave> implements IErpAdministrationMgtStaffGeneralLeaveService, ActEventStrategyService {

    @Autowired
    private IErpSystemMgtOrderSerialNumberService erpSystemMgtOrderSerialNumberService;
    @Autowired
    private IProcessInstanceService iProcessInstanceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtStaffGeneralLeaveRO ro) {
        ErpAdministrationMgtStaffGeneralLeave entity = new ErpAdministrationMgtStaffGeneralLeave();
        ro.setGeneralLeaveNumber(erpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.GENERAL_LEAVE));
        BeanUtil.copyProperties(ro, entity);
        if (super.save(entity)) {
            iProcessInstanceService.start(WorkflowKeyEnum.GENERAL_LEAVE, entity.getStaffGeneralLeaveGuid());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String staffGeneralLeaveGuid) {
        super.removeById(staffGeneralLeaveGuid);
        iProcessInstanceService.deleteProcessAndHisInst(staffGeneralLeaveGuid, WorkflowKeyEnum.GENERAL_LEAVE);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> staffGeneralLeaveGuids) {
        for (String staffGeneralLeaveGuid : staffGeneralLeaveGuids) {
            super.removeById(staffGeneralLeaveGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtStaffGeneralLeaveRO ro) {
        ErpAdministrationMgtStaffGeneralLeave entity = new ErpAdministrationMgtStaffGeneralLeave();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffGeneralLeaveRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public ErpAdministrationMgtStaffGeneralLeaveVO getDataById(String staffGeneralLeaveGuid) {
        return baseMapper.getDataByGuid(staffGeneralLeaveGuid);
    }

    @Override
    public List<ErpAdministrationMgtStaffGeneralLeaveVO> findList(ErpAdministrationMgtStaffGeneralLeaveQO qo) {
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpAdministrationMgtStaffGeneralLeaveVO> findPage(PageParams<ErpAdministrationMgtStaffGeneralLeaveQO> pageParams) {
        IPage<ErpAdministrationMgtStaffGeneralLeaveVO> page = pageParams.buildPage();
        ErpAdministrationMgtStaffGeneralLeaveQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }
}
