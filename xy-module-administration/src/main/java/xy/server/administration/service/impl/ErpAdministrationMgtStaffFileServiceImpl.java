package xy.server.administration.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import xy.server.administration.entity.ErpAdministrationMgtStaffFile;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffFileVO;
import xy.server.administration.mapper.ErpAdministrationMgtStaffFileMapper;
import xy.server.administration.service.IErpAdministrationMgtStaffFileService;

import java.util.List;

/**
 * <p>
 * 员工文件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
public class ErpAdministrationMgtStaffFileServiceImpl extends ServiceImpl<ErpAdministrationMgtStaffFileMapper, ErpAdministrationMgtStaffFile> implements IErpAdministrationMgtStaffFileService {

    /**
     * 根据员工id获取文件信息
     *
     * @param collect1
     * @return
     */
    @Override
    public List<ErpAdministrationMgtStaffFileVO> getFileList(List<String> collect1) {
        return baseMapper.getFileList(collect1);
    }
}
