package xy.server.administration.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.config.util.PageParams;
import org.springframework.stereotype.Service;
import xy.server.administration.entity.ErpAdministrationMgtStaffOnJobStatistics;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffOnJobStatisticsQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffOnJobStatisticsVO;
import xy.server.administration.mapper.ErpAdministrationMgtStaffOnJobStatisticsMapper;
import xy.server.administration.service.IErpAdministrationMgtStaffOnJobStatisticsService;

import java.util.List;

/**
 * <p>
 * 离职记录统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Service
public class ErpAdministrationMgtStaffOnJobStatisticsServiceImpl extends ServiceImpl<ErpAdministrationMgtStaffOnJobStatisticsMapper, ErpAdministrationMgtStaffOnJobStatistics> implements IErpAdministrationMgtStaffOnJobStatisticsService {

    @Override
    public List<ErpAdministrationMgtStaffOnJobStatisticsVO> findList(ErpAdministrationMgtStaffOnJobStatisticsQO qo){
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpAdministrationMgtStaffOnJobStatisticsVO> findPage(PageParams<ErpAdministrationMgtStaffOnJobStatisticsQO> pageParams){
        IPage<ErpAdministrationMgtStaffOnJobStatisticsVO> page = pageParams.buildPage();
        ErpAdministrationMgtStaffOnJobStatisticsQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }
}
