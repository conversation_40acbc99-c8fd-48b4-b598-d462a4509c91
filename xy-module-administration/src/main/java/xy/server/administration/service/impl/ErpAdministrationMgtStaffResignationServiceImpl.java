package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.enums.FormSourceEnum;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.config.util.PageParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.service.IProcessInstanceService;
import xy.server.activiti.utils.actEvent.ActEventEntity;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.administration.entity.ErpAdministrationMgtStaffOnJobStatistics;
import xy.server.administration.entity.ErpAdministrationMgtStaffResignation;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffResignationQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffResignationRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffOnJobStatisticsVO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffResignationVO;
import xy.server.administration.mapper.ErpAdministrationMgtStaffMapper;
import xy.server.administration.mapper.ErpAdministrationMgtStaffResignationMapper;
import xy.server.administration.service.IErpAdministrationMgtStaffOnJobStatisticsService;
import xy.server.administration.service.IErpAdministrationMgtStaffResignationService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 员工离职扩展表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Service("STAFF_RECORDS.RESIGNATION_RECORDS")
public class ErpAdministrationMgtStaffResignationServiceImpl extends ServiceImpl<ErpAdministrationMgtStaffResignationMapper, ErpAdministrationMgtStaffResignation> implements IErpAdministrationMgtStaffResignationService, ActEventStrategyService {
    @Autowired
    private IErpSystemMgtOrderSerialNumberService erpSystemMgtOrderSerialNumberService;
    @Autowired
    private IProcessInstanceService iProcessInstanceService;
    @Resource(name ="erpAdministrationMgtStaffMapper" )
    private ErpAdministrationMgtStaffMapper staffMapper;
    @Autowired
    private IErpAdministrationMgtStaffOnJobStatisticsService staffOnJobStatisticsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtStaffResignationRO ro) {
        ErpAdministrationMgtStaffResignation entity = new ErpAdministrationMgtStaffResignation();
        ro.setResignationNumber(erpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.RESIGNATION_RECORDS));
        BeanUtil.copyProperties(ro, entity);
        if (super.save(entity)) {
            updateResignation(entity.getDateOfResignation());
            iProcessInstanceService.start(WorkflowKeyEnum.RESIGNATION_RECORDS, entity.getStaffResignationGuid());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String staffResignationGuid) {
        ErpAdministrationMgtStaffResignation one = lambdaQuery().eq(ErpAdministrationMgtStaffResignation::getStaffResignationGuid, staffResignationGuid).one();
        super.removeById(staffResignationGuid);
        iProcessInstanceService.deleteProcessAndHisInst(staffResignationGuid, WorkflowKeyEnum.RESIGNATION_RECORDS);
        updateResignation(one.getDateOfResignation());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> staffResignationGuids) {
        for (String staffResignationGuid : staffResignationGuids) {
            super.removeById(staffResignationGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtStaffResignationRO ro) {
        ErpAdministrationMgtStaffResignation one = lambdaQuery().eq(ErpAdministrationMgtStaffResignation::getStaffResignationGuid, ro.getStaffResignationGuid()).one();
        ErpAdministrationMgtStaffResignation entity = new ErpAdministrationMgtStaffResignation();
        BeanUtil.copyProperties(ro, entity);
        super.updateById(entity);
        updateResignation(entity.getDateOfResignation());
        updateResignation(one.getDateOfResignation());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffResignationRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    private void updateResignation(LocalDateTime today) {
        //获取当月的在职人数以及离职人数
        ErpAdministrationMgtStaffOnJobStatisticsVO vo = staffMapper.getOnJob(today.toLocalDate());
        LambdaQueryWrapper<ErpAdministrationMgtStaffOnJobStatistics> wrapper = new LambdaQueryWrapper<>();
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        // 格式化当前日期
        String yearMonth = today.format(formatter);
        LocalDateTime dateTime = LocalDateTime.parse(yearMonth + "-30T00:00:00");
        wrapper.eq(ErpAdministrationMgtStaffOnJobStatistics::getThisDate, dateTime);
        if (Objects.nonNull(vo) && vo.getIsNotOnJob() > 0) {
            ErpAdministrationMgtStaffOnJobStatistics one = staffOnJobStatisticsService.getOne(wrapper);
            ErpAdministrationMgtStaffOnJobStatistics statistics = BeanUtil.copyProperties(vo, ErpAdministrationMgtStaffOnJobStatistics.class);
            if (Objects.isNull(one)) {
                statistics.setThisDate(dateTime);
                statistics.setResignationRate(new BigDecimal(vo.getIsNotOnJob()).divide(new BigDecimal(vo.getIsNotOnJob() + vo.getIsOnJob()), 3, BigDecimal.ROUND_HALF_UP));
                staffOnJobStatisticsService.save(statistics);
            } else {
                one.setThisDate(dateTime);
                one.setIsNotOnJob(vo.getIsNotOnJob());
                one.setIsOnJob(vo.getIsOnJob());
                statistics.setResignationRate(new BigDecimal(vo.getIsNotOnJob()).divide(new BigDecimal(vo.getIsNotOnJob() + vo.getIsOnJob()), 3, BigDecimal.ROUND_HALF_UP));
                staffOnJobStatisticsService.updateById(one);
            }
        } else {
            staffOnJobStatisticsService.remove(wrapper);
        }
    }

    @Override
    public ErpAdministrationMgtStaffResignationVO getDataById(String staffResignationGuid) {
        return baseMapper.getDataByGuid(staffResignationGuid);
    }

    @Override
    public List<ErpAdministrationMgtStaffResignationVO> findList(ErpAdministrationMgtStaffResignationQO qo) {
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpAdministrationMgtStaffResignationVO> findPage(PageParams<ErpAdministrationMgtStaffResignationQO> pageParams) {
        IPage<ErpAdministrationMgtStaffResignationVO> page = pageParams.buildPage();
        ErpAdministrationMgtStaffResignationQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }

    /**
     * 已完成
     *
     * @param processVO
     */
    @Override
    public void finishCall(ActEventEntity processVO) {
        ErpAdministrationMgtStaffResignationVO data = baseMapper.getDataByGuid(processVO.getBusinessKey());
        //更新员工离职状态
        baseMapper.updateStaffResignationStatus(data.getStaffGuid(), data.getDateOfResignation(), false);
    }

    /**
     * 重启
     *
     * @param processVO
     */
    @Override
    public void restartCall(ActEventEntity processVO) {
        ErpAdministrationMgtStaffResignationVO data = baseMapper.getDataByGuid(processVO.getBusinessKey());
        //更新员工离职状态
        baseMapper.updateStaffResignationStatus(data.getStaffGuid(), null, true);
    }
}
