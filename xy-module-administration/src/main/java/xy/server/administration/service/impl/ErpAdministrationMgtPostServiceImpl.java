package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.common.i18n.BaseResultErrorCodeImpl;
import com.xunyue.common.util.UsedUtils;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import com.xunyue.node.common.enums.TypeEnum;
import com.xunyue.node.entity.NodeType;
import com.xunyue.node.service.INodeTypeService;
import com.xunyue.usercenter.entity.model.dto.GroupDTO;
import com.xunyue.usercenter.service.IGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import xy.server.administration.entity.ErpAdministrationMgtDepartment;
import xy.server.administration.entity.ErpAdministrationMgtPost;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtPostQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtPostRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtPostVO;
import xy.server.administration.i18n.ResultErrorCode;
import xy.server.administration.mapper.ErpAdministrationMgtPostMapper;
import xy.server.administration.service.IErpAdministrationMgtDepartmentService;
import xy.server.administration.service.IErpAdministrationMgtPostService;

import java.util.List;

/**
 * <p>
 * 岗位管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Service
public class ErpAdministrationMgtPostServiceImpl extends ServiceImpl<ErpAdministrationMgtPostMapper, ErpAdministrationMgtPost> implements IErpAdministrationMgtPostService {

    @Autowired
    UsedUtils usedUtils;
    @Autowired
    IErpAdministrationMgtDepartmentService iErpAdministrationMgtDepartmentService;
    @Autowired
    private IGroupService groupService;
    @Autowired
    private INodeTypeService nodeTypeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtPostRO ro){
       this.saveOrUpdateVerify(ro);
        ErpAdministrationMgtPost entity = new ErpAdministrationMgtPost();
        BeanUtil.copyProperties(ro, entity);
        usedUtils.isUsed("erp_administration_mgt_department",ro.getDepartmentGuid(),"","department_guid");
        super.save(entity);
        // 双写新增
        GroupDTO groupDTO = this.buildGroupDTO(entity);
        if (ObjUtil.isNotNull(groupDTO)) {
            groupService.save(groupDTO);
            // 新增保存node-type关联表
            nodeTypeService.save(new NodeType(entity.getPostGuid(), TypeEnum.POST.getCode()));
        }
        return true;
    }

    /**
     * 构建GroupDTO
     *
     * @param entity 1.0 部门实体
     * @return GroupDTO
     */
    private GroupDTO buildGroupDTO(ErpAdministrationMgtPost entity) {
        if (ObjUtil.isNull(entity)) {
            return null;
        }
        GroupDTO groupDTO = new GroupDTO();
        groupDTO.setGroupId(entity.getPostGuid());
        groupDTO.setGroupIsolation(entity.getTenantGuid());
        groupDTO.setGroupTenantId(entity.getTenantGuid());
        groupDTO.setGroupDefaultType(TypeEnum.POST.getCode());
        groupDTO.setGroupName(entity.getPostName());
        groupDTO.setGroupParentId(entity.getDepartmentGuid());
        groupDTO.setGroupDescription(entity.getDescription());
        groupDTO.setGroupSerialNumber(entity.getSerialNumber());
        groupDTO.setGroupIsBanned(entity.getState());
        groupDTO.setGroupCreatorId(entity.getCreatorGuid());
        groupDTO.setGroupCreator(entity.getCreator());
        groupDTO.setGroupCreateTime(DateUtil.date(entity.getCreateDate()));
        groupDTO.setGroupUpdaterId(entity.getLastUpdaterGuid());
        groupDTO.setGroupUpdater(entity.getLastUpdater());
        groupDTO.setGroupUpdateTime(DateUtil.date(entity.getLastUpdateDate()));
        return groupDTO;
    }

    /**
     * 新增和修改时字段校验
     * @param ro
     */
    public void saveOrUpdateVerify(ErpAdministrationMgtPostRO ro) {
        // 判断名称是否重复
        LambdaQueryWrapper<ErpAdministrationMgtPost> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ErpAdministrationMgtPost::getTenantGuid, ro.getTenantGuid())
                .eq(ErpAdministrationMgtPost::getPostName, ro.getPostName())
                .eq(ErpAdministrationMgtPost::getDeleted, false)
                .ne(ro.getPostGuid() != null, ErpAdministrationMgtPost::getPostGuid, ro.getPostGuid());
        if (baseMapper.exists(queryWrapper)) {
            throw new FlowException(ResultErrorCode.NO_REPETITION_ADMINISTRATION_GANWEI);
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String postGuid){
        ErpAdministrationMgtPost administrationMgtPost = baseMapper.selectById(postGuid);
        if (administrationMgtPost.getIsUsed()){
            throw new FlowException(ResultErrorCode.NO_REPETITION_ADMINISTRATION_DEPARTMENTD,postGuid);
        }
        super.removeById(postGuid);
        // 双写批量删除
        return groupService.removeById(postGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtPostRO ro){
        if (ro.getIsUsed()){
            throw new FlowException(ResultErrorCode.NO_REPETITION_ADMINISTRATION_DEPARTMENTD,ro.getPostGuid());
        }
        this.saveOrUpdateVerify(ro);
        ErpAdministrationMgtPost entity = new ErpAdministrationMgtPost();
        BeanUtil.copyProperties(ro, entity);
        super.updateById(entity);
        // 双写更新
        GroupDTO groupDTO = this.buildGroupDTO(entity);
        if (ObjUtil.isNotNull(groupDTO)) {
            groupService.update(groupDTO);
        }
        return true;
    }

    @Override
    public ErpAdministrationMgtPostVO getById(String postGuid){

        return baseMapper.getDataByGuid(postGuid);
    }

    @Override
    public List<ErpAdministrationMgtPostVO> findList(ErpAdministrationMgtPostQO qo, String tenantGuid){
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpAdministrationMgtPostVO> findPage(PageParams<ErpAdministrationMgtPostQO> pageParams){
        IPage<ErpAdministrationMgtPostVO> page = pageParams.buildPage();
        ErpAdministrationMgtPostQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }

    @Override
    public Boolean saveData(InsertOrUpdateList<ErpAdministrationMgtPostRO> saveData, String tenantGuid) {
        if (CollUtil.isNotEmpty(saveData.getInsertList())) {
            saveData.getInsertList().forEach(x -> {
                x.setTenantGuid(tenantGuid);
                this.create(x);
            });
        }

        if (CollUtil.isNotEmpty(saveData.getUpdateList())) {
            saveData.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public Boolean reordered(List<SortEntityDto> entityDtoList) {
        entityDtoList.forEach(sortEntityDto -> {
            LambdaUpdateWrapper<ErpAdministrationMgtPost> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ErpAdministrationMgtPost::getSerialNumber, sortEntityDto.getSerialNumber());
            updateWrapper.eq(ErpAdministrationMgtPost::getPostGuid, sortEntityDto.getGuid());
            update(updateWrapper);
        });
        return true;
    }

    /**
     * 处理导入Excel数据
     * @param ro
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcelDataHandle(ErpAdministrationMgtPostRO ro) {
        // 处理部门
        if (!StringUtils.isEmpty(ro.getDepartmentGuid())) {
            // 根据名称获取guid
            ErpAdministrationMgtDepartment department = iErpAdministrationMgtDepartmentService.getOne(Wrappers
                    .<ErpAdministrationMgtDepartment>lambdaQuery()
                    .select(ErpAdministrationMgtDepartment::getDepartmentGuid)
                    .eq(ErpAdministrationMgtDepartment::getDepartmentName, ro.getDepartmentGuid()));
            if (department == null || StringUtils.isEmpty(department.getDepartmentGuid())) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "部门【" + ro.getDepartmentGuid() + "】不存在！");
            }
            ro.setDepartmentGuid(department.getDepartmentGuid());
        }
        // 保存数据到数据库
        this.create(ro);
    }
}
