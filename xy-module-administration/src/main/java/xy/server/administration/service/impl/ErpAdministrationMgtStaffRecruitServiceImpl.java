package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.administration.entity.ErpAdministrationMgtStaffRecruit;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffRecruitQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffRecruitRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffRecruitVO;
import xy.server.administration.mapper.ErpAdministrationMgtStaffRecruitMapper;
import xy.server.administration.service.IErpAdministrationMgtStaffRecruitService;

import java.util.List;

/**
 * <p>
 * 招聘申请 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
public class ErpAdministrationMgtStaffRecruitServiceImpl extends ServiceImpl<ErpAdministrationMgtStaffRecruitMapper, ErpAdministrationMgtStaffRecruit> implements IErpAdministrationMgtStaffRecruitService {
                                                                                    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtStaffRecruitRO ro){
        ErpAdministrationMgtStaffRecruit entity = new ErpAdministrationMgtStaffRecruit();
        BeanUtil.copyProperties(ro, entity);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String staffRecruitGuid){
        return super.removeById(staffRecruitGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> staffRecruitGuids) {
        for (String staffRecruitGuid : staffRecruitGuids) {
            super.removeById(staffRecruitGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtStaffRecruitRO ro){
        ErpAdministrationMgtStaffRecruit entity = new ErpAdministrationMgtStaffRecruit();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffRecruitRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public ErpAdministrationMgtStaffRecruitVO getDataById(String staffRecruitGuid){
        return baseMapper.getDataByGuid(staffRecruitGuid);
    }

    @Override
    public List<ErpAdministrationMgtStaffRecruitVO> findList(ErpAdministrationMgtStaffRecruitQO qo){
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpAdministrationMgtStaffRecruitVO> findPage(PageParams<ErpAdministrationMgtStaffRecruitQO> pageParams){
        IPage<ErpAdministrationMgtStaffRecruitVO> page = pageParams.buildPage();
        ErpAdministrationMgtStaffRecruitQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }
}
