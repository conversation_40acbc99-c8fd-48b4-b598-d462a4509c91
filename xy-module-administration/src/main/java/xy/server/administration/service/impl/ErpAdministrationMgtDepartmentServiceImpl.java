package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.common.i18n.BaseResultErrorCodeImpl;
import com.xunyue.common.util.UsedUtils;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import com.xunyue.node.common.enums.TypeEnum;
import com.xunyue.node.entity.NodeType;
import com.xunyue.node.service.INodeTypeService;
import com.xunyue.usercenter.entity.model.dto.GroupDTO;
import com.xunyue.usercenter.service.IGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import xy.server.administration.entity.ErpAdministrationMgtDepartment;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtDepartmentQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtDepartmentRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtDepartmentVO;
import xy.server.administration.entity.model.vo.ErpDepartmentMgtStaffVO;
import xy.server.administration.i18n.ResultErrorCode;
import xy.server.administration.mapper.ErpAdministrationMgtDepartmentMapper;
import xy.server.administration.service.IErpAdministrationMgtDepartmentService;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 部门表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Service
public class ErpAdministrationMgtDepartmentServiceImpl extends ServiceImpl<ErpAdministrationMgtDepartmentMapper, ErpAdministrationMgtDepartment> implements IErpAdministrationMgtDepartmentService {
    @Autowired
    UsedUtils usedUtils;
    @Autowired
    private IGroupService groupService;
    @Autowired
    private INodeTypeService nodeTypeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtDepartmentRO ro) {
        this.saveOrUpdateVerify(ro);
        ErpAdministrationMgtDepartment entity = new ErpAdministrationMgtDepartment();
        BeanUtil.copyProperties(ro, entity);
        usedUtils.isUsed("erp_administration_mgt_department", ro.getParentClassificationGuid(), "", "department_guid");

        super.save(entity);
        // 双写新增
        GroupDTO groupDTO = this.buildGroupDTO(entity);
        if (ObjUtil.isNotNull(groupDTO)) {
            groupService.save(groupDTO);
            // 新增保存node-type关联表
            nodeTypeService.save(new NodeType(entity.getDepartmentGuid(), TypeEnum.DEPARTMENT.getCode()));
        }
        return true;
    }

    /**
     * 新增和修改时字段校验
     *
     * @param ro
     */
    public void saveOrUpdateVerify(ErpAdministrationMgtDepartmentRO ro) {
        // 判断名称是否重复
        LambdaQueryWrapper<ErpAdministrationMgtDepartment> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ErpAdministrationMgtDepartment::getTenantGuid, ro.getTenantGuid())
                .eq(ErpAdministrationMgtDepartment::getDepartmentName, ro.getDepartmentName())
                .eq(ErpAdministrationMgtDepartment::getDeleted, false)
                .ne(ro.getDepartmentGuid() != null, ErpAdministrationMgtDepartment::getDepartmentGuid, ro.getDepartmentGuid());
        String parentGuid = ro.getParentClassificationGuid();
        if (StrUtil.isBlank(parentGuid)) {
            queryWrapper.and(wrapper -> wrapper.isNull(ErpAdministrationMgtDepartment::getParentClassificationGuid)
                    .or().eq(ErpAdministrationMgtDepartment::getParentClassificationGuid, ""));
        } else {
            queryWrapper.eq(ErpAdministrationMgtDepartment::getParentClassificationGuid, parentGuid);
        }
        if (baseMapper.exists(queryWrapper)) {
            throw new FlowException(StrUtil.isBlank(parentGuid) ?
                    ResultErrorCode.NO_REPETITION_ADMINISTRATION_DEPARTMENT :
                    ResultErrorCode.NO_REPETITION_ADMINISTRATION_DEPT);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String departmentGuid) {
        List<ErpAdministrationMgtDepartment> childrenList = baseMapper.getListByParentClassificationGuid(departmentGuid);
        if (CollUtil.isNotEmpty(childrenList)) {
            // 过滤掉【已被使用】的数据
            List<String> ids = childrenList.stream().filter(entity -> !entity.getIsUsed())
                    .map(vo -> vo.getDepartmentGuid())
                    .collect(Collectors.toList());
            if (ids.size() != childrenList.size()) {
                throw new FlowException(ResultErrorCode.NO_REPETITION_ADMINISTRATION_DEPARTMENTD.getMsg(), ResultErrorCode.NO_REPETITION_ADMINISTRATION_DEPARTMENTD.getCode());
            }
            super.removeByIds(ids);
            // 双写批量删除
            return groupService.removeBatch(ids);
        }
        return false;

//
//        ErpAdministrationMgtDepartment erpAdministrationMgtDepartment = baseMapper.selectOne(Wrappers.<ErpAdministrationMgtDepartment>lambdaQuery()
//                .select(ErpAdministrationMgtDepartment::getIsUsed)
//                .eq(ErpAdministrationMgtDepartment::getDepartmentGuid, departmentGuid));
//        if (erpAdministrationMgtDepartment.getIsUsed()) {
//            throw new FlowException(ResultErrorCode.NO_REPETITION_ADMINISTRATION_DEPARTMENTD.getMsg(), ResultErrorCode.NO_REPETITION_ADMINISTRATION_DEPARTMENTD.getCode());
//        }
//
//        boolean b = super.removeById(departmentGuid);
//
//        return b;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtDepartmentRO ro) {
        if (ro.getIsUsed()) {
            throw new FlowException(ResultErrorCode.NO_REPETITION_ADMINISTRATION_DEPARTMENTD, ro.getDepartmentGuid());
        }
        this.saveOrUpdateVerify(ro);
        ErpAdministrationMgtDepartment entity = new ErpAdministrationMgtDepartment();
        BeanUtil.copyProperties(ro, entity);
        super.updateById(entity);
        // 双写更新
        GroupDTO groupDTO = this.buildGroupDTO(entity);
        if (ObjUtil.isNotNull(groupDTO)) {
            groupService.update(groupDTO);
        }
        return true;
    }

    @Override
    public ErpAdministrationMgtDepartmentVO getById(String tenantGuid) {
        return baseMapper.getDataByGuid(tenantGuid);
    }

    @Override
    public List<ErpAdministrationMgtDepartmentVO> findList(ErpAdministrationMgtDepartmentQO qo, String tenantGuid) {
        List<ErpAdministrationMgtDepartmentVO> list = baseMapper.findList(qo);
        return list;
    }

    @Override
    public IPage<ErpAdministrationMgtDepartmentVO> findPage(PageParams<ErpAdministrationMgtDepartmentQO> pageParams) {
        IPage<ErpAdministrationMgtDepartmentVO> page = pageParams.buildPage();
        ErpAdministrationMgtDepartmentQO model = pageParams.getModel();
//        model.setTenantGuid(pageParams.getTenantGuid());
        IPage<ErpAdministrationMgtDepartmentVO> page1 = baseMapper.findPage(page, model);
        //处理父级求和
        page1.getRecords().forEach(x -> {
            x.setRNumber(x.getRNumber() + x.getChildren().stream().mapToInt(ErpAdministrationMgtDepartmentVO::getRNumber).sum());
            x.setPostNumber(x.getPostNumber() + x.getChildren().stream().mapToInt(ErpAdministrationMgtDepartmentVO::getPostNumber).sum());
        });
        return page1;
    }

    @Override
    public Boolean reordered(List<SortEntityDto> entityDtoList) {
        entityDtoList.forEach(sortEntityDto -> {
            LambdaUpdateWrapper<ErpAdministrationMgtDepartment> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ErpAdministrationMgtDepartment::getSerialNumber, sortEntityDto.getSerialNumber());
            updateWrapper.eq(ErpAdministrationMgtDepartment::getDepartmentGuid, sortEntityDto.getGuid());
            update(updateWrapper);
        });
        return true;
    }

    @Override
    public Boolean saveData(InsertOrUpdateList<ErpAdministrationMgtDepartmentRO> saveData, String tenantGuid) {
        if (CollUtil.isNotEmpty(saveData.getInsertList())) {
            saveData.getInsertList().forEach(x -> {
                x.setTenantGuid(tenantGuid);
                this.create(x);
            });
        }

        if (CollUtil.isNotEmpty(saveData.getUpdateList())) {
            saveData.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public Boolean upDeTeState(String departmentGuid) {
        ErpAdministrationMgtDepartment erpAdministrationMgtDepartment = baseMapper.selectById(departmentGuid);
        erpAdministrationMgtDepartment.setState(false);
        baseMapper.updateById(erpAdministrationMgtDepartment);
        // 双写更新
        GroupDTO groupDTO = this.buildGroupDTO(erpAdministrationMgtDepartment);
        if (ObjUtil.isNotNull(groupDTO)) {
            groupService.update(groupDTO);
        }
        return true;
    }

    @Override
    public Boolean removeBatchByIdss(List<String> ids) {
        ids.forEach(this::delete);
        return true;
    }

    /**
     * 根据部门id获取员工数据
     *
     * @param departmentGuid
     * @return
     */
    @Override
    public List<ErpDepartmentMgtStaffVO> getStaffList(String departmentGuid) {
        return baseMapper.getStaffList(departmentGuid);
    }

    /**
     * 处理导入Excel数据
     *
     * @param ro
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcelDataHandle(ErpAdministrationMgtDepartmentRO ro) {
        // 根据父级名称获取父级guid
        if (!StringUtils.isEmpty(ro.getParentClassificationGuid())) {
            // 根据名称获取guid
            ErpAdministrationMgtDepartment pEntity = baseMapper.selectOne(Wrappers.<ErpAdministrationMgtDepartment>lambdaQuery()
                    .select(ErpAdministrationMgtDepartment::getDepartmentGuid)
                    .eq(ErpAdministrationMgtDepartment::getDepartmentName, ro.getParentClassificationGuid()));
            if (pEntity == null || StringUtils.isEmpty(pEntity.getDepartmentGuid())) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "父级部门【" + ro.getParentClassificationGuid() + "】不存在！");
            }
            ro.setParentClassificationGuid(pEntity.getDepartmentGuid());
        }
        // 保存数据到数据库
        this.create(ro);
    }

    /**
     * 构建GroupDTO
     *
     * @param entity 1.0 部门实体
     * @return GroupDTO
     */
    private GroupDTO buildGroupDTO(ErpAdministrationMgtDepartment entity) {
        if (ObjUtil.isNull(entity)) {
            return null;
        }
        GroupDTO groupDTO = new GroupDTO();
        groupDTO.setGroupId(entity.getDepartmentGuid());
        groupDTO.setGroupIsolation(entity.getTenantGuid());
        groupDTO.setGroupTenantId(entity.getTenantGuid());
        groupDTO.setGroupDefaultType(TypeEnum.DEPARTMENT.getCode());
        groupDTO.setGroupName(entity.getDepartmentName());
        groupDTO.setGroupParentId(entity.getParentClassificationGuid().isEmpty() ? null : entity.getParentClassificationGuid());
        groupDTO.setGroupDescription(entity.getDescription());
        groupDTO.setGroupSerialNumber(entity.getSerialNumber());
        groupDTO.setGroupIsBanned(entity.getState());
        groupDTO.setGroupCreatorId(entity.getCreatorGuid());
        groupDTO.setGroupCreator(entity.getCreator());
        groupDTO.setGroupCreateTime(DateUtil.date(entity.getCreateDate()));
        groupDTO.setGroupUpdaterId(entity.getLastUpdaterGuid());
        groupDTO.setGroupUpdater(entity.getLastUpdater());
        groupDTO.setGroupUpdateTime(DateUtil.date(entity.getLastUpdateDate()));
        return groupDTO;
    }

}
