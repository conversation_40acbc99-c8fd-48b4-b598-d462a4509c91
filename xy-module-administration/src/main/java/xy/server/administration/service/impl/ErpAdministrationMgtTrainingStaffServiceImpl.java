package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.administration.entity.ErpAdministrationMgtTrainingStaff;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtTrainingStaffQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtTrainingStaffRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtTrainingStaffVO;
import xy.server.administration.mapper.ErpAdministrationMgtTrainingStaffMapper;
import xy.server.administration.service.IErpAdministrationMgtTrainingStaffService;

import java.util.List;

/**
 * <p>
 * 培训员工扩展表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
public class ErpAdministrationMgtTrainingStaffServiceImpl extends ServiceImpl<ErpAdministrationMgtTrainingStaffMapper, ErpAdministrationMgtTrainingStaff> implements IErpAdministrationMgtTrainingStaffService {
                    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtTrainingStaffRO ro){
        ErpAdministrationMgtTrainingStaff entity = new ErpAdministrationMgtTrainingStaff();
        BeanUtil.copyProperties(ro, entity);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String trainingStaffGuid){
        return super.removeById(trainingStaffGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> trainingStaffGuids) {
        for (String trainingStaffGuid : trainingStaffGuids) {
            super.removeById(trainingStaffGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtTrainingStaffRO ro){
        ErpAdministrationMgtTrainingStaff entity = new ErpAdministrationMgtTrainingStaff();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtTrainingStaffRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public ErpAdministrationMgtTrainingStaffVO getDataById(String trainingStaffGuid){
        return baseMapper.getDataByGuid(trainingStaffGuid);
    }

    @Override
    public List<ErpAdministrationMgtTrainingStaffVO> findList(ErpAdministrationMgtTrainingStaffQO qo){
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpAdministrationMgtTrainingStaffVO> findPage(PageParams<ErpAdministrationMgtTrainingStaffQO> pageParams){
        IPage<ErpAdministrationMgtTrainingStaffVO> page = pageParams.buildPage();
        ErpAdministrationMgtTrainingStaffQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }
}
