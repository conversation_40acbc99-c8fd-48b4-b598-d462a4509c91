package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.config.excel.EasyExcelDataHandleService;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtTeamMembers;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtTeamMembersQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtTeamMembersRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtTeamMembersVO;

import java.util.List;

/**
 * <p>
 * 班组组员列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
public interface IErpAdministrationMgtTeamMembersService extends IService<ErpAdministrationMgtTeamMembers>, EasyExcelDataHandleService<ErpAdministrationMgtTeamMembersRO> {
    boolean create(ErpAdministrationMgtTeamMembersRO ro);

    boolean delete(String teamMembersGuid);

    boolean update(ErpAdministrationMgtTeamMembersRO ro);

    ErpAdministrationMgtTeamMembersVO getById(String teamMembersGuid);

    List<ErpAdministrationMgtTeamMembersVO> findList(ErpAdministrationMgtTeamMembersQO qo, String tenantGuid);

    IPage<ErpAdministrationMgtTeamMembersVO> findPage(PageParams<ErpAdministrationMgtTeamMembersQO> pageParams);

    Boolean reordered(List<SortEntityDto> entityDtoList);
}
