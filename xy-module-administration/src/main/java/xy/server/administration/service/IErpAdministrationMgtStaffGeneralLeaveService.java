package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtStaffGeneralLeave;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffGeneralLeaveQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffGeneralLeaveRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffGeneralLeaveVO;

import java.util.List;

/**
 * @apiNote 员工请假记录表 服务类
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface IErpAdministrationMgtStaffGeneralLeaveService extends IService<ErpAdministrationMgtStaffGeneralLeave> {
                                                            boolean create(ErpAdministrationMgtStaffGeneralLeaveRO ro);

    boolean delete(String staffGeneralLeaveGuid);

    boolean deleteByBatch(List<String> staffGeneralLeaveGuids);

    boolean update(ErpAdministrationMgtStaffGeneralLeaveRO ro);

    boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffGeneralLeaveRO> dataList);

    ErpAdministrationMgtStaffGeneralLeaveVO getDataById(String staffGeneralLeaveGuid);

    List<ErpAdministrationMgtStaffGeneralLeaveVO> findList(ErpAdministrationMgtStaffGeneralLeaveQO qo);

    IPage<ErpAdministrationMgtStaffGeneralLeaveVO> findPage(PageParams<ErpAdministrationMgtStaffGeneralLeaveQO> pageParams);
}
