package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtStaffCheckIn;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffCheckInQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffCheckInRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffCheckInVO;

import java.util.List;

/**
 * <AUTHOR>
 * @apiNote 入住记录表 服务类
 * @since 2024-10-22
 */
public interface IErpAdministrationMgtStaffCheckInService extends IService<ErpAdministrationMgtStaffCheckIn> {
    boolean create(ErpAdministrationMgtStaffCheckInRO ro);

    boolean delete(String staffCheckInGuid);

    boolean deleteByBatch(List<String> staffCheckInGuids);

    boolean update(ErpAdministrationMgtStaffCheckInRO ro);

    boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffCheckInRO> dataList);

    ErpAdministrationMgtStaffCheckInVO getDataById(String staffCheckInGuid);

    List<ErpAdministrationMgtStaffCheckInVO> findList(ErpAdministrationMgtStaffCheckInQO qo);

    IPage<ErpAdministrationMgtStaffCheckInVO> findPage(PageParams<ErpAdministrationMgtStaffCheckInQO> pageParams);
}
