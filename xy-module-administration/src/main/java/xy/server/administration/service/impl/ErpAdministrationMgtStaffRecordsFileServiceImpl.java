package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.administration.entity.ErpAdministrationMgtStaffRecordsFile;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffRecordsFileRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffRecordsFileVO;
import xy.server.administration.mapper.ErpAdministrationMgtStaffRecordsFileMapper;
import xy.server.administration.service.IErpAdministrationMgtStaffRecordsFileService;

import java.util.List;

/**
 * <p>
 * 任务文件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Service
public class ErpAdministrationMgtStaffRecordsFileServiceImpl extends ServiceImpl<ErpAdministrationMgtStaffRecordsFileMapper, ErpAdministrationMgtStaffRecordsFile> implements IErpAdministrationMgtStaffRecordsFileService {
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtStaffRecordsFileRO ro){
        ErpAdministrationMgtStaffRecordsFile entity = new ErpAdministrationMgtStaffRecordsFile();
        BeanUtil.copyProperties(ro, entity);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String staffRecordsFileGuid){
        return super.removeById(staffRecordsFileGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> staffRecordsFileGuids) {
        for (String staffRecordsFileGuid : staffRecordsFileGuids) {
            super.removeById(staffRecordsFileGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtStaffRecordsFileRO ro){
        ErpAdministrationMgtStaffRecordsFile entity = new ErpAdministrationMgtStaffRecordsFile();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    public ErpAdministrationMgtStaffRecordsFileVO getDataById(String staffRecordsFileGuid){
        return baseMapper.getDataByGuid(staffRecordsFileGuid);
    }
}
