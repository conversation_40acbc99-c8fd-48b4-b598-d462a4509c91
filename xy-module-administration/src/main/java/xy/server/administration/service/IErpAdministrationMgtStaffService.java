package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.config.excel.EasyExcelDataHandleService;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtStaff;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffQO;
import xy.server.administration.entity.model.qo.ErpAuditorQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffRO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffResignationRO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffUserRo;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtDepartmentAndStaffVO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStafSVO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffVO;
import xy.server.administration.entity.model.vo.ErpAuditorVo;
import xy.server.dto.XyMemberDto;

import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * <p>
 * 员工列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
public interface IErpAdministrationMgtStaffService extends IService<ErpAdministrationMgtStaff>, EasyExcelDataHandleService<ErpAdministrationMgtStaffRO> {
    boolean create(ErpAdministrationMgtStaffRO ro) throws ParseException;

    boolean delete(String staffGuid);

    boolean update(ErpAdministrationMgtStaffRO ro);

    ErpAdministrationMgtStaffVO getById(String staffGuid);

    List<ErpAdministrationMgtStaffVO> findList(ErpAdministrationMgtStaffQO qo, String tenantGuid);

    IPage<ErpAdministrationMgtStaffVO> findPage(PageParams<ErpAdministrationMgtStaffQO> pageParams);

    Boolean saveData(InsertOrUpdateList<ErpAdministrationMgtStaffRO> saveData, String tenantGuid);

    Boolean reordered(List<SortEntityDto> entityDtoList);

    Boolean removeBatchByIdss(List<String> ids);

    ErpAdministrationMgtStafSVO getStaff(String loginAccount);

    /**
     * 离职
     * @param ro
     * @return
     */
    Boolean resignation(ErpAdministrationMgtStaffResignationRO ro);

    Boolean updateuserGuid(List<ErpAdministrationMgtStaffUserRo> ro);

    List<ErpAdministrationMgtStaffVO> noUserAvailableList(ErpAdministrationMgtStaffQO qo, String tenantGuid);

    /**
     * 获取部门及部门下的员工
     * @param qo
     * @return
     */
    List<ErpAdministrationMgtDepartmentAndStaffVO> findStaffAndDepartmentList(ErpAdministrationMgtStaffQO qo);

    /**
     * 批量获取审核人列表
     * 入参数，请先把businessKey和procInstId用_拼接起来
     * @param businessKey_procInstId
     * @return
     */
    List<ErpAuditorVo> getAuditorList(List<String> businessKey_procInstId);

    /**
     * 获取审核人，返回Map
     * @param businessKey_procInstId
     * @return
     */
    Map<String, ErpAuditorVo> getAuditorMap(List<String> businessKey_procInstId);

    /**
     * 异步获取审核人列表
     *
     * @param businessKey_procInstId
     * @param xyMemberDto
     * @return
     */
    CompletableFuture<Map<String , ErpAuditorVo>> getAuditorMapFeature(List<String> businessKey_procInstId, XyMemberDto xyMemberDto);


    Map<String , ErpAuditorVo> mapAuditMan(List<String> procInstIds);

    /**
     * 异步获取审核人列表
     *
     * @param businessKeyProcInstId
     * @param xyMemberDto
     * @return
     */
    CompletableFuture<Map<String , ErpAuditorVo>> mapAuditorMapFeature(List<String> businessKeyProcInstId, XyMemberDto xyMemberDto);

    /**
     * 加载审核人
     *
     * @param detailVOList 主列表
     * @param pkGetter1    businessKey Getter
     * @param pkGetter2    procInstId Getter
     * @param setter       主列表 setter
     * @param <DetailVO>
     */
    <DetailVO> void loaAuditorTool(List<DetailVO> detailVOList,
                                   Function<DetailVO, String> pkGetter1,
                                   Function<DetailVO, String> pkGetter2,
                                   BiConsumer<DetailVO, String> setter);
    /**
     * 批量获取审核人列表
     * 入参数，请先把businessKey和procInstId用_拼接起来
     * @param businessKey_procInstId
     * @param xyMemberDto 线程变量对象
     * @return
     */
    CompletableFuture<List<ErpAuditorVo>> getAuditorListAsync(List<String> businessKey_procInstId, XyMemberDto xyMemberDto);

    /**
     * 批量获取审核人Map
     * @param businessKey_procInstId
     * @param xyMemberDto
     * @return
     */
    CompletableFuture<Map<String,ErpAuditorVo>> getAuditorMapAsync(List<String> businessKey_procInstId, XyMemberDto xyMemberDto);


    List<ErpAdministrationMgtStaffVO> selectMerchandiserOrSalesman(List<String> orderGuids);

    List<ErpAdministrationMgtStaffVO> listTeamStaffByTeamGuids(List<String> teamGuids);

    /**
     * 获取审核人信息
     * @param list
     * @return
     */
    Map<String, ErpAuditorVo> mapNewAuditMan(List<String> list);

    /**
     * 同步员工信息到二点零
     * @param entity
     */
    void sycStaffData(ErpAdministrationMgtStaff entity);

    /**
     * 根据审核人查询审核流数据
     * @param qo
     * @return
     */

    List<ErpAuditorVo> selectAuditorByKey(ErpAuditorQO qo);
}
