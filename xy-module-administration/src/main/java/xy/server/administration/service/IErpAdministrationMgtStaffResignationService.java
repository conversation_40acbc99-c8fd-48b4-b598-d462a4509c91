package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtStaffResignation;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffResignationQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffResignationRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffResignationVO;

import java.util.List;

/**
 * @apiNote 员工离职扩展表 服务类
 * <AUTHOR>
 * @since 2023-12-11
 */
public interface IErpAdministrationMgtStaffResignationService extends IService<ErpAdministrationMgtStaffResignation> {
                        boolean create(ErpAdministrationMgtStaffResignationRO ro);

    boolean delete(String staffResignationGuid);

    boolean deleteByBatch(List<String> staffResignationGuids);

    boolean update(ErpAdministrationMgtStaffResignationRO ro);

    boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffResignationRO> dataList);

    ErpAdministrationMgtStaffResignationVO getDataById(String staffResignationGuid);

    List<ErpAdministrationMgtStaffResignationVO> findList(ErpAdministrationMgtStaffResignationQO qo);

    IPage<ErpAdministrationMgtStaffResignationVO> findPage(PageParams<ErpAdministrationMgtStaffResignationQO> pageParams);
}
