package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtStaffInjury;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffInjuryQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffInjuryRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffInjuryVO;

import java.util.List;

/**
 * @apiNote 工伤记录表 服务类
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface IErpAdministrationMgtStaffInjuryService extends IService<ErpAdministrationMgtStaffInjury> {
                                                            boolean create(ErpAdministrationMgtStaffInjuryRO ro);

    boolean delete(String staffInjuryGuid);

    boolean deleteByBatch(List<String> staffInjuryGuids);

    boolean update(ErpAdministrationMgtStaffInjuryRO ro);

    boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffInjuryRO> dataList);

    ErpAdministrationMgtStaffInjuryVO getDataById(String staffInjuryGuid);

    List<ErpAdministrationMgtStaffInjuryVO> findList(ErpAdministrationMgtStaffInjuryQO qo);

    IPage<ErpAdministrationMgtStaffInjuryVO> findPage(PageParams<ErpAdministrationMgtStaffInjuryQO> pageParams);
}
