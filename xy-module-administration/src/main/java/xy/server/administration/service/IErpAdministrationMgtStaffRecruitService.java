package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtStaffRecruit;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffRecruitQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffRecruitRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffRecruitVO;

import java.util.List;

/**
 * @apiNote 招聘申请 服务类
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface IErpAdministrationMgtStaffRecruitService extends IService<ErpAdministrationMgtStaffRecruit> {
                                                                                    boolean create(ErpAdministrationMgtStaffRecruitRO ro);

    boolean delete(String staffRecruitGuid);

    boolean deleteByBatch(List<String> staffRecruitGuids);

    boolean update(ErpAdministrationMgtStaffRecruitRO ro);

    boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffRecruitRO> dataList);

    ErpAdministrationMgtStaffRecruitVO getDataById(String staffRecruitGuid);

    List<ErpAdministrationMgtStaffRecruitVO> findList(ErpAdministrationMgtStaffRecruitQO qo);

    IPage<ErpAdministrationMgtStaffRecruitVO> findPage(PageParams<ErpAdministrationMgtStaffRecruitQO> pageParams);
}
