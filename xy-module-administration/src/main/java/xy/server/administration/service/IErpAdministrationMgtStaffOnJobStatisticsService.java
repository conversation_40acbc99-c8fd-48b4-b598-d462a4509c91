package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtStaffOnJobStatistics;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffOnJobStatisticsQO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffOnJobStatisticsVO;

import java.util.List;

/**
 * @apiNote 离职记录统计表 服务类
 * <AUTHOR>
 * @since 2024-07-22
 */
public interface IErpAdministrationMgtStaffOnJobStatisticsService extends IService<ErpAdministrationMgtStaffOnJobStatistics> {
    List<ErpAdministrationMgtStaffOnJobStatisticsVO> findList(ErpAdministrationMgtStaffOnJobStatisticsQO qo);

    IPage<ErpAdministrationMgtStaffOnJobStatisticsVO> findPage(PageParams<ErpAdministrationMgtStaffOnJobStatisticsQO> pageParams);
}
