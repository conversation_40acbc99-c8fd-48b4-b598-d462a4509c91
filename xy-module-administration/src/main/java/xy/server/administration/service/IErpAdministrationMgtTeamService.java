package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.config.excel.EasyExcelDataHandleService;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtTeam;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtTeamQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtTeamRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtTeamVO;

import java.util.List;

/**
 * <p>
 * 班组管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
public interface IErpAdministrationMgtTeamService extends IService<ErpAdministrationMgtTeam>, EasyExcelDataHandleService<ErpAdministrationMgtTeamRO> {
    boolean create(ErpAdministrationMgtTeamRO ro);

    boolean delete(String teamGuid);

    boolean update(ErpAdministrationMgtTeamRO ro);

    ErpAdministrationMgtTeamVO getById(String teamGuid);

    List<ErpAdministrationMgtTeamVO> findList(ErpAdministrationMgtTeamQO qo, String tenantGuid);

    IPage<ErpAdministrationMgtTeamVO> findPage(PageParams<ErpAdministrationMgtTeamQO> pageParams);

    Boolean reordered(List<SortEntityDto> entityDtoList);

    Boolean saveData(InsertOrUpdateList<ErpAdministrationMgtTeamRO> saveData, String tenantGuid);

    Boolean deleteByBatch(List<String> ids);
}
