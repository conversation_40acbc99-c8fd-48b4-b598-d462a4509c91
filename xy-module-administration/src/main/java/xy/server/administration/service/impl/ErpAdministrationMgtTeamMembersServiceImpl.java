package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.common.i18n.BaseResultErrorCodeImpl;
import com.xunyue.common.util.StringUtils;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.administration.entity.ErpAdministrationMgtStaff;
import xy.server.administration.entity.ErpAdministrationMgtTeam;
import xy.server.administration.entity.ErpAdministrationMgtTeamMembers;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtTeamMembersQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtTeamMembersRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtTeamMembersVO;
import xy.server.administration.i18n.ResultErrorCode;
import xy.server.administration.mapper.ErpAdministrationMgtTeamMembersMapper;
import xy.server.administration.service.IErpAdministrationMgtStaffService;
import xy.server.administration.service.IErpAdministrationMgtTeamMembersService;
import xy.server.administration.service.IErpAdministrationMgtTeamService;

import java.util.List;

/**
 * <p>
 * 班组组员列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service
public class ErpAdministrationMgtTeamMembersServiceImpl extends ServiceImpl<ErpAdministrationMgtTeamMembersMapper, ErpAdministrationMgtTeamMembers> implements IErpAdministrationMgtTeamMembersService {

    private final IErpAdministrationMgtTeamService iErpAdministrationMgtTeamService;
    private final IErpAdministrationMgtStaffService iErpAdministrationMgtStaffService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtTeamMembersRO ro) {
        this.saveOrUpdateVerify(ro);
        ErpAdministrationMgtTeamMembers entity = new ErpAdministrationMgtTeamMembers();
        BeanUtil.copyProperties(ro, entity);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String teamMembersGuid) {
        return super.removeById(teamMembersGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtTeamMembersRO ro) {
        this.saveOrUpdateVerify(ro);
        ErpAdministrationMgtTeamMembers entity = new ErpAdministrationMgtTeamMembers();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    public ErpAdministrationMgtTeamMembersVO getById(String teamMembersGuid) {

        return baseMapper.getDataByGuid(teamMembersGuid);
    }

    @Override
    public List<ErpAdministrationMgtTeamMembersVO> findList(ErpAdministrationMgtTeamMembersQO qo, String tenantGuid) {
        qo.setTenantGuid(tenantGuid);
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpAdministrationMgtTeamMembersVO> findPage(PageParams<ErpAdministrationMgtTeamMembersQO> pageParams) {
        IPage<ErpAdministrationMgtTeamMembersVO> page = pageParams.buildPage();
        ErpAdministrationMgtTeamMembersQO model = pageParams.getModel();
        model.setTenantGuid(pageParams.getTenantGuid());
        return baseMapper.findPage(page, model);
    }

    @Override
    public Boolean reordered(List<SortEntityDto> entityDtoList) {
        entityDtoList.forEach(sortEntityDto -> {
            LambdaUpdateWrapper<ErpAdministrationMgtTeamMembers> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ErpAdministrationMgtTeamMembers::getSerialNumber, sortEntityDto.getSerialNumber());
            updateWrapper.eq(ErpAdministrationMgtTeamMembers::getTeamMembersGuid, sortEntityDto.getGuid());
            update(updateWrapper);
        });
        return true;
    }

    /**
     * 处理导入Excel数据
     * @param ro
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcelDataHandle(ErpAdministrationMgtTeamMembersRO ro) {
        // 处理班组
        if (StringUtils.isNotEmpty(ro.getTeamGuid())) {
            // 根据班组编码查询班组guid
            ErpAdministrationMgtTeam team = iErpAdministrationMgtTeamService.getOne(Wrappers.<ErpAdministrationMgtTeam>lambdaQuery()
                    .select(ErpAdministrationMgtTeam::getTeamGuid)
                    .eq(ErpAdministrationMgtTeam::getTeamCode, ro.getTeamGuid()));
            if (ObjectUtil.isNull(team) || StringUtils.isEmpty(team.getTeamGuid())) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "班组【" + ro.getTeamGuid() + "】不存在！");
            }
            ro.setTeamGuid(team.getTeamGuid());
        }
        // 处理员工和员工部门
        if (StringUtils.isNotEmpty(ro.getStaffGuid())) {
            ErpAdministrationMgtStaff staff = iErpAdministrationMgtStaffService.getOne(Wrappers.<ErpAdministrationMgtStaff>lambdaQuery()
                    .select(ErpAdministrationMgtStaff::getStaffGuid, ErpAdministrationMgtStaff::getDepartmentGuid)
                    .eq(ErpAdministrationMgtStaff::getStaffCode, ro.getStaffGuid()));
            if (ObjectUtil.isNull(staff)) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "员工【" + ro.getStaffGuid() + "】不存在！");
            }
            ro.setStaffGuid(staff.getStaffGuid());
            ro.setDepartmentGuid(staff.getDepartmentGuid());
        }
        // 保存数据到数据库
        this.create(ro);
    }

    /**
     * 新增和修改时字段校验
     * @param ro
     */
    public void saveOrUpdateVerify(ErpAdministrationMgtTeamMembersRO ro) {
        // 判断名称是否重复
        LambdaQueryWrapper<ErpAdministrationMgtTeamMembers> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ErpAdministrationMgtTeamMembers::getTeamGuid, ro.getTeamGuid())
                .eq(ErpAdministrationMgtTeamMembers::getStaffGuid, ro.getStaffGuid())
                .ne(ro.getTeamMembersGuid() != null, ErpAdministrationMgtTeamMembers::getTeamMembersGuid, ro.getTeamMembersGuid());
        if (baseMapper.exists(queryWrapper)) {
            throw new FlowException(ResultErrorCode.NO_REPETITION_ADMINISTRATION_CHENY);
        }
    }
}
