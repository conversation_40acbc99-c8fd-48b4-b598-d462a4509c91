package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.administration.entity.ErpMessageUserBind;
import xy.server.administration.entity.model.qo.ErpMessageUserBindQO;
import xy.server.administration.entity.model.ro.ErpMessageUserBindRO;
import xy.server.administration.entity.model.vo.ErpMessageUserBindVO;
import xy.server.administration.mapper.ErpMessageUserBindMapper;
import xy.server.administration.service.IErpMessageUserBindService;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-14
 */
@Service
public class ErpMessageUserBindServiceImpl extends ServiceImpl<ErpMessageUserBindMapper, ErpMessageUserBind> implements IErpMessageUserBindService {
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpMessageUserBindRO ro) {
        // 同一员工同一平台只能有一条数据
        baseMapper.delete(Wrappers.<ErpMessageUserBind>lambdaQuery()
                .eq(ErpMessageUserBind::getStaffGuid, ro.getStaffGuid())
                .eq(ErpMessageUserBind::getPlatform, ro.getPlatform()));

        ErpMessageUserBind entity = new ErpMessageUserBind();
        BeanUtil.copyProperties(ro, entity);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String messageUserBindGuid) {
        return super.removeById(messageUserBindGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> messageUserBindGuids) {
        for (String messageUserBindGuid : messageUserBindGuids) {
            super.removeById(messageUserBindGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpMessageUserBindRO ro) {
        ErpMessageUserBind entity = new ErpMessageUserBind();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<ErpMessageUserBindRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public ErpMessageUserBindVO getDataById(String messageUserBindGuid) {
        return baseMapper.getDataByGuid(messageUserBindGuid);
    }

    @Override
    public List<ErpMessageUserBindVO> findList(ErpMessageUserBindQO qo) {
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpMessageUserBindVO> findPage(PageParams<ErpMessageUserBindQO> pageParams) {
        IPage<ErpMessageUserBindVO> page = pageParams.buildPage();
        ErpMessageUserBindQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }
}
