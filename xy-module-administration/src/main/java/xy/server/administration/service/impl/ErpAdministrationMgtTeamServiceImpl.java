package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.basic.entity.Staff;
import com.xunyue.basic.entity.model.dto.StaffDTO;
import com.xunyue.basic.service.IStaffService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.common.enums.FormSourceEnum;
import com.xunyue.common.enums.IsAutoCodeEnum;
import com.xunyue.common.i18n.BaseResultErrorCodeImpl;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.common.util.StringUtils;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import com.xunyue.node.common.enums.TypeEnum;
import com.xunyue.node.entity.NodeType;
import com.xunyue.node.service.INodeTypeService;
import com.xunyue.usercenter.entity.GroupSomeone;
import com.xunyue.usercenter.entity.SomeoneGroupRole;
import com.xunyue.usercenter.entity.model.dto.GroupDTO;
import com.xunyue.usercenter.entity.model.dto.GroupRoleDTO;
import com.xunyue.usercenter.entity.model.dto.GroupSomeoneDTO;
import com.xunyue.usercenter.entity.model.dto.RoleDTO;
import com.xunyue.usercenter.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.administration.entity.ErpAdministrationMgtDepartment;
import xy.server.administration.entity.ErpAdministrationMgtTeam;
import xy.server.administration.entity.ErpAdministrationMgtTeamMembers;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtTeamQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtTeamMembersRO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtTeamRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtTeamVO;
import xy.server.administration.i18n.ResultErrorCode;
import xy.server.administration.mapper.ErpAdministrationMgtTeamMapper;
import xy.server.administration.mapper.ErpAdministrationMgtTeamMembersMapper;
import xy.server.administration.service.IErpAdministrationMgtDepartmentService;
import xy.server.administration.service.IErpAdministrationMgtTeamService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 班组管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ErpAdministrationMgtTeamServiceImpl extends ServiceImpl<ErpAdministrationMgtTeamMapper, ErpAdministrationMgtTeam> implements IErpAdministrationMgtTeamService {

    private final ErpAdministrationMgtTeamMembersMapper teamMembersMapper;
    private final IErpSystemMgtOrderSerialNumberService systemMgtFormSourceService;
    private final IErpAdministrationMgtDepartmentService iErpAdministrationMgtDepartmentService;
    private final IRoleService roleService;
    private final IGroupService  groupService;
    private final IGroupRoleService groupRoleService;
    private final IGroupSomeoneService groupSomeoneService;
    private final ISomeoneGroupRoleService someoneGroupRoleService;
    private final INodeTypeService nodeTypeService;
    private final IStaffService staffService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtTeamRO ro) {
        saveOrUpdateVerifys(ro);
        if(String.valueOf(IsAutoCodeEnum.ID_CODE.getCode()).equals(ro.getIsCode()) || StringUtils.isEmpty(ro.getTeamCode())){
            ro.setTeamCode(systemMgtFormSourceService.generateOrderNumber(FormSourceEnum.TEAM_TEAM));
        }
        ErpAdministrationMgtTeam entity = new ErpAdministrationMgtTeam();
        BeanUtil.copyProperties(ro, entity);
        boolean save = super.save(entity);
        // 新增班组双写
        Map<String, String> groupRoleMap = new HashMap<>(2);
        GroupDTO groupDTO = this.buildGroupDTO(entity);
        if (ObjUtil.isNotNull(groupDTO)) {
            groupService.save(groupDTO);
            // 新增保存node-type关联表
            nodeTypeService.save(new NodeType(entity.getTeamGuid(), TypeEnum.TEAM.getCode()));
            // 关联初始化班组角色（组长、组员、班长），该租户没有初始化角色则先创建
            groupRoleMap = this.saveGroupRole(groupDTO);
        }
        //处理子数据
        if (CollUtil.isNotEmpty(ro.getTeamMembersROS())){
            final Map<String,String> finalStaffUserMap = findUserIdByStaffGuid(CollStreamUtil.toList(ro.getTeamMembersROS(), ErpAdministrationMgtTeamMembersRO::getStaffGuid));
            final Map<String, String> finalGroupRoleMap = groupRoleMap;
            ro.getTeamMembersROS().forEach(x->{
                //同一班组下员工不能相同
                saveOrUpdateVerify(x);
                x.setTeamGuid(entity.getTeamGuid());
                ErpAdministrationMgtTeamMembers erpAdministrationMgtTeamMembers = BeanUtil.toBean(x, ErpAdministrationMgtTeamMembers.class);
                teamMembersMapper.insert(erpAdministrationMgtTeamMembers);
                // 班组成员双写
                GroupSomeoneDTO groupSomeoneDTO = this.buildGroupSomeoneDTO(erpAdministrationMgtTeamMembers, finalStaffUserMap);
                if (ObjUtil.isNotNull(groupSomeoneDTO)) {
                    groupSomeoneService.save(groupSomeoneDTO);
                    // 新增保存someone-group-role关联表
                    saveSomeoneGroupRole(erpAdministrationMgtTeamMembers, finalGroupRoleMap, finalStaffUserMap);
                }
            });
        }
        return save;
    }

    private Map<String, String> findUserIdByStaffGuid(List<String> staffGuids) {
        List<Staff> staffList = staffService.listByIds(staffGuids);
        Map<String, String> staffUserMap = CollStreamUtil.toMap(staffList, Staff::getId, Staff::getUserId);
        Map<String, String> resultMap = new HashMap<>();
        // 结果输出的map[key:staffGuid, value:userId/staffGuid]
        staffGuids.forEach(staffGuid-> resultMap.put(staffGuid, StrUtil.isNotBlank(staffUserMap.get(staffGuid)) ? staffUserMap.get(staffGuid) : staffGuid));
        return resultMap;
    }

    /**
     * 新增和修改时字段校验
     * @param ro
     */
    public void saveOrUpdateVerifys(ErpAdministrationMgtTeamRO ro) {
        // 判断名称是否重复
        LambdaQueryWrapper<ErpAdministrationMgtTeam> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ErpAdministrationMgtTeam::getTenantGuid, ro.getTenantGuid())
                .eq(ErpAdministrationMgtTeam::getTeamName, ro.getTeamName())
                .eq(ErpAdministrationMgtTeam::getDeleted, false)
                .ne(ro.getTeamGuid() != null, ErpAdministrationMgtTeam::getTeamGuid, ro.getTeamGuid());
        if (baseMapper.exists(queryWrapper)) {
            throw new FlowException(ResultErrorCode.NO_REPETITION_ADMINISTRATION_ZHUMI);
        }
    }


    /**
     * 新增和修改时字段校验
     * @param ro
     */
    public void saveOrUpdateVerify(ErpAdministrationMgtTeamMembersRO ro) {
        // 判断名称是否重复
        LambdaQueryWrapper<ErpAdministrationMgtTeamMembers> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ErpAdministrationMgtTeamMembers::getTeamGuid, ro.getTeamGuid())
                .eq(ErpAdministrationMgtTeamMembers::getTenantGuid,ro.getTenantGuid())
                .eq(ErpAdministrationMgtTeamMembers::getStaffGuid, ro.getStaffGuid())
                .eq(ErpAdministrationMgtTeamMembers::getDeleted, false)
                .ne(ro.getTeamMembersGuid() != null, ErpAdministrationMgtTeamMembers::getTeamMembersGuid, ro.getTeamMembersGuid());
        if (teamMembersMapper.exists(queryWrapper)) {
            throw new FlowException(ResultErrorCode.NO_REPETITION_ADMINISTRATION_CHENY);
        }
    }



    public static void main(String[] args) {
        System.out.println();
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String teamGuid) {
        ErpAdministrationMgtTeam mgtTeam = baseMapper.selectById(teamGuid);
        if (Objects.nonNull(mgtTeam)&&mgtTeam.getIsUsed()){
            throw new FlowException(ResultErrorCode.NO_REPETITION_ADMINISTRATION_DEPARTMENTD,teamGuid);
        }
        super.removeById(teamGuid);
        // 删除双写
        return groupService.removeById(teamGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtTeamRO ro) {
        saveOrUpdateVerifys(ro);
        ErpAdministrationMgtTeam entity = new ErpAdministrationMgtTeam();
        BeanUtil.copyProperties(ro, entity);
        //处理子数据
        teamMembersMapper.deletes(entity.getTeamGuid());

        // 双写删除班组-成员数据
        groupSomeoneService.remove(Wrappers.<GroupSomeone>lambdaQuery().eq(GroupSomeone::getGroupId, entity.getTeamGuid()));
        // 查询新ERP班组角色关联表
        GroupRoleDTO groupRoleDTO = new GroupRoleDTO();
        groupRoleDTO.setGroupRoleTenantId(entity.getTenantGuid());
        groupRoleDTO.setGroupRoleGroupId(entity.getTeamGuid());
        Map<String, String> groupRoleMap = groupRoleService.findList(groupRoleDTO)
                .stream().collect(Collectors.toMap(GroupRoleDTO::getRoleSystemCode, GroupRoleDTO::getGroupRoleId));
        if (CollUtil.isNotEmpty(groupRoleMap)) {
            // 删除someone-group-role数据
            someoneGroupRoleService.remove(Wrappers.<SomeoneGroupRole>lambdaQuery().in(SomeoneGroupRole::getGroupRoleId,groupRoleMap.values()));
        }

        //处理子数据
        if (CollUtil.isNotEmpty(ro.getTeamMembersROS())){
            final Map<String,String> finalStaffUserMap = findUserIdByStaffGuid(CollStreamUtil.toList(ro.getTeamMembersROS(), ErpAdministrationMgtTeamMembersRO::getStaffGuid));
            ro.getTeamMembersROS().forEach(x->{
                //同一班组下员工不能相同
                saveOrUpdateVerify(x);
                x.setTeamGuid(entity.getTeamGuid());
                ErpAdministrationMgtTeamMembers erpAdministrationMgtTeamMembers = BeanUtil.toBean(x, ErpAdministrationMgtTeamMembers.class);
                teamMembersMapper.insert(erpAdministrationMgtTeamMembers);

                // 班组成员双写
                GroupSomeoneDTO groupSomeoneDTO = this.buildGroupSomeoneDTO(erpAdministrationMgtTeamMembers, finalStaffUserMap);
                if (ObjUtil.isNotNull(groupSomeoneDTO)) {
                    groupSomeoneService.save(groupSomeoneDTO);
                    // 新增保存someone-group-role关联表
                    saveSomeoneGroupRole(erpAdministrationMgtTeamMembers, groupRoleMap, finalStaffUserMap);
                }
            });
        }

        super.updateById(entity);
        // 双写更新
        GroupDTO groupDTO = this.buildGroupDTO(entity);
        if (ObjUtil.isNotNull(groupDTO)) {
            groupService.update(groupDTO);
        }
        return true;
    }

    @Override
    public ErpAdministrationMgtTeamVO getById(String teamGuid) {

        return baseMapper.getDataByGuid(teamGuid);
    }

    @Override
    public List<ErpAdministrationMgtTeamVO> findList(ErpAdministrationMgtTeamQO qo, String tenantGuid) {
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpAdministrationMgtTeamVO> findPage(PageParams<ErpAdministrationMgtTeamQO> pageParams) {
        IPage<ErpAdministrationMgtTeamVO> page = pageParams.buildPage();
        ErpAdministrationMgtTeamQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }

    @Override
    public Boolean reordered(List<SortEntityDto> entityDtoList) {
        entityDtoList.forEach(sortEntityDto -> {
            LambdaUpdateWrapper<ErpAdministrationMgtTeam> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ErpAdministrationMgtTeam::getSerialNumber, sortEntityDto.getSerialNumber());
            updateWrapper.eq(ErpAdministrationMgtTeam::getTeamGuid, sortEntityDto.getGuid());
            update(updateWrapper);
        });
        return true;
    }

    @Override
    public Boolean saveData(InsertOrUpdateList<ErpAdministrationMgtTeamRO> saveData, String tenantGuid) {
        if (CollUtil.isNotEmpty(saveData.getInsertList())) {
            saveData.getInsertList().forEach(x -> {
                x.setTenantGuid(tenantGuid);
                this.create(x);
            });
        }

        if (CollUtil.isNotEmpty(saveData.getUpdateList())) {
            saveData.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByBatch(List<String> ids) {
        ids.forEach(this::delete);
        return true;
    }

    /**
     * 处理导入Excel数据
     * @param ro
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcelDataHandle(ErpAdministrationMgtTeamRO ro) {
        // 处理部门
        if (StringUtils.isNotEmpty(ro.getDepartmentGuid())) {
            // 根据部门名称获取部门guid
            ErpAdministrationMgtDepartment department = iErpAdministrationMgtDepartmentService.getOne(Wrappers
                    .<ErpAdministrationMgtDepartment>lambdaQuery()
                    .select(ErpAdministrationMgtDepartment::getDepartmentGuid)
                    .eq(ErpAdministrationMgtDepartment::getDepartmentName, ro.getDepartmentGuid()));
            if (ObjectUtil.isNull(department) || StringUtils.isEmpty(department.getDepartmentGuid())) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "部门【" + ro.getDepartmentGuid() + "】不存在！");
            }
            ro.setDepartmentGuid(department.getDepartmentGuid());
        }
        // 保存数据到数据库
        this.create(ro);
    }

    /**
     * 构建GroupDTO
     *
     * @param entity 1.0
     * @return GroupDTO
     */
    private GroupDTO buildGroupDTO(ErpAdministrationMgtTeam entity) {
        if (ObjUtil.isNull(entity)) {
            return null;
        }
        GroupDTO groupDTO = new GroupDTO();
        groupDTO.setGroupId(entity.getTeamGuid());
        groupDTO.setGroupIsolation(entity.getTenantGuid());
        groupDTO.setGroupTenantId(entity.getTenantGuid());
        groupDTO.setGroupDefaultType(TypeEnum.TEAM.getCode());
        groupDTO.setGroupName(entity.getTeamName());
        groupDTO.setGroupUserCode(entity.getTeamCode());
        // 部门guid存到父级id字段中
        groupDTO.setGroupParentId(entity.getDepartmentGuid().isEmpty() ? null : entity.getDepartmentGuid());
        groupDTO.setGroupDescription(entity.getDescription());
        groupDTO.setGroupSerialNumber(entity.getSerialNumber());
        groupDTO.setGroupIsBanned(entity.getState());
        groupDTO.setGroupCreatorId(entity.getCreatorGuid());
        groupDTO.setGroupCreator(entity.getCreator());
        groupDTO.setGroupCreateTime(DateUtil.date(entity.getCreateDate()));
        groupDTO.setGroupUpdaterId(entity.getLastUpdaterGuid());
        groupDTO.setGroupUpdater(entity.getLastUpdater());
        groupDTO.setGroupUpdateTime(DateUtil.date(entity.getLastUpdateDate()));
        return groupDTO;
    }

    /**
     * 关联初始化班组角色（组长、组员、班长），该租户没有初始化角色则先创建
     * @param groupDTO
     */
    private Map<String, String> saveGroupRole(GroupDTO groupDTO) {
        RoleDTO roleDTO = new RoleDTO();
        roleDTO.setRoleTenantId(groupDTO.getGroupTenantId());
        roleDTO.setRoleIsolation(groupDTO.getGroupIsolation());
        roleDTO.setRoleSystemCodes(Arrays.asList("GROUP_LEADER", "GROUP_MEMBER", "GROUP_MONITOR"));
        roleDTO.setRoleDefaultType(TypeEnum.MES_TEAM_ROLE.getCode());
        List<RoleDTO> roleList = roleService.findList(roleDTO);
        Map<String, String> roleMap = new HashMap<>(2);
        if (CollUtil.isEmpty(roleList)) {
            // 不存在初始化角色则先创建
            String groupLeaderRoleId = IdUtil.fastSimpleUUID();
            String groupMemberRoleId = IdUtil.fastSimpleUUID();
            String groupMonitorRoleId = IdUtil.fastSimpleUUID();
            // 组长
            RoleDTO groupLeaderRoleDTO = new RoleDTO();
            groupLeaderRoleDTO.setRoleId(groupLeaderRoleId);
            groupLeaderRoleDTO.setRoleTenantId(groupDTO.getGroupTenantId());
            groupLeaderRoleDTO.setRoleIsolation(groupDTO.getGroupIsolation());
            groupLeaderRoleDTO.setRoleSystemCode("GROUP_LEADER");
            groupLeaderRoleDTO.setRoleDefaultType(TypeEnum.MES_TEAM_ROLE.getCode());
            groupLeaderRoleDTO.setRoleName("组长");
            groupLeaderRoleDTO.setRoleUserCode("GROUP_LEADER");
            groupLeaderRoleDTO.setRoleDescription("MES班组初始化角色-组长");
            roleList.add(groupLeaderRoleDTO);
            // 组员
            RoleDTO groupMemberRoleDTO = new RoleDTO();
            groupMemberRoleDTO.setRoleId(groupMemberRoleId);
            groupMemberRoleDTO.setRoleTenantId(groupDTO.getGroupTenantId());
            groupMemberRoleDTO.setRoleIsolation(groupDTO.getGroupIsolation());
            groupMemberRoleDTO.setRoleSystemCode("GROUP_MEMBER");
            groupMemberRoleDTO.setRoleDefaultType(TypeEnum.MES_TEAM_ROLE.getCode());
            groupMemberRoleDTO.setRoleName("组员");
            groupMemberRoleDTO.setRoleUserCode("GROUP_MEMBER");
            groupMemberRoleDTO.setRoleDescription("MES班组初始化角色-组员");
            roleList.add(groupMemberRoleDTO);
            // 班长
            RoleDTO groupMonitorRoleDTO = new RoleDTO();
            groupMonitorRoleDTO.setRoleId(groupMonitorRoleId);
            groupMonitorRoleDTO.setRoleTenantId(groupDTO.getGroupTenantId());
            groupMonitorRoleDTO.setRoleIsolation(groupDTO.getGroupIsolation());
            groupMonitorRoleDTO.setRoleSystemCode("GROUP_MONITOR");
            groupMonitorRoleDTO.setRoleDefaultType(TypeEnum.MES_TEAM_ROLE.getCode());
            groupMonitorRoleDTO.setRoleName("班长");
            groupMonitorRoleDTO.setRoleUserCode("GROUP_MONITOR");
            groupMonitorRoleDTO.setRoleDescription("MES班组初始化角色-班长");
            roleList.add(groupMonitorRoleDTO);
            roleService.saveBatch(roleList);
            // 新增保存node-type关联表
            nodeTypeService.saveBatch(roleList.stream().map(item -> new NodeType(item.getRoleId(), TypeEnum.MES_TEAM_ROLE.getCode())).collect(Collectors.toList()));
        }
        roleMap = roleList.stream().collect(Collectors.toMap(RoleDTO::getRoleSystemCode, RoleDTO::getRoleId));

        // 保存用户组-角色关联表
        GroupRoleDTO groupLeaderRoleDTO = new GroupRoleDTO();
        groupLeaderRoleDTO.setGroupRoleId(IdUtil.fastSimpleUUID());
        groupLeaderRoleDTO.setGroupId(groupDTO.getGroupId());
        groupLeaderRoleDTO.setRoleId(roleMap.get("GROUP_LEADER"));
        groupLeaderRoleDTO.setGroupRoleTenantId(groupDTO.getGroupTenantId());

        GroupRoleDTO groupMemberRoleDTO = new GroupRoleDTO();
        groupMemberRoleDTO.setGroupRoleId(IdUtil.fastSimpleUUID());
        groupMemberRoleDTO.setGroupId(groupDTO.getGroupId());
        groupMemberRoleDTO.setRoleId(roleMap.get("GROUP_MEMBER"));
        groupMemberRoleDTO.setGroupRoleTenantId(groupDTO.getGroupTenantId());

        GroupRoleDTO groupMonitorRoleDTO = new GroupRoleDTO();
        groupMonitorRoleDTO.setGroupRoleId(IdUtil.fastSimpleUUID());
        groupMonitorRoleDTO.setGroupId(groupDTO.getGroupId());
        groupMonitorRoleDTO.setRoleId(roleMap.get("GROUP_MONITOR"));
        groupMonitorRoleDTO.setGroupRoleTenantId(groupDTO.getGroupTenantId());
        groupRoleService.saveBatch(Arrays.asList(groupLeaderRoleDTO, groupMemberRoleDTO, groupMonitorRoleDTO));
        return MapUtil.builder(new HashMap<String, String>())
                .put("GROUP_LEADER", groupLeaderRoleDTO.getGroupRoleId())
                .put( "GROUP_MEMBER", groupMemberRoleDTO.getGroupRoleId())
                .put( "GROUP_MONITOR", groupMonitorRoleDTO.getGroupRoleId())
                .build();
    }

    /**
     * 构建GroupSomeoneDTO
     *
     * @param entity 1.0
     * @return GroupDTO
     */
    private GroupSomeoneDTO buildGroupSomeoneDTO(ErpAdministrationMgtTeamMembers entity, Map<String,String> staffUserMap) {
        if (ObjUtil.isNull(entity)) {
            return null;
        }
        GroupSomeoneDTO groupSomeoneDTO = new GroupSomeoneDTO();
        groupSomeoneDTO.setGroupSomeoneId(entity.getTeamMembersGuid());
        groupSomeoneDTO.setGroupSomeoneGroupId(entity.getTeamGuid());
        groupSomeoneDTO.setGroupSomeoneSomeoneId(staffUserMap.get(entity.getStaffGuid()));
        groupSomeoneDTO.setGroupSomeoneTenantId(entity.getTenantGuid());
        groupSomeoneDTO.setGroupSomeoneExtension(entity.getDescription());
        groupSomeoneDTO.setGroupSomeoneCreatorId(entity.getCreatorGuid());
        groupSomeoneDTO.setGroupSomeoneCreator(entity.getCreator());
        groupSomeoneDTO.setGroupSomeoneCreateTime(DateUtil.date(entity.getCreateDate()));
        groupSomeoneDTO.setGroupSomeoneUpdaterId(entity.getLastUpdaterGuid());
        groupSomeoneDTO.setGroupSomeoneUpdater(entity.getLastUpdater());
        groupSomeoneDTO.setGroupSomeoneUpdateTime(DateUtil.date(entity.getLastUpdateDate()));
        groupSomeoneDTO.setGroupSomeoneWeight(entity.getLevel());
        return groupSomeoneDTO;
    }

    /**
     * 保存用户-用户组-角色关联表
     * @param erpAdministrationMgtTeamMembers
     * @param groupRoleMap
     */
    private void saveSomeoneGroupRole(ErpAdministrationMgtTeamMembers erpAdministrationMgtTeamMembers, Map<String, String> groupRoleMap , Map<String,String> staffUserMap) {
        SomeoneGroupRole someoneGroupRole = new SomeoneGroupRole();
        someoneGroupRole.setTenantId(erpAdministrationMgtTeamMembers.getTenantGuid());
        someoneGroupRole.setGroupRoleId(groupRoleMap.get(Boolean.TRUE.equals(erpAdministrationMgtTeamMembers.getIsMonitor()) ? "GROUP_MONITOR" : (Boolean.TRUE.equals(erpAdministrationMgtTeamMembers.getIsGroup()) ? "GROUP_LEADER" : "GROUP_MEMBER")));
        someoneGroupRole.setSomeoneId(staffUserMap.get(erpAdministrationMgtTeamMembers.getStaffGuid()));
        someoneGroupRoleService.save(someoneGroupRole);
    }
}

