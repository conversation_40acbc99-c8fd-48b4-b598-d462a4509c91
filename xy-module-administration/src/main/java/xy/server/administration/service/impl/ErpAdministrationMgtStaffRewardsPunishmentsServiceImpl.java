package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.enums.FormSourceEnum;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.config.util.PageParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.service.IProcessInstanceService;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.administration.entity.ErpAdministrationMgtStaffRewardsPunishments;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffRewardsPunishmentsQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffRewardsPunishmentsRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffRewardsPunishmentsVO;
import xy.server.administration.mapper.ErpAdministrationMgtStaffRewardsPunishmentsMapper;
import xy.server.administration.service.IErpAdministrationMgtStaffRewardsPunishmentsService;

import java.util.List;

/**
 * <p>
 * 奖罚记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Service("STAFF_RECORDS.REWARD_AND_PENALTY_RECORDS")
public class ErpAdministrationMgtStaffRewardsPunishmentsServiceImpl extends ServiceImpl<ErpAdministrationMgtStaffRewardsPunishmentsMapper, ErpAdministrationMgtStaffRewardsPunishments> implements IErpAdministrationMgtStaffRewardsPunishmentsService, ActEventStrategyService {
    @Autowired
    private IErpSystemMgtOrderSerialNumberService erpSystemMgtOrderSerialNumberService;
    @Autowired
    private IProcessInstanceService iProcessInstanceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtStaffRewardsPunishmentsRO ro) {
        ErpAdministrationMgtStaffRewardsPunishments entity = new ErpAdministrationMgtStaffRewardsPunishments();
        ro.setRewardsPunishmentsNumber(erpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.REWARD_AND_PENALTY_RECORDS));
        BeanUtil.copyProperties(ro, entity);
        if (super.save(entity)) {
            // 启动审核流程
            iProcessInstanceService.start(WorkflowKeyEnum.REWARD_AND_PENALTY_RECORDS, entity.getStaffRewardsPunishmentsGuid());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String staffRewardsPunishmentsGuid) {
        super.removeById(staffRewardsPunishmentsGuid);
        iProcessInstanceService.deleteProcessAndHisInst(staffRewardsPunishmentsGuid, WorkflowKeyEnum.REWARD_AND_PENALTY_RECORDS);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> staffRewardsPunishmentsGuids) {
        for (String staffRewardsPunishmentsGuid : staffRewardsPunishmentsGuids) {
            super.removeById(staffRewardsPunishmentsGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtStaffRewardsPunishmentsRO ro) {
        ErpAdministrationMgtStaffRewardsPunishments entity = new ErpAdministrationMgtStaffRewardsPunishments();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffRewardsPunishmentsRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public ErpAdministrationMgtStaffRewardsPunishmentsVO getDataById(String staffRewardsPunishmentsGuid) {
        return baseMapper.getDataByGuid(staffRewardsPunishmentsGuid);
    }

    @Override
    public List<ErpAdministrationMgtStaffRewardsPunishmentsVO> findList(ErpAdministrationMgtStaffRewardsPunishmentsQO qo) {
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpAdministrationMgtStaffRewardsPunishmentsVO> findPage(PageParams<ErpAdministrationMgtStaffRewardsPunishmentsQO> pageParams) {
        IPage<ErpAdministrationMgtStaffRewardsPunishmentsVO> page = pageParams.buildPage();
        ErpAdministrationMgtStaffRewardsPunishmentsQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }
}
