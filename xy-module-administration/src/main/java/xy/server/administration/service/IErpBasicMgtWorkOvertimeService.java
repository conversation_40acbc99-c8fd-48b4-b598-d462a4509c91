package xy.server.administration.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpBasicMgtWorkOvertime;
import xy.server.administration.entity.model.qo.ErpBasicMgtWorkOvertimeQO;
import xy.server.administration.entity.model.ro.ErpBasicMgtWorkOvertimeRO;
import xy.server.administration.entity.model.vo.ErpBasicMgtWorkOvertimeVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 加班安排 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
public interface IErpBasicMgtWorkOvertimeService extends IService<ErpBasicMgtWorkOvertime> {
    boolean create(ErpBasicMgtWorkOvertimeRO ro);

    boolean delete(String workOvertimeGuid);

    boolean update(ErpBasicMgtWorkOvertimeRO ro);

    ErpBasicMgtWorkOvertimeVO getById(String workOvertimeGuid);

    List<ErpBasicMgtWorkOvertimeVO> findList(ErpBasicMgtWorkOvertimeQO qo, String tenantGuid);

    IPage<ErpBasicMgtWorkOvertimeVO> findPage(PageParams<ErpBasicMgtWorkOvertimeQO> pageParams);

    Boolean stopState(String workOvertimeGuid);

    Boolean saveDate(InsertOrUpdateList<ErpBasicMgtWorkOvertimeRO> saveData, String tenantGuid);

    Boolean removeBatchById(List<String> ids);

    List<ErpBasicMgtWorkOvertimeVO> selectList(LocalDateTime date1, String equipmentGuid);
}
