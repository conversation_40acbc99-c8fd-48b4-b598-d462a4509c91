package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtStaffPostAdjustment;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffPostAdjustmentQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffPostAdjustmentRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffPostAdjustmentVO;

import java.util.List;

/**
 * @apiNote 调岗记录表 服务类
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface IErpAdministrationMgtStaffPostAdjustmentService extends IService<ErpAdministrationMgtStaffPostAdjustment> {
                                                                            boolean create(ErpAdministrationMgtStaffPostAdjustmentRO ro);

    boolean delete(String staffPostAdjustmentGuid);

    boolean deleteByBatch(List<String> staffPostAdjustmentGuids);

    boolean update(ErpAdministrationMgtStaffPostAdjustmentRO ro);

    boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffPostAdjustmentRO> dataList);

    ErpAdministrationMgtStaffPostAdjustmentVO getDataById(String staffPostAdjustmentGuid);

    List<ErpAdministrationMgtStaffPostAdjustmentVO> findList(ErpAdministrationMgtStaffPostAdjustmentQO qo);

    IPage<ErpAdministrationMgtStaffPostAdjustmentVO> findPage(PageParams<ErpAdministrationMgtStaffPostAdjustmentQO> pageParams);
}
