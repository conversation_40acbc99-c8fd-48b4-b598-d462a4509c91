package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.enums.FormSourceEnum;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.config.util.PageParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.service.IProcessInstanceService;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.administration.entity.ErpAdministrationMgtStaffRecruit;
import xy.server.administration.entity.ErpAdministrationRecruit;
import xy.server.administration.entity.model.qo.ErpAdministrationRecruitQO;
import xy.server.administration.entity.model.ro.ErpAdministrationRecruitRO;
import xy.server.administration.entity.model.vo.ErpAdministrationRecruitVO;
import xy.server.administration.mapper.ErpAdministrationRecruitMapper;
import xy.server.administration.service.IErpAdministrationMgtStaffRecruitService;
import xy.server.administration.service.IErpAdministrationRecruitService;

import java.util.List;

/**
 * <p>
 * 工单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-26
 */
@Service("WORK_ORDER.ADMINISTRATION_STAFF_RECRUIT")
public class ErpAdministrationRecruitServiceImpl extends ServiceImpl<ErpAdministrationRecruitMapper, ErpAdministrationRecruit> implements IErpAdministrationRecruitService, ActEventStrategyService {

    @Autowired
    private IErpSystemMgtOrderSerialNumberService erpSystemMgtOrderSerialNumberService;
    @Autowired
    private IProcessInstanceService iProcessInstanceService;
    @Autowired
    private IErpAdministrationMgtStaffRecruitService recruitService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationRecruitRO ro) {
        ErpAdministrationRecruit entity = new ErpAdministrationRecruit();
        ro.setWorkorderNumber(erpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.ADMINISTRATION_STAFF_RECRUIT));
        BeanUtil.copyProperties(ro, entity);
        if (super.save(entity)) {
            ro.setWorkorderGuid(entity.getWorkorderGuid());
            recruitService.save(BeanUtil.copyProperties(ro, ErpAdministrationMgtStaffRecruit.class));
            iProcessInstanceService.start(WorkflowKeyEnum.ADMINISTRATION_STAFF_RECRUIT, entity.getWorkorderGuid());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String workorderGuid) {
        ErpAdministrationRecruitVO data = baseMapper.getDataByGuid(workorderGuid);
        super.removeById(workorderGuid);
        recruitService.removeById(data.getStaffRecruitGuid());
        iProcessInstanceService.deleteProcessAndHisInst(workorderGuid, WorkflowKeyEnum.ADMINISTRATION_STAFF_RECRUIT);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> workorderGuids) {
        for (String workorderGuid : workorderGuids) {
            super.removeById(workorderGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationRecruitRO ro) {
        ErpAdministrationRecruit entity = new ErpAdministrationRecruit();
        BeanUtil.copyProperties(ro, entity);
        super.updateById(entity);
       return recruitService.updateById(BeanUtil.copyProperties(ro, ErpAdministrationMgtStaffRecruit.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<ErpAdministrationRecruitRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public ErpAdministrationRecruitVO getDataById(String workorderGuid) {
        return baseMapper.getDataByGuid(workorderGuid);
    }

    @Override
    public List<ErpAdministrationRecruitVO> findList(ErpAdministrationRecruitQO qo) {
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpAdministrationRecruitVO> findPage(PageParams<ErpAdministrationRecruitQO> pageParams) {
        IPage<ErpAdministrationRecruitVO> page = pageParams.buildPage();
        ErpAdministrationRecruitQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }
}
