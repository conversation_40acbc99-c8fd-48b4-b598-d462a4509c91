package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationRecruit;
import xy.server.administration.entity.model.qo.ErpAdministrationRecruitQO;
import xy.server.administration.entity.model.ro.ErpAdministrationRecruitRO;
import xy.server.administration.entity.model.vo.ErpAdministrationRecruitVO;

import java.util.List;

/**
 * <AUTHOR>
 * @apiNote 工单表 服务类
 * @since 2024-04-26
 */
public interface IErpAdministrationRecruitService extends IService<ErpAdministrationRecruit> {
    boolean create(ErpAdministrationRecruitRO ro);

    boolean delete(String workorderGuid);

    boolean deleteByBatch(List<String> workorderGuids);

    boolean update(ErpAdministrationRecruitRO ro);

    boolean saveDate(InsertOrUpdateList<ErpAdministrationRecruitRO> dataList);

    ErpAdministrationRecruitVO getDataById(String workorderGuid);

    List<ErpAdministrationRecruitVO> findList(ErpAdministrationRecruitQO qo);

    IPage<ErpAdministrationRecruitVO> findPage(PageParams<ErpAdministrationRecruitQO> pageParams);
}
