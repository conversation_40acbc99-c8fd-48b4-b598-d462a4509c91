package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.config.excel.EasyExcelDataHandleService;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtDepartment;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtDepartmentQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtDepartmentRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtDepartmentVO;
import xy.server.administration.entity.model.vo.ErpDepartmentMgtStaffVO;

import java.util.List;

/**
 * <p>
 * 部门表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
public interface IErpAdministrationMgtDepartmentService extends IService<ErpAdministrationMgtDepartment>, EasyExcelDataHandleService<ErpAdministrationMgtDepartmentRO> {
    boolean create(ErpAdministrationMgtDepartmentRO ro);

    boolean delete(String departmentGuid);

    boolean update(ErpAdministrationMgtDepartmentRO ro);

    ErpAdministrationMgtDepartmentVO getById(String departmentGuid);

    List<ErpAdministrationMgtDepartmentVO> findList(ErpAdministrationMgtDepartmentQO qo, String tenantGuid);

    IPage<ErpAdministrationMgtDepartmentVO> findPage(PageParams<ErpAdministrationMgtDepartmentQO> pageParams);

    Boolean reordered(List<SortEntityDto> entityDtoList);

    Boolean saveData(InsertOrUpdateList<ErpAdministrationMgtDepartmentRO> saveData, String tenantGuid);

    Boolean upDeTeState(String departmentGuid);

    Boolean removeBatchByIdss(List<String> ids);

    /**
     * 根据部门id获取员工数据
     * @param departmentGuid
     * @return
     */
    List<ErpDepartmentMgtStaffVO> getStaffList(String departmentGuid);
}
