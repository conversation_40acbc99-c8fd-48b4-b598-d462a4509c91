package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtTrainingStaff;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtTrainingStaffQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtTrainingStaffRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtTrainingStaffVO;

import java.util.List;

/**
 * @apiNote 培训员工扩展表 服务类
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface IErpAdministrationMgtTrainingStaffService extends IService<ErpAdministrationMgtTrainingStaff> {
                    boolean create(ErpAdministrationMgtTrainingStaffRO ro);

    boolean delete(String trainingStaffGuid);

    boolean deleteByBatch(List<String> trainingStaffGuids);

    boolean update(ErpAdministrationMgtTrainingStaffRO ro);

    boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtTrainingStaffRO> dataList);

    ErpAdministrationMgtTrainingStaffVO getDataById(String trainingStaffGuid);

    List<ErpAdministrationMgtTrainingStaffVO> findList(ErpAdministrationMgtTrainingStaffQO qo);

    IPage<ErpAdministrationMgtTrainingStaffVO> findPage(PageParams<ErpAdministrationMgtTrainingStaffQO> pageParams);
}
