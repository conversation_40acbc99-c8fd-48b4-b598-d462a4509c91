package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.config.excel.EasyExcelDataHandleService;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtPost;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtPostQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtPostRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtPostVO;

import java.util.List;

/**
 * <p>
 * 岗位管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
public interface IErpAdministrationMgtPostService extends IService<ErpAdministrationMgtPost>, EasyExcelDataHandleService<ErpAdministrationMgtPostRO> {
    boolean create(ErpAdministrationMgtPostRO ro);

    boolean delete(String postGuid);

    boolean update(ErpAdministrationMgtPostRO ro);

    ErpAdministrationMgtPostVO getById(String postGuid);

    List<ErpAdministrationMgtPostVO> findList(ErpAdministrationMgtPostQO qo, String tenantGuid);

    IPage<ErpAdministrationMgtPostVO> findPage(PageParams<ErpAdministrationMgtPostQO> pageParams);

    Boolean saveData(InsertOrUpdateList<ErpAdministrationMgtPostRO> saveData, String tenantGuid);

    Boolean reordered(List<SortEntityDto> entityDtoList);
}
