package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.basic.entity.Staff;
import com.xunyue.basic.entity.model.dto.StaffDTO;
import com.xunyue.basic.service.IStaffService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.entity.model.SortEntityDto;
import com.xunyue.common.i18n.BaseResultErrorCodeImpl;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import com.xunyue.config.util.SpringBeanUtil;
import com.xunyue.node.common.enums.ClazzEnum;
import com.xunyue.tenant.sign.UserApi;
import com.xunyue.usercenter.entity.model.dto.UserDTO;
import com.xunyue.usercenter.service.IUserService;
import com.xy.util.BaseContext;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.administration.entity.*;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffQO;
import xy.server.administration.entity.model.qo.ErpAuditorQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffFileRO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffRO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffResignationRO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffUserRo;
import xy.server.administration.entity.model.vo.*;
import xy.server.administration.i18n.ResultErrorCode;
import xy.server.administration.mapper.ErpAdministrationMgtDepartmentMapper;
import xy.server.administration.mapper.ErpAdministrationMgtStaffMapper;
import xy.server.administration.service.*;
import xy.server.dto.XyMemberDto;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 员工列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ErpAdministrationMgtStaffServiceImpl extends ServiceImpl<ErpAdministrationMgtStaffMapper, ErpAdministrationMgtStaff> implements IErpAdministrationMgtStaffService {
    private final IErpAdministrationMgtStaffFileService fileService;
    private final IErpAdministrationMgtStaffResignationService staffResignationService;
    private final IErpAdministrationMgtDepartmentService iErpAdministrationMgtDepartmentService;
    private final IErpAdministrationMgtPostService iErpAdministrationMgtPostService;
    private final UserApi userApi;
    private final IStaffService staffService;
    private final IUserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtStaffRO ro) throws ParseException {
        String creator = BaseContext.getCreator();
        String staffShortName = BaseContext.getStaffShortName();
        String creatorGuid = BaseContext.getCreatorGuid();
        String tenantGuid = BaseContext.getTenantGuid();
        String staffGuid = BaseContext.getStaffGuid();
        this.saveOrUpdateVerify(ro);
        ErpAdministrationMgtStaff entity = new ErpAdministrationMgtStaff();
        BeanUtil.copyProperties(ro, entity);
        //拆解身份证并写入到出生日期
        if (StringUtils.isNotBlank(entity.getIdcard())) {
            // 解析日期字符串为LocalDate对象
            LocalDate dateOfBirth = LocalDate.parse(entity.getIdcard().substring(6, 14), DateTimeFormatter.ofPattern("yyyyMMdd"));
// 创建一个特定的时间（这里假设时间为00:00:00）
            LocalTime time = LocalTime.of(0, 0, 0);
// 将日期和时间组合成LocalDateTime对象
            LocalDateTime dateTimeOfBirth = LocalDateTime.of(dateOfBirth, time);
// 调用setDateOfBirth方法设置新的日期和时间
            entity.setDateOfBirth(dateTimeOfBirth);
        }
        if (StringUtils.isBlank(entity.getStaffFullName())) {
            //员工全称如为空默认与名称相同。
            entity.setStaffFullName(entity.getStaffShortName());
        }
        super.save(entity);
        if (CollUtil.isNotEmpty(ro.getFileList())){
            List<ErpAdministrationMgtStaffFileRO> collect = ro.getFileList().stream().peek(s -> s.setStaffGuid(entity.getStaffGuid())).collect(Collectors.toList());
            List<ErpAdministrationMgtStaffFile> list = BeanUtil.copyToList(collect, ErpAdministrationMgtStaffFile.class);
            fileService.saveOrUpdateBatch(list);
        }
        //同步员工信息到2.0
        ThreadUtil.execAsync(()->{
            BaseContext.setCreator(creator);
            BaseContext.setCreatorGuid(creatorGuid);
            BaseContext.setTenantGuid(tenantGuid);
            BaseContext.setStaffGuid(staffGuid);
            BaseContext.setStaffShortName(staffShortName);
            IErpAdministrationMgtStaffService staffService = SpringBeanUtil.getBean(IErpAdministrationMgtStaffService.class);
            staffService.sycStaffData(entity);
        });
        return true;
    }




    /**
     * 新增和修改时字段校验
     *
     * @param ro
     */
    public void saveOrUpdateVerify(ErpAdministrationMgtStaffRO ro) {
        // 判断名称是否重复
        LambdaQueryWrapper<ErpAdministrationMgtStaff> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ErpAdministrationMgtStaff::getTenantGuid, ro.getTenantGuid())
                .eq(ErpAdministrationMgtStaff::getStaffShortName, ro.getStaffShortName())
                .eq(ErpAdministrationMgtStaff::getDeleted, false)
                .ne(ro.getStaffGuid() != null, ErpAdministrationMgtStaff::getStaffGuid, ro.getStaffGuid());
        if (baseMapper.exists(queryWrapper)) {
            throw new FlowException(ResultErrorCode.NO_REPETITION_ADMINISTRATION_STAFFNAME);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String staffGuid) {
        //查询是否绑定客户信息
        Integer aa = baseMapper.getUsedCustomer(staffGuid);
        if (aa > 0) {
            throw new FlowException(ResultErrorCode.NO_REPETITION_ADMINISTRATION_STAFFDATELE.getMsg(), ResultErrorCode.NO_REPETITION_ADMINISTRATION_STAFFDATELE.getCode());
        }
        super.removeById(staffGuid);
        LambdaQueryWrapper<ErpAdministrationMgtStaffFile> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpAdministrationMgtStaffFile::getStaffGuid, staffGuid);
        fileService.remove(wrapper);
        //根据员工id获取2.0的员工数据
        Staff staff = staffService.getById(staffGuid);
        if (Objects.nonNull(staff)) {
            if (StringUtils.isNotBlank(staff.getUserId())) {
                userService.removeById(staff.getUserId());
            }
            staffService.removeById(staffGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtStaffRO ro) {
        String creator = BaseContext.getCreator();
        String staffShortName = BaseContext.getStaffShortName();
        String creatorGuid = BaseContext.getCreatorGuid();
        String tenantGuid = BaseContext.getTenantGuid();
        String staffGuid = BaseContext.getStaffGuid();
        this.saveOrUpdateVerify(ro);
        ErpAdministrationMgtStaff entity = new ErpAdministrationMgtStaff();
        BeanUtil.copyProperties(ro, entity);
        if (ObjectUtils.isNull(entity.getDateOfBirth())) {
            baseMapper.updateDateOfBirth(entity.getStaffGuid(), null);
        }
        super.updateById(entity);
        if (CollUtil.isNotEmpty(ro.getFileList())){
            List<ErpAdministrationMgtStaffFileRO> collect = ro.getFileList().stream().peek(s -> s.setStaffGuid(entity.getStaffGuid())).collect(Collectors.toList());
            List<ErpAdministrationMgtStaffFile> list = BeanUtil.copyToList(collect, ErpAdministrationMgtStaffFile.class);
            fileService.saveOrUpdateBatch(list);
        }
        //同步员工信息到2.0
        ThreadUtil.execAsync(()->{
            BaseContext.setCreator(creator);
            BaseContext.setCreatorGuid(creatorGuid);
            BaseContext.setTenantGuid(tenantGuid);
            BaseContext.setStaffGuid(staffGuid);
            BaseContext.setStaffShortName(staffShortName);
            IErpAdministrationMgtStaffService staffService = SpringBeanUtil.getBean(IErpAdministrationMgtStaffService.class);
            staffService.sycStaffData(entity);
        });
        return true;
    }

    @Override
    public ErpAdministrationMgtStaffVO getById(String tenantGuid) {

        return baseMapper.getDataByGuid(tenantGuid);
    }

    @Override
    public List<ErpAdministrationMgtStaffVO> findList(ErpAdministrationMgtStaffQO qo, String tenantGuid) {
        List<ErpAdministrationMgtStaffVO> collect = baseMapper.findList(qo).stream().distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            //根据员工id获取文件信息
            List<String> collect1 = collect.stream().map(ErpAdministrationMgtStaffVO::getStaffGuid).collect(Collectors.toList());
            List<ErpAdministrationMgtStaffFileVO> fileList = fileService.getFileList(collect1);
            if (CollUtil.isNotEmpty(fileList)){
                Map<String, List<ErpAdministrationMgtStaffFileVO>> map = fileList.stream().collect(Collectors.groupingBy(ErpAdministrationMgtStaffFileVO::getStaffGuid));
                collect.forEach(x -> {
                    List<ErpAdministrationMgtStaffFileVO> vos = map.get(x.getStaffGuid());
                    if (CollUtil.isNotEmpty(vos)) {
                        x.setFileList(vos);
                    }
                });
            }
        }
        return collect;
    }

    @Override
    public IPage<ErpAdministrationMgtStaffVO> findPage(PageParams<ErpAdministrationMgtStaffQO> pageParams) {
        IPage<ErpAdministrationMgtStaffVO> page = pageParams.buildPage();
        ErpAdministrationMgtStaffQO model = pageParams.getModel();
        IPage<ErpAdministrationMgtStaffVO> page1 = baseMapper.findPage(page, model);
        List<ErpAdministrationMgtStaffVO> collect = page1.getRecords().stream().distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            //根据员工id获取文件信息
            List<String> collect1 = collect.stream().map(ErpAdministrationMgtStaffVO::getStaffGuid).collect(Collectors.toList());
            List<ErpAdministrationMgtStaffFileVO> fileList = fileService.getFileList(collect1);
            if (CollUtil.isNotEmpty(fileList)){
                Map<String, List<ErpAdministrationMgtStaffFileVO>> map = fileList.stream().collect(Collectors.groupingBy(ErpAdministrationMgtStaffFileVO::getStaffGuid));
                collect.forEach(x -> {
                    List<ErpAdministrationMgtStaffFileVO> vos = map.get(x.getStaffGuid());
                    if (CollUtil.isNotEmpty(vos)) {
                        x.setFileList(vos);
                    }
                });
            }
        }
        page1.setRecords(collect);
        return page1;
    }

    @Override
    public Boolean saveData(InsertOrUpdateList<ErpAdministrationMgtStaffRO> saveData, String tenantGuid) {
        if (CollUtil.isNotEmpty(saveData.getInsertList())) {
            saveData.getInsertList().forEach(x -> {
                x.setTenantGuid(tenantGuid);
                try {
                    this.create(x);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            });
        }

        if (CollUtil.isNotEmpty(saveData.getUpdateList())) {
            saveData.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public Boolean reordered(List<SortEntityDto> entityDtoList) {
////        entityDtoList.forEach(sortEntityDto -> {
////            LambdaUpdateWrapper<ErpAdministrationMgtStaff> updateWrapper = new LambdaUpdateWrapper<>();
////            updateWrapper.set(ErpAdministrationMgtStaff::getStaffGuid, sortEntityDto.getSerialNumber());
////            updateWrapper.eq(ErpAdministrationMgtStaff::getTenantGuid, sortEntityDto.getGuid());
////            update(updateWrapper);
//        });
        return true;
    }

    @Override
    public Boolean removeBatchByIdss(List<String> ids) {
        ids.forEach(this::delete);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resignation(ErpAdministrationMgtStaffResignationRO ro) {
        //type true:离职 false恢复
        LambdaUpdateWrapper<ErpAdministrationMgtStaff> wrapper = new LambdaUpdateWrapper<>();
        if (ro.getType()) {
            wrapper.eq(ErpAdministrationMgtStaff::getStaffGuid, ro.getStaffGuid())
                    .set(ErpAdministrationMgtStaff::getIsOnTheJob, true)
                    .set(ErpAdministrationMgtStaff::getDateOfResignation, ro.getDateOfResignation());
            super.update(wrapper);
            //修改用户关联客户资料
            ro.setIsOnTheJob(false);
            ro.setDateOfResignation(LocalDateTime.now());
            baseMapper.updateCustomer(ro.getStaffGuid());
            return staffResignationService.create(ro);
        } else {
            wrapper.eq(ErpAdministrationMgtStaff::getStaffGuid, ro.getStaffGuid())
                    .set(ErpAdministrationMgtStaff::getIsOnTheJob, true);
            super.update(wrapper);
            LambdaUpdateWrapper<ErpAdministrationMgtStaffResignation> wrapper1 = new LambdaUpdateWrapper<>();
            wrapper1.eq(ErpAdministrationMgtStaffResignation::getStaffGuid, ro.getStaffGuid())
                    .set(ErpAdministrationMgtStaffResignation::getDeleted, true);
            return staffResignationService.update(wrapper1);
        }
    }

    /**
     * 同步用户信息
     *
     * @param ro
     * @return
     */
    @Override
    public Boolean updateuserGuid(List<ErpAdministrationMgtStaffUserRo> ro) {
        ro.forEach(x -> {
            ErpAdministrationMgtStaff erpAdministrationMgtStaff = baseMapper.selectById(x.getStaffGuid());
            if (null != erpAdministrationMgtStaff) {
                erpAdministrationMgtStaff.setUserGuid(x.getUserGuid());
                baseMapper.updateById(erpAdministrationMgtStaff);
            }
        });
        return true;
    }

    @Override
    public List<ErpAdministrationMgtStaffVO> noUserAvailableList(ErpAdministrationMgtStaffQO qo, String tenantGuid) {
        return baseMapper.noUserAvailableList(qo);
    }

    /**
     * 获取部门及部门下的员工
     *
     * @param qo
     * @return
     */
    @Override
    public List<ErpAdministrationMgtDepartmentAndStaffVO> findStaffAndDepartmentList(ErpAdministrationMgtStaffQO qo) {
        return baseMapper.findStaffAndDepartmentList(qo);
    }

    @Override
    public List<ErpAuditorVo> getAuditorList(List<String> businessKey_procInstId) {
        return baseMapper.getAuditorList(businessKey_procInstId);
    }

    /**
     * 加载审核人
     *
     * @param detailVOList 主列表
     * @param pkGetter1    businessKey Getter
     * @param pkGetter2    procInstId Getter
     * @param setter       主列表 setter
     * @param <DetailVO>
     */
    @Override
    public <DetailVO> void loaAuditorTool(List<DetailVO> detailVOList,
                                          Function<DetailVO, String> pkGetter1,
                                          Function<DetailVO, String> pkGetter2,
                                          BiConsumer<DetailVO, String> setter) {
        if (ObjUtil.isEmpty(detailVOList)) {
            return;
        }

        detailVOList = detailVOList.stream().filter(Objects::nonNull).collect(Collectors.toList());

        List<String> ids = new ArrayList<>();
        detailVOList.forEach(v -> {
            String key1 = pkGetter1.apply(v);
            String key2 = pkGetter2.apply(v);
            ids.add(key1 + "_" + key2);
        });

        if (ObjUtil.isEmpty(ids)) {
            return;
        }

        Map<String, ErpAuditorVo> keyMap = getAuditorMap(ids);

        detailVOList.forEach(detailVO -> {
            if (ObjUtil.isEmpty(detailVO)) {
                return;
            }
            String key1 = pkGetter1.apply(detailVO);
            String key2 = pkGetter2.apply(detailVO);

            ErpAuditorVo auditorVo = keyMap.get(key1 + "_" + key2);
            if (ObjUtil.isNotNull(auditorVo)) {
                setter.accept(detailVO, auditorVo.getName());
            }
        });

    }

    @Override
    public Map<String, ErpAuditorVo> getAuditorMap(List<String> businessKey_procInstId) {
        if (CollUtil.isEmpty(businessKey_procInstId)) {
            return new HashMap<>(0);
        }
        List<ErpAuditorVo> auditorList = baseMapper.getAuditorList(businessKey_procInstId);
        Map<String, ErpAuditorVo> auditorVoMap = auditorList.stream()
                .collect(Collectors.toMap(v -> v.getBusinessKey() + "_" + v.getProcInstId(), u -> u, (o, n) -> o));
        return auditorVoMap;
    }

    @Override
    public CompletableFuture<Map<String, ErpAuditorVo>> getAuditorMapFeature(List<String> businessKey_procInstId, XyMemberDto xyMemberDto) {
        return CompletableFuture.supplyAsync(() -> {
            userApi.setThreadLocal(xyMemberDto);
            return getAuditorMap(businessKey_procInstId);
        });
    }

    @Override
    public Map<String, ErpAuditorVo> mapAuditMan(List<String> procInstIds) {
        if (ObjUtil.isEmpty(procInstIds)) {
            return Collections.emptyMap();
        }
        List<ErpAuditorVo> auditManVOS = baseMapper.selectAuditMan(procInstIds);
        return CollStreamUtil.toMap(auditManVOS, ErpAuditorVo::getProcInstId, Function.identity());
    }

    @Override
    public CompletableFuture<Map<String, ErpAuditorVo>> mapAuditorMapFeature(List<String> businessKeyProcInstId, XyMemberDto xyMemberDto) {
        return CompletableFuture.supplyAsync(() -> {
            userApi.setThreadLocal(xyMemberDto);
            return mapAuditMan(businessKeyProcInstId);
        });
    }

    @Override
    @Async
    public CompletableFuture<List<ErpAuditorVo>> getAuditorListAsync(List<String> businessKey_procInstId, XyMemberDto xyMemberDto) {
        if (CollUtil.isEmpty(businessKey_procInstId)) {
            return CompletableFuture.completedFuture(new ArrayList<>());
        }
        businessKey_procInstId = businessKey_procInstId.stream().distinct().collect(Collectors.toList());
        userApi.setThreadLocal(xyMemberDto);
        return CompletableFuture.completedFuture(baseMapper.getAuditorList(businessKey_procInstId));
    }

    @Override
    public CompletableFuture<Map<String, ErpAuditorVo>> getAuditorMapAsync(List<String> businessKey_procInstId, XyMemberDto xyMemberDto) {
        if (CollUtil.isEmpty(businessKey_procInstId)) {
            return CompletableFuture.completedFuture(new HashMap<>());
        }
        businessKey_procInstId = businessKey_procInstId.stream().distinct().collect(Collectors.toList());
        userApi.setThreadLocal(xyMemberDto);
        List<ErpAuditorVo> auditorList = baseMapper.getAuditorList(businessKey_procInstId);
        Map<String, ErpAuditorVo> auditorVoMap = auditorList.stream()
                .collect(Collectors.toMap(v -> v.getBusinessKey() + "_" + v.getProcInstId(), u -> u, (o, n) -> o));
        return CompletableFuture.completedFuture(auditorVoMap);
    }

    @Override
    public ErpAdministrationMgtStafSVO getStaff(String loginAccount) {
        ErpAdministrationMgtStaffVO staffGuid = baseMapper.getDataByGuid(BaseContext.getStaffGuid());
        ErpAdministrationMgtStafSVO vo = new ErpAdministrationMgtStafSVO();
        if (null != staffGuid) {
            vo.setStaffFullName(staffGuid.getStaffFullName());
        }
        vo.setLoginAccount(loginAccount);
        return vo;
    }


    /**
     * 处理导入Excel数据
     *
     * @param ro
     */
    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcelDataHandle(ErpAdministrationMgtStaffRO ro) {
        // 处理部门
        if (!StringUtils.isEmpty(ro.getDepartmentGuid())) {
            // 根据名称获取guid
            ErpAdministrationMgtDepartment department = iErpAdministrationMgtDepartmentService.getOne(Wrappers
                    .<ErpAdministrationMgtDepartment>lambdaQuery()
                    .select(ErpAdministrationMgtDepartment::getDepartmentGuid)
                    .eq(ErpAdministrationMgtDepartment::getDepartmentName, ro.getDepartmentGuid()));
            if (department == null || StringUtils.isEmpty(department.getDepartmentGuid())) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "部门【" + ro.getDepartmentGuid() + "】不存在！");
            }
            ro.setDepartmentGuid(department.getDepartmentGuid());
        }
        // 处理岗位
        if (!StringUtils.isEmpty(ro.getPostGuid())) {
            // 根据名称获取guid
            ErpAdministrationMgtPost post = iErpAdministrationMgtPostService.getOne(Wrappers
                    .<ErpAdministrationMgtPost>lambdaQuery()
                    .select(ErpAdministrationMgtPost::getPostGuid)
                    .eq(ErpAdministrationMgtPost::getPostName, ro.getPostGuid()));
            if (post == null || StringUtils.isEmpty(post.getPostGuid())) {
                throw new FlowException(BaseResultErrorCodeImpl.CUSTOM_ERROR, "岗位【" + ro.getPostGuid() + "】不存在！");
            }
            ro.setPostGuid(post.getPostGuid());
        }
        // 保存数据到数据库
        this.create(ro);
    }


    @Override
    public List<ErpAdministrationMgtStaffVO> selectMerchandiserOrSalesman(List<String> orderGuids) {
        if (ObjUtil.isEmpty(orderGuids)) {
            return Collections.emptyList();
        }
        return baseMapper.selectMerchandiserOrSalesman(orderGuids);
    }

    @Override
    public List<ErpAdministrationMgtStaffVO> listTeamStaffByTeamGuids(List<String> teamGuids) {
        return baseMapper.listTeamStaffByTeamGuids(teamGuids);
    }

    /**
     * 获取审核人信息
     *
     * @param procInstIds
     * @return
     */
    @Override
    public Map<String, ErpAuditorVo> mapNewAuditMan(List<String> procInstIds) {
        if (ObjUtil.isEmpty(procInstIds)) {
            return Collections.emptyMap();
        }
        List<ErpAuditorVo> auditManVOS = baseMapper.mapNewAuditMan(procInstIds);
        return CollStreamUtil.toMap(auditManVOS, ErpAuditorVo::getProcInstId, Function.identity());
    }

    /**
     * 同步员工信息到二点零
     *
     * @param entity
     */
    @Override
    public void sycStaffData(ErpAdministrationMgtStaff entity) {
        if (Objects.isNull(entity)){
            return;
        }
        //根据员工id获取员工信息
        Staff staff = staffService.getById(entity.getStaffGuid());
        //组装用户数据
        UserDTO userDTO = new UserDTO();
        userDTO.setUserShortName(entity.getStaffShortName());
        userDTO.setUserName(entity.getStaffFullName());
        if (Objects.isNull(staff)||(Objects.nonNull(staff)&&StringUtils.isBlank(staff.getUserId()))) {
            userDTO.setUserId(IdWorker.getIdStr());
        }else {
            userDTO.setUserId(staff.getUserId());
        }
        userDTO.setUserSex(entity.getGender());
        userDTO.setUserMobile(entity.getMobilephone());
        userDTO.setUserQq(entity.getQqNumber());
        userDTO.setUserWechatNumber(entity.getWechatNumber());
        userDTO.setUserEmail(entity.getEmail());
        if (Objects.nonNull(entity.getDateOfBirth())) {
            userDTO.setUserBirthdate(Date.from(entity.getDateOfBirth().atZone(ZoneId.systemDefault()).toInstant()));
        }
        userDTO.setUserBirthplace(entity.getBirthAddress());
        userDTO.setUserEducation(entity.getEducationalBackground());
        userDTO.setUserNativePlace(entity.getResidentialDistrict());
        userDTO.setUserPermanentAddress(entity.getPlaceOfOrigin());
        userDTO.setUserMaritalStatus(entity.getMaritalStatus().toString());
        userDTO.setUserNation(entity.getEthnicity());
        userDTO.setUserClazz(ClazzEnum.XY_USERCENTER_USER.name());
        userDTO.setUserTenantId(entity.getTenantGuid());
        userDTO.setUserIsolation(entity.getTenantGuid());
        userDTO.setUserHeight(String.valueOf(entity.getHeight()));
        userDTO.setUserWeight(String.valueOf(entity.getWeight()));
        userDTO.setUserIdentification(entity.getIdcard());
        userDTO.setUserTelephone(entity.getTelephone());
        userDTO.setUserLocationAddress(entity.getResidentialAddress());
        userDTO.setUserGraduationInstitution(entity.getGraduationInstitution());
        if (Objects.nonNull(entity.getGraduationDate())) {
            userDTO.setUserGraduationDate(Date.from(entity.getGraduationDate().atZone(ZoneId.systemDefault()).toInstant()));
        }
        userDTO.setUserPoliticalLandscape(entity.getPoliticalLandscape());
        userService.saveOrUpdate(userDTO);
        //保存员工信息
        StaffDTO staffDTO = new StaffDTO();
        staffDTO.setStaffId(entity.getStaffGuid());
        staffDTO.setStaffShortName(entity.getStaffShortName());
        staffDTO.setStaffFullName(entity.getStaffFullName());
        staffDTO.setStaffTelephone(entity.getTelephone());
        staffDTO.setStaffQqNumber(entity.getQqNumber());
        staffDTO.setStaffWechatId(entity.getWechatNumber());
        staffDTO.setStaffEmail(entity.getEmail());
        staffDTO.setStaffDepartmentId(entity.getDepartmentGuid());
        staffDTO.setStaffPostId(entity.getPostGuid());
        staffDTO.setStaffIdcard(entity.getIdcard());
        staffDTO.setStaffSex(entity.getGender());
        staffDTO.setStaffOpenBank(entity.getOpeningBank());
        staffDTO.setStaffBankAccount(entity.getBankAccount());
        staffDTO.setStaffPhone(entity.getMobilephone());
        staffDTO.setStaffBirthDate(entity.getDateOfBirth());
        staffDTO.setStaffEducationLevel(entity.getEducationalBackground());
        staffDTO.setStaffEthnicity(entity.getEthnicity());
        staffDTO.setStaffHeight(entity.getHeight());
        staffDTO.setStaffWeight(entity.getWeight());
        staffDTO.setStaffHkRegion(entity.getResidentialDistrict());
        staffDTO.setStaffUserId(userDTO.getUserId());
        staffDTO.setStaffTenantId(entity.getTenantGuid());
        staffDTO.setStaffIsolation(entity.getTenantGuid());
        staffDTO.setStaffClazz(ClazzEnum.STAFF.name());
        staffDTO.setStaffResidentialAddress(entity.getResidentialAddress());
        staffDTO.setStaffBirthAddress(entity.getBirthAddress());
        staffDTO.setStaffBirthAreaId(entity.getBirthAddressAdministrativeAreaGuid());
        staffDTO.setStaffEmergencyContact(entity.getEmergencyContact());
        staffDTO.setStaffContactRelation(entity.getEmergencyContactRelationship());
        staffDTO.setStaffMaritalStatus(entity.getMaritalStatus());
        staffDTO.setStaffHireDate(entity.getDateOfEmployment());
        staffDTO.setStaffEducationLevel(entity.getEducationalBackground());
        staffDTO.setStaffGraduationSchool(entity.getGraduationInstitution());
        staffDTO.setStaffGradDate(entity.getGraduationDate());
        staffDTO.setStaffPoliticalStatus(entity.getPoliticalLandscape());
        staffDTO.setStaffIsTemporary(entity.getIsTemporaryWork());
        staffDTO.setStaffJobStatus(entity.getIsOnTheJob());
        staffDTO.setStaffLeaveDate(entity.getDateOfResignation());
        staffService.saveOrUpdate(staffDTO);
    }

    /**
     * 根据审核人查询审核流数据
     *
     * @param qo
     * @return
     */
    @Override
    public List<ErpAuditorVo> selectAuditorByKey(ErpAuditorQO qo) {
        return baseMapper.selectAuditorByKey(qo).stream().distinct().collect(Collectors.toList());
    }

}
