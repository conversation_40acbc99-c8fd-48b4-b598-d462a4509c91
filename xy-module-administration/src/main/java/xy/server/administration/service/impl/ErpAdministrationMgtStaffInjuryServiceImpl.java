package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.enums.FormSourceEnum;
import com.xunyue.common.enums.StaffRecordsFileSourceEnum;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.config.util.PageParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.service.IProcessInstanceService;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.administration.entity.ErpAdministrationMgtStaffInjury;
import xy.server.administration.entity.ErpAdministrationMgtStaffRecordsFile;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffInjuryQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffInjuryRO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffRecordsFileRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffInjuryVO;
import xy.server.administration.mapper.ErpAdministrationMgtStaffInjuryMapper;
import xy.server.administration.service.IErpAdministrationMgtStaffInjuryService;
import xy.server.administration.service.IErpAdministrationMgtStaffRecordsFileService;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 工伤记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Service("STAFF_RECORDS.OCCUPATIONAL_INJURY_RECORDS")
public class ErpAdministrationMgtStaffInjuryServiceImpl extends ServiceImpl<ErpAdministrationMgtStaffInjuryMapper, ErpAdministrationMgtStaffInjury> implements IErpAdministrationMgtStaffInjuryService, ActEventStrategyService {

    @Autowired
    private IErpSystemMgtOrderSerialNumberService erpSystemMgtOrderSerialNumberService;
    @Autowired
    private IProcessInstanceService iProcessInstanceService;
    @Autowired
    private IErpAdministrationMgtStaffRecordsFileService fileService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtStaffInjuryRO ro) {
        ErpAdministrationMgtStaffInjury entity = new ErpAdministrationMgtStaffInjury();
        ro.setInjuryNumber(erpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.OCCUPATIONAL_INJURY_RECORDS));
        BeanUtil.copyProperties(ro, entity);
        if (super.save(entity)) {
            //插入单据图片
            if (CollectionUtils.isNotEmpty(ro.getRecordsFileROS())){
                List<ErpAdministrationMgtStaffRecordsFileRO> collect = ro.getRecordsFileROS().stream().peek(s -> {
                    s.setSourceGuid(entity.getStaffInjuryGuid());
                    s.setSourceValue(StaffRecordsFileSourceEnum.OCCUPATIONAL_INJURY.getKey());
                }).collect(Collectors.toList());
                fileService.saveBatch(BeanUtil.copyToList(collect, ErpAdministrationMgtStaffRecordsFile.class));
            }
            // 启动审核流程
            iProcessInstanceService.start(WorkflowKeyEnum.OCCUPATIONAL_INJURY_RECORDS, entity.getStaffInjuryGuid());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String staffInjuryGuid) {
        super.removeById(staffInjuryGuid);
        LambdaQueryWrapper<ErpAdministrationMgtStaffRecordsFile> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpAdministrationMgtStaffRecordsFile::getSourceGuid,staffInjuryGuid);
        fileService.remove(wrapper);
        iProcessInstanceService.deleteProcessAndHisInst(staffInjuryGuid, WorkflowKeyEnum.OCCUPATIONAL_INJURY_RECORDS);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> staffInjuryGuids) {
        for (String staffInjuryGuid : staffInjuryGuids) {
            super.removeById(staffInjuryGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtStaffInjuryRO ro) {
        ErpAdministrationMgtStaffInjury entity = new ErpAdministrationMgtStaffInjury();
        BeanUtil.copyProperties(ro, entity);
        if (super.updateById(entity)) {
            LambdaQueryWrapper<ErpAdministrationMgtStaffRecordsFile> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ErpAdministrationMgtStaffRecordsFile::getSourceGuid,entity.getStaffGuid());
            fileService.remove(wrapper);
            //插入单据图片
            if (CollectionUtils.isNotEmpty(ro.getRecordsFileROS())){
                List<ErpAdministrationMgtStaffRecordsFileRO> collect = ro.getRecordsFileROS().stream().peek(s -> {
                    s.setSourceGuid(entity.getStaffInjuryGuid());
                    s.setSourceValue(StaffRecordsFileSourceEnum.OCCUPATIONAL_INJURY.getKey());
                    s.setStaffRecordsFileGuid("");
                }).collect(Collectors.toList());
                fileService.saveBatch(BeanUtil.copyToList(collect, ErpAdministrationMgtStaffRecordsFile.class));
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffInjuryRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public ErpAdministrationMgtStaffInjuryVO getDataById(String staffInjuryGuid) {
        return baseMapper.getDataByGuid(staffInjuryGuid);
    }

    @Override
    public List<ErpAdministrationMgtStaffInjuryVO> findList(ErpAdministrationMgtStaffInjuryQO qo) {
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpAdministrationMgtStaffInjuryVO> findPage(PageParams<ErpAdministrationMgtStaffInjuryQO> pageParams) {
        IPage<ErpAdministrationMgtStaffInjuryVO> page = pageParams.buildPage();
        ErpAdministrationMgtStaffInjuryQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }
}
