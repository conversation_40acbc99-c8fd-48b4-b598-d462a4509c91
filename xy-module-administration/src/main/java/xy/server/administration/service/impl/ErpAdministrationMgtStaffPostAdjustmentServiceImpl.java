package xy.server.administration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.common.enums.FormSourceEnum;
import com.xunyue.common.service.IErpSystemMgtOrderSerialNumberService;
import com.xunyue.config.util.PageParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.service.IProcessInstanceService;
import xy.server.activiti.utils.actEvent.ActEventEntity;
import xy.server.activiti.utils.actEvent.ActEventStrategyService;
import xy.server.administration.entity.ErpAdministrationMgtStaff;
import xy.server.administration.entity.ErpAdministrationMgtStaffPostAdjustment;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffPostAdjustmentQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffPostAdjustmentRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffPostAdjustmentVO;
import xy.server.administration.mapper.ErpAdministrationMgtStaffPostAdjustmentMapper;
import xy.server.administration.service.IErpAdministrationMgtStaffPostAdjustmentService;
import xy.server.administration.service.IErpAdministrationMgtStaffService;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 调岗记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Service("STAFF_RECORDS.TRANSFER_RECORDS")
public class ErpAdministrationMgtStaffPostAdjustmentServiceImpl extends ServiceImpl<ErpAdministrationMgtStaffPostAdjustmentMapper, ErpAdministrationMgtStaffPostAdjustment> implements IErpAdministrationMgtStaffPostAdjustmentService, ActEventStrategyService {

    @Autowired
    private IErpSystemMgtOrderSerialNumberService erpSystemMgtOrderSerialNumberService;
    @Autowired
    private IProcessInstanceService iProcessInstanceService;
    @Autowired
    private IErpAdministrationMgtStaffService staffService;

    /**
     * 重启
     *
     * @param processVO
     */
    @Override
    public void restartCall(ActEventEntity processVO) {
        ErpAdministrationMgtStaffPostAdjustment adjustment = baseMapper.selectById(processVO.getBusinessKey());
        if(Objects.nonNull(adjustment)){
            LambdaUpdateWrapper<ErpAdministrationMgtStaff> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(ErpAdministrationMgtStaff::getStaffGuid,adjustment.getStaffGuid())
                    .set(ErpAdministrationMgtStaff::getPostGuid,adjustment.getOriginalPostGuid());
            staffService.update(wrapper);
        }
    }

    /**
     * 已完成
     *
     * @param processVO
     */
    @Override
    public void finishCall(ActEventEntity processVO) {
        ErpAdministrationMgtStaffPostAdjustment adjustment = baseMapper.selectById(processVO.getBusinessKey());
        if(Objects.nonNull(adjustment)){
            LambdaUpdateWrapper<ErpAdministrationMgtStaff> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(ErpAdministrationMgtStaff::getStaffGuid,adjustment.getStaffGuid())
                    .set(ErpAdministrationMgtStaff::getPostGuid,adjustment.getNewPostGuid());
            staffService.update(wrapper);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ErpAdministrationMgtStaffPostAdjustmentRO ro) {
        ErpAdministrationMgtStaffPostAdjustment entity = new ErpAdministrationMgtStaffPostAdjustment();
        ro.setPostAdjustmentNumber(erpSystemMgtOrderSerialNumberService.generateOrderNumber(FormSourceEnum.TRANSFER_RECORDS));
        BeanUtil.copyProperties(ro, entity);
        if (super.save(entity)) {
            iProcessInstanceService.start(WorkflowKeyEnum.TRANSFER_RECORDS, entity.getStaffPostAdjustmentGuid());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String staffPostAdjustmentGuid) {
        super.removeById(staffPostAdjustmentGuid);
        iProcessInstanceService.deleteProcessAndHisInst(staffPostAdjustmentGuid, WorkflowKeyEnum.TRANSFER_RECORDS);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBatch(List<String> staffPostAdjustmentGuids) {
        for (String staffPostAdjustmentGuid : staffPostAdjustmentGuids) {
            super.removeById(staffPostAdjustmentGuid);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ErpAdministrationMgtStaffPostAdjustmentRO ro) {
        ErpAdministrationMgtStaffPostAdjustment entity = new ErpAdministrationMgtStaffPostAdjustment();
        BeanUtil.copyProperties(ro, entity);
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffPostAdjustmentRO> dataList) {
        if (CollectionUtil.isNotEmpty(dataList.getInsertList())) {
            dataList.getInsertList().forEach(this::create);
        }

        if (CollectionUtil.isNotEmpty(dataList.getUpdateList())) {
            dataList.getUpdateList().forEach(this::update);
        }
        return true;
    }

    @Override
    public ErpAdministrationMgtStaffPostAdjustmentVO getDataById(String staffPostAdjustmentGuid) {
        return baseMapper.getDataByGuid(staffPostAdjustmentGuid);
    }

    @Override
    public List<ErpAdministrationMgtStaffPostAdjustmentVO> findList(ErpAdministrationMgtStaffPostAdjustmentQO qo) {
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpAdministrationMgtStaffPostAdjustmentVO> findPage(PageParams<ErpAdministrationMgtStaffPostAdjustmentQO> pageParams) {
        IPage<ErpAdministrationMgtStaffPostAdjustmentVO> page = pageParams.buildPage();
        ErpAdministrationMgtStaffPostAdjustmentQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }
}
