package xy.server.administration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.common.entity.model.InsertOrUpdateList;
import com.xunyue.config.util.PageParams;
import xy.server.administration.entity.ErpAdministrationMgtStaffTraining;
import xy.server.administration.entity.model.qo.ErpAdministrationMgtStaffTrainingQO;
import xy.server.administration.entity.model.ro.ErpAdministrationMgtStaffTrainingRO;
import xy.server.administration.entity.model.vo.ErpAdministrationMgtStaffTrainingVO;

import java.util.List;

/**
 * @apiNote 培训记录表 服务类
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface IErpAdministrationMgtStaffTrainingService extends IService<ErpAdministrationMgtStaffTraining> {
                                                            boolean create(ErpAdministrationMgtStaffTrainingRO ro);

    boolean delete(String staffTrainingGuid);

    boolean deleteByBatch(List<String> staffTrainingGuids);

    boolean update(ErpAdministrationMgtStaffTrainingRO ro);

    boolean saveDate(InsertOrUpdateList<ErpAdministrationMgtStaffTrainingRO> dataList);

    ErpAdministrationMgtStaffTrainingVO getDataById(String staffTrainingGuid);

    List<ErpAdministrationMgtStaffTrainingVO> findList(ErpAdministrationMgtStaffTrainingQO qo);

    IPage<ErpAdministrationMgtStaffTrainingVO> findPage(PageParams<ErpAdministrationMgtStaffTrainingQO> pageParams);
}
