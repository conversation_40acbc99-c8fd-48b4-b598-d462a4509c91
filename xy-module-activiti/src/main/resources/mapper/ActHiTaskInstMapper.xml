<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.activiti.mapper.ActHiTaskInstMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.activiti.entity.ActHiTaskInst">
        <result property="id" column="ID_"/>
        <result property="procDefId" column="PROC_DEF_ID_"/>
        <result property="taskDefKey" column="TASK_DEF_KEY_"/>
        <result property="procInstId" column="PROC_INST_ID_"/>
        <result property="executionId" column="EXECUTION_ID_"/>
        <result property="name" column="NAME_"/>
        <result property="parentTaskId" column="PARENT_TASK_ID_"/>
        <result property="description" column="DESCRIPTION_"/>
        <result property="owner" column="OWNER_"/>
        <result property="assignee" column="ASSIGNEE_"/>
        <result property="startTime" column="START_TIME_"/>
        <result property="claimTime" column="CLAIM_TIME_"/>
        <result property="endTime" column="END_TIME_"/>
        <result property="duration" column="DURATION_"/>
        <result property="deleteReason" column="DELETE_REASON_"/>
        <result property="priority" column="PRIORITY_"/>
        <result property="dueDate" column="DUE_DATE_"/>
        <result property="formKey" column="FORM_KEY_"/>
        <result property="category" column="CATEGORY_"/>
        <result property="tenantId" column="TENANT_ID_"/>
    </resultMap>
</mapper>
