<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.activiti.mapper.ErpFormPendingNumberMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="VoResultMap" type="xy.server.activiti.entity.model.vo.ErpFormPendingNumberVO">
        <id column="form_pending_number_guid" property="formPendingNumberGuid"/>
        <result column="tenant_guid" property="tenantGuid"/>
        <result column="form_key" property="formKey"/>
        <result column="form_type_value" property="formTypeValue"/>
        <result column="source_form_key" property="sourceFormKey"/>
        <result column="source_form_type_value" property="sourceFormTypeValue"/>
        <result column="pending_number" property="pendingNumber"/>
        <result column="view_filter_config_guid" property="viewFilterConfigGuid"/>
        <result column="creator_guid" property="creatorGuid"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
        <result column="last_updater_guid" property="lastUpdaterGuid"/>
        <result column="last_updater" property="lastUpdater"/>
        <result column="last_update_date" property="lastUpdateDate"/>
    </resultMap>


    <select id="findPage" resultMap="VoResultMap">
        select *
        from erp_form_pending_number
        order by create_date desc
    </select>

    <select id="findList" resultMap="VoResultMap">
        select *
        from erp_form_pending_number
        order by create_date desc
    </select>

    <select id="getDataByGuid" resultMap="VoResultMap">
        select *
        from erp_form_pending_number
        where form_pending_number_guid = #{guid}
    </select>

    <select id="executeSql" resultType="java.math.BigDecimal">
        ${sql}
    </select>

    <!--查询当前员工配置的待开数量列表-->
    <select id="selectCurrentStaffConfigPendingNumberList" resultMap="VoResultMap">
        SELECT
            efpn.*
        FROM
            erp_view_filter_user evfu
            LEFT JOIN erp_form_pending_number efpn ON efpn.view_filter_config_guid = evfu.view_filter_config_guid
        WHERE
            evfu.staff_guid = #{staffGuid}
            AND efpn.pending_number > 0
    </select>
</mapper>
