<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.activiti.mapper.ActHiActinstMapper">

    <delete id="deleteByActId">
        delete from act_hi_actinst where proc_inst_id_ = #{procInstId}
        <if test="actIds != null and actIds.size() != 0">
            and act_id_ in
            <foreach collection="actIds" item="id" index="index" open="(" separator="," close=")" >
                #{id}
            </foreach>
        </if>
        <if test="isFinish">
            and end_time_ IS NOT NULL
        </if>
    </delete>

    <select id="findHistoryInfoList" resultType="xy.server.activiti.entity.model.vo.ActHistoryInfoVO">
        -- 先查询待办列表
        (
            SELECT DISTINCT
                RES.id_ AS ID,
                RES.name_ AS NAME,
                RES.proc_inst_id_ AS processInstanceId,
                RES.assignee_ AS assignee,
                RES.start_time_ AS startTime,
                RES.end_time_ AS endTime,
                'untreated' AS TYPE,
                eams.staff_full_name AS nickName,
                NULL AS COMMENT
            FROM
                ACT_HI_TASKINST RES
                INNER JOIN ACT_HI_PROCINST HPI ON RES.proc_inst_id_ = HPI.id_
                INNER JOIN ACT_RE_PROCDEF D ON RES.proc_def_id_ = D.id_
                LEFT JOIN erp_administration_mgt_staff eams ON RES.assignee_ = eams.staff_guid
            WHERE
                D.key_ = #{ processDefinitionKey }
                AND HPI.business_key_ = #{ businessKey }
                AND RES.end_time_ IS NULL
            ORDER BY
                startTime DESC
        ) UNION ALL
        -- 	再查询已办列表
        (
            SELECT DISTINCT
                RES.id_ AS ID,
                RES.name_ AS NAME,
                RES.proc_inst_id_ AS processInstanceId,
                ( CASE WHEN C.type_ = 'restart' THEN C.USER_ID_ ELSE RES.assignee_ END ) AS assignee,
                ( CASE WHEN C.type_ = 'restart' THEN C.TIME_ ELSE RES.start_time_ END ) AS startTime,
                ( CASE WHEN C.type_ = 'restart' THEN C.TIME_ ELSE RES.end_time_ END ) AS endTime,
                C.type_ AS TYPE,
                eams.staff_full_name AS nickName,
                string_agg ( C.message_, '。' ) AS COMMENT
            FROM
                ACT_HI_TASKINST RES
                INNER JOIN ACT_HI_PROCINST HPI ON RES.proc_inst_id_ = HPI.id_
                INNER JOIN ACT_RE_PROCDEF D ON RES.proc_def_id_ = D.id_
                LEFT JOIN ACT_HI_COMMENT C ON RES.id_ = C.task_id_
                LEFT JOIN erp_administration_mgt_staff eams ON RES.assignee_ = eams.staff_guid
            WHERE
                D.key_ = #{ processDefinitionKey }
                AND HPI.business_key_ = #{ businessKey }
                AND RES.end_time_ IS NOT NULL
                AND C.type_ != 'event'
            GROUP BY
                RES.id_,
                C.type_,
                C.TIME_,
                C.USER_ID_,
                eams.staff_full_name
            ORDER BY
                endTime DESC
        )
    </select>
    <select id="batchGetLatestHistoryInfo" resultType="xy.server.activiti.entity.model.vo.ActHistoryInfoVO">
        SELECT
            *
        FROM
            (
                SELECT
                    *,
                    ROW_NUMBER ( ) OVER ( PARTITION BY processInstanceId, businessKey ORDER BY endTime DESC ) AS rn
                FROM
                    (
                        -- 先查询待办列表
                        (
                            SELECT DISTINCT
                                RES.id_ AS ID,
                                RES.name_ AS NAME,
                                RES.proc_inst_id_ AS processInstanceId,
                                HPI.business_key_ AS businessKey,
                                RES.assignee_ AS assignee,
                                RES.start_time_ AS startTime,
                                RES.end_time_ AS endTime,
                                'untreated' AS TYPE,
                                eams.staff_full_name AS nickName,
                                NULL AS COMMENT
                            FROM
                                ACT_HI_TASKINST RES
                                INNER JOIN ACT_HI_PROCINST HPI ON RES.proc_inst_id_ = HPI.id_
                                INNER JOIN ACT_RE_PROCDEF D ON RES.proc_def_id_ = D.id_
                                LEFT JOIN erp_system_mgt_form_source_type_value esmfstv ON D.deployment_id_ = esmfstv.act_repository_guid
                                LEFT JOIN erp_system_mgt_form_source esmfs ON esmfs.form_source_guid = esmfstv.form_source_guid
                                LEFT JOIN erp_administration_mgt_staff eams ON RES.assignee_ = eams.staff_guid
                            WHERE
                                RES.end_time_ IS NULL
                                AND esmfstv.form_source_type_value = #{formSourceTypeValue}
                                AND esmfs.form_source_key = #{formSourceKey}
                                AND HPI.business_key_ IN
                                <foreach collection="businessKeys" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            ORDER BY
                                startTime DESC
                        ) UNION ALL
                        -- 	再查询已办列表
                        (
                            SELECT DISTINCT
                                RES.id_ AS ID,
                                RES.name_ AS NAME,
                                RES.proc_inst_id_ AS processInstanceId,
                                HPI.business_key_ AS businessKey,
                                ( CASE WHEN C.type_ = 'restart' THEN C.USER_ID_ ELSE RES.assignee_ END ) AS assignee,
                                ( CASE WHEN C.type_ = 'restart' THEN C.TIME_ ELSE RES.start_time_ END ) AS startTime,
                                ( CASE WHEN C.type_ = 'restart' THEN C.TIME_ ELSE RES.end_time_ END ) AS endTime,
                                C.type_ AS TYPE,
                                eams.staff_full_name AS nickName,
                                string_agg ( C.message_, '。' ) AS COMMENT
                            FROM
                                ACT_HI_TASKINST RES
                                INNER JOIN ACT_HI_PROCINST HPI ON RES.proc_inst_id_ = HPI.id_
                                INNER JOIN ACT_RE_PROCDEF D ON RES.proc_def_id_ = D.id_
                                LEFT JOIN ACT_HI_COMMENT C ON RES.id_ = C.task_id_
                                LEFT JOIN erp_system_mgt_form_source_type_value esmfstv ON D.deployment_id_ = esmfstv.act_repository_guid
                                LEFT JOIN erp_system_mgt_form_source esmfs ON esmfs.form_source_guid = esmfstv.form_source_guid
                                LEFT JOIN erp_administration_mgt_staff eams ON RES.assignee_ = eams.staff_guid
                            WHERE
                                RES.end_time_ IS NOT NULL
                                AND C.type_ != 'event'
                                AND esmfstv.form_source_type_value = #{formSourceTypeValue}
                                AND esmfs.form_source_key = #{formSourceKey}
                                AND HPI.business_key_ IN
                                <foreach collection="businessKeys" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            GROUP BY
                                RES.id_,
                                HPI.id_,
                                C.type_,
                                C.TIME_,
                                C.USER_ID_,
                                eams.staff_full_name
                            ORDER BY
                                endTime DESC
                        )
                    ) sub_table
            ) final_table
        WHERE rn = 1
    </select>
</mapper>