<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xy.server.activiti.mapper.ActBusinessFormMapper">
    <update id="updateTabAuditState">
        update ${workflowKeyEnum.tableName}
        set process_instance_id=#{processInstance.id},audit_status=#{businessStatusEnum.status}
        <if test="businessStatusEnum.status != null and businessStatusEnum.status != '' and businessStatusEnum.status.equals('finish')">
            ,audit_date=now()
        </if>
        <if test="businessStatusEnum.status != null and businessStatusEnum.status != '' and !businessStatusEnum.status.equals('finish')">
            ,audit_date=null
        </if>
        where ${workflowKeyEnum.tableIdFieldName} = #{processInstance.businessKey}
    </update>
    <update id="deleteProcessFields">
        update ${workflowKeyEnum.tableName}
        set process_instance_id = null,
            audit_status        = 'draft',
            audit_date          = null
        where ${workflowKeyEnum.tableIdFieldName} = #{businessKey}
    </update>

    <select id="selectTabAuditState" resultType="java.lang.String">
        select audit_status
        from ${workflowKeyEnum.tableName}
        where ${workflowKeyEnum.tableIdFieldName} = #{businessKey}
        limit 1
    </select>

    <!--查询业务工单流程实例id-->
    <select id="selectTabProcessInstanceId" resultType="java.lang.String">
        select DISTINCT process_instance_id
        from ${workflowKeyEnum.tableName}
        where ${workflowKeyEnum.tableIdFieldName} = #{businessKey}
        limit 1
    </select>

    <select id="getProcessInstanceKey" resultType="java.lang.String">
        SELECT key_
        FROM act_re_procdef
        WHERE deployment_id_ = #{deploymentId}
    </select>

    <!--根据businessKey获取流程定义Key-->
    <select id="getProcessDefinitionKeyByBusinessKey" resultType="java.lang.String">
        SELECT
            key_
        FROM
            act_re_procdef
        WHERE
            id_ = ( SELECT DISTINCT proc_def_id_ FROM act_hi_procinst WHERE business_key_ = #{businessKey} )
    </select>

    <select id="getDeploymentKeyById" resultType="java.lang.String">
        select key_
        from act_re_deployment
        where id_ = #{deploymentId}
    </select>
    <select id="getAssigneeNumber" resultType="java.lang.String">
        select *
        from ${workflowKeyEnum.tableName}
        where ${workflowKeyEnum.tableIdFieldName} = #{businessKey}
        limit 1
    </select>

    <!--根据staffGuids和platform获取ReceiverIds-->
    <select id="getReceiverIdsByStaffGuidsAndPlatform" resultType="java.lang.String">
        select DISTINCT
            receiver_id
        from
            erp_message_user_bind
        where
            staff_guid in
            <foreach collection="staffGuids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and platform = #{platform}
            and (receiver_id is not null or receiver_id != '')
    </select>
    <!--根据流程定义id获取表单定义key和value-->
    <select id="getFormSourceByProcessDefinitionId" resultType="java.util.Map">
        SELECT
            esmfs.form_source_key,
            esmfsty.form_source_type_value
        FROM
            act_re_procdef arp
            LEFT JOIN erp_system_mgt_form_source_type_value esmfsty ON esmfsty.act_repository_guid = arp.deployment_id_
            LEFT JOIN erp_system_mgt_form_source esmfs ON esmfs.form_source_guid = esmfsty.form_source_guid
        WHERE arp.id_ = #{processDefinitionId}
    </select>

    <!--通过流程实例id查询业务工单审核状态-->
    <select id="getTabAuditStateByProcessInstanceId" resultType="java.lang.String">
        SELECT
            tab.audit_status
        FROM
            ${workflowKeyEnum.tableName} tab
            LEFT JOIN ACT_RU_EXECUTION RES ON tab.process_instance_id = RES.PROC_INST_ID_
            AND tab.${workflowKeyEnum.tableIdFieldName} = RES.business_key_
        WHERE
            RES.PARENT_ID_ IS NULL
            AND RES.ID_ = #{processInstanceId}
            AND RES.PROC_INST_ID_ = #{processInstanceId}
    </select>

    <!--判断流程是否变动-->
    <select id="selectProcessIsChanged" resultType="java.lang.Boolean">
        SELECT
            CASE
                WHEN COUNT(1) = 0 THEN TRUE ELSE FALSE
            END
        FROM
            erp_system_mgt_form_source_type_value esmfstv
            LEFT JOIN erp_system_mgt_form_source esmfs ON esmfstv.form_source_guid = esmfs.form_source_guid
            LEFT JOIN act_re_procdef act_re_pro ON act_re_pro.deployment_id_ = esmfstv.act_repository_guid
            LEFT JOIN act_ru_execution act_ru_exe ON act_ru_exe.proc_def_id_ = act_re_pro.id_
        WHERE
            esmfs.form_source_key = #{auditProcessRO.formSourceKey}
            AND esmfstv.form_source_type_value = #{auditProcessRO.formSourceTypeValue}
            AND act_ru_exe.proc_inst_id_ = #{auditProcessRO.processInstanceId}
            AND act_ru_exe.business_key_ = #{auditProcessRO.businessKey}
    </select>
</mapper>
