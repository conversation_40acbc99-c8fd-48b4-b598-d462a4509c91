package xy.server.activiti.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunyue.config.util.PageParams;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.entity.ErpFormPendingNumber;
import xy.server.activiti.entity.model.qo.ErpFormPendingNumberQO;
import xy.server.activiti.entity.model.vo.ErpFormPendingNumberVO;
import xy.server.activiti.entity.model.vo.ProcessNavigationMessageDTO;
import xy.server.filter.enums.TableIdentificationEnum;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @apiNote 待开数量记录表 服务类
 * @since 2024-01-30
 */
@Valid
public interface IErpFormPendingNumberService extends IService<ErpFormPendingNumber> {

    /**
     * 更新待开数量（旧版，全部引用新接口后删除）
     * @param workflowKeyEnum 表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     * @param pendingNumber 待开数量
     */
    void updatePendingNumber(@NotNull WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum, BigDecimal pendingNumber);

    /**
     * 更新待开数量
     * @param workflowKeyEnum 表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     * @param pendingNumber 待开数量
     * @param tableIdentificationEnum 表格标识枚举
     */
    default void updatePendingNumber(@NotNull WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum, BigDecimal pendingNumber,
                                     @NotNull TableIdentificationEnum tableIdentificationEnum) {
        this.updatePendingNumber(workflowKeyEnum, sourceWorkflowKeyEnum, pendingNumber, tableIdentificationEnum, null);
    }

    /**
     * 更新待开数量
     * @param workflowKeyEnum 表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     * @param pendingNumber 待开数量
     * @param tableIdentificationEnum 表格标识枚举
     * @param paramMap 参数Map
     */
    void updatePendingNumber(@NotNull WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum, BigDecimal pendingNumber,
                             @NotNull TableIdentificationEnum tableIdentificationEnum, Map<String, String> paramMap);

    /**
     * 更新待开数量（发布事件出去，由业务自行计算待开数量）（旧版，全部引用新接口后删除）
     * @param workflowKeyEnum 表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     */
    void updatePendingNumber(@NotNull WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum);

    /**
     * 更新待开数量（发布事件出去，由业务自行计算待开数量）
     * @param workflowKeyEnum 表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     * @param tableIdentificationEnum 表格标识枚举
     */
    default void updatePendingNumber(@NotNull WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum,
                             @NotNull TableIdentificationEnum tableIdentificationEnum) {
        this.updatePendingNumber(workflowKeyEnum, sourceWorkflowKeyEnum, tableIdentificationEnum, null);
    }

    /**
     * 更新待开数量（发布事件出去，由业务自行计算待开数量）
     * @param workflowKeyEnum 表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     * @param tableIdentificationEnum 表格标识枚举
     * @param paramMap 参数Map
     */
    void updatePendingNumber(@NotNull WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum,
                             @NotNull TableIdentificationEnum tableIdentificationEnum, Map<String, String> paramMap);

    /**
     * 待开数量+1（旧版，全部引用新接口后删除）
     * @param workflowKeyEnum 表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     */
    void plusOne(@NotNull WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum);

    /**
     * 待开数量+1
     * @param workflowKeyEnum 表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     * @param tableIdentificationEnum 表格标识枚举
     */
    default void plusOne(@NotNull WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum,
                 @NotNull TableIdentificationEnum tableIdentificationEnum) {
        this.plusOne(workflowKeyEnum,sourceWorkflowKeyEnum,tableIdentificationEnum, null);
    }

    /**
     * 待开数量+1
     * @param workflowKeyEnum 表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     * @param tableIdentificationEnum 表格标识枚举
     * @param paramMap 参数Map
     */
    void plusOne(@NotNull WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum,
                 @NotNull TableIdentificationEnum tableIdentificationEnum, Map<String, String> paramMap);

    /**
     * 待开数量-1（旧版，全部引用新接口后删除）
     * @param workflowKeyEnum 表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     */
    void minusOne(@NotNull WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum);

    /**
     * 待开数量-1
     * @param workflowKeyEnum 表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     * @param tableIdentificationEnum 表格标识枚举
     */
    default void minusOne(@NotNull WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum,
                  @NotNull TableIdentificationEnum tableIdentificationEnum) {
        this.minusOne(workflowKeyEnum, sourceWorkflowKeyEnum, tableIdentificationEnum, null);
    }

    /**
     * 待开数量-1
     * @param workflowKeyEnum 表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     * @param tableIdentificationEnum 表格标识枚举
     * @param paramMap 参数Map
     */
    void minusOne(@NotNull WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum,
                  @NotNull TableIdentificationEnum tableIdentificationEnum, Map<String, String> paramMap);

    /**
     * 查询表单待开数量列表
     * @return
     */
    List<ErpFormPendingNumberVO> findCurrentStaffPendingNumberList();
    List<ErpFormPendingNumberVO> findList(ErpFormPendingNumberQO qo);

    IPage<ErpFormPendingNumberVO> findPage(PageParams<ErpFormPendingNumberQO> pageParams);

    /**
     * 查询当前员工流程导航消息
     * @return
     */
    ProcessNavigationMessageDTO getProcessNavigationMessage();
}
