package xy.server.activiti.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.xunyue.activiti.config.CustomProcessDiagramGenerator;
import com.xunyue.activiti.config.ICustomProcessDiagramGenerator;
import com.xunyue.activiti.config.WorkflowConstants;
import com.xunyue.activiti.entity.ActNodeAssignee;
import com.xunyue.activiti.service.IActNodeAssigneeService;
import com.xunyue.common.i18n.BaseResultErrorCodeImpl;
import com.xunyue.config.exception.FlowException;
import com.xy.util.BaseContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.bpmn.model.FlowNode;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.runtime.Execution;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.activiti.cmd.AddCommentCmd;
import xy.server.activiti.cmd.DeleteTaskCmd;
import xy.server.activiti.common.constant.ActConstant;
import xy.server.activiti.common.enums.BusinessStatusEnum;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.entity.model.ro.ActAuditProcessRO;
import xy.server.activiti.entity.model.vo.ActHistoryInfoVO;
import xy.server.activiti.entity.model.vo.ActMessageDTO;
import xy.server.activiti.entity.model.vo.ActReturnResultVO;
import xy.server.activiti.factory.WorkflowService;
import xy.server.activiti.i18n.ResultErrorCode;
import xy.server.activiti.mapper.ActBusinessFormMapper;
import xy.server.activiti.service.IActHiActinstService;
import xy.server.activiti.service.IProcessInstanceService;
import xy.server.activiti.service.ITaskService;
import xy.server.activiti.utils.WorkFlowUtils;
import xy.server.basic.entity.ErpSystemMgtFormSourceTypeValue;
import xy.server.basic.mapper.ErpSystemMgtFormSourceTypeValueMapper;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProcessInstanceServiceImpl extends WorkflowService implements IProcessInstanceService {

    private final IActNodeAssigneeService iActNodeAssigneeService;
    private final ErpSystemMgtFormSourceTypeValueMapper mgtFormSourceTypeValueMapper;
    private final ActBusinessFormMapper actBusinessFormMapper;
    private final IActHiActinstService iActHiActinstService;
    private final ITaskService iTaskService;
    private final WorkFlowUtils workFlowUtils;

    /**
     * 提交审核（申请人节点）
     * @param auditProcessRO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActReturnResultVO commitAudit(ActAuditProcessRO auditProcessRO) {
        if (StringUtils.isEmpty(auditProcessRO.getProcessInstanceId())) {
            // 如果流程实例为空，则先调用启动流程接口，再继续提交
            WorkflowKeyEnum workflowKeyEnum = WorkflowKeyEnum.getEumByKey(auditProcessRO.getFormSourceKey(), auditProcessRO.getFormSourceTypeValue());
            // 先删除旧流程（如果存在）
            this.deleteProcessAndHisInst(auditProcessRO.getBusinessKey(), workflowKeyEnum);
            ActReturnResultVO actReturnResultVO = this.start(workflowKeyEnum, auditProcessRO.getBusinessKey());
            if (!BusinessStatusEnum.DRAFT.getStatus().equals(actReturnResultVO.getAuditStatus())) {
                // 如果启动流程后，流程状态 != "draft"，则直接返回，不继续提交（兼容自动提交）
                return actReturnResultVO;
            }
            auditProcessRO.setProcessInstanceId(actReturnResultVO.getProcessInstanceId());
        }

        // 查询当前节点是否是【申请人】节点
        List<Task> tasks = taskService.createTaskQuery().processInstanceId(auditProcessRO.getProcessInstanceId())
                .processInstanceBusinessKey(auditProcessRO.getBusinessKey()).active().list();
        // 从流程变量中获取【申请人】节点activity_id
        String startActivityId = (String) runtimeService.getVariable(auditProcessRO.getProcessInstanceId(), ActConstant.START_ACTIVITY_ID);
        if (CollectionUtil.isEmpty(tasks) || tasks.size() > 1 || !tasks.get(0).getTaskDefinitionKey().equals(startActivityId)) {
            throw new FlowException(ResultErrorCode.PROCESS_IS_NOT_ALLOWED_COMMIT_Audit);
        }
        // 没有指定审核人时，需要先认领任务
        if (StringUtils.isEmpty(tasks.get(0).getAssignee())) {
            // 现在启动流程不限制提交人，所以提交时需要先认领任务
            taskService.claim(tasks.get(0).getId(), BaseContext.getStaffGuid());
        }
        return this.audit(true, auditProcessRO);
    }

    /**
     * 撤销提交
     * @param auditProcessRO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActReturnResultVO cancelCommitAudit(ActAuditProcessRO auditProcessRO) {
        // 查询历史节点
        List<HistoricActivityInstance> historicActivityInstanceList = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(auditProcessRO.getProcessInstanceId()).finished().activityType(ActConstant.USER_TASK)
                .orderByHistoricActivityInstanceEndTime().desc().list();

        // 从流程变量中获取【申请人】节点activity_id
        String startActivityId = (String) historyService.createHistoricVariableInstanceQuery()
                .processInstanceId(auditProcessRO.getProcessInstanceId()).variableName(ActConstant.START_ACTIVITY_ID).singleResult().getValue();

        if (CollectionUtil.isEmpty(historicActivityInstanceList) || !historicActivityInstanceList.get(0).getActivityId().equals(startActivityId)) {
            throw new FlowException(ResultErrorCode.PROCESS_IS_NOT_ALLOWED_CANCEL_COMMIT_Audit);
        }
        return this.cancelAudit(auditProcessRO);
    }

    /**
     * 审核（通过/驳回）
     * @param auditProcessRO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActReturnResultVO audit(Boolean result, ActAuditProcessRO auditProcessRO) {
        // 校验流程
        this.verifyProcess(auditProcessRO);

        // 1.查询任务
        TaskQuery taskQuery = taskService.createTaskQuery().taskCandidateOrAssigned(BaseContext.getStaffGuid())
                .processInstanceBusinessKey(auditProcessRO.getBusinessKey())
                .processInstanceId(auditProcessRO.getProcessInstanceId());
        if (StringUtils.isNotEmpty(auditProcessRO.getTaskId())) {
            taskQuery.taskId(auditProcessRO.getTaskId());
        }
        Task task = taskQuery.active().singleResult();

        if (ObjectUtil.isNull(task)) {
            throw new FlowException(ResultErrorCode.TASK_DOE_NOT_EXIST);
        }

        if (task.isSuspended()) {
            throw new FlowException(ResultErrorCode.TASK_HAS_BEEN_SUSPENDED);
        }

        return result ? iTaskService.completeTask(auditProcessRO, task) : iTaskService.rejectTask(auditProcessRO, task);
    }

    /**
     * 撤销审核
     *
     * @param auditProcessRO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActReturnResultVO cancelAudit(ActAuditProcessRO auditProcessRO) {
        // 校验流程
        this.verifyProcess(auditProcessRO);

        // 撤审时【审批意见】不能为空
        if (StringUtils.isEmpty(auditProcessRO.getComment())) {
            throw new FlowException(ResultErrorCode.COMMENT_IS_EMPTY);
        }
        // 校验流程实例是否存在
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(auditProcessRO.getProcessInstanceId())
                .processInstanceBusinessKey(auditProcessRO.getBusinessKey()).active().singleResult();
        if (ObjectUtil.isNull(processInstance)) {
            throw new FlowException(ResultErrorCode.NOT_PROCESS_INSTANCE);
        }
        // 当前登录员工guid
        String currentStaffGuid = BaseContext.getStaffGuid();
        // 查询历史节点
        List<HistoricActivityInstance> historicActivityInstanceList = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(processInstance.getId()).finished().activityType(ActConstant.USER_TASK)
                .orderByHistoricActivityInstanceEndTime().desc().list();
        if (CollectionUtil.isEmpty(historicActivityInstanceList)) {
            throw new FlowException(ResultErrorCode.NOT_ALLOW_CANCEL);
        }
        // 获取最后一个已完成的节点
        HistoricActivityInstance latestActivity = historicActivityInstanceList.get(0);
        String startActivityId = (String) runtimeService.getVariable(processInstance.getId(), ActConstant.START_ACTIVITY_ID);
        // 撤回到【申请人申请】环节单独判断
        //if (latestActivity.getActivityId().equals(startActivityId) && !latestActivity.getAssignee().equals(currentStaffGuid)) {
        //    throw new FlowException(ResultErrorCode.NOT_IS_ASSIGNEE);
        //}
        // 先判断当前员工是否是latestActivity节点的办里人
        if (!latestActivity.getActivityId().equals(startActivityId) && !latestActivity.getAssignee().equals(currentStaffGuid)) {
            // 再当前员工在最后【已完成】节点配置的审核人列表，则不允许撤回
            if (!workFlowUtils.isAssignee(latestActivity.getProcessDefinitionId(), latestActivity.getActivityId())) {
                throw new FlowException(ResultErrorCode.NOT_IS_ASSIGNEE);
            }
        }
        // 获取当前流程所有节点配置信息列表
        List<ActNodeAssignee> actNodeAssigneeList = iActNodeAssigneeService.getInfoByProcessDefinitionId(processInstance.getProcessDefinitionId());
        // 查询当前待办任务列表
        List<Task> oldTaskList = taskService.createTaskQuery().processInstanceId(processInstance.getId()).list();
        // 获取流程模型实例 BpmnModel
        BpmnModel bpmnModel = repositoryService.getBpmnModel(latestActivity.getProcessDefinitionId());
        // 获取目标节点信息
        FlowNode targetFlowNode = (FlowNode) bpmnModel.getFlowElement(latestActivity.getActivityId());

        // 获取所有下一任务节点对应的FlowElement
        List<String> nextNodeIds = workFlowUtils.getNextNode(targetFlowNode).stream().map(FlowNode::getId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(nextNodeIds) || CollectionUtil.isEmpty(oldTaskList)) {
            throw new FlowException(ResultErrorCode.NOT_ALLOW_CANCEL);
        }
        for (String nextNodeId : nextNodeIds) {
            // 查询下一节点中是否存在已办，有则不允许撤回
            List<HistoricActivityInstance> nextNodeHistoricActivityList = historyService.createHistoricActivityInstanceQuery()
                    .processInstanceId(processInstance.getId()).finished().activityId(nextNodeId).list();
            if (CollectionUtil.isNotEmpty(nextNodeHistoricActivityList)) {
                throw new FlowException(ResultErrorCode.NOT_ALLOW_CANCEL);
            }
        }
        ActReturnResultVO actReturnResultVO = new ActReturnResultVO();
        actReturnResultVO.setProcessInstanceId(processInstance.getId());

        String deploymentKey = workFlowUtils.getDeploymentKeyById(processInstance.getDeploymentId());
        WorkflowKeyEnum workflowKeyEnum = WorkflowKeyEnum.getEumByKey(deploymentKey);
        // 发布事件-->撤回 让业务工单层先处理
        workFlowUtils.publishActEvent(workflowKeyEnum.getServiceName(), BusinessStatusEnum.CANCEL, processInstance.getBusinessKey(), actReturnResultVO);
        // 撤回其中一个节点的待办
        Task task = oldTaskList.get(0);
        FlowNode taskNode = (FlowNode) bpmnModel.getFlowElement(task.getTaskDefinitionKey());

        String comment = auditProcessRO.getComment() + "=>撤回到【" + targetFlowNode.getName() + "】";
        AddCommentCmd addCommentCmd = new AddCommentCmd(task.getId(), task.getProcessInstanceId(), ActConstant.CANCEL_AUDIT_COMMENT_TYPE, comment);
        // 流程回退
        workFlowUtils.processRollback(task, taskNode, targetFlowNode, addCommentCmd);

        // 12.删除其他待办任务
        List<Task> newTaskList = taskService.createTaskQuery().processInstanceId(processInstance.getId()).orderByTaskCreateTime().desc().list();
        for (Task newTask : newTaskList) {
            if (StringUtils.isBlank(newTask.getParentTaskId()) && nextNodeIds.contains(newTask.getTaskDefinitionKey())) {
                DeleteTaskCmd deleteTaskCmd = new DeleteTaskCmd(newTask.getId());
                processEngine.getManagementService().executeCommand(deleteTaskCmd);
            }
        }
        // 13.查询最新待办任务并设置办理人或候选人
        Task newTask = taskService.createTaskQuery().processInstanceId(processInstance.getId()).taskDefinitionKey(targetFlowNode.getId()).singleResult();

        for (Task oldTask : oldTaskList) {
            ActNodeAssignee actNodeAssignee = actNodeAssigneeList.stream().filter(e -> oldTask.getTaskDefinitionKey().equals(e.getNodeId())).findFirst().orElse(null);
            // 发送外部消息平台消息
            BusinessStatusEnum businessStatusEnum = newTask.getTaskDefinitionKey().equals(startActivityId) ? BusinessStatusEnum.DRAFT : BusinessStatusEnum.CANCEL;
            workFlowUtils.sendPlatformMessage(workflowKeyEnum, businessStatusEnum, actNodeAssignee);
            // 发送消息，旧待办任务列表所有相关员工待办事项-1
            workFlowUtils.sendMessage(workFlowUtils.getAssigneeIdList(actNodeAssignee), workflowKeyEnum, BusinessStatusEnum.WAITING, processInstance.getBusinessKey(), false);
        }

        if (newTask.getTaskDefinitionKey().equals(startActivityId)) {
            // 最新节点为【申请人申请】节点单独处理
            // 获取最新历史申请人节点办理人

            List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery()
                    .processInstanceId(processInstance.getId()).taskDefinitionKey(newTask.getTaskDefinitionKey())
                    .finished().orderByHistoricTaskInstanceEndTime().desc().list();
            String startActivityAssignee = historicTaskInstanceList.get(0).getAssignee();
            // 注释代码原因：【申请人】节点不限制提交人
            // taskService.setAssignee(newTask.getId(), startActivityAssignee);
            // 14. 更新业务状态
            actBusinessFormMapper.updateTabAuditState(workflowKeyEnum, processInstance, BusinessStatusEnum.DRAFT);
            actReturnResultVO.setAuditStatus(BusinessStatusEnum.DRAFT.getStatus());
            // 发送消息，申请人待办事项+1
            workFlowUtils.sendMessage(startActivityAssignee, workflowKeyEnum, BusinessStatusEnum.DRAFT, processInstance.getBusinessKey(), true);
        } else {
            ActNodeAssignee actNodeAssignee = actNodeAssigneeList.stream().filter(e -> newTask.getTaskDefinitionKey().equals(e.getNodeId())).findFirst().orElse(null);
            workFlowUtils.settingAssignee(newTask, actNodeAssignee);
            // 查询历史活动节点条数，判断是审核中还是待审核
            long count = historyService.createHistoricActivityInstanceQuery()
                    .processInstanceId(processInstance.getProcessInstanceId()).activityType(ActConstant.USER_TASK).finished().count();
            BusinessStatusEnum businessStatusEnum = count > 1 ? BusinessStatusEnum.IN_REVIEW : BusinessStatusEnum.WAITING;
            // 14. 更新业务状态
            actBusinessFormMapper.updateTabAuditState(workflowKeyEnum, processInstance, businessStatusEnum);
            actReturnResultVO.setAuditStatus(businessStatusEnum.getStatus());
            // 发送消息，相关办理人待办事项+1
            workFlowUtils.sendMessage(workFlowUtils.getAssigneeIdList(actNodeAssignee), workflowKeyEnum, BusinessStatusEnum.WAITING, processInstance.getBusinessKey(), true);
        }
        return actReturnResultVO;
    }

    /**
     * 查询流程审批记录
     *
     * @param businessKey
     * @param formSourceKey
     * @param formSourceTypeValue
     * @return
     */
    @Override
    public List<ActHistoryInfoVO> getHistoryInfoList(String businessKey, String formSourceKey, String formSourceTypeValue) {
        WorkflowKeyEnum workflowKeyEnum = WorkflowKeyEnum.getEumByKey(formSourceKey, formSourceTypeValue);
        String processDefinitionKey = workFlowUtils.getProcessInstanceKey(workflowKeyEnum);
        //String processDefinitionKey = actBusinessFormMapper.getProcessDefinitionKeyByBusinessKey(businessKey);
        return iActHiActinstService.findHistoryInfoList(businessKey, processDefinitionKey);
        ////查询任务办理记录
        //List<HistoricTaskInstance> list = historyService.createHistoricTaskInstanceQuery()
        //        .processInstanceBusinessKey(businessKey).processDefinitionKey(processDefinitionKey)
        //        .orderByHistoricTaskInstanceEndTime().desc().list();
        //List<ActHistoryInfoVO> actHistoryInfoVOList = new ArrayList<>();
        //for (HistoricTaskInstance historicTaskInstance : list) {
        //    // 1.先处理正常历史记录
        //    ActHistoryInfoVO actHistoryInfoVO = new ActHistoryInfoVO();
        //    BeanUtils.copyProperties(historicTaskInstance, actHistoryInfoVO);
        //    actHistoryInfoVO.setStatus(actHistoryInfoVO.getEndTime() == null ? "待处理" : "已处理");
        //    List<Comment> taskComments = taskService.getTaskComments(historicTaskInstance.getId());
        //    if (CollectionUtil.isNotEmpty(taskComments)) {
        //        actHistoryInfoVO.setCommentId(taskComments.get(0).getId());
        //        String message = taskComments.stream().map(Comment::getFullMessage).collect(Collectors.joining("。"));
        //        if (StringUtils.isNotBlank(message)) {
        //            actHistoryInfoVO.setComment(message);
        //        }
        //    }
        //    actHistoryInfoVOList.add(actHistoryInfoVO);
        //    // 2.再处理重启流程记录
        //    List<Comment> restartProcessComments = taskService.getTaskComments(historicTaskInstance.getId(), ActConstant.RESTART_PROCESS_COMMENT_TYPE);
        //    if (CollectionUtil.isNotEmpty(restartProcessComments)) {
        //        Comment restartProcessComment = restartProcessComments.get(0);
        //        ActHistoryInfoVO restartProcessHiInfo = new ActHistoryInfoVO();
        //        BeanUtils.copyProperties(historicTaskInstance, restartProcessHiInfo);
        //        restartProcessHiInfo.setAssignee(restartProcessComment.getUserId());
        //        restartProcessHiInfo.setStartTime(restartProcessComment.getTime());
        //        restartProcessHiInfo.setEndTime(restartProcessComment.getTime());
        //        restartProcessHiInfo.setStatus(restartProcessHiInfo.getEndTime() == null ? "待处理" : "已处理");
        //        restartProcessHiInfo.setCommentId(restartProcessComment.getId());
        //        restartProcessHiInfo.setComment(restartProcessComment.getFullMessage());
        //        actHistoryInfoVOList.add(restartProcessHiInfo);
        //    }
        //}
        ////翻译人员名称
        //if (CollUtil.isNotEmpty(actHistoryInfoVOList)) {
        //    List<String> assigneeList = actHistoryInfoVOList.stream().filter(e -> StringUtils.isNotEmpty(e.getAssignee()))
        //            .map(e -> e.getAssignee()).collect(Collectors.toList());
        //    if (CollUtil.isNotEmpty(assigneeList)) {
        //        actHistoryInfoVOList.forEach(e -> {
        //            e.setNickName(iActNodeAssigneeService.getStaffFullName(e.getAssignee()));
        //        });
        //    }
        //}
        //List<ActHistoryInfoVO> collect = new ArrayList<>();
        ////待办理
        //List<ActHistoryInfoVO> waitingTask = actHistoryInfoVOList.stream().filter(e -> e.getEndTime() == null).collect(Collectors.toList());
        ////已办理
        //List<ActHistoryInfoVO> finishTask = actHistoryInfoVOList.stream().filter(e -> e.getEndTime() != null).collect(Collectors.toList());
        //collect.addAll(finishTask);
        //collect.addAll(waitingTask);
        //return collect.stream().sorted(Comparator.comparing(ActHistoryInfoVO::getStartTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 通过流程实例id获取历史流程图
     *
     * @param processInstanceId
     * @param response
     */
    @Override
    public void getHistoryProcessImage(String processInstanceId, HttpServletResponse response) {
        // 设置页面不缓存
        response.setHeader("Pragma", "no-cache");
        response.addHeader("Cache-Control", "must-revalidate");
        response.addHeader("Cache-Control", "no-cache");
        response.addHeader("Cache-Control", "no-store");
        response.setDateHeader("Expires", 0);
        InputStream inputStream = null;
        try {
            // 1. 查询流程实例历史数据
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(processInstanceId).singleResult();
            if (ObjectUtil.isNull(historicProcessInstance)) {
                throw new FlowException(ResultErrorCode.NOT_PROCESS_INSTANCE);
            }
            // 2. 查询流程中已执行的节点，按时开始时间降序排列
            List<HistoricActivityInstance> historicActivityInstanceList = historyService.createHistoricActivityInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .orderByHistoricActivityInstanceEndTime()
                    .desc().list();

            // 3. 单独的提取高亮节点id ( 绿色）
            List<String> histExecutedActivityIdList = new ArrayList<>();
            for (HistoricActivityInstance activityInstance : historicActivityInstanceList) {
                histExecutedActivityIdList.add(activityInstance.getActivityId());
            }
            BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());
            // 实例化流程图生成器
            CustomProcessDiagramGenerator generator = new CustomProcessDiagramGenerator();
            // 获取高亮连线id
            List<String> highLightedFlows = generator.getHighLightedFlows(bpmnModel, historicActivityInstanceList);
            // 4. 正在执行的节点 （红色）
            Set<String> executedActivityIdList = runtimeService.createExecutionQuery().processInstanceId(processInstanceId).list()
                    .stream().map(Execution::getActivityId).collect(Collectors.toSet());

            ICustomProcessDiagramGenerator diagramGenerator = (ICustomProcessDiagramGenerator) processEngine.getProcessEngineConfiguration().getProcessDiagramGenerator();
            inputStream = diagramGenerator.generateDiagram(bpmnModel, "png", histExecutedActivityIdList,
                    highLightedFlows, null, null, null, null, 1.0, new Color[]{WorkflowConstants.COLOR_NORMAL, WorkflowConstants.COLOR_CURRENT}, executedActivityIdList);

            // 响应相关图片
            response.setContentType("image/png");

            byte[] bytes = IOUtils.toByteArray(inputStream);
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(bytes);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    public ActReturnResultVO deleteProcessAndHisInst(String businessKey, WorkflowKeyEnum workflowKeyEnum) {
        ActReturnResultVO actReturnResultVO = new ActReturnResultVO();
        String processDefinitionKey = workFlowUtils.getProcessInstanceKey(workflowKeyEnum);
        //String processInstanceId = actBusinessFormMapper.selectTabProcessInstanceId(workflowKeyEnum, businessKey);
        if (StringUtils.isNotEmpty(processDefinitionKey)) {
            //1.查询是否有正在允许中的任务，有则删除
            List<Task> taskList = taskService.createTaskQuery().processInstanceBusinessKey(businessKey)
                    .processDefinitionKey(processDefinitionKey).active().list();
            if (CollectionUtil.isNotEmpty(taskList)) {
                taskList.forEach(task -> {
                    // 发送消息，待办任务相关办理人待办事项-1
                    // 从流程变量中获取【申请人申请】环节id
                    String startActivityId = (String) historyService.createHistoricVariableInstanceQuery().processInstanceId(task.getProcessInstanceId())
                            .variableName(ActConstant.START_ACTIVITY_ID).singleResult().getValue();
                    if (task.getTaskDefinitionKey().equals(startActivityId)) {
                        // 当前任务是【申请人】节点
                        workFlowUtils.sendMessage(task.getAssignee(), workflowKeyEnum, BusinessStatusEnum.DRAFT, businessKey, false);
                    } else {
                        ActNodeAssignee actNodeAssignee = iActNodeAssigneeService.getInfo(task.getProcessDefinitionId(), task.getTaskDefinitionKey());
                        workFlowUtils.sendMessage(workFlowUtils.getAssigneeIdList(actNodeAssignee), workflowKeyEnum, BusinessStatusEnum.WAITING, businessKey, false);
                    }
                });
            }
            List<ProcessInstance> processInstanceList = runtimeService.createProcessInstanceQuery()
                    .processInstanceBusinessKey(businessKey).processDefinitionKey(processDefinitionKey).list();
            if (CollectionUtil.isNotEmpty(processInstanceList)) {
                processInstanceList.forEach(e -> runtimeService.deleteProcessInstance(e.getId(), BaseContext.getStaffGuid() + "删除了当前流程实例"));
            }
            //2.删除历史记录
            List<HistoricProcessInstance> historicProcessInstanceList = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceBusinessKey(businessKey).processDefinitionKey(processDefinitionKey).list();
            if (CollectionUtil.isNotEmpty(historicProcessInstanceList)) {
                historicProcessInstanceList.forEach(e -> historyService.deleteHistoricProcessInstance(e.getId()));
            }
        }
        //4.发布事件-->删除
        workFlowUtils.publishActEvent(workflowKeyEnum.getServiceName(), BusinessStatusEnum.DELETE, businessKey, actReturnResultVO);

        return actReturnResultVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActReturnResultVO restartProcess(ActAuditProcessRO auditProcessRO) {
        if (StringUtils.isEmpty(auditProcessRO.getComment())) {
            throw new FlowException(ResultErrorCode.COMMENT_IS_EMPTY);
        }

        if (StringUtils.isEmpty(auditProcessRO.getProcessInstanceId())) {
            ActReturnResultVO actReturnResultVO = new ActReturnResultVO();
            // 流程实例为空，调用启动流程接口，并返回
            WorkflowKeyEnum workflowKeyEnum = WorkflowKeyEnum.getEumByKey(auditProcessRO.getFormSourceKey(), auditProcessRO.getFormSourceTypeValue());
            // 发布事件-->重启
            workFlowUtils.publishActEvent(workflowKeyEnum.getServiceName(), BusinessStatusEnum.RESTART, auditProcessRO.getBusinessKey(), actReturnResultVO);
            // 先删除旧流程（如果存在）
            this.deleteProcessAndHisInst(auditProcessRO.getBusinessKey(), workflowKeyEnum);
            // 先把单据状态改为草稿，再去启动新流程
            actBusinessFormMapper.deleteProcessFields(workflowKeyEnum, auditProcessRO.getBusinessKey());
            this.start(workflowKeyEnum, auditProcessRO.getBusinessKey());
            return null;
        }

        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(auditProcessRO.getProcessInstanceId())
                .processInstanceBusinessKey(auditProcessRO.getBusinessKey()).singleResult();
        if (ObjectUtil.isNull(historicProcessInstance)) {
            throw new FlowException(ResultErrorCode.NOT_EXIST_PROCESS);
        }
        if (historicProcessInstance.getEndTime() == null) {
            throw new FlowException(ResultErrorCode.RESTART_PROCESS_FAILURE_BE_NOT_FINISH);
        }

        // 查询原流程历史记录
        List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(auditProcessRO.getProcessInstanceId()).orderByHistoricTaskInstanceEndTime().desc().list();
        // 查询原流程结束节点最新一条历史记录
        HistoricTaskInstance entTask = historicTaskInstanceList.get(0);
        // 判断是否存在重启记录，若有则不允许重启
        List<Comment> taskComments = taskService.getTaskComments(entTask.getId(), ActConstant.RESTART_PROCESS_COMMENT_TYPE);
        if (CollectionUtil.isNotEmpty(taskComments)) {
            throw new FlowException(ResultErrorCode.NOT_ALLOW_RESTART_PROCESS);
        }
        ActReturnResultVO actReturnResultVO = new ActReturnResultVO();

        // 查询当前待办任务列表
        List<Task> oldTaskList = taskService.createTaskQuery().processInstanceId(auditProcessRO.getProcessInstanceId())
                .processInstanceBusinessKey(auditProcessRO.getBusinessKey()).list();
        String deploymentKey = workFlowUtils.getDeploymentKeyById(historicProcessInstance.getDeploymentId());
        WorkflowKeyEnum workflowKeyEnum = WorkflowKeyEnum.getEumByKey(deploymentKey);
        // 发布事件-->重启
        workFlowUtils.publishActEvent(workflowKeyEnum.getServiceName(), BusinessStatusEnum.RESTART, historicProcessInstance.getBusinessKey(), actReturnResultVO);
        // 0.如果存在旧待办任务，则删除
        if (CollectionUtil.isNotEmpty(oldTaskList)) {
            List<ActNodeAssignee> actNodeAssigneeList = iActNodeAssigneeService.getInfoByProcessDefinitionId(oldTaskList.get(0).getProcessDefinitionId());
            // 删除历史记录
            Set<String> oldTaskKeySet = oldTaskList.stream().map(Task::getTaskDefinitionKey).collect(Collectors.toSet());
            iActHiActinstService.deleteByActId(auditProcessRO.getProcessInstanceId(), oldTaskKeySet, true);
            // 删除待办任务
            oldTaskList.forEach(oldTask -> {
                DeleteTaskCmd deleteTaskCmd = new DeleteTaskCmd(oldTask.getId());
                processEngine.getManagementService().executeCommand(deleteTaskCmd);
            });
            // 发送消息，旧待办任务列表所有相关员工待办事项-1
            Set<String> taskDefinitionKeys = oldTaskList.stream().map(Task::getTaskDefinitionKey).collect(Collectors.toSet());
            Set<String> assigneeIdSet = actNodeAssigneeList.stream().filter(e -> taskDefinitionKeys.contains(e.getNodeId()))
                    .flatMap(e -> workFlowUtils.getAssigneeIdList(e).stream()).collect(Collectors.toSet());
            workFlowUtils.sendMessage(assigneeIdSet, workflowKeyEnum, BusinessStatusEnum.WAITING, historicProcessInstance.getBusinessKey(), false);
        }

        // 1.原流程最后节点追加记录
        String comment = auditProcessRO.getComment() + "=>重启流程";
        AddCommentCmd addCommentCmd = new AddCommentCmd(entTask.getId(), entTask.getProcessInstanceId(), ActConstant.RESTART_PROCESS_COMMENT_TYPE, comment);
        processEngine.getManagementService().executeCommand(addCommentCmd);
        // 2.查找原流程【申请人申请】节点办理人
        String startActivityId = (String) historyService.createHistoricVariableInstanceQuery().processInstanceId(auditProcessRO.getProcessInstanceId())
                .variableName(ActConstant.START_ACTIVITY_ID).singleResult().getValue();
        // 查询原流程开始节点最旧一条历史记录
        HistoricTaskInstance startTask = historicTaskInstanceList.stream()
                .sorted(Comparator.comparing(HistoricTaskInstance::getEndTime))
                .filter(e -> e.getTaskDefinitionKey().equals(startActivityId))
                .findFirst().orElse(null);
        // 3.启动新流程

        // 设置启动人
        Authentication.setAuthenticatedUserId(BaseContext.getStaffGuid());
        // 启动流程实例（提交申请）
        String processInstanceKey = workFlowUtils.getProcessInstanceKey(workflowKeyEnum);
        Map<String, Object> variables = historicProcessInstance.getProcessVariables();
        variables.put(ActConstant.BUSINESSKEY, historicProcessInstance.getBusinessKey());
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(processInstanceKey, historicProcessInstance.getBusinessKey(), variables);
        actReturnResultVO.setProcessInstanceId(pi.getId());
        // 将流程定义名称 作为 流程实例名称
        runtimeService.setProcessInstanceName(pi.getProcessInstanceId(), pi.getProcessDefinitionName());
        // 2.申请人执行流程
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(pi.getId()).list();
        if (taskList.size() > 1) {
            throw new FlowException(ResultErrorCode.FIRST_LINK_PROPOSER);
        }
        //taskService.setAssignee(taskList.get(0).getId(), startTask.getAssignee());
        runtimeService.setVariable(pi.getId(), ActConstant.START_ACTIVITY_ID, taskList.get(0).getTaskDefinitionKey());
        // 更新业务状态
        actBusinessFormMapper.updateTabAuditState(workflowKeyEnum, pi, BusinessStatusEnum.DRAFT);
        actReturnResultVO.setAuditStatus(BusinessStatusEnum.DRAFT.getStatus());
        // 发布事件-->重启
        //workFlowUtils.publishEvent(workflowKeyEnum.getServiceName(), BusinessStatusEnum.RESTART, historicProcessInstance.getBusinessKey());
        // 发送消息，新流程办理人待办事项+1
        workFlowUtils.sendMessage(startTask.getAssignee(), workflowKeyEnum, BusinessStatusEnum.DRAFT, historicProcessInstance.getBusinessKey(), true);
        return actReturnResultVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActReturnResultVO forcedResetProcess(ActAuditProcessRO auditProcessRO) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceBusinessKey(auditProcessRO.getBusinessKey()).processInstanceId(auditProcessRO.getProcessInstanceId()).singleResult();
        if (ObjectUtil.isNull(processInstance)) {
            throw new FlowException(ResultErrorCode.NOT_PROCESS_INSTANCE);
        }
        WorkflowKeyEnum workflowKeyEnum = WorkflowKeyEnum.getEumByKey(auditProcessRO.getFormSourceKey(), auditProcessRO.getFormSourceTypeValue());

        ActReturnResultVO actReturnResultVO = new ActReturnResultVO();
        actReturnResultVO.setAuditStatus(BusinessStatusEnum.DELETE.getStatus());
        actReturnResultVO.setProcessInstanceId(processInstance.getId());
        // 发布事件-->强制重启
        workFlowUtils.publishActEvent(workflowKeyEnum.getServiceName(), BusinessStatusEnum.FORCED_RESET, processInstance.getBusinessKey(), actReturnResultVO);
        // 删除流程
        this.deleteProcessAndHisInst(auditProcessRO.getBusinessKey(), workflowKeyEnum);
        // 更新业务单据状态
        actBusinessFormMapper.deleteProcessFields(workflowKeyEnum, auditProcessRO.getBusinessKey());
        return actReturnResultVO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActReturnResultVO start(WorkflowKeyEnum workflowKeyEnum, String businessKey, Map<String, Object> variables) {
        if (StringUtils.isBlank(businessKey)) {
            throw new FlowException(ResultErrorCode.MUST_START_BUSINESS_ID);
        }
        ActReturnResultVO actReturnResultVO = new ActReturnResultVO();

        // 查询业务单据审核状态，如果为【审核完成】，则不启动流程（为适配前端直接保存已审核完成的业务单据）
        if (BusinessStatusEnum.FINISH.getStatus().equals(actBusinessFormMapper.selectTabAuditState(workflowKeyEnum, businessKey))) {
            // 发布事件-->审核完成
            workFlowUtils.publishActEvent(workflowKeyEnum.getServiceName(), BusinessStatusEnum.FINISH, businessKey, actReturnResultVO);
            return actReturnResultVO;
        }

        ErpSystemMgtFormSourceTypeValue erpSystemMgtFormSourceTypeValue = mgtFormSourceTypeValueMapper
                .getFormSourceTypeValue(workflowKeyEnum.getFormSourceKey(), workflowKeyEnum.getFormSourceTypeValue());
        // 设置启动人
        String currentStaffGuid = BaseContext.getStaffGuid();
        Authentication.setAuthenticatedUserId(currentStaffGuid);
        // 1.启动流程实例（提交申请）
        String processInstanceKey = workFlowUtils.getProcessInstanceKey(workflowKeyEnum);
        variables.put(ActConstant.BUSINESSKEY, businessKey);
        //ProcessInstance pi = runtimeService.startProcessInstanceByKey(workflowKeyEnum.getKey(), BusinessKey, variables);
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(processInstanceKey, businessKey, variables);
        // 将流程定义名称 作为 流程实例名称
        runtimeService.setProcessInstanceName(pi.getProcessInstanceId(), pi.getProcessDefinitionName());
        actReturnResultVO.setProcessInstanceId(pi.getProcessInstanceId());
        // 2.申请人执行流程
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(pi.getId()).list();
        if (taskList.size() > 1) {
            throw new FlowException(ResultErrorCode.FIRST_LINK_PROPOSER);
        }
        Task currentTask = taskList.get(0);
        //taskService.setAssignee(currentTask.getId(), currentStaffGuid);
        runtimeService.setVariable(pi.getId(), ActConstant.START_ACTIVITY_ID, currentTask.getTaskDefinitionKey());
        // 更新业务状态
        actBusinessFormMapper.updateTabAuditState(workflowKeyEnum, pi, BusinessStatusEnum.DRAFT);
        actReturnResultVO.setAuditStatus(BusinessStatusEnum.DRAFT.getStatus());
        // 发布事件-->草稿
        workFlowUtils.publishActEvent(workflowKeyEnum.getServiceName(), BusinessStatusEnum.DRAFT, pi.getBusinessKey(), actReturnResultVO);
        // 发送消息，当前员工待办事项+1
        workFlowUtils.sendMessage(currentStaffGuid, workflowKeyEnum, BusinessStatusEnum.DRAFT, pi.getBusinessKey(), true);

        // 判断  是否自动启动流程实例
        if (ObjectUtil.isNotEmpty(erpSystemMgtFormSourceTypeValue) && erpSystemMgtFormSourceTypeValue.getIsStartProcessInstance()) {
            // 查询最新任务
            // Task newTask = taskService.createTaskQuery().processInstanceId(pi.getId()).processInstanceBusinessKey(businessKey).taskAssignee(currentStaffGuid).active().singleResult();
            Task newTask = taskService.createTaskQuery().processInstanceId(pi.getId()).processInstanceBusinessKey(businessKey).active().singleResult();
            ActAuditProcessRO auditProcessRO = new ActAuditProcessRO();
            auditProcessRO.setComment("自动提交");
            //taskCompleteRO.setVariables(variables);
            // 申请人提交任务
            return iTaskService.completeTask(auditProcessRO, newTask);
        }
        return actReturnResultVO;
    }

    @Override
    public List<ActMessageDTO> getActMessageList() {
        return workFlowUtils.getMessageByStaffGuid(BaseContext.getStaffGuid());
    }

    /**
     * 批量提交审核（申请人节点）
     * @param collect
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ActReturnResultVO> batchCommitAudit(List<ActAuditProcessRO> collect) {
        List<ActAuditProcessRO> auditProcessROList = collect.stream().distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(auditProcessROList)) {
            return null;
        }
        List<ActReturnResultVO> returnResultVOList = new ArrayList<>(auditProcessROList.size());
        for (int i = 0; i < auditProcessROList.size(); i++) {
            try {
                returnResultVOList.add(this.commitAudit(auditProcessROList.get(i)));
            } catch (FlowException e) {
                throw new FlowException(ResultErrorCode.BATCH_PROCESSING_FAILURE, String.valueOf(i), String.valueOf(e.getCode()));
            } catch (Exception e) {
                throw new FlowException(ResultErrorCode.BATCH_PROCESSING_FAILURE, String.valueOf(i), String.valueOf(BaseResultErrorCodeImpl.SYS_ERROR.getCode()));
            }
        }
        return returnResultVOList;
    }

    /**
     * 批量撤销提交
     * @param collect
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ActReturnResultVO> batchCancelCommitAudit(List<ActAuditProcessRO> collect) {
        List<ActAuditProcessRO> auditProcessROList = collect.stream().distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(auditProcessROList)) {
            return null;
        }
        List<ActReturnResultVO> returnResultVOList = new ArrayList<>(auditProcessROList.size());
        for (int i = 0; i < auditProcessROList.size(); i++) {
            try {
                returnResultVOList.add(this.cancelCommitAudit(auditProcessROList.get(i)));
            } catch (FlowException e) {
                throw new FlowException(ResultErrorCode.BATCH_PROCESSING_FAILURE, String.valueOf(i), String.valueOf(e.getCode()));
            } catch (Exception e) {
                throw new FlowException(ResultErrorCode.BATCH_PROCESSING_FAILURE, String.valueOf(i), String.valueOf(BaseResultErrorCodeImpl.SYS_ERROR.getCode()));
            }
        }
        return returnResultVOList;
    }

    /**
     * 批量审核（通过/驳回）
     * @param result
     * @param collect
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ActReturnResultVO> batchAudit(Boolean result, List<ActAuditProcessRO> collect) {
        List<ActAuditProcessRO> auditProcessROList = collect.stream().distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(auditProcessROList)) {
            return null;
        }
        List<ActReturnResultVO> returnResultVOList = new ArrayList<>(auditProcessROList.size());
        for (int i = 0; i < auditProcessROList.size(); i++) {
            try {
                returnResultVOList.add(this.audit(result, auditProcessROList.get(i)));
            } catch (FlowException e) {
                throw new FlowException(ResultErrorCode.BATCH_PROCESSING_FAILURE, String.valueOf(i), String.valueOf(e.getCode()));
            } catch (Exception e) {
                throw new FlowException(ResultErrorCode.BATCH_PROCESSING_FAILURE, String.valueOf(i), String.valueOf(BaseResultErrorCodeImpl.SYS_ERROR.getCode()));
            }
        }
        return returnResultVOList;
    }

    /**
     * 批量撤销审核
     * @param collect
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ActReturnResultVO> batchCancelAudit(List<ActAuditProcessRO> collect) {
        List<ActAuditProcessRO> auditProcessROList = collect.stream().distinct().collect(Collectors.toList());        if (CollectionUtil.isEmpty(auditProcessROList)) {
            return null;
        }
        List<ActReturnResultVO> returnResultVOList = new ArrayList<>(auditProcessROList.size());
        for (int i = 0; i < auditProcessROList.size(); i++) {
            try {
                returnResultVOList.add(this.cancelAudit(auditProcessROList.get(i)));
            } catch (FlowException e) {
                throw new FlowException(ResultErrorCode.BATCH_PROCESSING_FAILURE, String.valueOf(i), String.valueOf(e.getCode()));
            } catch (Exception e) {
                throw new FlowException(ResultErrorCode.BATCH_PROCESSING_FAILURE, String.valueOf(i), String.valueOf(BaseResultErrorCodeImpl.SYS_ERROR.getCode()));
            }
        }
        return returnResultVOList;
    }

    /**
     * 批量重启流程
     * @param collect
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ActReturnResultVO> batchRestartProcess(List<ActAuditProcessRO> collect) {
        List<ActAuditProcessRO> auditProcessROList = collect.stream().distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(auditProcessROList)) {
            return null;
        }
        List<ActReturnResultVO> returnResultVOList = new ArrayList<>(auditProcessROList.size());
        for (int i = 0; i < auditProcessROList.size(); i++) {
            try {
                returnResultVOList.add(this.restartProcess(auditProcessROList.get(i)));
            } catch (FlowException e) {
                throw new FlowException(e.getMessage(),e.getCode(),e.getArgs());
            } catch (Exception e) {
                throw new FlowException(ResultErrorCode.BATCH_PROCESSING_FAILURE, String.valueOf(i), String.valueOf(BaseResultErrorCodeImpl.SYS_ERROR.getCode()));
            }
        }
        return returnResultVOList;
    }

    /**
     * 批量强制重置流程
     * @param collect
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ActReturnResultVO> batchForcedResetProcess(List<ActAuditProcessRO> collect) {
        List<ActAuditProcessRO> auditProcessROList = collect.stream().distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(auditProcessROList)) {
            return null;
        }
        List<ActReturnResultVO> returnResultVOList = new ArrayList<>(auditProcessROList.size());
        for (int i = 0; i < auditProcessROList.size(); i++) {
            try {
                returnResultVOList.add(this.forcedResetProcess(auditProcessROList.get(i)));
            } catch (FlowException e) {
                throw new FlowException(ResultErrorCode.BATCH_PROCESSING_FAILURE, String.valueOf(i), String.valueOf(e.getCode()));
            } catch (Exception e) {
                throw new FlowException(ResultErrorCode.BATCH_PROCESSING_FAILURE, String.valueOf(i), String.valueOf(BaseResultErrorCodeImpl.SYS_ERROR.getCode()));
            }
        }
        return returnResultVOList;
    }

    /**
     * 校验流程
     * @param auditProcessRO
     */
    private void verifyProcess(ActAuditProcessRO auditProcessRO) {
        // 判断流程是否变动
        if (actBusinessFormMapper.selectProcessIsChanged(auditProcessRO)) {
            throw new FlowException(ResultErrorCode.THE_REVIEW_PROCESS_HAS_BEEN_CHANGED);
        }
    }

    @Override
    public ActReturnResultVO invalidProcess(ActAuditProcessRO auditProcessRO) {
        if (StringUtils.isEmpty(auditProcessRO.getComment())) {
            throw new FlowException(ResultErrorCode.COMMENT_IS_EMPTY);
        }
        WorkflowKeyEnum workflowKeyEnum = WorkflowKeyEnum.getEumByKey(auditProcessRO.getFormSourceKey(), auditProcessRO.getFormSourceTypeValue());
        ActReturnResultVO actReturnResultVO = new ActReturnResultVO();
        actReturnResultVO.setProcessInstanceId(auditProcessRO.getProcessInstanceId());
        actReturnResultVO.setAuditStatus(BusinessStatusEnum.INVALID.getStatus());
        // 发布事件-->作废
        workFlowUtils.publishActEvent(workflowKeyEnum.getServiceName(), BusinessStatusEnum.INVALID, auditProcessRO.getBusinessKey(), actReturnResultVO);
        // 更新业务状态
        String processInstanceKey = workFlowUtils.getProcessInstanceKey(workflowKeyEnum);
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(processInstanceKey, auditProcessRO.getBusinessKey(), auditProcessRO.getVariables());
        actBusinessFormMapper.updateTabAuditState(workflowKeyEnum, pi, BusinessStatusEnum.INVALID);
        return actReturnResultVO;
    }

    @Override
    public List<ActReturnResultVO> batchInvalidProcess(List<ActAuditProcessRO> auditProcessROList) {
        List<ActReturnResultVO> resultVOList = new ArrayList<>();
        auditProcessROList.forEach(v -> resultVOList.add(invalidProcess(v)));
        return resultVOList;
    }
}
