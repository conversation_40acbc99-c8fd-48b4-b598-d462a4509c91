package xy.server.activiti.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.xunyue.activiti.entity.ActNodeAssignee;
import com.xunyue.activiti.service.IActNodeAssigneeService;
import com.xunyue.common.util.StringUtils;
import com.xunyue.config.exception.FlowException;
import com.xy.util.BaseContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.bpmn.model.FlowNode;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.history.HistoricTaskInstanceQuery;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.Assert;
import xy.server.activiti.cmd.AddCommentCmd;
import xy.server.activiti.cmd.DeleteExecutionCmd;
import xy.server.activiti.common.constant.ActConstant;
import xy.server.activiti.common.enums.BusinessStatusEnum;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.entity.model.qo.TaskQO;
import xy.server.activiti.entity.model.ro.ActAuditProcessRO;
import xy.server.activiti.entity.model.vo.ActReturnResultVO;
import xy.server.activiti.factory.WorkflowService;
import xy.server.activiti.i18n.ResultErrorCode;
import xy.server.activiti.mapper.ActBusinessFormMapper;
import xy.server.activiti.mapper.ActHiTaskInstMapper;
import xy.server.activiti.service.IActHiActinstService;
import xy.server.activiti.service.ITaskService;
import xy.server.activiti.utils.WorkFlowUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service("xyTaskService")
@RequiredArgsConstructor
@Slf4j
public class TaskServiceImpl extends WorkflowService implements ITaskService {

    private final IActNodeAssigneeService iActNodeAssigneeService;
    private final IActHiActinstService iActHiActinstService;
    private final ActBusinessFormMapper actBusinessFormMapper;
    private final ActHiTaskInstMapper actHiTaskInstMapper;
    private final WorkFlowUtils workFlowUtils;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 完成任务
     *TODO
     * 发送信息
     * @param auditProcessRO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActReturnResultVO completeTask(ActAuditProcessRO auditProcessRO, Task task) {
        try {
            ActReturnResultVO actReturnResultVO = new ActReturnResultVO();
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
            actReturnResultVO.setProcessInstanceId(processInstance.getId());
            // 查询流程定义key绑定的枚举类
            String deploymentKey = actBusinessFormMapper.getDeploymentKeyById(processInstance.getDeploymentId());
            WorkflowKeyEnum workflowKeyEnum = WorkflowKeyEnum.getEumByKey(deploymentKey);
            // 从流程变量中获取【申请人申请】环节id
            String startActivityId = (String) runtimeService.getVariable(processInstance.getId(), ActConstant.START_ACTIVITY_ID);
            // 获取当前登录人staffGuid
            String currentStaffGuid = BaseContext.getStaffGuid();

            // 2. 判断下一节点是否是会签 如果是会签 将选择的人员放到会签变量
            List<ActNodeAssignee> actNodeAssignees = iActNodeAssigneeService.getInfoByProcessDefinitionId(task.getProcessDefinitionId());
//            for (ActNodeAssignee actNodeAssignee : actNodeAssignees) {
//                String column = actNodeAssignee.getMultipleColumn();
//                String assigneeId = actNodeAssignee.getAssigneeId();
//                if (actNodeAssignee.getMultiple() && actNodeAssignee.getIsShow()) {
//                    List<Long> userIdList = req.getAssignees(actNodeAssignee.getMultipleColumn());
//                    if (CollectionUtil.isNotEmpty(userIdList)) {
//                        taskService.setVariable(task.getId(), column, userIdList);
//                    }
//                }
//                //判断是否有会签并且不需要弹窗选人的节点
//                if (actNodeAssignee.getMultiple() && !actNodeAssignee.getIsShow() && (StringUtils.isBlank(column) || StringUtils.isBlank(assigneeId))) {
//                    throw new ServiceException("请检查【" + processInstance.getProcessDefinitionKey() + "】配置 ");
//                }
//                if (actNodeAssignee.getMultiple() && !actNodeAssignee.getIsShow()) {
//                    workFlowUtils.settingAssignee(task, actNodeAssignee, actNodeAssignee.getMultiple());
//                }
//            }
            // 3. 指定任务审批意见
            AddCommentCmd addCommentCmd = new AddCommentCmd(task.getId(), task.getProcessInstanceId(), auditProcessRO.getComment());
            processEngine.getManagementService().executeCommand(addCommentCmd);
            if (StringUtils.isEmpty(task.getAssignee())) {
                // 办理人为空，则设置当前登录人为办理人
                taskService.setAssignee(task.getId(), currentStaffGuid);
            }
            // 设置变量
            taskService.setVariables(task.getId(), auditProcessRO.getVariables());
            // 4. 完成任务
            taskService.complete(task.getId());

            // 发布消息，当前员工待办事项-1
            if (task.getTaskDefinitionKey().equals(startActivityId)) {
                // 如果当前节点是【申请人申请】
                workFlowUtils.sendMessage(task.getAssignee(), workflowKeyEnum, BusinessStatusEnum.DRAFT, processInstance.getBusinessKey(), false);
            } else {
                // 其他节点
                ActNodeAssignee oldNodeAssignee = actNodeAssignees.stream().filter(e -> task.getTaskDefinitionKey().equals(e.getNodeId())).findFirst().orElse(null);
                workFlowUtils.sendMessage(workFlowUtils.getAssigneeIdList(oldNodeAssignee), workflowKeyEnum, BusinessStatusEnum.WAITING, processInstance.getBusinessKey(), false);
            }

            // 自动办理
//            Boolean autoComplete = workFlowUtils.autoComplete(workflowKeyEnum, processInstance, actNodeAssignees, req);
//            if (autoComplete) {
//                List<Task> nextTaskList = taskService.createTaskQuery().processInstanceId(task.getProcessInstanceId()).list();
//                if (!CollectionUtil.isEmpty(nextTaskList)) {
//                    for (Task t : nextTaskList) {
//                        ActNodeAssignee nodeAssignee = actNodeAssignees.stream().filter(e -> t.getTaskDefinitionKey().equals(e.getNodeId())).findFirst().orElse(null);
//                        if (ObjectUtil.isNull(nodeAssignee)) {
//                            throw new FlowException(ResultErrorCode.TASK_NODE_DEPLOYMENT);
//                        }
//                        workFlowUtils.settingAssignee(t, nodeAssignee);
//                    }
//                } else {
//                    // 更新业务状态已完成 办结流程
//                    actBusinessFormMapper.updateTabAuditState(workflowKeyEnum, processInstance, BusinessStatusEnum.FINISH);
//                    // 发布事件-->审核完成
//                    workFlowUtils.publishEvent(workflowKeyEnum.getServiceName(), BusinessStatusEnum.FINISH, processInstance.getBusinessKey(), actReturnResultVO);
//                    return actReturnResultVO;
//                }
//                // 发送站内信
////                workFlowUtils.sendMessage(req.getSendMessage(), processInstance.getProcessInstanceId());
//                // 更新业务状态为：办理中
//                actBusinessFormMapper.updateTabAuditState(workflowKeyEnum, processInstance, BusinessStatusEnum.WAITING);
//                // 发布事件-->审核中
//                workFlowUtils.publishEvent(workflowKeyEnum.getServiceName(), BusinessStatusEnum.WAITING, processInstance.getBusinessKey(), actReturnResultVO);
//                return actReturnResultVO;
//            }
            // 5.查询下一环节任务列表
            List<Task> nextTaskList = taskService.createTaskQuery().processInstanceId(task.getProcessInstanceId()).list();
            // 5.1如果为空，办结任务
            if (CollectionUtil.isEmpty(nextTaskList)) {
                // 更新业务状态已完成 办结流程
                actBusinessFormMapper.updateTabAuditState(workflowKeyEnum, processInstance, BusinessStatusEnum.FINISH);
                actReturnResultVO.setAuditStatus(BusinessStatusEnum.FINISH.getStatus());
                // 发布事件-->审核完成
                workFlowUtils.publishActEvent(workflowKeyEnum.getServiceName(), BusinessStatusEnum.FINISH, processInstance.getBusinessKey(), actReturnResultVO);
                return actReturnResultVO;
            }
            // 5.2. 如果不为空 指定办理人
            for (Task t : nextTaskList) {
                ActNodeAssignee newNodeAssignee = actNodeAssignees.stream().filter(e -> t.getTaskDefinitionKey().equals(e.getNodeId())).findFirst().orElse(null);
                if (ObjectUtil.isNull(newNodeAssignee)) {
                    throw new FlowException(ResultErrorCode.TASK_NODE_DEPLOYMENT);
                }
                // 不需要弹窗选人
                if (!newNodeAssignee.getIsShow() && StringUtils.isBlank(t.getAssignee()) && !newNodeAssignee.getMultiple()) {
                    // 设置人员
                    workFlowUtils.settingAssignee(t, newNodeAssignee);
                } else if (newNodeAssignee.getIsShow() && StringUtils.isBlank(t.getAssignee()) && !newNodeAssignee.getMultiple()) {
                    //// 弹窗选人 根据当前任务节点id获取办理人
                    //List<String> assignees = req.getAssignees(t.getTaskDefinitionKey());
                    //if (CollectionUtil.isEmpty(assignees)) {
                    //    throw new FlowException(ResultErrorCode.NO_APPROVER_CONFIGURED);
                    //}
                    //// 设置选人
                    //workFlowUtils.setAssignee(t, assignees);
                }
                // 发送外部消息平台消息
                workFlowUtils.sendPlatformMessage(workflowKeyEnum, BusinessStatusEnum.WAITING, newNodeAssignee);
                // 发送消息，相关代办人待办事项+1
                workFlowUtils.sendMessage(workFlowUtils.getAssigneeIdList(newNodeAssignee), workflowKeyEnum, BusinessStatusEnum.WAITING, processInstance.getBusinessKey(), true);
            }
            // 查询历史活动节点条数，判断是审核中还是待审核
            long count = historyService.createHistoricActivityInstanceQuery()
                    .processInstanceId(processInstance.getProcessInstanceId()).activityType(ActConstant.USER_TASK).finished().count();
            BusinessStatusEnum businessStatusEnum = count > 1 ? BusinessStatusEnum.IN_REVIEW : BusinessStatusEnum.WAITING;
            // 更新业务状态为：办理中
            actBusinessFormMapper.updateTabAuditState(workflowKeyEnum, processInstance, businessStatusEnum);
            actReturnResultVO.setAuditStatus(businessStatusEnum.getStatus());
            // 发布事件-->审核中
            workFlowUtils.publishActEvent(workflowKeyEnum.getServiceName(), businessStatusEnum, processInstance.getBusinessKey(), actReturnResultVO);
            return actReturnResultVO;
        } catch (Exception e) {
            //e.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("办理失败:",e);
            throw e;
        }
    }

    /**
     * 驳回任务
     *
     * @param auditProcessRO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActReturnResultVO rejectTask(ActAuditProcessRO auditProcessRO, Task task) {
        if (StringUtils.isEmpty(auditProcessRO.getComment())) {
            throw new FlowException(ResultErrorCode.COMMENT_IS_EMPTY);
        }
        // 流程实例
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
        // 目标节点为空，则默认驳回到上一节点环节
        if (StringUtils.isEmpty(auditProcessRO.getTargetActivityId())) {
            List<HistoricActivityInstance> historicActivityInstances = historyService.createHistoricActivityInstanceQuery()
                    .processInstanceId(processInstance.getId()).finished().activityType(ActConstant.USER_TASK)
                    .orderByHistoricActivityInstanceEndTime().desc().list();
            if (CollectionUtil.isEmpty(historicActivityInstances)) {
                throw new FlowException(ResultErrorCode.NOT_BACK_TO_STARTEVENT);
            }
            auditProcessRO.setTargetActivityId(historicActivityInstances.get(0).getActivityId());
        }
        // 当前任务和目标任务为同一个任务，驳回失败！
        if (task.getTaskDefinitionKey().equals(auditProcessRO.getTargetActivityId())) {
            throw new FlowException(ResultErrorCode.NOT_BACK_TASK_REPEAT);
        }

        String deploymentKey = actBusinessFormMapper.getDeploymentKeyById(processInstance.getDeploymentId());
        WorkflowKeyEnum workflowKeyEnum = WorkflowKeyEnum.getEumByKey(deploymentKey);
        // 从流程变量中获取【申请人申请】环节id
        String startActivityId = (String) runtimeService.getVariable(processInstance.getId(), ActConstant.START_ACTIVITY_ID);
        // 获取当前流程所有节点配置信息列表
        List<ActNodeAssignee> actNodeAssigneeList = iActNodeAssigneeService.getInfoByProcessDefinitionId(processInstance.getProcessDefinitionId());
        ActReturnResultVO actReturnResultVO = new ActReturnResultVO();
        actReturnResultVO.setProcessInstanceId(processInstance.getId());
        // 1. 获取流程模型实例 BpmnModel
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
        // 2.当前节点信息
        FlowNode curFlowNode = (FlowNode) bpmnModel.getFlowElement(task.getTaskDefinitionKey());
        // 获取目标节点信息
        FlowNode targetFlowNode = (FlowNode) bpmnModel.getFlowElement(auditProcessRO.getTargetActivityId());

        String comment = auditProcessRO.getComment() + "=>驳回【" + targetFlowNode.getName() + "】";
        AddCommentCmd addCommentCmd = new AddCommentCmd(task.getId(), task.getProcessInstanceId(), ActConstant.REJECT_COMMENT_TYPE, comment);
        // 流程回退
        workFlowUtils.processRollback(task, curFlowNode, targetFlowNode, addCommentCmd);

        // 发送消息，当前任务相关办理人待办事项-1
        //ActNodeAssignee oldNodeAssignee = actNodeAssigneeList.stream().filter(e -> task.getTaskDefinitionKey().equals(e.getNodeId())).findFirst().orElse(null);
        //workFlowUtils.sendMessage(workFlowUtils.getAssigneeIdList(oldNodeAssignee), workflowKeyEnum, BusinessStatusEnum.WAITING, processInstance.getBusinessKey(), false);

        // 查询新的任务列表
        Task newTask = taskService.createTaskQuery().processInstanceId(processInstance.getId())
                .taskDefinitionKey(auditProcessRO.getTargetActivityId()).active().singleResult();
        Assert.notNull(newTask, "驳回后新的节点待办任务不能为空！");
        // 如果驳回到【申请人申请】环节，则设置业务工单审核状态为成【草稿】；其他环节则为【待审核】
        if (newTask.getTaskDefinitionKey().equals(startActivityId)) {
            // 获取最新历史申请人节点办理人
            // 获取最新历史申请人节点办理人
            List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery()
                    .processInstanceId(processInstance.getId()).taskDefinitionKey(newTask.getTaskDefinitionKey())
                    .finished().orderByHistoricTaskInstanceEndTime().desc().list();
            String startActivityAssignee = historicTaskInstanceList.get(0).getAssignee();
            // 设置办理人为流程发起人
            DeleteExecutionCmd executionCmd = new DeleteExecutionCmd(newTask.getExecutionId());
            processEngine.getManagementService().executeCommand(executionCmd);
            // 设置办理人
            // 注释代码原因：【申请人】节点不限制提交人
            //taskService.setAssignee(newTask.getId(), startActivityAssignee);
            // 12.更新业务状态
            actBusinessFormMapper.updateTabAuditState(workflowKeyEnum, processInstance, BusinessStatusEnum.DRAFT);
            actReturnResultVO.setAuditStatus(BusinessStatusEnum.DRAFT.getStatus());
            // 发布事件-->驳回
            workFlowUtils.publishActEvent(workflowKeyEnum.getServiceName(), BusinessStatusEnum.BACK, processInstance.getBusinessKey(), actReturnResultVO);
            // 发送外部消息平台消息
            workFlowUtils.sendPlatformMessage(workflowKeyEnum, BusinessStatusEnum.BACK, startActivityAssignee);
            // 发送消息，新任务办理人待办事项+1
            workFlowUtils.sendMessage(startActivityAssignee, workflowKeyEnum, BusinessStatusEnum.DRAFT, processInstance.getBusinessKey(), true);
            return actReturnResultVO;
        }

        ActNodeAssignee newNodeAssignee = actNodeAssigneeList.stream().filter(e -> newTask.getTaskDefinitionKey().equals(e.getNodeId())).findFirst().orElse(null);

        if (ObjectUtil.isNotEmpty(newNodeAssignee) && !newNodeAssignee.getMultiple()) {
            DeleteExecutionCmd executionCmd = new DeleteExecutionCmd(newTask.getExecutionId());
            processEngine.getManagementService().executeCommand(executionCmd);
            // 设置办理人
            workFlowUtils.settingAssignee(newTask, newNodeAssignee);
        }
        // 查询历史活动节点条数，判断是审核中还是待审核
        long count = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(processInstance.getProcessInstanceId()).activityType(ActConstant.USER_TASK).finished().count();
        // 12.更新业务状态
        BusinessStatusEnum businessStatusEnum = count > 1 ? BusinessStatusEnum.IN_REVIEW : BusinessStatusEnum.WAITING;
        actBusinessFormMapper.updateTabAuditState(workflowKeyEnum, processInstance, businessStatusEnum);
        actReturnResultVO.setAuditStatus(businessStatusEnum.getStatus());
        // 发布事件-->驳回
        workFlowUtils.publishActEvent(workflowKeyEnum.getServiceName(), BusinessStatusEnum.BACK, processInstance.getBusinessKey(), actReturnResultVO);
        // 发送外部消息平台消息
        workFlowUtils.sendPlatformMessage(workflowKeyEnum, BusinessStatusEnum.BACK, newNodeAssignee);
        // 发送消息，新任务办理人待办事项+1
        workFlowUtils.sendMessage(workFlowUtils.getAssigneeIdList(newNodeAssignee), workflowKeyEnum, BusinessStatusEnum.WAITING, processInstance.getBusinessKey(), true);
        return actReturnResultVO;
    }

    /**
     * 根据当前员工GUID查询任务列表
     *
     * @param qo
     * @return
     */
    @Override
    public List<Task> getTaskListWaitByStaff(TaskQO qo) {
        String staffGuid = BaseContext.getStaffGuid();
        TaskQuery query = taskService.createTaskQuery()
                // 候选人或者办理人
                .taskCandidateOrAssigned(staffGuid)
                .orderByTaskCreateTime().asc();
        // 多条件匹配查询
        if (StringUtils.isNotEmpty(qo.getTaskName())) {
            query.taskNameLikeIgnoreCase("%" + qo.getTaskName() + "%");
        }
        if (StringUtils.isNotEmpty(qo.getProcessInstId())) {
            query.processInstanceId(qo.getProcessInstId());
        }
        if (StringUtils.isNotEmpty(qo.getTaskId())) {
            query.taskId(qo.getTaskId());
        }
        if (qo.getWorkflowKeyEnum() != null) {
            query.processDefinitionKey(workFlowUtils.getProcessInstanceKey(qo.getWorkflowKeyEnum()));
        }

        return query.list();
    }

    /**
     * 根据当前员工GUID查询已办任务列表
     *
     * @param qo
     * @return
     */
    @Override
    public List<HistoricTaskInstance> getTaskFinishByStaff(TaskQO qo) {
        //当前登录人
        String staffGuid = BaseContext.getStaffGuid();
        HistoricTaskInstanceQuery query = historyService.createHistoricTaskInstanceQuery()
                .taskAssignee(staffGuid).finished().orderByHistoricTaskInstanceStartTime().asc();
        if (StringUtils.isNotEmpty(qo.getTaskName())) {
            query.taskNameLikeIgnoreCase("%" + qo.getTaskName() + "%");
        }
        if (StringUtils.isNotEmpty(qo.getProcessInstId())) {
            query.processInstanceId(qo.getProcessInstId());
        }
        if (StringUtils.isNotEmpty(qo.getTaskId())) {
            query.taskId(qo.getTaskId());
        }
        if (qo.getWorkflowKeyEnum() != null) {
            query.processDefinitionKey(workFlowUtils.getProcessInstanceKey(qo.getWorkflowKeyEnum()));
        }
        return query.list();
    }

    /**
     * 根据当前员工GUID查询所有流程实例id（包含待办和已办）
     *
     * @param qo
     * @return
     */
    @Override
    public Set<String> getProcessInstanceIds(TaskQO qo) {
        Set<String> piIdSet = new HashSet<>();
        // 代办列表
        List<Task> taskWaitList = this.getTaskListWaitByStaff(qo);
        piIdSet.addAll(taskWaitList.stream().map(Task::getProcessInstanceId).collect(Collectors.toSet()));
        // 已办列表
        List<HistoricTaskInstance> taskFinishList = this.getTaskFinishByStaff(qo);
        piIdSet.addAll(taskFinishList.stream().map(HistoricTaskInstance::getProcessInstanceId).collect(Collectors.toSet()));

        return piIdSet;
    }
}
