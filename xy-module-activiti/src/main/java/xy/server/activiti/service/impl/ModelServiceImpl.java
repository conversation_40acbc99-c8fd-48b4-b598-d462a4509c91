package xy.server.activiti.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import lombok.RequiredArgsConstructor;
import org.activiti.editor.constants.ModelDataJsonConstants;
import org.activiti.engine.repository.Deployment;
import org.activiti.engine.repository.Model;
import org.activiti.engine.repository.ModelQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.activiti.common.constant.ActConstant;
import xy.server.activiti.entity.model.ro.ModelRO;
import xy.server.activiti.factory.WorkflowService;
import xy.server.activiti.service.IModelService;

import java.nio.charset.StandardCharsets;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ModelServiceImpl extends WorkflowService implements IModelService {

    private final ObjectMapper objectMapper;

    /**
     * 查询模型列表
     *
     * @param modelRO
     * @return
     */
    @Override
    public IPage<Model> getByPage(PageParams<ModelRO> modelRO) {
        ModelRO modelBo = modelRO.getModel();
        IPage<Model> list = modelRO.buildPage();
        ModelQuery query = repositoryService.createModelQuery();
        if (modelBo != null) {
            if (StringUtils.isNotBlank(modelBo.getName())) {
                query.modelNameLike("%" + modelBo.getName() + "%");
            }
            if (StringUtils.isNotBlank(modelBo.getKey())) {
                query.modelKey(modelBo.getKey());
            }
        }
        query.orderByLastUpdateTime().desc();
        //创建时间降序排列
        query.orderByCreateTime().desc();
        // 分页查询
        List<Model> modelList = query.listPage((int) modelRO.getCurrent(), (int) modelRO.getSize());
        if (CollectionUtil.isNotEmpty(modelList)) {
            modelList.forEach(e -> {
                boolean isNull = JSONUtil.isNull(JSONUtil.parseObj(e.getMetaInfo()).get(ModelDataJsonConstants.MODEL_DESCRIPTION));
                if (!isNull) {
                    e.setMetaInfo((String) JSONUtil.parseObj(e.getMetaInfo()).get(ModelDataJsonConstants.MODEL_DESCRIPTION));
                } else {
                    e.setMetaInfo("");
                }
            });
        }
        // 总记录数
        long total = query.count();
        list.setRecords(modelList);
        list.setTotal(total);
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Model add(ModelRO modelRO) {
        try {
            int version = 0;
            Model checkModel = repositoryService.createModelQuery().modelKey(modelRO.getKey()).singleResult();
            if (ObjectUtil.isNotNull(checkModel)) {
                throw new FlowException("模型key已存在", 100);
            }
            // 1. 初始空的模型
            Model model = repositoryService.newModel();
            model.setKey(modelRO.getKey());
            model.setName(modelRO.getName());
            model.setVersion(version);

            // 封装模型json对象
            ObjectNode objectNode = objectMapper.createObjectNode();
            objectNode.put(ModelDataJsonConstants.MODEL_NAME, modelRO.getName());
            objectNode.put(ModelDataJsonConstants.MODEL_REVISION, version);
            objectNode.put(ModelDataJsonConstants.MODEL_DESCRIPTION, modelRO.getDescription());
            model.setMetaInfo(objectNode.toString());
            // 保存初始化的模型基本信息数据
            repositoryService.saveModel(model);

            // 封装模型对象基础数据json串
            // {"id":"canvas","resourceId":"canvas","stencilset":{"namespace":"http://b3mn.org/stencilset/bpmn2.0#"},"properties":{"process_id":"未定义"}}
            ObjectNode editorNode = objectMapper.createObjectNode();
            ObjectNode stencilSetNode = objectMapper.createObjectNode();
            stencilSetNode.put("namespace", ActConstant.NAMESPACE);
            editorNode.replace("stencilset", stencilSetNode);
            // 标识key
            ObjectNode propertiesNode = objectMapper.createObjectNode();
            propertiesNode.put("process_id", modelRO.getKey());
            propertiesNode.put("name", modelRO.getName());
            editorNode.replace("properties", propertiesNode);
            repositoryService.addModelEditorSource(model.getId(), editorNode.toString().getBytes(StandardCharsets.UTF_8));
            return model;
        } catch (Exception e) {
            e.printStackTrace();
            throw new FlowException(e.getMessage(), 100);
        }
    }

    /**
     * 流程部署
     *
     * @param id
     * @return
     */
    @Override
    public boolean deploy(String id) {
        try {
            //1.查询流程定义模型xml
            byte[] xmlBytes = repositoryService.getModelEditorSource(id);
            if (xmlBytes == null) {
                throw new FlowException("模型数据为空，请先设计流程定义模型，再进行部署", 100);
            }
            //2. 查询流程定义模型的图片
            byte[] pngBytes = repositoryService.getModelEditorSourceExtra(id);

            // 查询模型的基本信息
            Model model = repositoryService.getModel(id);
            // xml资源的名称 ，对应act_ge_bytearray表中的name_字段
            String processName = model.getName() + ".bpmn20.xml";
            // 图片资源名称，对应act_ge_bytearray表中的name_字段
            String pngName = model.getName() + "." + model.getKey() + ".png";

            //3. 调用部署相关的api方法进行部署流程定义
            Deployment deployment = repositoryService.createDeployment()
                    // 部署名称
                    .name(model.getName())
                    // 部署标识key
                    .key(model.getKey())
                    // bpmn20.xml资源
                    .addBytes(processName, xmlBytes)
                    // png资源
                    .addBytes(pngName, pngBytes)
                    .deploy();

            // 更新 部署id 到流程定义模型数据表中
            model.setDeploymentId(deployment.getId());
            repositoryService.saveModel(model);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
