package xy.server.activiti.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xunyue.activiti.entity.ActDeploymentCondition;
import com.xunyue.activiti.entity.ActNodeAssignee;
import com.xunyue.activiti.service.IActDeploymentConditionService;
import com.xunyue.activiti.service.IActNodeAssigneeService;
import com.xunyue.common.util.StringUtils;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.BpmnAutoLayout;
import org.activiti.bpmn.converter.BpmnXMLConverter;
import org.activiti.bpmn.model.Process;
import org.activiti.bpmn.model.*;
import org.activiti.editor.language.json.converter.BpmnJsonConverter;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.repository.Deployment;
import org.activiti.engine.repository.DeploymentBuilder;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.repository.ProcessDefinitionQuery;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import xy.server.activiti.common.constant.ActConstant;
import xy.server.activiti.common.enums.DataJsonTypeEnum;
import xy.server.activiti.entity.model.qo.ProcessDefinitionQO;
import xy.server.activiti.entity.model.ro.ProcessDefinitionRO;
import xy.server.activiti.entity.model.vo.*;
import xy.server.activiti.factory.WorkflowService;
import xy.server.activiti.i18n.ResultErrorCode;
import xy.server.activiti.service.IProcessDefinitionService;
import xy.server.basic.entity.ErpSystemMgtFormSourceTypeValue;
import xy.server.basic.entity.model.vo.ErpSystemMgtFormSourceTypeValueVO;
import xy.server.basic.mapper.ErpSystemMgtFormSourceTypeValueMapper;

import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.zip.ZipInputStream;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProcessDefinitionServiceImpl extends WorkflowService implements IProcessDefinitionService {

    private final IActNodeAssigneeService iActNodeAssigneeService;
    private final IActDeploymentConditionService iActDeploymentConditionService;
    private final ErpSystemMgtFormSourceTypeValueMapper erpSystemMgtFormSourceTypeValueMapper;

    @Override
    public IPage<ProcessDefinitionVO> findPage(PageParams<ProcessDefinitionQO> pageParams) {
        IPage<ProcessDefinitionVO> list = pageParams.buildPage();
        ProcessDefinitionQO definitionBo = pageParams.getModel();
        ProcessDefinitionQuery query = repositoryService.createProcessDefinitionQuery();
        if (definitionBo != null) {
            if (!"".equals(definitionBo.getKey()) || definitionBo.getKey() != null) {
                query.processDefinitionKeyLike("%" + definitionBo.getKey() + "%");
            }
            if (!"".equals(definitionBo.getName()) || definitionBo.getName() != null) {
                query.processDefinitionNameLike("%" + definitionBo.getName() + "%");
            }
        }
        // 分页查询
        List<ProcessDefinitionVO> processDefinitionVoList = new ArrayList<>();
        int size = (int) pageParams.getSize();
        int firstResult = ((int) pageParams.getCurrent() - 1) * size;
        List<ProcessDefinition> definitionList = query.latestVersion().listPage(firstResult, size);
        for (ProcessDefinition processDefinition : definitionList) {
            // 部署时间
            Deployment deployment = repositoryService.createDeploymentQuery()
                    .deploymentId(processDefinition.getDeploymentId()).singleResult();
            ProcessDefinitionVO processDefinitionVo = BeanUtil.toBean(processDefinition, ProcessDefinitionVO.class);
            if (ObjectUtil.isNotEmpty(deployment) && deployment.getDeploymentTime() != null) {
                processDefinitionVo.setDeploymentTime(deployment.getDeploymentTime());
            }
            processDefinitionVoList.add(processDefinitionVo);
        }
        // 总记录数
        long total = query.count();
        list.setTotal(total);
        list.setRecords(processDefinitionVoList);
        return list;
    }

    /**
     * 删除流程定义
     *
     * @param deploymentId
     * @param definitionId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteDeployment(String deploymentId, String definitionId) {
        try {
            List<HistoricTaskInstance> taskInstanceList = historyService.createHistoricTaskInstanceQuery().processDefinitionId(definitionId).list();
            if (CollectionUtil.isNotEmpty(taskInstanceList)) {
                throw new FlowException("当前流程定义已被使用不可删除", 100);
            }
            repositoryService.deleteDeployment(deploymentId);
            if (!iActNodeAssigneeService.delByDefinitionId(definitionId)) {
                throw new FlowException("删除失败", 100);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 流程部署定义
     *
     * @param file
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deployByFile(MultipartFile file, String deployByFile) {
        try {
            // 文件名+后缀名
            String filename = file.getOriginalFilename();
            // 文件后缀名
            assert filename != null;
            String suffix = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
            InputStream inputStream = file.getInputStream();
            DeploymentBuilder deployment = repositoryService.createDeployment();
            if (ActConstant.ZIP.equals(suffix)) {
                // zip
                deployment.addZipInputStream(new ZipInputStream(inputStream));
            } else {
                // xml 或 bpmn
                deployment.addInputStream(filename, inputStream);
            }
            // 部署名称
            deployment.name(filename.substring(0, filename.lastIndexOf(".")));
//            String key = UUID.randomUUID().toString().replace("-","");
//            deployment.key(key);
            // 开始部署
            Deployment deployments = deployment.deploy();
            //表单来源表
//            ErpSystemMgtFormSource erpSystemMgtFormSource = erpSystemMgtFormSourceMapper.selectById(deployByFile);
//            erpSystemMgtFormSource.setActRepositoryGuid(deployments.getId());
//            erpSystemMgtFormSourceMapper.updateById(erpSystemMgtFormSource);
//            this.setting(deployments.getId());
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            throw new FlowException("部署失败" + e.getMessage(), 100);
        }
    }

    /**
     * 激活或挂起
     *
     * @param data
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateProcDefState(Map<String, Object> data) {
        try {
            String definitionId = data.get("definitionId").toString();
            String description = data.get("description").toString();
            //更新原因
            iActNodeAssigneeService.updateDescriptionById(definitionId, description);
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(definitionId).singleResult();
            if (processDefinition.isSuspended()) {
                // 将当前为挂起状态更新为激活状态
                // 参数说明：参数1：流程定义id,参数2：是否激活（true是否级联对应流程实例，激活了则对应流程实例都可以审批），
                // 参数3：什么时候激活，如果为null则立即激活，如果为具体时间则到达此时间后激活
                repositoryService.activateProcessDefinitionById(definitionId, true, null);
            } else {
                // 将当前为激活状态更新为挂起状态
                // 参数说明：参数1：流程定义id,参数2：是否挂起（true是否级联对应流程实例，挂起了则对应流程实例都不可以审批），
                // 参数3：什么时候挂起，如果为null则立即挂起，如果为具体时间则到达此时间后挂起
                repositoryService.suspendProcessDefinitionById(definitionId, true, null);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            throw new FlowException("操作失败" + e.getMessage(), 100);
        }
    }

    /**
     * 查询流程环节
     *
     * @param processDefinitionId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setting(String processDefinitionId) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        List<Process> processes = bpmnModel.getProcesses();
        List<ActProcessNodeVO> processNodeVoList = new ArrayList<>();
        Collection<FlowElement> elements = processes.get(0).getFlowElements();
        //获取开始节点后第一个节点
        ActProcessNodeVO firstNode = new ActProcessNodeVO();
        for (FlowElement element : elements) {
            if (element instanceof StartEvent) {
                List<SequenceFlow> outgoingFlows = ((StartEvent) element).getOutgoingFlows();
                for (SequenceFlow outgoingFlow : outgoingFlows) {
                    FlowElement flowElement = outgoingFlow.getTargetFlowElement();
                    if (flowElement instanceof UserTask) {
                        firstNode.setNodeId(flowElement.getId());
                        firstNode.setNodeName(flowElement.getName());
                        firstNode.setProcessDefinitionId(processDefinitionId);
                        firstNode.setIndex(0);
                    }
                }
            }
        }
        processNodeVoList.add(firstNode);
        for (FlowElement element : elements) {
            ActProcessNodeVO actProcessNodeVo = new ActProcessNodeVO();
            if (element instanceof UserTask && !firstNode.getNodeId().equals(element.getId())) {
                actProcessNodeVo.setNodeId(element.getId());
                actProcessNodeVo.setNodeName(element.getName());
                actProcessNodeVo.setProcessDefinitionId(processDefinitionId);
                actProcessNodeVo.setIndex(1);
                processNodeVoList.add(actProcessNodeVo);
            } else if (element instanceof SubProcess) {
                Collection<FlowElement> flowElements = ((SubProcess) element).getFlowElements();
                for (FlowElement flowElement : flowElements) {
                    ActProcessNodeVO actProcessNode = new ActProcessNodeVO();
                    if (flowElement instanceof UserTask && !firstNode.getNodeId().equals(flowElement.getId())) {
                        actProcessNode.setNodeId(flowElement.getId());
                        actProcessNode.setNodeName(flowElement.getName());
                        actProcessNode.setProcessDefinitionId(processDefinitionId);
                        actProcessNode.setIndex(1);
                        processNodeVoList.add(actProcessNode);
                    }
                }
            }
        }
        ActProcessNodeVO actProcessNodeVo = processNodeVoList.get(0);
        ActNodeAssignee actNodeAssignee = new ActNodeAssignee();
        actNodeAssignee.setProcessDefinitionId(processDefinitionId);
        actNodeAssignee.setNodeId(actProcessNodeVo.getNodeId());
        actNodeAssignee.setNodeName(actProcessNodeVo.getNodeName());
        actNodeAssignee.setIsShow(false);
        actNodeAssignee.setIsBack(true);
        actNodeAssignee.setMultiple(false);
        actNodeAssignee.setIndex(0);
        ActNodeAssignee info = iActNodeAssigneeService.getInfo(actProcessNodeVo.getProcessDefinitionId(), actProcessNodeVo.getNodeId());
        if (ObjectUtil.isEmpty(info)) {
            iActNodeAssigneeService.delByDefinitionIdAndNodeId(actProcessNodeVo.getProcessDefinitionId(), actProcessNodeVo.getNodeId());
            iActNodeAssigneeService.add(actNodeAssignee);
        }
        return true;
    }

    /**
     * 查看xml文件
     *
     * @param deploymentId
     * @return
     */
    @Override
    public ProcessDataVO getProcessData(String deploymentId, String formSourceKey, String formSourceTypeValue) {
        StringBuilder xml = new StringBuilder();

        InputStream inputStream = null;
        try {
            // 拼接流程部署表name_字段
            String deploymentName = formSourceKey + ActConstant.DEPLOYMENT_KEY_LINK + formSourceTypeValue + ActConstant.BPMN_SUFFIX;
            inputStream = repositoryService.getResourceAsStream(deploymentId, deploymentName);
            xml.append(IOUtils.toString(inputStream, ActConstant.UTF_8));

            ProcessDataVO processDataVO = new ProcessDataVO();
            processDataVO.setFile(xml.toString());
            processDataVO.setFormSourceKey(formSourceKey);

            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploymentId).singleResult();

            List<ActDeploymentCondition> actDeploymentConditions = iActDeploymentConditionService.list(Wrappers.<ActDeploymentCondition>lambdaQuery()
                    .eq(ActDeploymentCondition::getProcessDefinitionId, processDefinition.getId()));
            List<ActNodeAssignee> actNodeAssignees = iActNodeAssigneeService.list(Wrappers.<ActNodeAssignee>lambdaQuery()
                    .eq(ActNodeAssignee::getProcessDefinitionId, processDefinition.getId()));

            List<DataJsonVO> dataJsonVOS = new ArrayList<>(actDeploymentConditions.size() + actNodeAssignees.size());
            actDeploymentConditions.forEach(entity -> {
                DataJsonVO vo = new DataJsonVO();
                vo.setType(DataJsonTypeEnum.CONDITIONS.getType());
                vo.setConditionExpressionId(entity.getConditionExpressionId());
                vo.setData(entity.getTermJson());
                dataJsonVOS.add(vo);
            });
            actNodeAssignees.forEach(entity -> {
                DataJsonVO vo = new DataJsonVO();
                vo.setType(DataJsonTypeEnum.NODT.getType());
                vo.setNodeId(entity.getNodeId());
                vo.setData(JSON.toJSONString(entity));
                dataJsonVOS.add(vo);
            });
            processDataVO.setJson(JSON.toJSONString(dataJsonVOS));

            return processDataVO;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {

            }
        }

        return null;
    }


    @Override
    public ProcessDataJsonVO getProcessJson(String deploymentId, String formSourceKey, String formSourceTypeValue) {

        InputStream inputStream = null;
        InputStreamReader in = null;
        XMLStreamReader xtr = null;
        try {
            // 拼接流程部署表name_字段
            String deploymentName = formSourceKey + ActConstant.DEPLOYMENT_KEY_LINK + formSourceTypeValue + ActConstant.BPMN_SUFFIX;
            inputStream = repositoryService.getResourceAsStream(deploymentId, deploymentName);

            in = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
            XMLInputFactory xif = XMLInputFactory.newInstance();
            xtr = xif.createXMLStreamReader(in);

            // 然后转为bpmnModel
            BpmnModel bpmnModel = new BpmnXMLConverter().convertToBpmnModel(xtr);
            // bpmnModel转json
            BpmnJsonConverter converter = new BpmnJsonConverter();

            ObjectNode editorJsonNode = converter.convertToJson(bpmnModel);

            ProcessDataJsonVO processDataJsonVO = new ProcessDataJsonVO();
            processDataJsonVO.setEditorJsonNode(editorJsonNode);

            processDataJsonVO.setFormSourceKey(formSourceKey);
            processDataJsonVO.setFormSourceTypeValue(formSourceTypeValue);

            ErpSystemMgtFormSourceTypeValueVO erpSystemMgtFormSourceTypeValue = erpSystemMgtFormSourceTypeValueMapper.getXyFlowJson(formSourceKey, formSourceTypeValue);
            if (erpSystemMgtFormSourceTypeValue != null) {
                processDataJsonVO.setXyFlowJson(erpSystemMgtFormSourceTypeValue.getXyFlowJson());
            }

            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploymentId).singleResult();

            List<ActDeploymentCondition> actDeploymentConditions = iActDeploymentConditionService.list(Wrappers.<ActDeploymentCondition>lambdaQuery()
                    .eq(ActDeploymentCondition::getProcessDefinitionId, processDefinition.getId()));
            List<ActNodeAssignee> actNodeAssignees = iActNodeAssigneeService.list(Wrappers.<ActNodeAssignee>lambdaQuery()
                    .eq(ActNodeAssignee::getProcessDefinitionId, processDefinition.getId()));

            List<DataJsonVO> dataJsonVOS = new ArrayList<>(actDeploymentConditions.size() + actNodeAssignees.size());
            actDeploymentConditions.forEach(entity -> {
                DataJsonVO vo = new DataJsonVO();
                vo.setType(DataJsonTypeEnum.CONDITIONS.getType());
                vo.setConditionExpressionId(entity.getConditionExpressionId());
                vo.setData(entity.getTermJson());
                dataJsonVOS.add(vo);
            });
            actNodeAssignees.forEach(entity -> {
                DataJsonVO vo = new DataJsonVO();
                vo.setType(DataJsonTypeEnum.NODT.getType());
                vo.setNodeId(entity.getNodeId());
                vo.setData(JSON.toJSONString(entity));
                dataJsonVOS.add(vo);
            });
            processDataJsonVO.setJson(JSON.toJSONString(dataJsonVOS));

            return processDataJsonVO;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (in != null) {
                    in.close();
                }
                if (xtr != null) {
                    xtr.close();
                }
            } catch (Exception e) {

            }

        }

        return null;
    }

    /**
     * 部署/保存或更新
     *
     * @param ro
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveData(ProcessDefinitionRO ro) {
        ErpSystemMgtFormSourceTypeValue formSourceTypeValue = erpSystemMgtFormSourceTypeValueMapper
                .getFormSourceTypeValue(ro.getFormSourceKey(), ro.getFormSourceTypeValue());
        if (formSourceTypeValue == null) {
            throw new FlowException(ResultErrorCode.NOT_FORM_SOURCE);
        }
        if (StringUtils.isNotEmpty(formSourceTypeValue.getActRepositoryGuid())) {
            // 校验旧流程是否全部结束
            // 暂时注释掉
            // todo 这里兼容性不好，只改了后面流程，前面流程没有变化也不能调整
//            List<ProcessInstance> processInstanceList = runtimeService.createProcessInstanceQuery().deploymentId(formSourceTypeValue.getActRepositoryGuid()).active().list();
//            if (CollectionUtil.isNotEmpty(processInstanceList)) {
//                throw new FlowException(ResultErrorCode.PROCESS_HAS_NOT_BEEN_COMPLETED);
//            }
        }
        // 流程部署
        Deployment deployment = this.deployByBytes(ro);
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deployment.getId()).singleResult();
        if (ObjectUtil.isNull(processDefinition)) {
            throw new FlowException(ResultErrorCode.DEPLOYMENT_FAILURE);
        }
        if (StrUtil.isNotBlank(ro.getJson())) {
            List<DataJsonVO> dataJsonVO = JSON.parseArray(ro.getJson(), DataJsonVO.class);
            for (DataJsonVO jsonVO : dataJsonVO) {
                // 类型 0-节点，1-条件
                Integer type = jsonVO.getType();
                // 审核节点
                if (type.equals(DataJsonTypeEnum.NODT.getType())) {
                    ActNodeAssignee actNodeAssignee = JSON.parseObject(jsonVO.data, ActNodeAssignee.class);
                    actNodeAssignee.setProcessDefinitionId(processDefinition.getId());
                    iActNodeAssigneeService.add(actNodeAssignee);
                }
                // 条件线段
                if (type.equals(DataJsonTypeEnum.CONDITIONS.getType())) {
                    ActDeploymentCondition actDeploymentCondition = new ActDeploymentCondition();
                    actDeploymentCondition.setProcessDefinitionId(processDefinition.getId());
                    actDeploymentCondition.setConditionExpressionId(jsonVO.conditionExpressionId);
                    actDeploymentCondition.setTermJson(jsonVO.data);
                    iActDeploymentConditionService.save(actDeploymentCondition);
                }
            }
        }
        formSourceTypeValue.setActRepositoryGuid(deployment.getId());
        formSourceTypeValue.setXyFlowJson(JSON.parse(ro.getXyJson()));
        erpSystemMgtFormSourceTypeValueMapper.updateById(formSourceTypeValue);
        return true;
    }

    // 部署
    public Deployment deployFile(MultipartFile file) {
        try {
            // 文件名+后缀名
            String filename = file.getOriginalFilename();
            // 文件后缀名
            assert filename != null;
            String suffix = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
            InputStream inputStream = file.getInputStream();
            DeploymentBuilder deployment = repositoryService.createDeployment();
            if (ActConstant.ZIP.equals(suffix)) {
                // zip
                deployment.addZipInputStream(new ZipInputStream(inputStream));
            } else {
                // xml 或 bpmn
                deployment.addInputStream(filename, inputStream);
            }
            // 部署名称
            deployment.name(filename.substring(0, filename.lastIndexOf(".")));
            String key = UUID.randomUUID().toString().replace("-", "");
            deployment.key(key);
            // 开始部署
            Deployment deployments = deployment.deploy();
            return deployments;
        } catch (IOException e) {
            e.printStackTrace();
            throw new FlowException(ResultErrorCode.DEPLOYMENT_FAILURE);
        }
    }

    // 部署
    @Transactional(rollbackFor = Exception.class)
    public Deployment deployByBytes(ProcessDefinitionRO ro) {
        try {
            // 文件名+后缀名
            String key = ro.getFormSourceKey() + ActConstant.DEPLOYMENT_KEY_LINK + ro.getFormSourceTypeValue();
            String filename = key + ActConstant.BPMN_SUFFIX;
            // readTree(这里面是json)
            ObjectNode modelNode = (ObjectNode) new ObjectMapper().readTree(ro.getFile().getBytes(StandardCharsets.UTF_8));
            // 转model
            BpmnModel model = new BpmnJsonConverter().convertToBpmnModel(modelNode);
            new BpmnAutoLayout(model).execute();
            // 转xml
            byte[] bpmnBytes = new BpmnXMLConverter().convertToXML(model);
            String xmlData = new String(bpmnBytes);

            log.info(xmlData);

            Deployment deploy = repositoryService.createDeployment()
                    .addString(filename, xmlData)
                    .name(filename)
                    .key(key)
                    .deploy();
            return deploy;
        } catch (Exception e) {
            e.printStackTrace();
            throw new FlowException(ResultErrorCode.DEPLOYMENT_FAILURE);
        }
    }
}
