package xy.server.activiti.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.activiti.engine.history.HistoricActivityInstance;
import xy.server.activiti.entity.ActHiActinst;
import xy.server.activiti.entity.model.vo.ActHistoryInfoVO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @data 2023/9/5 17:34
 * @apiNote act历史活动节点表service
 */
public interface IActHiActinstService extends IService<ActHiActinst> {

    void deleteInvalidData(@NotNull HistoricActivityInstance historicActivityInstance);

    void deleteByActId(@NotBlank String procInstId, @NotEmpty Set<String> actIds, boolean isFinish);

    /**
     * 获取审核记录列表
     * @param businessKey
     * @param processDefinitionKey
     * @return
     */
    List<ActHistoryInfoVO> findHistoryInfoList(String businessKey, String processDefinitionKey);

    /**
     * 批量获取最新一条审核记录
     * @param businessKeys
     * @param formSourceKey
     * @param formSourceTypeValue
     * @return
     */
    List<ActHistoryInfoVO> batchGetLatestHistoryInfo(List<String> businessKeys, String formSourceKey, String formSourceTypeValue);
}
