package xy.server.activiti.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.activiti.engine.history.HistoricActivityInstance;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import xy.server.activiti.entity.ActHiActinst;
import xy.server.activiti.entity.model.vo.ActHistoryInfoVO;
import xy.server.activiti.mapper.ActHiActinstMapper;
import xy.server.activiti.service.IActHiActinstService;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @data 2023/9/5 17:46
 * @apiNote act历史活动节点实现类
 */
@Service
public class ActHiActinstServiceImpl extends ServiceImpl<ActHiActinstMapper, ActHiActinst> implements IActHiActinstService {
    @Override
    public void deleteInvalidData(HistoricActivityInstance historicActivityInstance) {
        // 查询旧节点数据
        ActHiActinst actHiActinst = baseMapper.selectOne(Wrappers.<ActHiActinst>lambdaQuery()
                .eq(ActHiActinst::getProcInstId, historicActivityInstance.getProcessInstanceId())
                .eq(ActHiActinst::getActId, historicActivityInstance.getActivityId())
                .isNotNull(ActHiActinst::getEndTime)
                .ne(ActHiActinst::getId, historicActivityInstance.getId()));
        if (!ObjectUtils.isEmpty(actHiActinst)) {
            baseMapper.delete(Wrappers.<ActHiActinst>lambdaQuery()
                    .eq(ActHiActinst::getProcInstId, historicActivityInstance.getProcessInstanceId())
                    .isNotNull(ActHiActinst::getEndTime)
                    .between(ActHiActinst::getEndTime, actHiActinst.getEndTime(), historicActivityInstance.getStartTime())
                    .ne(ActHiActinst::getId, historicActivityInstance.getId()));
        }
    }

    @Override
    public void deleteByActId(String procInstId, Set<String> actIds, boolean isFinish) {
        baseMapper.deleteByActId(procInstId, actIds, isFinish);
    }

    /**
     * 获取审核记录列表
     * @param businessKey
     * @param processDefinitionKey
     * @return
     */
    @Override
    public List<ActHistoryInfoVO> findHistoryInfoList(String businessKey, String processDefinitionKey) {
        return baseMapper.findHistoryInfoList(businessKey, processDefinitionKey);
    }

    /**
     * 批量获取最新一条审核记录
     * @param businessKeys
     * @param formSourceKey
     * @param formSourceTypeValue
     * @return
     */
    @Override
    public List<ActHistoryInfoVO> batchGetLatestHistoryInfo(List<String> businessKeys, String formSourceKey, String formSourceTypeValue) {
        return baseMapper.batchGetLatestHistoryInfo(businessKeys, formSourceKey, formSourceTypeValue);
    }
}
