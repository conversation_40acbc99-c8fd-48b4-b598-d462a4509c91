package xy.server.activiti.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.config.util.PageParams;
import org.activiti.engine.repository.Model;
import xy.server.activiti.entity.model.ro.ModelRO;

public interface IModelService {
    /**
     * 查询模型列表
     *
     * @param modelRO
     * @return
     */
    IPage<Model> getByPage(PageParams<ModelRO> modelRO);

    /**
     * 新建模型
     *
     * @param modelRO
     * @return
     */
    Model add(ModelRO modelRO);

    /**
     * 流程部署
     *
     * @param id
     * @return
     */
    boolean deploy(String id);
}
