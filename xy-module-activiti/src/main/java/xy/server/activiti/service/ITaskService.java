package xy.server.activiti.service;

import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.task.Task;
import xy.server.activiti.entity.model.qo.TaskQO;
import xy.server.activiti.entity.model.ro.ActAuditProcessRO;
import xy.server.activiti.entity.model.vo.ActReturnResultVO;

import java.util.List;
import java.util.Set;

public interface ITaskService {

    /**
     * 完成任务
     * @param auditProcessRO
     * @param task
     */
    ActReturnResultVO completeTask(ActAuditProcessRO auditProcessRO, Task task);

    /**
     * 驳回任务
     * @param auditProcessRO
     * @param task
     * @return
     */
    ActReturnResultVO rejectTask(ActAuditProcessRO auditProcessRO, Task task);

    /**
     * 根据当前员工GUID查询待办任务列表
     * @param qo
     * @return
     */
    List<Task> getTaskListWaitByStaff(TaskQO qo);

    /**
     * 根据当前员工GUID查询已办任务列表
     * @param qo
     * @return
     */
    List<HistoricTaskInstance> getTaskFinishByStaff(TaskQO qo);

    /**
     * 根据当前员工GUID查询所有流程实例id（包含待办和已办）
     * @param qo
     * @return
     */
    Set<String> getProcessInstanceIds(TaskQO qo);

}
