package xy.server.activiti.service;

import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.entity.model.ro.ActAuditProcessRO;
import xy.server.activiti.entity.model.vo.ActHistoryInfoVO;
import xy.server.activiti.entity.model.vo.ActMessageDTO;
import xy.server.activiti.entity.model.vo.ActReturnResultVO;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface IProcessInstanceService {

    /**
     * 提交审核（申请人节点）
     * @param auditProcessRO
     * @return
     */
    ActReturnResultVO commitAudit(ActAuditProcessRO auditProcessRO);

    /**
     * 撤销提交
     * @param auditProcessRO
     * @return
     */
    ActReturnResultVO cancelCommitAudit(ActAuditProcessRO auditProcessRO);

    /**
     * 审核（通过/驳回）
     * @param result
     * @param auditProcessRO
     * @return
     */
    ActReturnResultVO audit(Boolean result, ActAuditProcessRO auditProcessRO);

    /**
     * 撤销审核
     * @param auditProcessRO
     * @return
     */
    ActReturnResultVO cancelAudit(ActAuditProcessRO auditProcessRO);

    /**
     * 重启流程
     * @param auditProcessRO
     * @return
     */
    ActReturnResultVO restartProcess(ActAuditProcessRO auditProcessRO);

    /**
     * 强制重置流程
     * @param auditProcessRO
     * @return
     */
    ActReturnResultVO forcedResetProcess(ActAuditProcessRO auditProcessRO);
    /**
     * 查询流程审批记录
     * @param businessKey
     * @param formSourceKey
     * @param formSourceTypeValue
     * @return
     */
    List<ActHistoryInfoVO> getHistoryInfoList(String businessKey, String formSourceKey, String formSourceTypeValue);

    /**
     * 通过流程实例id获取历史流程图
     * @param processInstanceId
     * @param response
     */
    void getHistoryProcessImage(String processInstanceId, HttpServletResponse response);

    /**
     * 删除程实例，删除历史记录，删除业务与流程关联信息
     * @param businessKey
     * @param formSourceKey
     * @param formSourceTypeValue
     * @return
     */
    default ActReturnResultVO deleteProcessAndHisInst(String businessKey, String formSourceKey, String formSourceTypeValue) {
        WorkflowKeyEnum workflowKeyEnum = WorkflowKeyEnum.getEumByKey(formSourceKey, formSourceTypeValue);
        return this.deleteProcessAndHisInst(businessKey, workflowKeyEnum);
    }

    ActReturnResultVO deleteProcessAndHisInst(String businessKey, WorkflowKeyEnum workflowKeyEnum);

    /**
     * 启动流程（创建流程实例）
     * @param workflowKeyEnum
     * @param businessKey 业务id
     * @param variables 流程变量Map
     * @return
     */
    ActReturnResultVO start(WorkflowKeyEnum workflowKeyEnum, String businessKey, Map<String, Object> variables);

    default ActReturnResultVO start(WorkflowKeyEnum workflowKeyEnum, String businessKey) {
        return this.start(workflowKeyEnum, businessKey, new HashMap<>(1));
    }

    /**
     * 获取当前登录人的流程相关待办事项信息
     * @return
     */
    List<ActMessageDTO> getActMessageList();

    /**
     * 批量提交审核（申请人节点）
     * @param auditProcessROList
     * @return
     */
    List<ActReturnResultVO> batchCommitAudit(List<ActAuditProcessRO> auditProcessROList);

    /**
     * 批量撤销提交
     * @param auditProcessROList
     * @return
     */
    List<ActReturnResultVO> batchCancelCommitAudit(List<ActAuditProcessRO> auditProcessROList);

    /**
     * 批量审核（通过/驳回）
     * @param result
     * @param auditProcessROList
     * @return
     */
    List<ActReturnResultVO> batchAudit(Boolean result, List<ActAuditProcessRO> auditProcessROList);

    /**
     * 批量撤销审核
     * @param auditProcessROList
     * @return
     */
    List<ActReturnResultVO> batchCancelAudit(List<ActAuditProcessRO> auditProcessROList);

    /**
     * 批量重启流程
     * @param auditProcessROList
     * @return
     */
    List<ActReturnResultVO> batchRestartProcess(List<ActAuditProcessRO> auditProcessROList);

    /**
     * 批量强制重置流程
     * @param auditProcessROList
     * @return
     */
    List<ActReturnResultVO> batchForcedResetProcess(List<ActAuditProcessRO> auditProcessROList);

    /**
     * 批量作废流程
     * @param auditProcessROList
     * @return
     */
    List<ActReturnResultVO> batchInvalidProcess(List<ActAuditProcessRO> auditProcessROList);

    /**
     * 作废流程
     * @param actAuditProcessRO
     * @return
     */
    ActReturnResultVO invalidProcess(ActAuditProcessRO actAuditProcessRO);
}
