package xy.server.activiti.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.config.util.PageParams;
import org.springframework.web.multipart.MultipartFile;
import xy.server.activiti.entity.model.qo.ProcessDefinitionQO;
import xy.server.activiti.entity.model.ro.ProcessDefinitionRO;
import xy.server.activiti.entity.model.vo.ProcessDataJsonVO;
import xy.server.activiti.entity.model.vo.ProcessDataVO;
import xy.server.activiti.entity.model.vo.ProcessDefinitionVO;

import java.util.Map;

public interface IProcessDefinitionService {

    /**
     * 分页查询流程定义列表
     *
     * @param processDefinition
     * @return
     */
    IPage<ProcessDefinitionVO> findPage(PageParams<ProcessDefinitionQO> processDefinition);

    /**
     * 删除流程定义
     *
     * @param deploymentId
     * @param definitionId
     * @return
     */
    Boolean deleteDeployment(String deploymentId, String definitionId);

    /**
     * 通过zip或xml部署流程定义
     *
     * @param file
     * @return
     */
    Boolean deployByFile(MultipartFile file, String formSourceGuid);

    /**
     * 激活或者挂起流程定义
     *
     * @param data
     * @return
     */
    Boolean updateProcDefState(Map<String, Object> data);

    /**
     * 查询流程环节
     *
     * @param processDefinitionId
     * @return
     */
    Boolean setting(String processDefinitionId);

    /**
     * 查看xml文件
     *
     * @param deploymentId
     * @return
     */
    ProcessDataVO getProcessData(String deploymentId, String formSourceKey, String formSourceTypeValue);

    /**
     * 查看xml文件并转成JSON
     * @param deploymentId
     * @param formSourceKey
     * @param formSourceTypeValue
     * @return
     */
    ProcessDataJsonVO getProcessJson(String deploymentId, String formSourceKey, String formSourceTypeValue);

    /**
     * 部署/保存或更新
     * @return
     */
    Boolean saveData(ProcessDefinitionRO ro);
}
