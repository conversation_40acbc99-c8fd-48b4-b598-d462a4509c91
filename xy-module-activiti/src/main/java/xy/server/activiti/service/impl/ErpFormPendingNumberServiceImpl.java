package xy.server.activiti.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.util.StringAssembler;
import com.xunyue.config.util.PageParams;
import com.xy.util.BaseContext;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.entity.ErpFormPendingNumber;
import xy.server.activiti.entity.model.qo.ErpFormPendingNumberQO;
import xy.server.activiti.entity.model.vo.ErpFormPendingNumberVO;
import xy.server.activiti.entity.model.vo.ProcessNavigationMessageDTO;
import xy.server.activiti.mapper.ErpFormPendingNumberMapper;
import xy.server.activiti.service.IErpFormPendingNumberService;
import xy.server.activiti.utils.WorkFlowUtils;
import xy.server.activiti.utils.formPendingEvent.FormPendingEventSource;
import xy.server.filter.entity.ErpViewFilterConfig;
import xy.server.filter.enums.TableIdentificationEnum;
import xy.server.filter.enums.ViewFilterConfigTypeEnum;
import xy.server.filter.service.IErpViewFilterConfigService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 待开数量记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-30
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service
public class ErpFormPendingNumberServiceImpl extends ServiceImpl<ErpFormPendingNumberMapper, ErpFormPendingNumber> implements IErpFormPendingNumberService {

    private final ApplicationEventPublisher applicationEventPublisher;
    private final IErpViewFilterConfigService iErpViewFilterConfigService;
    private final WorkFlowUtils workFlowUtils;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePendingNumber(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum, BigDecimal pendingNumber) {

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePendingNumber(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum, BigDecimal pendingNumber,
                                    TableIdentificationEnum tableIdentificationEnum, Map<String, String> paramMap) {

    }

    /**
     * 更新待开数量（弃用）
     *
     * @param workflowKeyEnum       表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     * @param pendingNumber         待开数量
     */
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public synchronized void updatePendingNumber_Discard(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum, BigDecimal pendingNumber) {
        ErpFormPendingNumber entity = this.selectOne(workflowKeyEnum, sourceWorkflowKeyEnum, null);
        entity = ObjectUtil.isNull(entity) ? new ErpFormPendingNumber(workflowKeyEnum, sourceWorkflowKeyEnum) : entity;
        entity.setPendingNumber(pendingNumber);
        super.saveOrUpdate(entity);
        // 发送mqtt消息队列
        //applicationEventPublisher.publishEvent(new ActMessageEventSource(ActConstant.FORM_PENDING_NUMBER_MQTT_TOPIC, this.findPendingNumberList()));
    }

    /**
     * 更新待开数量 （弃用）
     *
     * @param workflowKeyEnum         表单标识枚举
     * @param sourceWorkflowKeyEnum   来源表单标识枚举
     * @param pendingNumber           待开数量
     * @param tableIdentificationEnum 表格标识枚举
     * @param paramMap                参数Map
     */
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public synchronized void updatePendingNumber_Discard(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum, BigDecimal pendingNumber,
                                                         TableIdentificationEnum tableIdentificationEnum, Map<String, String> paramMap) {
        // 实体列表，做批量更新保存用
        List<ErpFormPendingNumber> entityList = new ArrayList<>();
        // 1.先保存一条不关联配置guid数据
        ErpFormPendingNumber entity = this.selectOne(workflowKeyEnum, sourceWorkflowKeyEnum, null);
        entity = ObjectUtil.isNull(entity) ? new ErpFormPendingNumber(workflowKeyEnum, sourceWorkflowKeyEnum) : entity;
        entity.setPendingNumber(pendingNumber);
        entityList.add(entity);

        // 2.根据表格标识查询视图筛选配置数据，循环保存待开数量关联配置guid数据
        this.buildDataByTableIdentification(workflowKeyEnum, sourceWorkflowKeyEnum, tableIdentificationEnum, paramMap, entityList);

        super.saveOrUpdateBatch(entityList);
    }

    /**
     * 更新待开数量（发布事件出去，由业务自行计算待开数量）
     *
     * @param workflowKeyEnum       表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void updatePendingNumber(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum) {
        applicationEventPublisher.publishEvent(new FormPendingEventSource(workflowKeyEnum, sourceWorkflowKeyEnum));
    }

    /**
     * 更新待开数量（发布事件出去，由业务自行计算待开数量）
     *
     * @param workflowKeyEnum         表单标识枚举
     * @param sourceWorkflowKeyEnum   来源表单标识枚举
     * @param tableIdentificationEnum 表格标识枚举
     * @param paramMap                参数Map
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void updatePendingNumber(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum,
                                                 TableIdentificationEnum tableIdentificationEnum, Map<String, String> paramMap) {
        FormPendingEventSource formPendingEventSource = new FormPendingEventSource(workflowKeyEnum, sourceWorkflowKeyEnum, tableIdentificationEnum, paramMap);
        applicationEventPublisher.publishEvent(formPendingEventSource);
    }

    /**
     * 待开数量+1
     *
     * @param workflowKeyEnum       表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void plusOne(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum) {
        ErpFormPendingNumber entity = selectOne(workflowKeyEnum, sourceWorkflowKeyEnum, null);
        entity = ObjectUtil.isNull(entity) ? new ErpFormPendingNumber(workflowKeyEnum, sourceWorkflowKeyEnum) : entity;
        entity.setPendingNumber(entity.getPendingNumber().add(BigDecimal.ONE));
        super.saveOrUpdate(entity);
        // 发送mqtt消息队列
        //applicationEventPublisher.publishEvent(new ActMessageEventSource(ActConstant.FORM_PENDING_NUMBER_MQTT_TOPIC, this.findPendingNumberList()));
    }

    /**
     * 待开数量+1
     *
     * @param workflowKeyEnum         表单标识枚举
     * @param sourceWorkflowKeyEnum   来源表单标识枚举
     * @param tableIdentificationEnum 表格标识枚举
     * @param paramMap                参数Map
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void plusOne(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum,
                                     TableIdentificationEnum tableIdentificationEnum, Map<String, String> paramMap) {
        // 实体列表，做批量更新保存用
        List<ErpFormPendingNumber> entityList = new ArrayList<>();
        // 1.先保存一条不关联配置guid数据
        ErpFormPendingNumber entity = selectOne(workflowKeyEnum, sourceWorkflowKeyEnum, null);
        entity = ObjectUtil.isNull(entity) ? new ErpFormPendingNumber(workflowKeyEnum, sourceWorkflowKeyEnum) : entity;
        entity.setPendingNumber(entity.getPendingNumber().add(BigDecimal.ONE));
        entityList.add(entity);
        // 2.根据表格标识查询视图筛选配置数据，循环保存待开数量关联配置guid数据
        this.buildDataByTableIdentification(workflowKeyEnum, sourceWorkflowKeyEnum, tableIdentificationEnum, paramMap, entityList);

        super.saveOrUpdateBatch(entityList);
    }

    /**
     * 待开数量-1
     *
     * @param workflowKeyEnum       表单标识枚举
     * @param sourceWorkflowKeyEnum 来源表单标识枚举
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void minusOne(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum) {
        ErpFormPendingNumber entity = this.selectOne(workflowKeyEnum, sourceWorkflowKeyEnum, null);
        if (ObjectUtil.isNotNull(entity)) {
            entity.setPendingNumber(entity.getPendingNumber().subtract(BigDecimal.ONE));
            super.updateById(entity);
            // 发送mqtt消息队列
            //applicationEventPublisher.publishEvent(new ActMessageEventSource(ActConstant.FORM_PENDING_NUMBER_MQTT_TOPIC, this.findPendingNumberList()));
        }
    }

    /**
     * 待开数量-1
     *
     * @param workflowKeyEnum         表单标识枚举
     * @param sourceWorkflowKeyEnum   来源表单标识枚举
     * @param tableIdentificationEnum 表格标识枚举
     * @param paramMap                参数Map
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void minusOne(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum,
                                      TableIdentificationEnum tableIdentificationEnum, Map<String, String> paramMap) {
        ErpFormPendingNumber entity = this.selectOne(workflowKeyEnum, sourceWorkflowKeyEnum, null);
        if (ObjectUtil.isNotNull(entity)) {
            // 实体列表，做批量更新保存用
            List<ErpFormPendingNumber> entityList = new ArrayList<>();
            // 1.先保存一条不关联配置guid数据
            entity.setPendingNumber(entity.getPendingNumber().subtract(BigDecimal.ONE));
            entityList.add(entity);
            // 2.根据表格标识查询视图筛选配置数据，循环保存待开数量关联配置guid数据
            this.buildDataByTableIdentification(workflowKeyEnum, sourceWorkflowKeyEnum, tableIdentificationEnum, paramMap, entityList);

            super.saveOrUpdateBatch(entityList);
        }
    }

    /**
     * 查询表单待开数量列表
     *
     * @return
     */
    @Override
    public List<ErpFormPendingNumberVO> findCurrentStaffPendingNumberList() {
        // 1.查询当前员工绑定配置的待开数量数据列表
        List<ErpFormPendingNumberVO> currentStaffConfigPendingNumberList = baseMapper.selectCurrentStaffConfigPendingNumberList(BaseContext.getStaffGuid());
        // 2.查询所有未绑定视图筛选配置guid的待开数量数据列表
        List<ErpFormPendingNumber> entityList = baseMapper.selectList(Wrappers.<ErpFormPendingNumber>lambdaQuery()
                .isNull(ErpFormPendingNumber::getViewFilterConfigGuid)
                .gt(ErpFormPendingNumber::getPendingNumber, BigDecimal.ZERO));
        // 3.两个列表合并，同一个表单标识以绑定配置的为准
        List<ErpFormPendingNumberVO> collect = entityList.stream().map(entity -> {
            ErpFormPendingNumberVO oneData = currentStaffConfigPendingNumberList.stream().filter(item -> {
                if (StringUtils.isEmpty(item.getSourceFormKey()) || StringUtils.isEmpty(item.getSourceFormTypeValue())) {
                    return item.getFormKey().equals(entity.getFormKey()) && item.getFormTypeValue().equals(entity.getFormTypeValue());
                } else {
                    return item.getFormKey().equals(entity.getFormKey()) && item.getFormTypeValue().equals(entity.getFormTypeValue())
                            && item.getSourceFormKey().equals(entity.getSourceFormKey()) && item.getSourceFormTypeValue().equals(entity.getSourceFormTypeValue());
                }
            }).findFirst().orElse(null);
            return ObjectUtil.isNull(oneData) ? BeanUtil.toBean(entity, ErpFormPendingNumberVO.class) : null;
        }).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        return Stream.concat(currentStaffConfigPendingNumberList.stream(), collect.stream()).collect(Collectors.toList());
    }

    @Override
    public List<ErpFormPendingNumberVO> findList(ErpFormPendingNumberQO qo) {
        return baseMapper.findList(qo);
    }

    @Override
    public IPage<ErpFormPendingNumberVO> findPage(PageParams<ErpFormPendingNumberQO> pageParams) {
        IPage<ErpFormPendingNumberVO> page = pageParams.buildPage();
        ErpFormPendingNumberQO model = pageParams.getModel();
        return baseMapper.findPage(page, model);
    }

    /**
     * 查询当前员工流程导航消息
     *
     * @return
     */
    @Override
    public ProcessNavigationMessageDTO getProcessNavigationMessage() {
        ProcessNavigationMessageDTO processNavigationMessageDTO = new ProcessNavigationMessageDTO();
        processNavigationMessageDTO.setActMessageList(workFlowUtils.findCurrentStaffActMessageList());
        processNavigationMessageDTO.setFormPendingNumberList(this.findCurrentStaffPendingNumberList());
        return processNavigationMessageDTO;
    }

    /**
     * 根据流程枚举查询一条数据
     *
     * @param workflowKeyEnum
     * @param sourceWorkflowKeyEnum
     * @param viewFilterConfigGuid
     * @return
     */
    private ErpFormPendingNumber selectOne(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum, String viewFilterConfigGuid) {
        if (ObjectUtil.isNull(workflowKeyEnum)) {
            return null;
        }
        LambdaQueryWrapper<ErpFormPendingNumber> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpFormPendingNumber::getFormKey, workflowKeyEnum.getFormSourceKey())
                .eq(ErpFormPendingNumber::getFormTypeValue, workflowKeyEnum.getFormSourceTypeValue());
        if (Objects.nonNull(sourceWorkflowKeyEnum)) {
            wrapper.eq(ErpFormPendingNumber::getSourceFormKey, sourceWorkflowKeyEnum.getFormSourceKey())
                    .eq(ErpFormPendingNumber::getSourceFormTypeValue, sourceWorkflowKeyEnum.getFormSourceTypeValue());
        } else {
            wrapper.isNull(ErpFormPendingNumber::getSourceFormKey).isNull(ErpFormPendingNumber::getSourceFormTypeValue);
        }
        if (StringUtils.isEmpty(viewFilterConfigGuid)) {
            wrapper.isNull(ErpFormPendingNumber::getViewFilterConfigGuid);
        } else {
            wrapper.eq(ErpFormPendingNumber::getViewFilterConfigGuid, viewFilterConfigGuid);
        }
        return baseMapper.selectOne(wrapper);
    }

    /**
     * 根据表格标识组装待开数量数据
     *
     * @param workflowKeyEnum         表单标识枚举
     * @param sourceWorkflowKeyEnum   来源表单标识枚举
     * @param tableIdentificationEnum 表格标识枚举
     * @param paramMap                参数Map
     * @param entityList              实体列表
     */
    private void buildDataByTableIdentification(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum,
                                                TableIdentificationEnum tableIdentificationEnum, Map<String, String> paramMap, List<ErpFormPendingNumber> entityList) {
        if (ObjectUtil.isNull(workflowKeyEnum) || ObjectUtil.isNull(tableIdentificationEnum) || entityList == null) {
            return;
        }
        // 根据表格标识查询SQL类型的视图筛选配置数据
        List<ErpViewFilterConfig> viewFilterConfigList = iErpViewFilterConfigService.list(Wrappers.<ErpViewFilterConfig>lambdaQuery()
                .eq(ErpViewFilterConfig::getGridName, tableIdentificationEnum.getGridName())
                .eq(ErpViewFilterConfig::getFormClassName, tableIdentificationEnum.getFormClassName())
                .eq(ErpViewFilterConfig::getViewFilterConfigType, ViewFilterConfigTypeEnum.SQL.getValue()));
        if (CollectionUtil.isNotEmpty(viewFilterConfigList)) {
            viewFilterConfigList.forEach(config -> {
                // 参数Map不为空，替换sql参数
                String sql = MapUtil.isNotEmpty(paramMap) ? StringAssembler.assemble(config.getViewFilterConfigJson(), paramMap::get) : config.getViewFilterConfigJson();
                // 执行sql，获取返回的待开数量
                BigDecimal sqlPendingNumber = baseMapper.executeSql(sql);
                // 组装待开数量表数据，加入实体列表
                ErpFormPendingNumber formPendingNumberEntity = this.selectOne(workflowKeyEnum, sourceWorkflowKeyEnum, config.getViewFilterConfigGuid());
                formPendingNumberEntity = ObjectUtil.isNull(formPendingNumberEntity) ?
                        new ErpFormPendingNumber(workflowKeyEnum, sourceWorkflowKeyEnum, config.getViewFilterConfigGuid()) : formPendingNumberEntity;
                formPendingNumberEntity.setPendingNumber(sqlPendingNumber);
                entityList.add(formPendingNumberEntity);
            });
        }
    }
}
