package xy.server.activiti.cmd;

import com.xy.util.BaseContext;
import org.activiti.engine.impl.interceptor.Command;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.persistence.entity.CommentEntity;
import org.activiti.engine.task.Comment;

/**
 * <AUTHOR>
 * @data 2023/9/7 14:36
 * @apiNote
 */
public class AddCommentCmd implements Command<Comment> {
    protected String taskId;
    protected String processInstanceId;
    protected String type;
    protected String message;

    public AddCommentCmd(String taskId, String processInstanceId, String message) {
        this.taskId = taskId;
        this.processInstanceId = processInstanceId;
        this.message = message;
    }

    public AddCommentCmd(String taskId, String processInstanceId, String type, String message) {
        this.taskId = taskId;
        this.processInstanceId = processInstanceId;
        this.type = type;
        this.message = message;
    }

    @Override
    public Comment execute(CommandContext commandContext) {
        String userId = BaseContext.getStaffGuid();
        CommentEntity comment = (CommentEntity)commandContext.getCommentEntityManager().create();
        comment.setUserId(userId);
        comment.setType(this.type == null ? "comment" : this.type);
        comment.setTime(commandContext.getProcessEngineConfiguration().getClock().getCurrentTime());
        comment.setTaskId(this.taskId);
        comment.setProcessInstanceId(this.processInstanceId);
        comment.setAction("AddComment");
        String eventMessage = this.message.replaceAll("\\s+", " ");
        if (eventMessage.length() > 163) {
            eventMessage = eventMessage.substring(0, 160) + "...";
        }

        comment.setMessage(eventMessage);
        comment.setFullMessage(this.message);
        commandContext.getCommentEntityManager().insert(comment);
        return comment;
    }
}
