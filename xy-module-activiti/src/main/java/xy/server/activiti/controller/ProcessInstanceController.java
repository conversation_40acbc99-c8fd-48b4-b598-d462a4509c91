package xy.server.activiti.controller;

import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.trans.annotation.XyTransMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.activiti.entity.model.ro.ActAuditProcessRO;
import xy.server.activiti.entity.model.vo.ActHistoryInfoVO;
import xy.server.activiti.entity.model.vo.ActMessageDTO;
import xy.server.activiti.entity.model.vo.ActReturnResultVO;
import xy.server.activiti.service.IActHiActinstService;
import xy.server.activiti.service.IProcessInstanceService;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "流程实例")
@RestController
@RequestMapping("/activiti/processInstance")
@SystemClassLog(code = "processInstance")
@Validated
public class ProcessInstanceController {

    private final IProcessInstanceService service;
    private final IActHiActinstService iActHiActinstService;

    /**
     * 提交审核（申请人节点）
     */
    @PostMapping("/commitAudit")
    @ApiOperation(value = "提交审核（申请人节点）")
    @SystemMethodLog(type = "modify", description = "提交审核（申请人节点）")
    public ActReturnResultVO commitAudit(@RequestBody @Validated ActAuditProcessRO auditProcessRO) {
        return service.commitAudit(auditProcessRO);
    }

    /**
     * 撤销提交
     */
    @PostMapping("/cancelCommitAudit")
    @ApiOperation(value = "撤销提交")
    @SystemMethodLog(type = "modify", description = "撤销提交")
    public ActReturnResultVO cancelCommitAudit(@RequestBody @Validated ActAuditProcessRO auditProcessRO) {
        return service.cancelCommitAudit(auditProcessRO);
    }

    /**
     * 审核（通过/驳回）
     */
    @PostMapping("/audit")
    @ApiOperation(value = "审核（通过/驳回）")
    @SystemMethodLog(type = "modify", description = "审核（通过/驳回）")
    public ActReturnResultVO audit(@NotNull @RequestParam("result") Boolean result,
                                   @RequestBody @Validated ActAuditProcessRO auditProcessRO) {
        return service.audit(result, auditProcessRO);
    }
    /**
     * 撤销审核
     */
    @PostMapping("/cancelAudit")
    @ApiOperation(value = "撤销审核")
    @SystemMethodLog(type = "modify", description = "撤销审核")
    public ActReturnResultVO cancelAudit(@RequestBody @Validated ActAuditProcessRO auditProcessRO) {
        return service.cancelAudit(auditProcessRO);
    }

    /**
     * @Description: 重启流程
     */
    @PostMapping("/restartProcess")
    @ApiOperation(value = "重启流程")
    @SystemMethodLog(type = "modify", description = "重启流程")
    public ActReturnResultVO restartProcess(@RequestBody @Validated ActAuditProcessRO auditProcessRO) {
        return service.restartProcess(auditProcessRO);
    }

    /**
     * @Description: 重启流程
     */
    @PostMapping("/forcedResetProcess")
    @ApiOperation(value = "强制重置流程")
    @SystemMethodLog(type = "modify", description = "强制重置流程")
    public ActReturnResultVO forcedResetProcess(@RequestBody @Validated ActAuditProcessRO auditProcessRO) {
        return service.forcedResetProcess(auditProcessRO);
    }

    /**
     * @Description: 查询流程审批记录
     * @param: processInstanceId
     */
    @GetMapping("/getHistoryInfoList")
    @ApiOperation(value = "查询流程审批记录")
    @SystemMethodLog(type = "query", description = "查询流程审批记录")
    @XyTransMethod
    public List<ActHistoryInfoVO> getHistoryInfoList(@NotBlank(message = "业务id不能为空") @RequestParam("businessKey") String businessKey,
                                                     @NotBlank(message = "表单定义key不能为空") @RequestParam("formSourceKey") String formSourceKey,
                                                     @NotBlank(message = "表单定义key不能为空") @RequestParam("formSourceTypeValue") String formSourceTypeValue) {
        return service.getHistoryInfoList(businessKey, formSourceKey, formSourceTypeValue);
    }

    @GetMapping("/batchGetLatestHistoryInfo")
    @ApiOperation(value = "批量获取最新一条审核记录")
    @SystemMethodLog(type = "query", description = "批量获取最新一条审核记录")
    @XyTransMethod
    public List<ActHistoryInfoVO> batchGetLatestHistoryInfo(@NotEmpty(message = "业务id列表不能为空") @RequestParam("businessKeys") List<String> businessKeys,
                                                            @NotBlank(message = "表单定义key不能为空") @RequestParam("formSourceKey") String formSourceKey,
                                                            @NotBlank(message = "表单定义key不能为空") @RequestParam("formSourceTypeValue") String formSourceTypeValue) {
        return iActHiActinstService.batchGetLatestHistoryInfo(businessKeys, formSourceKey, formSourceTypeValue);
    }

    /**
     * @Description: 通过流程实例id获取历史流程图
     */
//    @SaIgnore
    @GetMapping("/getHistoryProcessImage/{processInstanceId}")
    @ApiOperation(value = "通过流程实例id获取历史流程图")
    @SystemMethodLog(type = "query", description = "通过流程实例id获取历史流程图")
    public void getHistoryProcessImage(@NotBlank(message = "流程实例id不能为空") @PathVariable String processInstanceId,
                                       HttpServletResponse response) {
        service.getHistoryProcessImage(processInstanceId, response);
    }

    /**
     * @Description: 删除程实例，删除历史记录，删除业务与流程关联信息
     * @param: processInstId
     */
    @DeleteMapping("/deleteRuntimeProcessAndHisInst")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "modify", description = "删除")
    public ActReturnResultVO deleteProcessAndHisInst(@NotBlank(message = "业务id不能为空") @RequestParam("businessKey") String businessKey,
                                                     @NotBlank(message = "表单定义key不能为空") @RequestParam("formSourceKey") String formSourceKey,
                                                     @NotBlank(message = "表单定义key不能为空") @RequestParam("formSourceTypeValue") String formSourceTypeValue) {
        return service.deleteProcessAndHisInst(businessKey, formSourceKey, formSourceTypeValue);
    }

    /**
     * 获取当前登录人的流程相关待办事项信息
     * @return
     */
    @GetMapping("/getActMessageList")
    @ApiOperation(value = "获取当前登录人的流程相关待办事项信息")
    @SystemMethodLog(type = "query", description = "获取当前登录人的流程相关待办事项信息")
    public List<ActMessageDTO> getActMessageList() {
        return service.getActMessageList();
    }


    @PostMapping("/batchCommitAudit")
    @ApiOperation(value = "批量提交审核（申请人节点）")
    @SystemMethodLog(type = "modify", description = "批量提交审核（申请人节点）")
    public List<ActReturnResultVO> batchCommitAudit(@RequestBody @Validated List<ActAuditProcessRO> auditProcessROList) {
        return service.batchCommitAudit(auditProcessROList);
    }

    @PostMapping("/batchCancelCommitAudit")
    @ApiOperation(value = "批量撤销提交")
    @SystemMethodLog(type = "modify", description = "批量撤销提交")
    public List<ActReturnResultVO> batchCancelCommitAudit(@RequestBody @Validated List<ActAuditProcessRO> auditProcessROList) {
        return service.batchCancelCommitAudit(auditProcessROList);
    }

    @PostMapping("/batchAudit")
    @ApiOperation(value = "批量审核（通过/驳回）")
    @SystemMethodLog(type = "modify", description = "审核（通过/驳回）")
    public List<ActReturnResultVO> batchAudit(@NotNull @RequestParam("result") Boolean result,
                                              @RequestBody @Validated List<ActAuditProcessRO> auditProcessROList) {
        return service.batchAudit(result, auditProcessROList);
    }

    @PostMapping("/batchCancelAudit")
    @ApiOperation(value = "批量撤销审核")
    @SystemMethodLog(type = "modify", description = "批量撤销审核")
    public List<ActReturnResultVO> batchCancelAudit(@RequestBody @Validated List<ActAuditProcessRO> auditProcessROList) {
        return service.batchCancelAudit(auditProcessROList);
    }

    @PostMapping("/batchRestartProcess")
    @ApiOperation(value = "批量重启流程")
    @SystemMethodLog(type = "modify", description = "批量重启流程")
    public List<ActReturnResultVO> batchRestartProcess(@RequestBody @Validated List<ActAuditProcessRO> auditProcessROList) {
        return service.batchRestartProcess(auditProcessROList);
    }

    @PostMapping("/batchForcedResetProcess")
    @ApiOperation(value = "批量强制重置流程")
    @SystemMethodLog(type = "modify", description = "批量重启流程")
    public List<ActReturnResultVO> batchForcedResetProcess(@RequestBody @Validated List<ActAuditProcessRO> auditProcessROList) {
        return service.batchForcedResetProcess(auditProcessROList);
    }

    @PostMapping("/batchInvalidProcess")
    @ApiOperation(value = "批量作废流程")
    @SystemMethodLog(type = "modify", description = "批量作废流程")
    public List<ActReturnResultVO> batchInvalidProcess(@RequestBody @Validated List<ActAuditProcessRO> auditProcessROList) {
        return service.batchInvalidProcess(auditProcessROList);
    }


}
