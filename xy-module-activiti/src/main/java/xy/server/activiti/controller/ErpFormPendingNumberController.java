package xy.server.activiti.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.activiti.entity.model.qo.ErpFormPendingNumberQO;
import xy.server.activiti.entity.model.vo.ErpFormPendingNumberVO;
import xy.server.activiti.entity.model.vo.ProcessNavigationMessageDTO;
import xy.server.activiti.service.IErpFormPendingNumberService;

import java.util.List;


/**
 * <AUTHOR>
 * @apiNote 待开数量记录表 controller
 * @since 2024-01-30
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "待开数量记录表")
@RestController
@RequestMapping("/activiti/erp-form-pending-number")
@SystemClassLog(code = "ErpFormPendingNumberController")
public class ErpFormPendingNumberController {
    private final IErpFormPendingNumberService service;

    @GetMapping("/findCurrentStaffPendingNumberList")
    @ApiOperation(value = "查询当前员工表单待开数量列表")
    //@SystemMethodLog(type = "query", description = "查询当前员工表单待开数量列表")
    public List<ErpFormPendingNumberVO> findCurrentStaffPendingNumberList() {
        return service.findCurrentStaffPendingNumberList();
    }

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询待开数量记录表")
    public List<ErpFormPendingNumberVO> findList(@RequestBody @Validated ErpFormPendingNumberQO qo) {
        return service.findList(qo);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询待开数量记录表")
    public IPage<ErpFormPendingNumberVO> findPage(@RequestBody @Validated PageParams<ErpFormPendingNumberQO> pageParams) {
        return service.findPage(pageParams);
    }

    @GetMapping("/getProcessNavigationMessage")
    @ApiOperation(value = "查询当前员工流程导航消息")
    //@SystemMethodLog(type = "query", description = "查询当前员工流程导航消息")
    public ProcessNavigationMessageDTO getProcessNavigationMessage() {
        return service.getProcessNavigationMessage();
    }

}
