package xy.server.activiti.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.common.validate.AddGroup;
import com.xunyue.common.validate.QueryGroup;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.repository.Model;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xy.server.activiti.entity.model.ro.ModelRO;
import xy.server.activiti.service.IModelService;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "模型")
@RestController
@RequestMapping("/activiti/model")
@SystemClassLog(code = "model")
public class ModelController {

    private final IModelService iModelService;

    private final RepositoryService repositoryService;

    /**
     * 查询模型列表
     *
     * @param modelRO
     * @return
     */
    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询模型")
    @SystemMethodLog(type = "query", description = "分页查询模型")
    public IPage<Model> getByPage(@Validated(QueryGroup.class) @RequestBody PageParams<ModelRO> modelRO) {
        return iModelService.getByPage(modelRO);
    }

    /**
     * @Description: 新建模型
     */
    @ApiOperation("新建模型")
    @SystemMethodLog(type = "modify", description = "新建模型")
//    @RepeatSubmit()
    @PostMapping()
    public Model add(@Validated(AddGroup.class) @RequestBody ModelRO modelRO) {
        return iModelService.add(modelRO);
    }

    /**
     * @Description: 删除流程定义模型
     */
    @DeleteMapping("/{ids}")
    @SystemMethodLog(type = "delete", description = "删除流程定义模型")
    //    @RepeatSubmit()
    @Transactional(rollbackFor = Exception.class)
    public boolean add(@NotEmpty(message = "主键不能为空") @PathVariable String[] ids) {
        for (String id : ids) {
            repositoryService.deleteModel(id);
            return true;
        }
        return false;
    }

    /**
     * @Description: 通过流程定义模型id部署流程定义
     */
    @ApiOperation("部署流程定义")
    @SystemMethodLog(type = "modify", description = "部署流程定义")
    @PostMapping("/deploy/{id}")
    public boolean deploy(@NotBlank(message = "流程部署id不能为空") @PathVariable("id") String id) {
        return iModelService.deploy(id);
    }
}
