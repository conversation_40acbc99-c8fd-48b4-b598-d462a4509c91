package xy.server.activiti.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.config.util.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import xy.server.activiti.entity.model.qo.ProcessDefinitionQO;
import xy.server.activiti.entity.model.ro.ProcessDefinitionRO;
import xy.server.activiti.entity.model.vo.ProcessDataJsonVO;
import xy.server.activiti.entity.model.vo.ProcessDataVO;
import xy.server.activiti.entity.model.vo.ProcessDefinitionVO;
import xy.server.activiti.service.IProcessDefinitionService;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "流程定义")
@RestController
@RequestMapping("/activiti/processDefinition")
@SystemClassLog(code = "processDefinition")
public class ProcessDefinitionController {

    @Autowired
    private IProcessDefinitionService processDefinitionService;

    /**
     * 获取流程定义集合
     *
     * @param processDefinition
     */
    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询流程定义")
    @SystemMethodLog(type = "query", description = "分页查询流程定义")
    public IPage<ProcessDefinitionVO> findPage(@RequestBody @Validated PageParams<ProcessDefinitionQO> processDefinition) {
        return processDefinitionService.findPage(processDefinition);
    }

    /**
     * @Description: 查询单条流程
     */
    @GetMapping("/setting/{processDefinitionId}")
    @ApiOperation(value = "查询单条流程")
    @SystemMethodLog(type = "query", description = "查询")
    public Boolean setting(@NotBlank(message = "流程定义id不能为空") @PathVariable String processDefinitionId) {
        return processDefinitionService.setting(processDefinitionId);
    }


    /**
     * @Description: 删除流程定义
     * @param: deploymentId 流程部署id
     * @param: definitionId 流程定义id
     */

    @DeleteMapping("/delete/{deploymentId}/{definitionId}")
    @ApiOperation(value = "删除流程定义")
    @SystemMethodLog(type = "delete", description = "删除流程定义")
    public Boolean deleteDeployment(@NotBlank(message = "流程部署id不能为空") @PathVariable String deploymentId,
                                    @NotBlank(message = "流程定义id不能为空") @PathVariable String definitionId) {
        return processDefinitionService.deleteDeployment(deploymentId, definitionId);
    }

    /**
     * @Description: 通过zip或xml部署流程定义
     * @param: file
     */
    @PostMapping("/deployByFile")
    @ApiOperation(value = "流程定义管理")
    @SystemMethodLog(type = "modify", description = "流程定义管理")
    public Boolean deployByFile(@RequestParam("file") MultipartFile file,
                                @RequestParam("formSourceGuid") String formSourceGuid) {
        return processDefinitionService.deployByFile(file, formSourceGuid);
    }

    /**
     * @Description: 查看流程文件
     */
    @GetMapping("/getProcess/{deploymentId}/{formSourceKey}/{formSourceTypeValue}")
    @ApiOperation(value = "查看流程文件")
    @SystemMethodLog(type = "query", description = "查看流程文件")
    public ProcessDataVO getProcessData(@NotBlank(message = "流程定义id不能为空") @PathVariable String deploymentId,
                                        @PathVariable String formSourceKey,
                                        @PathVariable String formSourceTypeValue) {
        return processDefinitionService.getProcessData(deploymentId, formSourceKey, formSourceTypeValue);
    }

    /**
     * @Description: 查看流程文件
     */
    @GetMapping("/getProcessJson/{deploymentId}/{formSourceKey}/{formSourceTypeValue}")
    @ApiOperation(value = "查看流程文件")
    @SystemMethodLog(type = "query", description = "查看流程文件")
    public ProcessDataJsonVO getProcessJson(@NotBlank(message = "流程定义id不能为空") @PathVariable String deploymentId,
                                            @PathVariable String formSourceKey,
                                            @PathVariable String formSourceTypeValue) {
        return processDefinitionService.getProcessJson(deploymentId, formSourceKey, formSourceTypeValue);
    }


    /**
     * @Description: 激活或者挂起流程定义
     */
    @PutMapping("/updateProcDefState")
    @ApiOperation(value = "激活或者挂起流程定义")
    @SystemMethodLog(type = "modify", description = "激活或者挂起流程定义")
    public Boolean updateProcDefState(@RequestBody Map<String, Object> data) {
        return processDefinitionService.updateProcDefState(data);
    }

    /**
     * 新增或者更新
     */
    @PostMapping("/saveData")
    @ApiOperation(value = "新增或者更新")
    @SystemMethodLog(type = "modify", description = "新增或者更新")
    public Boolean saveData(@RequestBody ProcessDefinitionRO ro) {
        return processDefinitionService.saveData(ro);
    }
}
