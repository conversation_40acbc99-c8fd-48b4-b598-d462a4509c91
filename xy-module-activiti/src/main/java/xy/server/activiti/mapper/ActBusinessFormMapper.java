package xy.server.activiti.mapper;

import org.activiti.engine.runtime.ProcessInstance;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.activiti.common.enums.BusinessStatusEnum;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.entity.model.ro.ActAuditProcessRO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2023/8/31 10:57
 * @apiNote 操作业务表单mapper
 */
@Mapper
public interface ActBusinessFormMapper {

    /**
     * 更新实际表审核状态
     * @param workflowKeyEnum 业务工单枚举
     * @param processInstance 流程实例对象
     * @param businessStatusEnum 业务状态枚举
     * @return
     */
    int updateTabAuditState(@Param("workflowKeyEnum") WorkflowKeyEnum workflowKeyEnum,
                            @Param("processInstance") ProcessInstance processInstance,
                            @Param("businessStatusEnum") BusinessStatusEnum businessStatusEnum);

    /**
     * 查询业务工单审核状态
     * @param workflowKeyEnum 业务工单枚举
     * @param businessKey 业务表单id
     * @return
     */
    String selectTabAuditState(@Param("workflowKeyEnum") WorkflowKeyEnum workflowKeyEnum, @Param("businessKey") String businessKey);

    /**
     * 通过流程实例id查询业务工单审核状态
     * @param processInstanceId 业务表单id
     * @return
     */
    String getTabAuditStateByProcessInstanceId(@Param("workflowKeyEnum") WorkflowKeyEnum workflowKeyEnum,
                                               @Param("processInstanceId") String processInstanceId);

    /**
     * 查询业务工单流程实例id
     * @param workflowKeyEnum 业务工单枚举
     * @param businessKey 业务表单id
     * @return
     */
    String selectTabProcessInstanceId(@Param("workflowKeyEnum") WorkflowKeyEnum workflowKeyEnum, @Param("businessKey") String businessKey);

    /**
     * 删除业务表单流程相关字段，流程状态重置为草稿
     * @param workflowKeyEnum 业务工单枚举
     * @param businessKey 业务表单id
     * @return
     */
    int deleteProcessFields(@Param("workflowKeyEnum") WorkflowKeyEnum workflowKeyEnum, @Param("businessKey") String businessKey);

    /**
     * 获取流程Key
     * @param deploymentId
     * @return
     */
    String getProcessInstanceKey(@Param("deploymentId")String deploymentId);

    /**
     * 根据businessKey获取流程定义Key
     * @param businessKey
     * @return
     */
    String getProcessDefinitionKeyByBusinessKey(@Param("businessKey") String businessKey);

    /**
     * 根据部署Id获取部署key
     * @param deploymentId
     * @return
     */
    String getDeploymentKeyById(@Param("deploymentId") String deploymentId);

    /**
     * 根据流程定义id获取表单定义key和value
     * @param processDefinitionId
     * @return
     */
    Map<String, String> getFormSourceByProcessDefinitionId(@Param("processDefinitionId") String processDefinitionId);

    /**
     * 根据staffGuids和platform获取ReceiverIds
     * @param staffGuids
     * @param platform
     * @return
     */
    List<String> getReceiverIdsByStaffGuidsAndPlatform(@Param("staffGuids") List<String> staffGuids, @Param("platform") String platform);

    /**
     * 判断流程是否变动
     * @param auditProcessRO
     * @return
     */
    boolean selectProcessIsChanged(@Param("auditProcessRO") ActAuditProcessRO auditProcessRO);
}
