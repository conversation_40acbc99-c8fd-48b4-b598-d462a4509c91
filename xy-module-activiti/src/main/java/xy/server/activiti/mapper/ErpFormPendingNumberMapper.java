package xy.server.activiti.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.activiti.entity.ErpFormPendingNumber;
import xy.server.activiti.entity.model.qo.ErpFormPendingNumberQO;
import xy.server.activiti.entity.model.vo.ErpFormPendingNumberVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 待开数量记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-30
 */
@Mapper
public interface ErpFormPendingNumberMapper extends BaseMapper<ErpFormPendingNumber> {

    /**
     * 根据GUID获取
     *
     * @param guid
     * @return
     */
    ErpFormPendingNumberVO getDataByGuid(@Param("guid") String guid);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<ErpFormPendingNumberVO> findList(@Param("model") ErpFormPendingNumberQO qo);

    /**
     * 分页查询
     *
     * @param page
     * @param model
     * @return
     */
    IPage<ErpFormPendingNumberVO> findPage(@Param("page") IPage<ErpFormPendingNumberVO> page, @Param("model") ErpFormPendingNumberQO model);

    BigDecimal executeSql(@Param("sql") String sql);

    /**
     * 查询当前员工配置的待开数量列表
     * @param staffGuid
     * @return
     */
    List<ErpFormPendingNumberVO> selectCurrentStaffConfigPendingNumberList(@Param("staffGuid") String staffGuid);
}
