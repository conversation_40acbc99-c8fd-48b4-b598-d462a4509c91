package xy.server.activiti.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xy.server.activiti.entity.ActHiActinst;
import xy.server.activiti.entity.model.vo.ActHistoryInfoVO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @data 2023/9/5 17:48
 * @apiNote act历史活动节点表mapper
 */
@Mapper
public interface ActHiActinstMapper extends BaseMapper<ActHiActinst> {

    @Delete("delete from act_hi_actinst  where task_id_ = #{taskId}")
    int deleteByTaskId(@Param("taskId") String taskId);

    int deleteByActId(@Param("procInstId") String procInstId, @Param("actIds") Set<String> actIds, @Param("isFinish") boolean isFinish);

    /**
     * 获取审核记录列表
     * @param businessKey
     * @param processDefinitionKey
     * @return
     */
    List<ActHistoryInfoVO> findHistoryInfoList(@Param("businessKey") String businessKey, @Param("processDefinitionKey") String processDefinitionKey);

    /**
     * 批量获取最新一条审核记录
     * @param businessKeys
     * @param formSourceKey
     * @param formSourceTypeValue
     * @return
     */
    List<ActHistoryInfoVO> batchGetLatestHistoryInfo(@Param("businessKeys") List<String> businessKeys,
                                                     @Param("formSourceKey") String formSourceKey,
                                                     @Param("formSourceTypeValue") String formSourceTypeValue);
}
