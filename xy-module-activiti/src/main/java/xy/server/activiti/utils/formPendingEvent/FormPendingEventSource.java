package xy.server.activiti.utils.formPendingEvent;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.filter.enums.TableIdentificationEnum;

import java.util.Map;

/**
 * <AUTHOR>
 * @data 2024-02-29 10:48
 * @apiNote 表单待开数量事件源
 */
@Getter
@Setter
public class FormPendingEventSource extends ApplicationEvent {

    /**
     * 表单定义枚举
     */
    private WorkflowKeyEnum workflowKeyEnum;

    /**
     * 来源表单定义枚举
     */
    private WorkflowKeyEnum sourceWorkflowKeyEnum;

    /**
     * 表格标识枚举
     */
    private TableIdentificationEnum tableIdentificationEnum;

    /**
     * 参数Map
     */
    private Map<String, String> paramMap;

    public FormPendingEventSource(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum) {
        super(workflowKeyEnum);
        this.workflowKeyEnum = workflowKeyEnum;
        this.sourceWorkflowKeyEnum = sourceWorkflowKeyEnum;
    }

    public FormPendingEventSource(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum,
                                  TableIdentificationEnum tableIdentificationEnum, Map<String, String> paramMap) {
        super(workflowKeyEnum);
        this.workflowKeyEnum = workflowKeyEnum;
        this.sourceWorkflowKeyEnum = sourceWorkflowKeyEnum;
        this.tableIdentificationEnum = tableIdentificationEnum;
        this.paramMap = paramMap;
    }
}
