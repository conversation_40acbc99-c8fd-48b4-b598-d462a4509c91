package xy.server.activiti.utils.actEvent;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @data 2023/8/30 17:24
 * @apiNote act事件源
 */

@Getter
@Setter
public class ActEventSource extends ApplicationEvent {
    private ActEventEntity actEventEntity;
    public ActEventSource(ActEventEntity actEventEntity) {
        super(actEventEntity);
        this.actEventEntity = actEventEntity;
    }
}
