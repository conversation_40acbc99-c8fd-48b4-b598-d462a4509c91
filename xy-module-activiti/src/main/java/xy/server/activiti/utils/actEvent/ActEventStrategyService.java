package xy.server.activiti.utils.actEvent;

import cn.hutool.extra.spring.SpringUtil;
import xy.server.activiti.common.enums.BusinessStatusEnum;
import xy.server.activiti.service.IProcessInstanceService;
import xy.server.activiti.service.ITaskService;

/**
 * <AUTHOR>
 * @data 2023/8/30 17:01
 * @apiNote 流程通知事件策略模式接口定义
 */

public interface ActEventStrategyService {

    IProcessInstanceService iProcessInstanceService = SpringUtil.getBean(IProcessInstanceService.class);
    ITaskService iTaskService = SpringUtil.getBean(ITaskService.class);
    /**
     * 草稿
     *
     * @param processVO
     */
    default void draftCall(ActEventEntity processVO) {}

    /**
     * 待审核
     *
     * @param processVO
     */
    default void waitingCall(ActEventEntity processVO) {}

    /**
     * 审核中
     *
     * @param processVO
     */
    default void inReviewCall(ActEventEntity processVO) {}

    /**
     * 已完成
     * @param processVO
     */
    default void finishCall(ActEventEntity processVO) {}

    /**
     * 已作废
     * @param processVO
     */
    default void invalidCall(ActEventEntity processVO) {}

    /**
     * 已删除
     * @param processVO
     */
    default void deleteCall(ActEventEntity processVO) {}

    /**
     * 已驳回（审核不通过回到上一节点）
     * @param processVO
     */
    default void backCall(ActEventEntity processVO) {}
    /**
     * 已撤销（上一节点撤销到下一节点）
     * @param processVO
     */
    default void cancelCall(ActEventEntity processVO) {}
    /**
     * 重启
     * @param processVO
     */
    default void restartCall(ActEventEntity processVO) {}

    /**
     * 强制重置
     * @param processVO
     */
    default void forcedResetCall(ActEventEntity processVO) {}
    /**
     * 发生改变{后}通知
     * @param processVO
     * @param businessStatusEnum
     */
    default void eventCallAfter(ActEventEntity processVO, BusinessStatusEnum businessStatusEnum){}

    /**
     * 发生改变{前}通知
     * @param processVO
     * @param businessStatusEnum
     */
    default void eventCallBefore(ActEventEntity processVO, BusinessStatusEnum businessStatusEnum){}
}
