package xy.server.activiti.utils.actEvent;

import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2023/8/30 17:27
 * @apiNote act事件策略调用处理
 */
@Component
public class ActEventService {
    @Resource
    private final Map<String, ActEventStrategyService> actEventStrategyServiceMap = new HashMap<>();

    /**
     * 用户注册事件监听
     */
    @EventListener(value = ActEventSource.class)
    @Order(1)//一个事件多个事监听，在同步的情况下，使用@order值越小，执行顺序优先
    public void xyEventListener(ActEventSource eventSource) {
        ActEventEntity eventEntity = eventSource.getActEventEntity();
        // 通用事件(前置)
        actEventStrategyServiceMap.get(eventEntity.getServerName()).eventCallBefore(eventSource.getActEventEntity(), eventEntity.getBusinessStatusEnum());
        // 调用策略实现
        switch (eventEntity.getBusinessStatusEnum()) {
            case DRAFT:
                actEventStrategyServiceMap.get(eventEntity.getServerName()).draftCall(eventSource.getActEventEntity());
                break;
            case WAITING:
                actEventStrategyServiceMap.get(eventEntity.getServerName()).waitingCall(eventSource.getActEventEntity());
                break;
            case IN_REVIEW:
                actEventStrategyServiceMap.get(eventEntity.getServerName()).inReviewCall(eventSource.getActEventEntity());
                break;
            case FINISH:
                actEventStrategyServiceMap.get(eventEntity.getServerName()).finishCall(eventSource.getActEventEntity());
                break;
            case INVALID:
                actEventStrategyServiceMap.get(eventEntity.getServerName()).invalidCall(eventSource.getActEventEntity());
                break;
            case DELETE:
                actEventStrategyServiceMap.get(eventEntity.getServerName()).deleteCall(eventSource.getActEventEntity());
                break;
            case BACK:
                actEventStrategyServiceMap.get(eventEntity.getServerName()).backCall(eventSource.getActEventEntity());
                break;
            case CANCEL:
                actEventStrategyServiceMap.get(eventEntity.getServerName()).cancelCall(eventSource.getActEventEntity());
                break;
            case RESTART:
                actEventStrategyServiceMap.get(eventEntity.getServerName()).restartCall(eventSource.getActEventEntity());
                break;
            case FORCED_RESET:
                actEventStrategyServiceMap.get(eventEntity.getServerName()).forcedResetCall(eventSource.getActEventEntity());
                break;
            default:
                break;
        }
        // 通用事件(后置)
        actEventStrategyServiceMap.get(eventEntity.getServerName()).eventCallAfter(eventSource.getActEventEntity(), eventEntity.getBusinessStatusEnum());
    }
}
