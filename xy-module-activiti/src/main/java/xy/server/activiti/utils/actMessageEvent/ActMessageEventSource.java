package xy.server.activiti.utils.actMessageEvent;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @data 2023-12-13 9:42
 * @apiNote activiti流程消息事件源
 */
@Getter
@Setter
public class ActMessageEventSource extends ApplicationEvent {

    /**
     * 消息主题
     */
    private String topic;

    /**
     * 消息对象
     */
    private Object messageObj;

    public ActMessageEventSource(String topic, Object messageObj) {
        super(messageObj);
        this.topic = topic;
        this.messageObj = messageObj;
    }
}
