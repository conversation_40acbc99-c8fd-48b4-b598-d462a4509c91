package xy.server.activiti.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.xunyue.activiti.entity.ActNodeAssignee;
import com.xunyue.activiti.service.IActNodeAssigneeService;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.RedisUtil;
import com.xunyue.tenant.sign.UserApi;
import com.xy.util.BaseContext;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.*;
import org.activiti.engine.impl.persistence.entity.TaskEntity;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.Assert;
import xy.server.activiti.cmd.AddCommentCmd;
import xy.server.activiti.common.constant.ActConstant;
import xy.server.activiti.common.enums.BusinessStatusEnum;
import xy.server.activiti.common.enums.WorkflowKeyEnum;
import xy.server.activiti.entity.ActHiTaskInst;
import xy.server.activiti.entity.model.vo.ActMessageDTO;
import xy.server.activiti.entity.model.vo.ActReturnResultVO;
import xy.server.activiti.factory.WorkflowService;
import xy.server.activiti.i18n.ResultErrorCode;
import xy.server.activiti.mapper.ActBusinessFormMapper;
import xy.server.activiti.service.IActHiActinstService;
import xy.server.activiti.service.IActHiTaskInstService;
import xy.server.activiti.utils.actEvent.ActEventEntity;
import xy.server.activiti.utils.actEvent.ActEventSource;
import xy.server.basic.entity.ErpSystemMgtFormSourceTypeValue;
import xy.server.basic.mapper.ErpSystemMgtFormSourceTypeValueMapper;
import xy.server.policy.common.enums.MessageTemplateEnum;
import xy.server.policy.common.enums.MessageType;
import xy.server.policy.entity.model.vo.ErpMessageTemplateBodyVO;
import xy.server.policy.entity.model.vo.ErpMessageTemplateVO;
import xy.server.policy.service.IErpMessageService;
import xy.server.policy.service.IErpMessageTemplateService;
import xy.server.policy.service.IMessagePlatformConfigService;

import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RequiredArgsConstructor(onConstructor_ = @Autowired, access = AccessLevel.PRIVATE)
@Component
@Slf4j
public class WorkFlowUtils extends WorkflowService {

    private final IActHiTaskInstService iActHiTaskInstService;
    private final IActNodeAssigneeService iActNodeAssigneeService;
    private final ActBusinessFormMapper actBusinessFormMapper;
    private final ErpSystemMgtFormSourceTypeValueMapper mgtFormSourceTypeValueMapper;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final RedisUtil redisUtil;
    private final UserApi userApi;
    private final IMessagePlatformConfigService iMessagePlatformConfigService;
    private final IErpMessageTemplateService iErpMessageTemplateService;
    private final IErpMessageService iErpMessageService;
    private final IActHiActinstService iActHiActinstService;

    private static final ReentrantLock REENTRANT_LOCK = new ReentrantLock(true);

    /**
     * @Description: 创建流程任务
     * @param: parentTask
     * @param: createTime
     * @return: org.flowable.task.service.impl.persistence.entity.TaskEntity
     * @author: gssong
     * @Date: 2022/3/13
     */
    public TaskEntity createNewTask(Task currentTask, Date createTime) {
        TaskEntity task = null;
        if (ObjectUtil.isNotEmpty(currentTask)) {
            task = (TaskEntity) taskService.newTask();
            task.setCategory(currentTask.getCategory());
            task.setDescription(currentTask.getDescription());
            task.setTenantId(currentTask.getTenantId());
            task.setAssignee(currentTask.getAssignee());
            task.setName(currentTask.getName());
            task.setProcessDefinitionId(currentTask.getProcessDefinitionId());
            task.setProcessInstanceId(currentTask.getProcessInstanceId());
            task.setTaskDefinitionKey(currentTask.getTaskDefinitionKey());
            task.setPriority(currentTask.getPriority());
            task.setCreateTime(createTime);
            taskService.saveTask(task);
        }
        if (ObjectUtil.isNotNull(task)) {
            ActHiTaskInst hiTaskInst = iActHiTaskInstService.getById(task.getId());
            if (ObjectUtil.isNotEmpty(hiTaskInst)) {
                hiTaskInst.setProcDefId(task.getProcessDefinitionId());
                hiTaskInst.setProcInstId(task.getProcessInstanceId());
                hiTaskInst.setTaskDefKey(task.getTaskDefinitionKey());
                hiTaskInst.setStartTime(createTime);
                iActHiTaskInstService.updateById(hiTaskInst);
            }
        }
        return task;
    }

//    /**
//     * 自动办理
//     * @param workflowKeyEnum 业务工单枚举
//     * @param processInstance 流程实例
//     * @param actNodeAssignees
//     * @param req
//     * @return
//     */
//    public Boolean autoComplete(WorkflowKeyEnum workflowKeyEnum, ProcessInstance processInstance, List<ActNodeAssignee> actNodeAssignees, List<String> assignees) {
//        List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstance.getId()).list();
//        if (CollectionUtil.isEmpty(taskList)) {
//            actBusinessFormMapper.updateTabAuditState(workflowKeyEnum, processInstance, BusinessStatusEnum.FINISH);
//        }
//        for (Task task : taskList) {
//            ActNodeAssignee nodeAssignee = actNodeAssignees.stream().filter(e -> task.getTaskDefinitionKey().equals(e.getNodeId())).findFirst().orElse(null);
//            if (ObjectUtil.isNull(nodeAssignee)) {
//                throw new FlowException(ResultErrorCode.TASK_NODE_DEPLOYMENT);
//            }
//
//            if (!nodeAssignee.getAutoComplete()) {
//                return false;
//            }
//            settingAssignee(task, nodeAssignee);
//            List<String> assignees = req.getAssignees(task.getTaskDefinitionKey());
//            if (!nodeAssignee.getIsShow() && CollectionUtil.isNotEmpty(assignees) && assignees.contains(BaseContext.getStaffGuid())) {
//                taskService.addComment(task.getId(), task.getProcessInstanceId(), "流程引擎满足条件自动办理");
//                taskService.complete(task.getId());
////                recordExecuteNode(task, actNodeAssignees);
//            } else {
//                settingAssignee(task, nodeAssignee);
//            }
//
//        }
//        List<Task> list = taskService.createTaskQuery().processInstanceId(processInstance.getId())
//                .taskCandidateOrAssigned(BaseContext.getStaffGuid()).list();
//        if (CollectionUtil.isEmpty(list)) {
//            return false;
//        }
//        for (Task task : list) {
//            taskService.addComment(task.getId(), task.getProcessInstanceId(), "流程引擎满足条件自动办理");
//            taskService.complete(task.getId());
////            recordExecuteNode(task, actNodeAssignees);
//        }
//        autoComplete(workflowKeyEnum, processInstance, actNodeAssignees, req);
//        return true;
//    }

    /**
     * 设置任务执行人员
     * @param task
     * @param actNodeAssignee
     */
    public void settingAssignee(Task task, ActNodeAssignee actNodeAssignee) {
        //按业务规则选人
        if (ActConstant.WORKFLOW_RULE.equals(actNodeAssignee.getChooseWay())) {
//            ActBusinessRuleVo actBusinessRuleVo = iActBusinessRuleService.queryById(actNodeAssignee.getBusinessRuleId());
//            List<String> ruleAssignList = ruleAssignList(actBusinessRuleVo, task.getId(), task.getName());
//            List<Long> userIdList = new ArrayList<>();
//            for (String userId : ruleAssignList) {
//                userIdList.add(Long.valueOf(userId));
//            }
//            if (multiple) {
//                taskService.setVariable(task.getId(), actNodeAssignee.getMultipleColumn(), userIdList);
//            } else {
//                setAssignee(task, userIdList);
//            }
        } else {
            if (StringUtils.isBlank(actNodeAssignee.getAssigneeId())) {
                throw new FlowException(ResultErrorCode.TASK_NODE_DEPLOYMENT);
            }
            // 设置审批人员
            List<String> assignees = getAssigneeIdList(actNodeAssignee);
            if (actNodeAssignee.getMultiple()) {
                taskService.setVariable(task.getId(), actNodeAssignee.getMultipleColumn(), assignees);
            } else {
                this.setAssignee(task, assignees);
            }
        }
    }

    /**
     * 查询审批人
     * @param actNodeAssignee 流程节点配置
     * @return
     */
    public List<String> getAssigneeIdList(ActNodeAssignee actNodeAssignee) {
        Assert.notNull(actNodeAssignee, "流程节点配置不能为空！");
        List<String> assgneeIdList = new ArrayList<>();
        List<String> paramList = Arrays.asList(actNodeAssignee.getAssigneeId().split(","));

        if (ActConstant.WORKFLOW_PERSON.equals(actNodeAssignee.getChooseWay())) {
            // 按用户id查询
            assgneeIdList = paramList;
        }else if (ActConstant.WORKFLOW_ROLE.equals(actNodeAssignee.getChooseWay())) {
            // 按角色查询员工guids
            assgneeIdList = userApi.getStaffGuidsByRoleGuids(paramList);
        }
        else if (ActConstant.WORKFLOW_DEPT.equals(actNodeAssignee.getChooseWay())) {
            // TODO 按部门id查询用户
//            LambdaQueryWrapper<ErpAdministrationMgtStaff> queryWrapper = Wrappers.lambdaQuery();
//            queryWrapper.in(ErpAdministrationMgtStaff::getDepartmentGuid, paramList);
//            assgneeIdList = iErpAdministrationMgtStaffService.list(queryWrapper).stream().map(ErpAdministrationMgtStaff::getStaffGuid).collect(Collectors.toList());
        }
        if (CollectionUtil.isEmpty(assgneeIdList)) {
            throw new FlowException(ResultErrorCode.NO_APPROVER_CONFIGURED);
        }
        return assgneeIdList;
    }

    /**
     * 查询抄送人
     * @param actNodeAssignee 流程节点配置
     * @return
     */
    public List<String> getCopyAssigneeIdList(ActNodeAssignee actNodeAssignee) {
        Assert.notNull(actNodeAssignee, "流程节点配置不能为空！");
        List<String> copyAssgneeIdList = new ArrayList<>();
        if (actNodeAssignee.getIsCopy() != null && actNodeAssignee.getIsCopy()) {
            List<String> paramList = Arrays.asList(actNodeAssignee.getCopyAssigneeId().split(","));

            if (ActConstant.WORKFLOW_PERSON.equals(actNodeAssignee.getCopyChooseWay())) {
                // 按用户id查询
                copyAssgneeIdList = paramList;
            }else if (ActConstant.WORKFLOW_ROLE.equals(actNodeAssignee.getCopyChooseWay())) {
                // 按角色查询员工guids
                copyAssgneeIdList = userApi.getStaffGuidsByRoleGuids(paramList);
            }
            else if (ActConstant.WORKFLOW_DEPT.equals(actNodeAssignee.getCopyChooseWay())) {
                // TODO 按部门id查询用户
    //            LambdaQueryWrapper<ErpAdministrationMgtStaff> queryWrapper = Wrappers.lambdaQuery();
    //            queryWrapper.in(ErpAdministrationMgtStaff::getDepartmentGuid, paramList);
    //            assgneeIdList = iErpAdministrationMgtStaffService.list(queryWrapper).stream().map(ErpAdministrationMgtStaff::getStaffGuid).collect(Collectors.toList());
            }
        }
        return copyAssgneeIdList;
    }

    /**
     * 设置任务人员
     * @param task
     * @param assignees
     */
    public void setAssignee(Task task, List<String> assignees) {
        if (assignees.size() == 1) {
            taskService.setAssignee(task.getId(), assignees.get(0));
        } else {
            // 多个作为候选人
            for (String assignee : assignees) {
                taskService.addCandidateUser(task.getId(), assignee);
            }
        }
    }

    /**
     * 判断当前员工是否为指定task节点的受让人
     * @param processDefinitionId 流程定义id
     * @param taskActivityId 任务节点ActivityId
     * @return
     */
    public boolean isAssignee(String processDefinitionId, String taskActivityId) {
        ActNodeAssignee nodeAssignee = iActNodeAssigneeService.getInfo(processDefinitionId, taskActivityId);
        if (ObjectUtil.isNull(nodeAssignee)) {
            throw new FlowException(ResultErrorCode.TASK_NODE_DEPLOYMENT);
        }
        List<String> assigneeIdList = this.getAssigneeIdList(nodeAssignee);
        return assigneeIdList.contains(BaseContext.getStaffGuid());
    }

    /**
     * 发布act事件策略
     * @param serverName
     * @param businessStatusEnum
     * @param businessKey
     * @param actReturnResultVO
     */
    public void publishActEvent(String serverName, BusinessStatusEnum businessStatusEnum, String businessKey, ActReturnResultVO actReturnResultVO) {
        if (StringUtils.isNotEmpty(serverName)) {
            ActEventEntity actEventEntity = new ActEventEntity();
            actEventEntity.setServerName(serverName);
            actEventEntity.setBusinessStatusEnum(businessStatusEnum);
            actEventEntity.setBusinessKey(businessKey);
            if (actReturnResultVO == null) {
                actReturnResultVO = new ActReturnResultVO();
            }
            actReturnResultVO.setAuditOperationType(businessStatusEnum.getStatus());
            actEventEntity.setActReturnResultVO(actReturnResultVO);
            applicationEventPublisher.publishEvent(new ActEventSource(actEventEntity));
        }
    }

    /**
     * 发送外部平台消息（企业微信、钉钉等）
     * @param businessStatusEnum 事件枚举
     * @param staffGuid 指定员工
     * @param workflowKeyEnum 流程定义枚举
     */
    public void sendPlatformMessage(WorkflowKeyEnum workflowKeyEnum, BusinessStatusEnum businessStatusEnum, String staffGuid) {
        ActNodeAssignee actNodeAssignee = new ActNodeAssignee();
        actNodeAssignee.setChooseWay(ActConstant.WORKFLOW_PERSON);
        actNodeAssignee.setAssigneeId(staffGuid);
        this.sendPlatformMessage(workflowKeyEnum, businessStatusEnum, actNodeAssignee);
    }

    /**
     * 发送外部平台消息（企业微信、钉钉等）
     * @param businessStatusEnum 事件枚举
     * @param actNodeAssignee 节点配置
     * @param workflowKeyEnum 流程定义枚举
     */
    public void sendPlatformMessage(WorkflowKeyEnum workflowKeyEnum, BusinessStatusEnum businessStatusEnum, ActNodeAssignee actNodeAssignee) {
        if (ObjectUtil.isNotNull(workflowKeyEnum) && ObjectUtil.isNotNull(businessStatusEnum) && ObjectUtil.isNotNull(actNodeAssignee)) {
            // 获取消息平台，当前默认取第一条
            //String messagePlatform = iMessagePlatformConfigService.getFirstMessagePlatform();
            String messagePlatform = "WECHAT_WORK_ROBOT";
            // 获取消息类型，当前固定拿Markdown类型的
            MessageType messageType = MessageType.getMessageTypeByName(messagePlatform + "_TEXT");
            // 根据事件不同，使用不同的消息模版
            MessageTemplateEnum messageTemplateEnum = null;
            switch (businessStatusEnum) {
                case WAITING:
                    messageTemplateEnum = MessageTemplateEnum.AUDIT_PROCESS_TO_BE_REVIEWED;
                    break;
                case BACK:
                    messageTemplateEnum = MessageTemplateEnum.AUDIT_PROCESS_WAS_REJECTED;
                    break;
                case CANCEL:
                    messageTemplateEnum = MessageTemplateEnum.AUDIT_PROCESS_HAS_BEEN_WITHDRAWN;
                    break;
                case DRAFT:
                    messageTemplateEnum = MessageTemplateEnum.AUDIT_PROCESS_WITHDRAWN_SUBMISSION;
                    break;
                default:
                    return;
            }
            // 获取消息模板，使用内容
            ErpMessageTemplateVO messageTemplateVO = iErpMessageTemplateService.getOneByCodeAndType(messageTemplateEnum.name(), messageType.name());
            if (ObjectUtil.isNotNull(messageTemplateVO) && CollectionUtil.isNotEmpty(messageTemplateVO.getTemplateBodyList())) {
                ErpMessageTemplateBodyVO templateBodyVO = messageTemplateVO.getTemplateBodyList().stream()
                        .filter(item -> "content".equals(item.getMessageTemplateBodyField()))
                        .findFirst().orElse(null);
                if (ObjectUtil.isNotNull(templateBodyVO) && StringUtils.isNotEmpty(templateBodyVO.getMessageTemplateBodyContent())) {
                    // 动态替换表单定义名称
                    Map<String, String> params = new HashMap<>(1);
                    params.put("formSourceName", workflowKeyEnum.getName());
                    String messageTemplateBodyContent = StringAssembler.assemble(templateBodyVO.getMessageTemplateBodyContent(), params::get);

                    // 组装消息内容
                    JSONObject messageJsonObject = new JSONObject();
                    messageJsonObject.put(templateBodyVO.getMessageTemplateBodyField(), messageTemplateBodyContent);
                    // 根据配置的审核人信息获取员工配置的外部消息平台receiverId
                    // 审核人列表
                    List<String> assigneeIdList = this.getAssigneeIdList(actNodeAssignee);
                    // 抄送人列表
                    List<String> copyAssigneeIdList = this.getCopyAssigneeIdList(actNodeAssignee);
                    // 合并在一起
                    List<String> allStaffGuids = Stream.concat(assigneeIdList.stream(), copyAssigneeIdList.stream()).collect(Collectors.toList());
                    // 查询关联的消息平台内的receiverIds
                    List<String> receiverIds = null;
                    if (CollectionUtil.isNotEmpty(allStaffGuids)) {
                        receiverIds = actBusinessFormMapper.getReceiverIdsByStaffGuidsAndPlatform(allStaffGuids, messagePlatform);
                    }
                    // 发送消息
                    iErpMessageService.sendMessage(messageType, messageJsonObject, receiverIds, null);
                }
            }

        }

    }

    /**
     * 查询指定节点的所有下一级UserTask
     * @param curFlowNode
     * @return
     */
    public List<FlowNode> getNextNode(FlowNode curFlowNode) {
        List<FlowNode> flowNodeList = new ArrayList<>();
        List<SequenceFlow> outgoingFlows = curFlowNode.getOutgoingFlows();
        for (SequenceFlow outgoingFlow : outgoingFlows) {
            FlowNode targetFlowNode = (FlowNode) outgoingFlow.getTargetFlowElement();
            if (targetFlowNode instanceof UserTask) {
                flowNodeList.add(targetFlowNode);
            } else if (targetFlowNode instanceof Gateway) {
                flowNodeList.addAll(this.getNextNode(targetFlowNode));
            }
        }
        return flowNodeList;
    }

    /**
     * 查询指定节点后边所有UserTask
     * @param curFlowNode
     * @return
     */
    public List<FlowNode> getAllNextNode(FlowNode curFlowNode) {
        List<FlowNode> flowNodeList = new ArrayList<>();
        List<SequenceFlow> outgoingFlows = curFlowNode.getOutgoingFlows();
        for (SequenceFlow outgoingFlow : outgoingFlows) {
            FlowNode targetFlowNode = (FlowNode) outgoingFlow.getTargetFlowElement();
            if (targetFlowNode instanceof UserTask) {
                flowNodeList.add(targetFlowNode);
            }
            if (CollectionUtil.isNotEmpty(targetFlowNode.getOutgoingFlows())) {
                flowNodeList.addAll(this.getAllNextNode(targetFlowNode));
            }
        }
        return flowNodeList;
    }

    /**
     * 获取实际的流程key
     *
     * @param workflowKeyEnum
     * @return
     */
    public String getProcessInstanceKey(WorkflowKeyEnum workflowKeyEnum) {
        ErpSystemMgtFormSourceTypeValue erpSystemMgtFormSourceTypeValue = mgtFormSourceTypeValueMapper
                .getFormSourceTypeValue(workflowKeyEnum.getFormSourceKey(), workflowKeyEnum.getFormSourceTypeValue());
        if (erpSystemMgtFormSourceTypeValue == null) {
            throw new FlowException(ResultErrorCode.NOT_FORM_SOURCE);
        }
        return actBusinessFormMapper.getProcessInstanceKey(erpSystemMgtFormSourceTypeValue.getActRepositoryGuid());
    }


    /**
     * 根据流程例key获取部署key
     *
     * @param deploymentId
     * @return
     */
    public String getDeploymentKeyById(String deploymentId) {
        return actBusinessFormMapper.getDeploymentKeyById(deploymentId);
    }

    /**
     * 批量发送消息
     * @param staffGuids
     * @param workflowKeyEnum
     * @param isAdd
     */
    public synchronized void sendMessage(Collection<String> staffGuids, WorkflowKeyEnum workflowKeyEnum, BusinessStatusEnum businessStatusEnum, String businessKey, boolean isAdd) {
        Assert.notEmpty(staffGuids, "员工guids不能为空");
        staffGuids.forEach(staffGuid -> this.sendMessage(staffGuid, workflowKeyEnum, businessStatusEnum, businessKey, isAdd));
    }

    /**
     * 发送消息
     * @param staffGuid 员工guid
     * @param workflowKeyEnum 流程枚举
     * @param isAdd 加或减
     */
    public synchronized void sendMessage(String staffGuid, WorkflowKeyEnum workflowKeyEnum, BusinessStatusEnum businessStatusEnum, String businessKey, boolean isAdd) {
        /*Assert.notNull(staffGuid, "员工guid不能为空");
        String redisKey = ActConstant.REDIS_CACHE_PREFIX + staffGuid;
        // 从redis中查询当前员工activiti消息列表
        List<ActMessageDTO> messageDTOList = (List<ActMessageDTO>) (redisUtil.get(redisKey) == null ? new ArrayList<>() : redisUtil.get(redisKey));
        // 获取目标下标
        int index = messageDTOList.stream().filter(item -> workflowKeyEnum.getFormSourceKey().equals(item.getFormSourceKey())
                && workflowKeyEnum.getFormSourceTypeValue().equals(item.getFormSourceTypeValue())
                && businessStatusEnum.getStatus().equals(item.getAuditStatus())).findFirst().map(messageDTOList::indexOf).orElse(-1);
        if (isAdd && index == -1) {
            // 增加且在redis中没找到目标，直接add
            messageDTOList.add(new ActMessageDTO(staffGuid, workflowKeyEnum, businessStatusEnum, businessKey));
        } else if (isAdd && index != -1) {
            // 增加且在redis中找到目标，通过下标获取目标并且消息数量+1
            messageDTOList.get(index).setMessageQuantity(messageDTOList.get(index).getMessageQuantity() + 1);
            messageDTOList.get(index).getBusinessKeys().add(businessKey);
        } else if (!isAdd && index != -1) {
            // 减少且在redis中找到目标，通过下标获取目标并且消息数量-1
            messageDTOList.get(index).setMessageQuantity(messageDTOList.get(index).getMessageQuantity() - 1);
            // 移除businessKey
            Set<String> businessKeys = messageDTOList.get(index).getBusinessKeys();
            if (CollectionUtil.isNotEmpty(businessKeys) && businessKeys.contains(businessKey)) {
                businessKeys.remove(businessKey);
            }
        } else {
            // 减少且在redis中没到目标，不做任何处理
        }
        // 保存到redis中
        redisUtil.set(redisKey, messageDTOList, 0);
        // 发送mqtt消息队列
        //applicationEventPublisher.publishEvent(new ActMessageEventSource(redisKey, messageDTOList));*/
    }

    /**
     * 根据staffGuid获取存在redis中的消息
     * @param staffGuid
     * @return
     */
    public List<ActMessageDTO> getMessageByStaffGuid(String staffGuid) {
        Assert.notNull(staffGuid, "员工guid不能为空");
        String redisKey = ActConstant.REDIS_CACHE_PREFIX + staffGuid;
        return (List<ActMessageDTO>) redisUtil.get(redisKey);
    }

    /**
     * 获取当前员工actMessage列表
     */
    public List<ActMessageDTO> findCurrentStaffActMessageList() {
        String currentStaffGuid = BaseContext.getStaffGuid();
        String redisKey = ActConstant.REDIS_CACHE_PREFIX + currentStaffGuid;
        // 先查询redis缓存中的数据
        Object redisDataObj = redisUtil.get(redisKey);
        if (ObjectUtil.isNotNull(redisDataObj)) {
            return (List<ActMessageDTO>) redisDataObj;
        }

        // 查询当前登录人所有待办事项
        List<Task> taskList = taskService.createTaskQuery().taskCandidateOrAssigned(currentStaffGuid).active().list();
        List<ActMessageDTO> actMessageDTOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(taskList)) {
            for (Task task : taskList) {
                ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
                // 通过流程定义id查询表单定义key,value
                Map<String, String> formSourceMap = actBusinessFormMapper.getFormSourceByProcessDefinitionId(task.getProcessDefinitionId());
                if (ObjectUtil.isNull(formSourceMap) || ObjectUtil.isNull(processInstance)) {
                    continue;
                }
                // 获取枚举
                WorkflowKeyEnum workflowKeyEnum = WorkflowKeyEnum.getEumByKey(formSourceMap.get("form_source_key"), formSourceMap.get("form_source_type_value"));
                if (ObjectUtil.isNull(workflowKeyEnum)) {
                    continue;
                }
                // 查询业务表单审核状态
                String auditState = actBusinessFormMapper.selectTabAuditState(workflowKeyEnum, processInstance.getBusinessKey());
                if (StringUtils.isEmpty(auditState) || BusinessStatusEnum.FINISH.getStatus().equals(auditState)) {
                    continue;
                }
                BusinessStatusEnum businessStatusEnum = auditState.equals(BusinessStatusEnum.WAITING.getStatus())
                        || auditState.equals(BusinessStatusEnum.IN_REVIEW.getStatus()) ? BusinessStatusEnum.WAITING : BusinessStatusEnum.DRAFT;
                // 从列表中查询是否存在一天相同的formSourceKey、formSourceTypeValue和auditStatus，获取下标
                int index = actMessageDTOList.stream()
                        .filter(item -> workflowKeyEnum.getFormSourceKey().equals(item.getFormSourceKey())
                                && workflowKeyEnum.getFormSourceTypeValue().equals(item.getFormSourceTypeValue())
                                && businessStatusEnum.getStatus().equals(item.getAuditStatus()))
                        .findFirst()
                        .map(actMessageDTOList::indexOf)
                        .orElse(-1);
                if (index != -1) {
                    actMessageDTOList.get(index).setMessageQuantity(actMessageDTOList.get(index).getMessageQuantity() + 1);
                    actMessageDTOList.get(index).getBusinessKeys().add(processInstance.getBusinessKey());
                } else {
                    actMessageDTOList.add(new ActMessageDTO(currentStaffGuid, workflowKeyEnum, businessStatusEnum, processInstance.getBusinessKey()));
                }
            }
        }
        // 重新保存到redis中
        redisUtil.set(redisKey, actMessageDTOList, 0);
        return actMessageDTOList;
    }

    /**
     * 流程回退
     * @param task 任务
     * @param currentFlowNode 当前节点
     * @param targetFlowNode 目标节点
     * @param addCommentCmd 备注cmd命令执行对象
     */
    public void processRollback(Task task, FlowNode currentFlowNode, FlowNode targetFlowNode, AddCommentCmd addCommentCmd) {
        REENTRANT_LOCK.lock();
        boolean isError = false;
        // 1.获取当前节点原出口连线
        List<SequenceFlow> sequenceFlowList = currentFlowNode.getOutgoingFlows();
        // 2.临时存储当前节点的原出口连线
        List<SequenceFlow> oriSequenceFlows = new ArrayList<>();
        oriSequenceFlows.addAll(sequenceFlowList);
        try {
            // 3. 将当前节点的原出口清空
            sequenceFlowList.clear();
            // 4. 获取目标节点的入口连线
            List<SequenceFlow> incomingFlows = targetFlowNode.getIncomingFlows();
            // 5. 存储所有目标出口
            List<SequenceFlow> targetSequenceFlow = new ArrayList<>();
            for (SequenceFlow incomingFlow : incomingFlows) {
                // 找到入口连线的源头（获取目标节点的父节点）
                FlowNode source = (FlowNode) incomingFlow.getSourceFlowElement();
                List<SequenceFlow> sequenceFlows;
                if (source instanceof ParallelGateway) {
                    // 并行网关: 获取目标节点的父节点（并行网关）的所有出口，
                    sequenceFlows = source.getOutgoingFlows();
                } else {
                    // 其他类型父节点, 则获取目标节点的入口连续
                    sequenceFlows = targetFlowNode.getIncomingFlows();
                }
                targetSequenceFlow.addAll(sequenceFlows);
            }
            // 6. 将当前节点的出口设置为新节点
            List<SequenceFlow> targetSequenceList = targetSequenceFlow.stream().collect(Collectors
                    .collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(e -> e.getTargetFlowElement().getId()))),
                            ArrayList::new));

            currentFlowNode.setOutgoingFlows(targetSequenceList);
            processEngine.getManagementService().executeCommand(addCommentCmd);
            // 设置办理人
            taskService.setAssignee(task.getId(), BaseContext.getStaffGuid());
            // 7. 完成任务
            taskService.complete(task.getId());
        } catch (Exception e) {
            isError = true;
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("流程回退失败:",e);
            throw e;
        } finally {
            // 11. 完成撤审功能后，将当前节点的原出口方向进行恢复
            currentFlowNode.setOutgoingFlows(oriSequenceFlows);
            if (!isError) {
                // 删除后边活动节点历史数据
                Set<String> delNodeIds = this.getAllNextNode(targetFlowNode).stream().map(FlowNode::getId).collect(Collectors.toSet());
                // 包含已撤回节点的旧记录
                delNodeIds.add(targetFlowNode.getId());
                iActHiActinstService.deleteByActId(task.getProcessInstanceId(), delNodeIds, true);
            }
            REENTRANT_LOCK.unlock();
        }
    }
}
