package xy.server.activiti.utils;

import cn.hutool.core.util.ObjUtil;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther: Minuhy
 * @Date: 2022/10/2 13:02
 * @Decsription: 装配 ${} 表达式的值
 */
public class StringAssembler {
    public interface Assemble {
        /**
         * 装配过程中需要通过${}里面的表达式找到对应的值
         *
         * @param key ${}里面的表达式
         * @return ${}里面的表达式对应的值
         */
        String getStringByKey(String key);
    }

    /**
     * 装配 ${} 格式的字符串
     *
     * @param str      原始字符串
     * @param assemble 装配过程中获取值的接口
     * @return 装配好之后的字符串
     */
    public static String assemble(String str, Assemble assemble) {
        String pattern = "\\$\\{[^}]+}";

        Pattern r = Pattern.compile(pattern);

        while (true) {
            Matcher m = r.matcher(str);
            if (m.find()) {
                String key = getKey(m.group());
                String value = assemble.getStringByKey(key);
                if (ObjUtil.isNotNull(value)) {
                    str = m.replaceFirst(value);
                } else {
                    break;
                }
            } else {
                break;
            }
        }

        return str;
    }

    /**
     * 获取 ${} 里面的值
     *
     * @param g ${} 表达式
     * @return ${} 里面的值
     */
    public static String getKey(String g) {
        return g.substring(2, g.length() - 1);
    }
}


