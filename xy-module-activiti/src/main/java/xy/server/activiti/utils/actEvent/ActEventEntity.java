package xy.server.activiti.utils.actEvent;

import lombok.Data;
import xy.server.activiti.common.enums.BusinessStatusEnum;
import xy.server.activiti.entity.model.vo.ActReturnResultVO;

/**
 * <AUTHOR>
 * @data 2023/8/30 17:23
 * @apiNote act事件实体
 */
@Data
public class ActEventEntity {
    /**
     * 服务名（@Service注解中填的值）
     */
    private String serverName;

    /**
     * 事件标识
     */
    private BusinessStatusEnum businessStatusEnum;

    /**
     * 业务表单id
     */
    private String businessKey;

    /**
     * activity流程返回结果VO
     */
    private ActReturnResultVO actReturnResultVO;

    /**
     * 审核状态
     */
    //private BusinessStatusEnum auditStatus;
}
