package xy.server.activiti.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import xy.server.activiti.common.constant.ActConstant;

@Getter
@AllArgsConstructor
public enum WorkflowKeyEnum {


    /**
     * 供应商审核
     */
    SUPPLIER_AUDIT("SUPPLIER", "SUPPLIER_AUDIT", "供应商审核", "erp_supplier_mgt_supplier", "supplier_guid", "SUPPLIER.SUPPLIER_AUDIT"),

    /**
     * 工单-采购申请单
     */
    WORK_ORDER_PURCHASE("WORK_ORDER", "PURCHASE", "工单-采购申请单审核", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.PURCHASE"),
    /**
     * 订单-采购订单
     */
    ORDER_PURCHASE("ORDER", "PURCHASE", "订单-采购订单审核", "erp_business_mgt_order", "order_guid", "ORDER.PURCHASE"),
    /**
     * 工单-开工单
     */
    WORK_ORDER_OPEN_WORK("WORK_ORDER", "OPEN_WORK_ORDER", "工单-开工单", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.OPEN_WORK_ORDER"),
    /**
     * 工单-研发工单
     */
    RESEARCH_WORK_ORDER("RESEARCH_AND_DEVELOPMENT_APPLICATION", "RESEARCH_WORK_ORDER", "工单-研发工单", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.OPEN_WORK_ORDER"),
    /**
     * 工单核价
     */
    WORK_ORDER_PRICING("WORK_ORDER", "PRICING_WORK_ORDER", "工单核价", "erp_production_mgt_workorder_pricing", "workorder_pricing_guid", "WORK_ORDER.WORK_ORDER_PRICING"),
    /**
     * 经验BOM
     */
    WORK_EXPERIENCE_BOM("WORK_ORDER", "EXPERIENCE_BOM", "工单-经验BOM", "erp_production_mgt_experience_production_process", "experience_production_process_guid", "WORK_ORDER.EXPERIENCE_BOM"),
    /**
     * 产品BOM
     */
    WORK_CONVENTION_BOM("WORK_ORDER", "CONVENTION_BOM", "工单-产品BOM", "erp_production_mgt_experience_production_process", "experience_production_process_guid", "WORK_ORDER.EXPERIENCE_BOM"),
    /**
     * 工单-外发申请单
     */
    WORK_ORDER_OUTGOING("WORK_ORDER", "OUTGOING", "工单-外发申请单审核", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.OUTGOING"),
    /**
     * 物料评审
     */
    MATERIAL_REVIEW("WORK_ORDER", "MATERIAL_REVIEW", "物料评审", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.MATERIAL_REVIEW"),
    /**
     * 工单-保养工单
     */
    WORK_ORDER_MAINTENANCE_WORK("WORK_ORDER", "MAINTENANCE_WORKS", "工单-保养工单审核", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.MAINTENANCE_WORK"),
    /**
     * 工单-维修工单
     */
    WORK_ORDER_REPAIR_WORK("WORK_ORDER", "REPAIR_WORKS", "工单-维修工单审核", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.REPAIR_WORK"),
    /**
     * 生产加工申请单
     */
    PROCESSING_APPLICATION("WORK_ORDER", "PROCESSING_APPLICATION", "工单-生产加工申请单", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.PROCESSING_APPLICATION"),
    /**
     * 订单-外发订单
     */
    ORDER_OUTGOING("ORDER", "OUTGOING", "订单-外发订单审核", "erp_business_mgt_order", "order_guid", "ORDER.OUTGOING"),
    /**
     * 订单-订单通知单
     */
    ORDER_NOTICE("ORDER", "ORDER_NOTICE", "订单-订单通知单", "erp_business_mgt_order", "order_guid", "ORDER.ORDER_NOTICE"),
    /**
     * 客户管理
     */
    CUSTOM_CUSTOMER("CUSTOM", "CUSTOMER", "客户管理", "erp_customer_mgt_customer", "customer_guid", "CUSTOM.CUSTOMER"),
    /**
     * 质检管理-FQC
     */
    QUALITY_INSPECTION_FQC("QUALITY_INSPECTION_TYPE", "FQC", "质检管理-FQC", "erp_quality_control_mgt_quality_inspection", "quality_inspection_guid", "QUALITY_INSPECTION_TYPE.FOIQC"),
    /**
     * 质检管理-OQC
     */
    QUALITY_INSPECTION_OQC("QUALITY_INSPECTION_TYPE", "OQC", "质检管理-OQC", "erp_quality_control_mgt_quality_inspection", "quality_inspection_guid", "QUALITY_INSPECTION_TYPE.FOIQC"),
    /**
     * 质检管理-IQC
     */
    QUALITY_INSPECTION_IQC("QUALITY_INSPECTION_TYPE", "IQC", "质检管理-IQC", "erp_quality_control_mgt_quality_inspection", "quality_inspection_guid", "QUALITY_INSPECTION_TYPE.FOIQC"),
    /**
     * 质检管理-质量检验标准
     */
    QUALITY_INSPECTION_STANDARDS("QUALITY_MODULE", "INSPECTION_STANDARDS", "质检管理-质量检验标准", "erp_quality_control_mgt_quality_inspection_items", "quality_inspection_items_guid", "QUALITY_MODULE.INSPECTION_STANDARDS"),
    /**
     * 物料到货管理
     */
    WORK_ORDER_MATERIAL_DELIVERY("WORK_ORDER", "MATERIAL_DELIVERY", "物料到货管理", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.MATERIAL_DELIVERY"),
    /**
     * 外发到货管理
     */
    WORK_ORDER_OUTGOING_ARRIVAL("WORK_ORDER", "OUTGOING_ARRIVAL", "外发到货管理", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.OUTGOING_ARRIVAL"),
    /**
     * 成品到货管理
     */
    WORK_ORDER_FINISHED_PRODUCTION("WORK_ORDER", "FINISHED_PRODUCTION", "成品到货管理", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.MATERIAL_DELIVERY"),
    /**
     * 物料入库管理
     */
    INVENTORY_MGT_STOCK_MANAGEMENT("INVENTORY_MGT", "STOCK_MANAGEMENT", "物料入库管理", "erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.STOCK_MANAGEMENT"),
    /**
     * 成品入库管理
     */
    INVENTORY_MGT_FINISHED_PRODUCT_MANAGEMENT("INVENTORY_MGT", "FINISHED_PRODUCT_MANAGEMENT", "成品入库管理", "erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.STOCK_MANAGEMENT"),
    /**
     * 半成品入库管理
     */
    INVENTORY_MGT_SEMI_FINISHED_PRODUCT_MANAGEMENT("INVENTORY_MGT", "SEMI_FINISHED_PRODUCT_MANAGEMENT", "半成品入库管理", "erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.STOCK_MANAGEMENT"),
    /**
     * 模具入库管理
     */
    INVENTORY_MGT_MOULD_MANAGEMENT("INVENTORY_MGT", "MOULD_MANAGEMENT", "模具入库管理", "erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.STOCK_MANAGEMENT"),
    /**
     * 质检管理-IPQC
     */
    QUALITY_INSPECTION_IPQC("QUALITY_INSPECTION_TYPE", "IPQC", "质检管理-IPQC", "erp_quality_control_mgt_quality_inspection", "quality_inspection_guid", "QUALITY_INSPECTION_TYPE.IPQC"),
    /**
     * 加班安排审核
     */
    WORK_OVERTIME("WORK_OVERTIME", "WORK_OVERTIME", "加班安排审核", "erp_basic_mgt_work_overtime", "work_overtime_guid", "WORK_OVERTIME.WORK_OVERTIME"),
    /**
     * 物料出库申请单审核
     */
    MATERIAL_OUTBOUND_REQUISITION("WORK_ORDER", "MATERIAL_OUTBOUND_REQUISITION", "工单-物料出库申请单审核", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.MATERIAL_OUTBOUND_REQUISITION"),
    /**
     * 库单管理-物料出库
     */
    INVENTORY_MGT_MATERIAL_OUTBOUND("INVENTORY_MGT", "MATERIAL_OUTBOUND", "库单管理-物料出库", "erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.OUTBOUND"),
    /**
     * 库单管理-成品出库
     */
    INVENTORY_MGT_FINISHED_PRODUCT_OUTBOUND("INVENTORY_MGT", "FINISHED_PRODUCT_OUTBOUND", "库单管理-成品出库", "erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.OUTBOUND"),
    /**
     * 库单管理-半成品出库
     */
    INVENTORY_MGT_SEMI_FINISHED_PRODUCT_OUTBOUND("INVENTORY_MGT", "SEMI_FINISHED_PRODUCT_OUTBOUND", "库单管理-半成品出库", "erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.OUTBOUND"),
    /**
     * 库单管理-模具出库
     */
    INVENTORY_MGT_MOULD_OUTBOUND("INVENTORY_MGT", "MOULD_OUTBOUND", "库单管理-模具出库", "erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.OUTBOUND"),
    /**
     * 库单管理-盘亏出库
     */
    INVENTORY_MGT_SCRAP_OUTBOUND("INVENTORY_MGT", "SCRAP_OUTBOUND", "库单管理-盘亏出库", "erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.OUTBOUND"),
    /**
     * 订单管理
     */
    BUSINESS_MGT_ORDER("ORDER", "BUSINESS_MGT_ORDER", "订单管理", "erp_business_mgt_order", "order_guid", "ORDER.BUSINESS_MGT_ORDER"),
 /**
     * 打样订单
     */
    SAMPLE_ORDER("ORDER", "SAMPLE_ORDER", "订单管理", "erp_business_mgt_order", "order_guid", "ORDER.SAMPLE_ORDER"),
    /**
     * 成品出库通知单审核
     */
    FINISHED_PRODUCT_OUTBOUND_NOTIFICATION("WORK_ORDER", "FINISHED_PRODUCT_OUTBOUND_NOTIFICATION", "工单-成品出库通知单审核", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.FINISHED_PRODUCT_OUTBOUND_NOTIFICATION"),
    /**
     * 成品销货
     */
    FINISHED_PRODUCT_SALES("INVENTORY_MGT", "FINISHED_PRODUCT_SALES","工单-成品销货","erp_production_mgt_workorder","workorder_guid","INVENTORY_MGT.FINISHED_PRODUCT_SALES"),

    /**
     * 库单管理-物料盘点
     */
    INVENTORY_MGT_MATERIAL_COUNTING("INVENTORY_MGT", "MATERIAL_COUNTING", "库单管理-物料盘点", "erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.MATERIAL_COUNTING"),
    /**
     * 库单管理-成品盘点
     */
    INVENTORY_MGT_FINISHED_PRODUCT_COUNTING("INVENTORY_MGT", "FINISHED_PRODUCT_COUNTING", "库单管理-成品盘点", "erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.MATERIAL_COUNTING"),
    /**
     * 库单管理-半成品盘点
     */
    INVENTORY_MGT_SEMI_FINISHED_PRODUCT_COUNTING("INVENTORY_MGT", "SEMI_FINISHED_PRODUCT_COUNTING", "库单管理-半成品盘点", "erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.MATERIAL_COUNTING"),
    /**
     * 库单管理-模具盘点
     */
    INVENTORY_MGT_MOULD_COUNTING("INVENTORY_MGT", "MOULD_COUNTING", "库单管理-模具盘点", "erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.MATERIAL_COUNTING"),
    /**
     * 业务管理-合同模板管理
     */
    CONTRACT_TEMPLATE("CONTRACT", "CONTRACT_TEMPLATE", "业务管理-合同模板", "erp_basic_mgt_contract_template", "contract_template_guid", "CONTRACT.CONTRACT_TEMPLATE"),
    /**
     * 业务管理-合同管理
     */
    CONTRACT_MANAGEMENT("CONTRACT", "CONTRACT_MANAGEMENT", "业务管理-合同管理", "erp_basic_mgt_contract", "contract_guid", "CONTRACT.CONTRACT_MANAGEMENT"),
    /**
     * 运输管理-物流运输
     */
    ORDER_LOGISTICS_TRANSPORTATION_ORDER("ORDER", "LOGISTICS_TRANSPORTATION_ORDER", "运输管理-物流运输", "erp_business_mgt_order", "order_guid", "ORDER.LOGISTICS_TRANSPORTATION_ORDER"),
    /**
     * 运输管理-厂车运输
     */
    TRANSPORTATION_WORK_ORDER("WORK_ORDER", "TRANSPORTATION_WORK_ORDER", "运输管理-厂车运输", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.TRANSPORTATION_WORK_ORDER"),
      /**
     * 运输管理-厂车运输计划
     */
    TRANSPORTATION_PLAN_WORK_ORDER("WORK_ORDER", "TRANSPORTATION_PLAN", "运输管理-厂车运输计划", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.TRANSPORTATION_PLAN_WORK_ORDER"),
    /**
     * 对账单-客户对账单
     */
    STATEMENT_BILL_CUSTOMER("STATEMENT_BILL", "CUSTOMER", "对账单-客户对账单", "erp_finance_mgt_statement_bill", "statement_bill_guid", "STATEMENT_BILL.CUSTOMER"),
    /**
     * 对账单-供应商对账单
     */
    STATEMENT_BILL_SUPPLIER("STATEMENT_BILL", "SUPPLIER", "对账单-供应商对账单", "erp_finance_mgt_statement_bill", "statement_bill_guid", "STATEMENT_BILL.SUPPLIER"),
    /**
     * 对账单-加工商对账单
     */
    STATEMENT_BILL_PROCESSOR("STATEMENT_BILL", "PROCESSOR", "对账单-加工商对账单", "erp_finance_mgt_statement_bill", "statement_bill_guid", "STATEMENT_BILL.SUPPLIER"),
    /**
     * 生产管理-研发申请单
     */
    RESEARCH_AND_DEVELOPMENT_APPLICATION("RESEARCH_AND_DEVELOPMENT_MANAGEMENT", "RESEARCH_AND_DEVELOPMENT_APPLICATION", "生产管理-研发申请单", "erp_production_mgt_workorder", "workorder_guid", "RESEARCH_AND_DEVELOPMENT_MANAGEMENT.RESEARCH_AND_DEVELOPMENT_APPLICATION"),
    /**
     * 预收预付单-客户预收款
     */
    CUSTOMER_ADVANCE_PAYMENT("ADVANCE_PAYMENT_VOUCHER", "CUSTOMER_ADVANCE_PAYMENT", "预收预付单-客户预收款", "erp_finance_mgt_advance_payment", "advance_payment_guid", "CUSTOMER.ADVANCE_PAYMENT_VOUCHER"),
    /**
     * 预收预付单-供应商预付款
     */
    SUPPLIER_ADVANCE_PAYMENT("ADVANCE_PAYMENT_VOUCHER", "SUPPLIER_ADVANCE_PAYMENT", "预收预付单-供应商预付款", "erp_finance_mgt_advance_payment", "advance_payment_guid", "SUPPLIER.ADVANCE_PAYMENT_VOUCHER"),
    /**
     * 预收预付单-加工商预付款
     */
    PROCESSOR_ADVANCE_PAYMENT("ADVANCE_PAYMENT_VOUCHER", "PROCESSOR_ADVANCE_PAYMENT", "预收预付单-加工商预付款", "erp_finance_mgt_advance_payment", "advance_payment_guid", "PROCESSOR.ADVANCE_PAYMENT_VOUCHER"),
    /**
     * 结算单-应付结算
     */
    ACCOUNTS_PAYABLE_SETTLEMENT("SETTLEMENT_NOTE", "ACCOUNTS_PAYABLE_SETTLEMENT", "结算单-应收结算", "erp_finance_mgt_settlement_bill", "settlement_bill_guid", "SETTLEMENT_NOTE.ACCOUNTS_SETTLEMENT"),
    /**
     * 结算单-应收结算
     */
    ACCOUNTS_RECEIVABLE_SETTLEMENT("SETTLEMENT_NOTE", "ACCOUNTS_RECEIVABLE_SETTLEMENT", "结算单-应收结算", "erp_finance_mgt_settlement_bill", "settlement_bill_guid", "SETTLEMENT_NOTE.ACCOUNTS_SETTLEMENT"),
    /**
     * 应收/应付账单-应收账单
     */
    PAYMENT_PLAN_RECEIVABLE("PAYMENT_PLAN", "RECEIVABLE", "应收/应付账单-应收账单", "erp_finance_mgt_payment_plan", "payment_plan_guid", "PAYMENT_PLAN.RECEIVABLE"),
    /**
     * 应收/应付账单-应付账单
     */
    PAYMENT_PLAN_PAYABLE("PAYMENT_PLAN", "PAYABLE", "应收/应付账单-应付账单", "erp_finance_mgt_payment_plan", "payment_plan_guid", "PAYMENT_PLAN.PAYABLE"),
    /**
     * 财务管理-发票管理（开票登记）
     */
    INVOICE_BILL("INVOICE_BILL", "INVOICE_BILL", "财务管理-发票管理", "erp_finance_mgt_invoice_bill", "invoice_bill_guid", "INVOICE_BILL.INVOICE_BILL"),
    /**
     * 财务管理-发票管理（收票登记）
     */
    RECEIVE_INVOICE_REGISTRATION("INVOICE_BILL", "RECEIVE_INVOICE_REGISTRATION", "财务管理-收票登记", "erp_finance_mgt_invoice_bill", "invoice_bill_guid", "INVOICE_BILL.INVOICE_BILL"),
    /**
     * 业务管理-客户投诉管理
     */
    CUSTOMER_COMPLAINTS("CUSTOMER_COMPLAINTS", "CUSTOMER_COMPLAINTS", "业务管理-客户投诉管理", "erp_business_mgt_customer_complaints", "customer_complaints_guid", "CUSTOMER_COMPLAINTS.CUSTOMER_COMPLAINTS"),
    /**
     * 生产日报表
     */
    DAILY_PRODUCTION_REPORT("PRODUCTION_MANAGEMENT","DAILY_PRODUCTION_REPORT","生产日报表","erp_basic_mgt_daily_report_summary","daily_report_summary_guid","PRODUCTION_MANAGEMENT.DAILY_PRODUCTION_REPORT"),
    /**
     * 生产任务
     */
    PRODUCTION_TASKS("PRODUCTION_MANAGEMENT","PRODUCTION_TASKS","生产任务","erp_basic_mgt_tasks","tasks_guid","PRODUCTION_MANAGEMENT.PRODUCTION_TASKS"),
    /**
     * 工单-包装单
     */
    WORK_ORDER_PACKAGE("WORK_ORDER", "PACKAGE", "工单-包装单", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.PACKAGE"),
    /**
     * 装舱单
     */
    BILL_OF_LADING_STOWAGE("BILL_OF_LADING", "STOWAGE", "装舱单", "erp_business_mgt_packing_list", "packing_list_guid", "BILL_OF_LADING.STOWAGE"),
    /**
     * 凭证
     */
    VOUCHER_MANAGEMENT("VOUCHER_MANAGEMENT", "VOUCHER", "凭证", "erp_finance_mgt_proof", "proof_guid", "VOUCHER_MANAGEMENT.VOUCHER"),
    /**
     * 业务管理-形式发票管理
     */
    PROFORMA_INVOICE("PROFORMA_INVOICE", "PROFORMA_INVOICE", "业务管理-形式发票管理", "erp_business_mgt_proforma_invoice", "proforma_invoice_guid", "PROFORMA_INVOICE.PROFORMA_INVOICE"),
    /**
     * 仓单管理-物料出仓
     */
    WAREHOUSE_MGT_MATERIAL_OUT_WAREHOUSE("WAREHOUSE_MGT", "MATERIAL_OUT_WAREHOUSE", "仓单管理-物料出仓", "erp_warehouse_mgt_warehouse_receipt", "warehouse_receipt_guid", "WAREHOUSE_MGT.OUT_WAREHOUSE"),
    /**
     * 仓单管理-成品出仓
     */
    WAREHOUSE_MGT_FINISHED_PRODUCT_OUT_WAREHOUSE("WAREHOUSE_MGT", "FINISHED_PRODUCT_OUT_WAREHOUSE", "仓单管理-成品出仓", "erp_warehouse_mgt_warehouse_receipt", "warehouse_receipt_guid", "WAREHOUSE_MGT.OUT_WAREHOUSE"),
    /**
     * 仓单管理-半成品出仓
     */
    WAREHOUSE_MGT_SEMI_FINISHED_PRODUCT_OUT_WAREHOUSE("WAREHOUSE_MGT", "SEMI_FINISHED_PRODUCT_OUT_WAREHOUSE", "仓单管理-半成品出仓", "erp_warehouse_mgt_warehouse_receipt", "warehouse_receipt_guid", "WAREHOUSE_MGT.OUT_WAREHOUSE"),
    /**
     * 仓单管理-模具出仓
     */
    WAREHOUSE_MGT_MOULD_OUT_WAREHOUSE("WAREHOUSE_MGT", "MOULD_OUT_WAREHOUSE", "仓单管理-模具出仓", "erp_warehouse_mgt_warehouse_receipt", "warehouse_receipt_guid", "WAREHOUSE_MGT.OUT_WAREHOUSE"),
    /**
     * 仓单管理-成品入仓
     */
    WAREHOUSE_MGT_FINISHED_PRODUCT_IN_WAREHOUSE("WAREHOUSE_MGT", "FINISHED_PRODUCT_IN_WAREHOUSE", "仓单管理-成品入仓", "erp_warehouse_mgt_warehouse_receipt", "warehouse_receipt_guid", "WAREHOUSE_MGT.IN_WAREHOUSE"),
    /**
     * 仓单管理-半成品入仓
     */
    WAREHOUSE_MGT_SEMI_FINISHED_PRODUCT_IN_WAREHOUSE("WAREHOUSE_MGT", "SEMI_FINISHED_PRODUCT_IN_WAREHOUSE", "仓单管理-半成品入仓", "erp_warehouse_mgt_warehouse_receipt", "warehouse_receipt_guid", "WAREHOUSE_MGT.IN_WAREHOUSE"),
    /**
     * 仓单管理-模具入仓
     */
    WAREHOUSE_MGT_MOULD_IN_WAREHOUSE("WAREHOUSE_MGT", "MOULD_IN_WAREHOUSE", "仓单管理-模具入仓", "erp_warehouse_mgt_warehouse_receipt", "warehouse_receipt_guid", "WAREHOUSE_MGT.IN_WAREHOUSE"),
    /**
     * 仓单管理-物料入仓
     */
    WAREHOUSE_MGT_MATERIAL_IN_WAREHOUSE("WAREHOUSE_MGT", "MATERIAL_IN_WAREHOUSE", "仓单管理-物料入仓", "erp_warehouse_mgt_warehouse_receipt", "warehouse_receipt_guid", "WAREHOUSE_MGT.IN_WAREHOUSE"),
    /**
     * 仓单管理-成品移仓
     */
    WAREHOUSE_MGT_FINISHED_PRODUCT_TRANSFER_WAREHOUSE("WAREHOUSE_MGT", "FINISHED_PRODUCT_TRANSFER_WAREHOUSE", "仓单管理-成品移仓", "erp_warehouse_mgt_warehouse_receipt", "warehouse_receipt_guid", "WAREHOUSE_MGT.TRANSFER_WAREHOUSE"),
    /**
     * 仓单管理-物料移仓
     */
    WAREHOUSE_MGT_MATERIAL_TRANSFER_WAREHOUSE("WAREHOUSE_MGT", "MATERIAL_TRANSFER_WAREHOUSE", "仓单管理-物料移仓", "erp_warehouse_mgt_warehouse_receipt", "warehouse_receipt_guid", "WAREHOUSE_MGT.TRANSFER_WAREHOUSE"),
    /**
     * 仓单管理-半成品移仓
     */
    WAREHOUSE_MGT_SEMI_FINISHED_PRODUCT_TRANSFER_WAREHOUSE("WAREHOUSE_MGT", "SEMI_FINISHED_PRODUCT_TRANSFER_WAREHOUSE", "仓单管理-半成品移仓", "erp_warehouse_mgt_warehouse_receipt", "warehouse_receipt_guid", "WAREHOUSE_MGT.TRANSFER_WAREHOUSE"),
    /**
     * 仓单管理-半成品移仓
     */
    WAREHOUSE_MGT_MOULD_TRANSFER_WAREHOUSE("WAREHOUSE_MGT", "MOULD_TRANSFER_WAREHOUSE", "仓单管理-模具移仓", "erp_warehouse_mgt_warehouse_receipt", "warehouse_receipt_guid", "WAREHOUSE_MGT.TRANSFER_WAREHOUSE"),
    /**
     * 库单管理-成品退货申请
     */
    FINISHED_PRODUCT_RETURN_APPLICATION("WORK_ORDER","FINISHED_PRODUCT_RETURN_APPLICATION","库单管理-成品退货申请","erp_production_mgt_workorder","workorder_guid","WORK_ORDER.RETURN_APPLICATION"),
    /**
     * 库单管理-半成品退货申请
     */
    SEMI_FINISHED_PRODUCT_RETURN_APPLICATION("WORK_ORDER","SEMI_FINISHED_PRODUCT_RETURN_APPLICATION","库单管理-半成品退货申请","erp_production_mgt_workorder","workorder_guid","WORK_ORDER.RETURN_APPLICATION"),
    /**
     * 库单管理-物料退货申请
     */
    MATERIAL_RETURN_APPLICATION("WORK_ORDER","MATERIAL_RETURN_APPLICATION","库单管理-物料退货申请","erp_production_mgt_workorder","workorder_guid","WORK_ORDER.MATERIAL_RETURN_APPLICATION"),
    /**
     * 库单管理-生产退料申请
     */
    PRODUCTION_RETURN_APPLICATION("WORK_ORDER","PRODUCTION_RETURN_APPLICATION","库单管理-生产退料申请","erp_production_mgt_workorder","workorder_guid","WORK_ORDER.PRODUCTION_RETURN_APPLICATION"),
    /**
     * 业务管理-客户调费管理
     */
    CUSTOMER_ADJUSTMENT_FEE_MANAGEMENT("WORK_ORDER","CUSTOMER_ADJUSTMENT_FEE_MANAGEMENT","业务管理-客户调费管理","erp_production_mgt_workorder","workorder_guid","WORK_ORDER.ADJUSTMENT_FEE_MANAGEMENT"),
    /**
     * 业务管理-物流商调费管理
     */
    LOGISTICS_ADJUSTMENT_FEE_MANAGEMENT("WORK_ORDER","LOGISTICS_ADJUSTMENT_FEE_MANAGEMENT","业务管理-物流商调费管理","erp_production_mgt_workorder","workorder_guid","WORK_ORDER.ADJUSTMENT_FEE_MANAGEMENT"),
    /**
     * 业务管理-供应商调费管理
     */
    SUPPLIER_ADJUSTMENT_FEE_MANAGEMENT("WORK_ORDER","SUPPLIER_ADJUSTMENT_FEE_MANAGEMENT","业务管理-供应商调费管理","erp_production_mgt_workorder","workorder_guid","WORK_ORDER.ADJUSTMENT_FEE_MANAGEMENT"),
    /**
     * 业务管理-加工商调费管理
     */
    PROCESSOR_ADJUSTMENT_FEE_MANAGEMENT("WORK_ORDER","PROCESSOR_ADJUSTMENT_FEE_MANAGEMENT","业务管理-加工商调费管理","erp_production_mgt_workorder","workorder_guid","WORK_ORDER.ADJUSTMENT_FEE_MANAGEMENT"),
    /**
     * 财务管理-财务分摊
     */
    FINANCIAL_ALLOCATION("FINANCIAL_ALLOCATION","FINANCIAL_ALLOCATION","财务管理-财务分摊","erp_finance_mgt_allocation","allocation_guid","FINANCIAL.ALLOCATION"),
    /**
     * 库单管理-物料标签单
     */
    MATERIAL_LABEL("WORK_ORDER","MATERIAL_LABEL","库单管理-物料标签单","erp_production_mgt_workorder","workorder_guid","WORK_ORDER.MATERIAL_LABEL"),
    /**
     * 业务管理-业务预测管理
     */
    BUSINESS_FORECASTING("WORK_ORDER","BUSINESS_FORECASTING","业务管理-业务预测管理","erp_production_mgt_workorder","workorder_guid","WORK_ORDER.BUSINESS_FORECASTING"),
    /**
     * 库单管理-物料期初库存导入
     */
    INVENTORY_MGT_MATERIAL_INITIAL_INVENTORY_IMPORT("INVENTORY_MGT", "MATERIAL_INITIAL_INVENTORY_IMPORT", "库单管理-物料期初库存导入", "erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.INITIAL_INVENTORY_IMPORT"),
    /**
     * 库单管理-成品期初库存导入
     */
    INVENTORY_MGT_FINISHED_PRODUCT_INITIAL_INVENTORY_IMPORT("INVENTORY_MGT", "FINISHED_PRODUCT_INITIAL_INVENTORY_IMPORT", "库单管理-成品期初库存导入", "erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.INITIAL_INVENTORY_IMPORT"),
    /**
     * 仓库管理-成品报废出库申请
     */
    FINISHED_PRODUCT_SCRAP_OUTBOUND_APPLICATION("WORK_ORDER", "FINISHED_PRODUCT_SCRAP_OUTBOUND_APPLICATION", "仓库管理-成品报废出库申请", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.SCRAP_OUTBOUND_APPLICATION"),
    //永久增加的流程
    /**
     * 员工相关记录-工伤记录
     */
    OCCUPATIONAL_INJURY_RECORDS("STAFF_RECORDS", "OCCUPATIONAL_INJURY_RECORDS", "员工相关记录-工伤记录", "erp_administration_mgt_staff_injury", "staff_injury_guid", "STAFF_RECORDS.OCCUPATIONAL_INJURY_RECORDS"),
    /**
     * 员工相关记录-培训记录
     */
    TRAINING_RECORDS("STAFF_RECORDS", "TRAINING_RECORDS", "员工相关记录-培训记录", "erp_administration_mgt_staff_training", "staff_training_guid", "STAFF_RECORDS.TRAINING_RECORDS"),
    /**
     * 员工相关记录-奖惩记录
     */
    REWARD_AND_PENALTY_RECORDS("STAFF_RECORDS", "REWARD_AND_PENALTY_RECORDS", "员工相关记录-奖惩记录", "erp_administration_mgt_staff_rewards_punishments", "staff_rewards_punishments_guid", "STAFF_RECORDS.REWARD_AND_PENALTY_RECORDS"),
    /**
     * 员工相关记录-调岗记录
     */
    TRANSFER_RECORDS("STAFF_RECORDS", "TRANSFER_RECORDS", "员工相关记录-调岗记录", "erp_administration_mgt_staff_post_adjustment", "staff_post_adjustment_guid", "STAFF_RECORDS.TRANSFER_RECORDS"),
    /**
     * 员工相关记录-入住记录
     */
    CHECK_IN_RECORDS("STAFF_RECORDS", "CHECK_IN_RECORDS", "员工相关记录-入住记录", "erp_administration_mgt_staff_check_in", "staff_check_in_guid", "STAFF_RECORDS.CHECK_IN_RECORDS"),
    /**
     * 员工相关记录-报餐记录
     */
    MEAL_REPORTING_RECORDS("STAFF_RECORDS", "MEAL_REPORTING_RECORDS", "员工相关记录-报餐记录", "erp_administration_mgt_staff_meal", "staff_meal_guid", "STAFF_RECORDS.MEAL_REPORTING_RECORDS"),
    /**
     * 员工相关记录-离职记录
     */
    RESIGNATION_RECORDS("STAFF_RECORDS", "RESIGNATION_RECORDS", "员工相关记录-离职记录", "erp_administration_mgt_staff_resignation", "staff_resignation_guid", "STAFF_RECORDS.RESIGNATION_RECORDS"),
    /**
     * 员工相关记录-请假记录
     */
    GENERAL_LEAVE("STAFF_RECORDS", "GENERAL_LEAVE", "员工相关记录-请假记录", "erp_administration_mgt_staff_general_leave", "staff_general_leave_guid", "STAFF_RECORDS.GENERAL_LEAVE"),
    /**
     * 招聘申请
     */
    ADMINISTRATION_STAFF_RECRUIT("WORK_ORDER", "ADMINISTRATION_STAFF_RECRUIT", "招聘申请", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.ADMINISTRATION_STAFF_RECRUIT"),
    /**
     * 质量管理-质检测试
     */
    QUALITY_TEST_ITEMS("QUALITY_MODULE", "QUALITY_TEST_ITEMS", "质量管理-质检测试", "erp_quality_control_mgt_quality_test_items", "quality_test_items_guid", "QUALITY_MODULE.QUALITY_TEST_ITEMS"),
    /**
     * 采购管理-采购预测管理
     */
    WORK_ORDER_PRODUCTION_FORECAST("WORK_ORDER", "PRODUCTION_FORECAST", "采购管理-采购预测管理", "erp_production_mgt_workorder", "workorder_guid", "WORK_ORDER.PRODUCTION_FORECAST"),
     /**
     * 物料产品报价-客户产品报价
     */
    CUSTOMER_QUOTATION("MATERIAL_QUOTATION", "CUSTOMER_QUOTATION", "物料产品报价-客户产品报价", "erp_material_mgt_material_quotation_receipt", "material_quotation_receipt_guid", "MATERIAL_QUOTATION"),
    /**
     * 物料产品报价-供应商物料报价
     */
    SUPPLIER_QUOTATION("MATERIAL_QUOTATION", "SUPPLIER_QUOTATION", "物料产品报价-供应商物料报价", "erp_material_mgt_material_quotation_receipt", "material_quotation_receipt_guid", "MATERIAL_QUOTATION"),
    /**
     * 质量管理-质检测试
     */
    QUALITY_INSPECTION_TEST("QUALITY_MODULE", "QUALITY_INSPECTION_TEST", "质量管理-质检测试", "erp_quality_control_mgt_quality_test", "quality_test_guid", "QUALITY_INSPECTION_TEST"),
    /**
     * 质量检验-基础质量检验标准项
     */
    BASIC_INSPECTION_ITEMS("QUALITY_MODULE", "BASIC_INSPECTION_ITEMS", "质量检验-基础质量检验标准项", "erp_quality_mgt_basic_inspection", "basic_inspection_guid", "BASIC_INSPECTION_ITEMS"),
    /**
     * 库单管理-成品条码盘点
     */
    FINISHED_GOODS_PRODUCT_COUNT_FORM("INVENTORY_MGT","FINISHED_GOODS_PRODUCT_COUNT_FORM","库单管理-成品条码盘点","erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.MATERIAL_COUNTING"),
    /**
     * 库单管理-物料条码盘点
     */
    INVENTORY_GOODS_COUNT_FORM("INVENTORY_MGT","INVENTORY_GOODS_COUNT_FORM","库单管理-物料条码盘点","erp_inventory_mgt_inventory_receipt", "inventory_receipt_guid", "INVENTORY_MGT.MATERIAL_COUNTING"),
    /**
     * 纸质资料
     */
    PAPER_DATA_BOM("WORK_ORDER", "PAPER_DATA_BOM", "纸质资料", "erp_production_mgt_experience_production_process", "experience_production_process_guid", "WORK_ORDER.EXPERIENCE_BOM"),
    /**
     * 产品资料
     */
    PRODUCT_INFORMATION("WORK_ORDER", "PRODUCT_INFORMATION", "产品资料", "erp_production_mgt_experience_production_process", "experience_production_process_guid", "WORK_ORDER.EXPERIENCE_BOM"),

    ;

    /**
     * key
     */
    private final String formSourceKey;

    /**
     * key
     */
    private final String formSourceTypeValue;

    /**
     *
     */
    private final String name;

    /**
     * 表名
     */
    private final String tableName;

    /**
     * 表id字段名
     */
    private final String tableIdFieldName;

    /**
     * service服务名称
     */
    private final String serviceName;

    /**
     * @Description: 获取业务状态
     * @param: status 状态
     * @return: void
     * @author: gssong
     * @Date: 2022/09/02
     */
    public static WorkflowKeyEnum getEumByKey(String formSourceKey, String formSourceTypeValue) {
        if (StringUtils.isBlank(formSourceKey) || StringUtils.isBlank(formSourceTypeValue)) {
            return null;
        }

        for (WorkflowKeyEnum statusEnum : WorkflowKeyEnum.values()) {
            if (statusEnum.getFormSourceKey().equals(formSourceKey) && statusEnum.getFormSourceTypeValue().equals(formSourceTypeValue)) {
                return statusEnum;
            }
        }
        return null;
    }

    public static WorkflowKeyEnum getEumByKey(String processKey) {
        if (StringUtils.isBlank(processKey)) {
            return null;
        }
        String formSourceKey = processKey.split("\\" + ActConstant.DEPLOYMENT_KEY_LINK)[0];
        String formSourceTypeValue = processKey.split("\\" + ActConstant.DEPLOYMENT_KEY_LINK)[1];

        return getEumByKey(formSourceKey, formSourceTypeValue);
    }

    public String getKey() {
        return this.getFormSourceKey() + ActConstant.DEPLOYMENT_KEY_LINK + this.getFormSourceTypeValue();
    }
}
