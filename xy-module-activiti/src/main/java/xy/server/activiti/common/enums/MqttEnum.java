package xy.server.activiti.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MqttEnum {
    /**
     * 单据审核完成提醒
     */
    MQTT_FINISH("finish", "有您提交的单据已经审核完成"),
    /**
     * 提醒下一个审核人审核单据
     */
    MQTT_NEXT("next", "有您需要审核的新单据"),
    /**
     * 单据审核完成提醒
     */
    MQTT_ROLLBACK("back", "您有被驳回的单据需要重新审核审核"),
    ;

    /**
     * 状态
     */
    private final String status;

    /**
     * 描述
     */
    private final String desc;
}
