package xy.server.activiti.common.enums;

import com.xunyue.config.exception.FlowException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import xy.server.activiti.i18n.ResultErrorCode;

/**
 * @description: 业务枚举
 */
@Getter
@AllArgsConstructor
public enum BusinessStatusEnum {
    /**
     * 已撤回
     */
    CANCEL("cancel", "撤回"),
    /**
     * 草稿
     */
    DRAFT("draft", "草稿"),
    /**
     * 待审核
     */
    WAITING("waiting", "待审核"),
    /**
     * 审核中
     */
    IN_REVIEW("in_review", "审核中"),
    /**
     * 已完成
     */
    FINISH("finish", "已审核"),
    /**
     * 已作废
     */
    INVALID("invalid", "已作废"),
    /**
     * 已删除
     */
    DELETE("delete", "已删除"),
    /**
     * 已退回
     */
    BACK("back", "驳回"),
    /**
     * 重启
     */
    RESTART("restart", "重启"),
    /**
     * 强制重置
     */
    FORCED_RESET("forced_reset", "强制重置");

    /**
     * 状态
     */
    private final String status;

    /**
     * 描述
     */
    private final String desc;

    /**
     * @Description: 获取业务状态
     * @param: status 状态
     * @return: void
     * @author: gssong
     * @Date: 2022/09/02
     */
    public static BusinessStatusEnum getEumByStatus(String status) {
        if (StringUtils.isBlank(status)) {
            return null;
        }

        for (BusinessStatusEnum statusEnum : BusinessStatusEnum.values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * @Description: 启动流程校验
     * @param: status 状态
     * @return: void
     * @author: gssong
     * @Date: 2022/7/28
     */
    public static void checkStatus(String status) {
        if (status.equals(WAITING.getStatus())) {
            throw new FlowException(ResultErrorCode.BUSINESS_STATUS_IN_EXAMINATION_APPROVAL);
        } else if (status.equals(FINISH.getStatus())) {
            throw new FlowException(ResultErrorCode.BUSINESS_STATUS_OFF_STOCKS);
        } else if (status.equals(INVALID.getStatus())) {
            throw new FlowException(ResultErrorCode.BUSINESS_STATUS_HAVE_BEEN_VOIDED);
        } else if (status.equals(DELETE.getStatus())) {
            throw new FlowException(ResultErrorCode.BUSINESS_STATUS_HAVE_DELETED);
        }
    }

    /**
     * @Description: 校验撤销申请
     * @param: status 状态
     * @return: void
     * @author: gssong
     * @Date: 2022/7/28
     */
    public static void checkCancel(String status) {
        if (status.equals(FINISH.getStatus())) {
            throw new FlowException(ResultErrorCode.BUSINESS_STATUS_OFF_STOCKS);
        } else if (status.equals(INVALID.getStatus())) {
            throw new FlowException(ResultErrorCode.BUSINESS_STATUS_HAVE_BEEN_VOIDED);
        } else if (status.equals(DELETE.getStatus())) {
            throw new FlowException(ResultErrorCode.BUSINESS_STATUS_HAVE_DELETED);
        } else if (status.equals(CANCEL.getStatus())) {
            throw new FlowException(ResultErrorCode.BUSINESS_STATUS_WITHDRAWN);
        } else if (status.equals(BACK.getStatus())) {
            throw new FlowException(ResultErrorCode.BUSINESS_STATUS_RETURNED);
        }
    }
}

