package xy.server.activiti.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @data 2023/8/29 11:08
 * @apiNote ActNodeTermVO对象中的type枚举(条件类型)
 */
@Getter
@AllArgsConstructor
public enum ActNodeTermTypeEnum {
    /**
     * 下拉框
     */
    SELECT(1),
    /**
     * 数字
     */
    NUMBER(2),
    /**
     * 字符串
     */
    STRING(3),
    /**
     * 时间
     */
    DATETIME(4),
    /**
     * 人员
     */
    PERSONNEL(5),
    /**
     * 部门
     */
    DEPT(6),
    /**
     * 职位
     */
    POSITION(7);

    private final Integer type;
}
