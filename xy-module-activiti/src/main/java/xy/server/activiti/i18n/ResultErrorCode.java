package xy.server.activiti.i18n;

import com.xunyue.common.i18n.BaseResultErrorCode;

public enum ResultErrorCode implements BaseResultErrorCode {

//    NO_REPETITION_CURRENCY("未找到" + ACT_BUSINESS_STATUS + "属性：" + e.getMessage(), 1000100001);

    MUST_START_BUSINESS_ID("启动工作流时必须包含业务ID", 1010000001),
    FIRST_LINK_PROPOSER("请检查流程第一个环节是否为申请人", 1010000002),
    TASK_HAS_BEEN_SUSPENDED("当前任务已被挂起", 1010000003),
    HANDLING_FAILURE("办理失败", 1010000004),
    REVOCATION_FAILURE("撤销失败", 1010000005),
    DEPLOYMENT_FAILURE("部署失败", 1010000006),
    BACK_FAILURE("驳回失败", 1010000007),
    TASK_DOE_NOT_EXIST("任务不存在或您不是当前审批人", 101000009),
    NOT_BACK_TASK_REPEAT("当前任务和目标任务为同一个任务，驳回失败！", 1010000010),
    NOT_BACK_TO_STARTEVENT("上级节点是流程开始节点无法驳回，驳回失败！", 1010000011),
    NOT_IS_ASSIGNEE("您不是该任务审批人", 1010000012),
    NOT_PROCESS_INSTANCE("流程实例不存在", 1010000013),
    CANCEL_FAILURE_NOT_ASSIGNEE("撤回失败，您不是该流程办理人", 1010000014),
    CANCEL_FAILURE_NEXT_NODE_IS_FINISH("撤回失败，下一节点已审批", 1010000015),
    NOT_ALLOW_CANCEL("该流程不允许撤回或您没有权限撤回", 1010000016),

    NOT_EXIST_PROCESS("流程不存在", 1010000017),
    RESTART_PROCESS_FAILURE_BE_NOT_FINISH("重启流程失败，该流程还未结束", 1010000018),
    NOT_ALLOW_RESTART_PROCESS("该流程已重启过，不允许重复重启", 1010000019),

    TASK_NODE_DEPLOYMENT("请检查节点配置", 1010000020),
    NO_APPROVER_CONFIGURED("任务环节未配置审批人", 1010000021),

    BUSINESS_STATUS_IN_EXAMINATION_APPROVAL("该单据已提交过申请,正在审批中", 1010000022),
    BUSINESS_STATUS_OFF_STOCKS("该单据已完成申请", 1010000023),
    BUSINESS_STATUS_HAVE_BEEN_VOIDED("该单据已作废", 1010000024),
    BUSINESS_STATUS_HAVE_DELETED("该单据已删除", 1010000025),
    BUSINESS_STATUS_WITHDRAWN("该单据已撤回", 1010000026),
    BUSINESS_STATUS_RETURNED("该单据已退回", 1010000027),
    NOT_FORM_SOURCE("表单定义不存在", 1010000028),
    APPLICANT_DOES_NOT_EXIST("单据申请人不存在",1010000029),
    COMMENT_IS_EMPTY("非正常流程操作时，【备注/意见】不能为空",1010000030),
    PROCESS_IS_NOT_ALLOWED_COMMIT_Audit("该流程不允许提交审核！",1010000031),
    PROCESS_IS_NOT_ALLOWED_CANCEL_COMMIT_Audit("该流程不允许撤销提交！",1010000032),
    PROCESS_HAS_NOT_BEEN_COMPLETED("存在流程未审核完成，无法保存新流程！", 1010000033),
    BATCH_PROCESSING_FAILURE("批量办理失败！下标${0}错误代码${1}", 1010000034),
    THE_REVIEW_PROCESS_HAS_BEEN_CHANGED("该审核流程已变更，请点击【强制重置流程】后再操作", 1010000035),
    ;


    private String msg;
    private int code;

    ResultErrorCode(String msg, int code) {
        this.msg = msg;
        this.code = code;

    }

    @Override
    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
