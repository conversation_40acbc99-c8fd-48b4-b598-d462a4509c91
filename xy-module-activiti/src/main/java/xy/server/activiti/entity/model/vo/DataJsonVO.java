package xy.server.activiti.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "DataJsonVO对象", description = "")
public class DataJsonVO {

    @ApiModelProperty(value = "类型 0-节点，1-条件")
    public Integer type;
    @ApiModelProperty(value = "线标识")
    public String conditionExpressionId;
    @ApiModelProperty(value = "节点标识")
    public String nodeId;
    @ApiModelProperty(value = "节点｜线数据")
    public String data;

}
