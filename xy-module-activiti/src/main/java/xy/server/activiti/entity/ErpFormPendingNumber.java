package xy.server.activiti.entity;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xunyue.config.mybatisplus.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import xy.server.activiti.common.enums.WorkflowKeyEnum;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 待开数量记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-30
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName(value = "erp_form_pending_number")
public class ErpFormPendingNumber extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId("form_pending_number_guid")
    private String formPendingNumberGuid;
    /**
     * 表单标识（采购订单）
     */
    @TableField("form_key")
    private String formKey;
    /**
     * 表单类型（采购订单）
     */
    @TableField("form_type_value")
    private String formTypeValue;
    /**
     * 来源表单标识(如：采购申请单)
     */
    @TableField("source_form_key")
    private String sourceFormKey;
    /**
     * 来源表单类型(如：采购申请单)
     */
    @TableField("source_form_type_value")
    private String sourceFormTypeValue;
    /**
     * 待处理数量
     */
    @TableField("pending_number")
    private BigDecimal pendingNumber;
    /**
     * 待处理数量
     */
    @TableField("view_filter_config_guid")
    private String viewFilterConfigGuid;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;

    public ErpFormPendingNumber(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum) {
        this.formKey = workflowKeyEnum.getFormSourceKey();
        this.formTypeValue = workflowKeyEnum.getFormSourceTypeValue();
        if (ObjectUtil.isNotNull(sourceWorkflowKeyEnum)) {
            this.sourceFormKey = sourceWorkflowKeyEnum.getFormSourceKey();
            this.sourceFormTypeValue = sourceWorkflowKeyEnum.getFormSourceTypeValue();
        }
        this.pendingNumber = BigDecimal.ZERO;
    }

    public ErpFormPendingNumber(WorkflowKeyEnum workflowKeyEnum, WorkflowKeyEnum sourceWorkflowKeyEnum, String viewFilterConfigGuid) {
        this.formKey = workflowKeyEnum.getFormSourceKey();
        this.formTypeValue = workflowKeyEnum.getFormSourceTypeValue();
        if (ObjectUtil.isNotNull(sourceWorkflowKeyEnum)) {
            this.sourceFormKey = sourceWorkflowKeyEnum.getFormSourceKey();
            this.sourceFormTypeValue = sourceWorkflowKeyEnum.getFormSourceTypeValue();
        }
        this.pendingNumber = BigDecimal.ZERO;
        this.viewFilterConfigGuid = viewFilterConfigGuid;
    }


}
