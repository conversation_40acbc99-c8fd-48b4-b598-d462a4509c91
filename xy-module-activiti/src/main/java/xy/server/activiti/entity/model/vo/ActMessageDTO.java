package xy.server.activiti.entity.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.activiti.common.enums.BusinessStatusEnum;
import xy.server.activiti.common.enums.WorkflowKeyEnum;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @data 2023-12-12 14:45
 * @apiNote activiti消息DTO（存在redis内）
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ActMessageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("员工guid")
    private String staffGuid;

    @ApiModelProperty("审核状态")
    private String auditStatus;

    @ApiModelProperty("表单定义Key")
    private String formSourceKey;

    @ApiModelProperty("表单定义类型值")
    private String formSourceTypeValue;

    @ApiModelProperty("消息数量")
    private int messageQuantity;

    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;

    public ActMessageDTO(String staffGuid, WorkflowKeyEnum workflowKeyEnum, BusinessStatusEnum businessStatusEnum, String businessKey) {
        this.staffGuid = staffGuid;
        this.formSourceKey = workflowKeyEnum.getFormSourceKey();
        this.formSourceTypeValue = workflowKeyEnum.getFormSourceTypeValue();
        this.auditStatus = businessStatusEnum.getStatus();
        this.messageQuantity = 1;
        // 记录业务单据guid
        this.businessKeys = new HashSet<>(1);
        this.businessKeys.add(businessKey);
    }
}
