package xy.server.activiti.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @data 2023/9/5 17:36
 * @apiNote act历史活动节点表
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("act_hi_actinst")
public class ActHiActinst  implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @TableId("id_")
    private String id;
    /**
     * 流程定义id
     */
    @TableField("proc_def_id_")
    private String procDefId;
    /**
     * 流程实例id
     */
    @TableField("proc_inst_id_")
    private String procInstId;
    /**
     * 执行实例id
     */
    @TableField("execution_id_")
    private String executionId;
    /**
     * act节点id
     */
    @TableField("act_id_")
    private String actId;
    /**
     * 任务id
     */
    @TableField("task_id_")
    private String taskId;
    /**
     * 外部流程实例id
     */
    @TableField("call_proc_inst_id_")
    private String callProcInstId;
    /**
     * act节点名称
     */
    @TableField("act_name_")
    private String actName;
    /**
     * act节点类型
     */
    @TableField("act_type_")
    private String actType;
    /**
     * 办理人
     */
    @TableField("assignee_")
    private String assignee;
    /**
     * 开始时间
     */
    @TableField("start_time_")
    private String startTime;
    /**
     * 结束时间
     */
    @TableField("end_time_")
    private String endTime;
    /**
     * 时间间隔
     */
    @TableField("duration_")
    private String duration;
    /**
     * 删除原因
     */
    @TableField("delete_reason_")
    private String deleteReason;
    /**
     * 租户id
     */
    @TableField("tenant_id_")
    private String tenantId;

}
