package xy.server.activiti.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2023-12-08 9:44
 * @apiNote 审核流程RO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ActAuditProcessRO", description = "审核流程RO")
public class ActAuditProcessRO {

    @NotBlank
    @ApiModelProperty(value = "表单定义")
    private String formSourceKey;

    @NotBlank
    @ApiModelProperty(value = "表单定义类型值")
    private String formSourceTypeValue;

    @NotBlank
    @ApiModelProperty(value = "业务id")
    private String businessKey;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "目标任务ActivityId（驳回时使用）")
    private String targetActivityId;

    @ApiModelProperty(value = "审批意见")
    private String comment;

    @ApiModelProperty(value = "流程变量")
    private Map<String, Object> variables;

}
