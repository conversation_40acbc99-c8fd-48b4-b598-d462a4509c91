package xy.server.activiti.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 审核流条件定义
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-15
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("act_node_term")
public class ActNodeTerm implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId("act_node_term_guid")
    private String actNodeTermGuid;
    /**
     * 单据
     */
    @TableField("form_source_guid")
    private String formSourceGuid;
    @TableField("name")
    private String name;
    /**
     * 条件类型，
     * 1、下拉框
     * 2、数字
     * 3、字符串
     * 4、人员
     * 5、时间
     * 6、部门
     * 7、职位
     */
    @TableField("type")
    private Integer type;
    /**
     * 条件数据来源类型
     * 1、接口
     * 2、json
     * 3、SQL语句
     */
    @TableField("data_source_type")
    private Integer dataSourceType;
    /**
     * 条件数据来源
     */
    @TableField("data_source")
    private String dataSource;
    /**
     * 下拉框数据来源
     */
    @TableField("select_data_source")
    private String selectDataSource;

    /**
     * 表单唯一标识
     */
    @TableField("form_source_key")
    private String formSourceKey;

    /**
     * 表单定义类型值
     */
    @TableField("form_source_type_value")
    private String formSourceTypeValue;


}
