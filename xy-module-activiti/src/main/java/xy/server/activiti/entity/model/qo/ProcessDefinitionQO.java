package xy.server.activiti.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ProcessDefinitionQO对象", description = "")
public class ProcessDefinitionQO extends BaseEntity {

    @ApiModelProperty(value = "租户Guid")
    private String tenantGuid;

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "")
    @NotNull(message = "流程name不能为空")
    private String name;

    @ApiModelProperty(value = "")
    @NotNull(message = "流程key不能为空")
    private String key;

    @ApiModelProperty(value = "")
    private int version;

    @ApiModelProperty(value = "")
    private String deploymentId;

    @ApiModelProperty(value = "")
    private String resourceName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "")
    private Date deploymentTime;

    /**
     * 流程实例状态 1 激活 2 挂起
     */
    @ApiModelProperty(value = "")
    private Integer suspendState;
}
