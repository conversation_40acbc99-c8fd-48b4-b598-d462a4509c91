package xy.server.activiti.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @program: ruoyi-vue-plus
 * @description: 流程审批记录视图
 * @author: gssong
 * @created: 2021/10/16 15:36
 */
@Data
public class ActHistoryInfoVO implements Serializable {

    private static final long serialVersionUID=1L;
    /**
     * 任务id
     */
    private String id;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 流程实例id
     */
    private String processInstanceId;
    /**
     * 业务单据guid
     */
    private String businessKey;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 类型
     */
    private String type;

    @XyTrans(dictionaryKey = "HISTORY_INFO_TYPE", dictionaryValue = "type")
    private String typeDictValue;

    /**
     * 状态
     */
    private String status;
    /**
     * 办理人id
     */
    private String assignee;
    /**
     * 办理人名称
     */
    private String nickName;
    /**
     * 审批信息
     */
    private String comment;
}
