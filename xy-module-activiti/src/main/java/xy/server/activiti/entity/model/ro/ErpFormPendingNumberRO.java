package xy.server.activiti.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>
 * 待开数量记录表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpFormPendingNumberRO对象", description = "待开数量记录表")
public class ErpFormPendingNumberRO extends BaseEntity {

    @ApiModelProperty(value = "主键 ")
    private String formPendingNumberGuid;

    @NotNull(message = "表单标识（采购订单） 不能为空")
    @ApiModelProperty(value = "表单标识（采购订单） ")
    private String formKey;

    @NotNull(message = "表单类型（采购订单）不能为空")
    @ApiModelProperty(value = "表单类型（采购订单）")
    private String formTypeValue;

    @NotNull(message = "来源表单标识(如：采购申请单) 不能为空")
    @ApiModelProperty(value = "来源表单标识(如：采购申请单) ")
    private String sourceFormKey;

    @NotNull(message = "来源表单类型(如：采购申请单) 不能为空")
    @ApiModelProperty(value = "来源表单类型(如：采购申请单) ")
    private String sourceFormTypeValue;

    @NotNull(message = "待处理数量不能为空")
    @ApiModelProperty(value = "待处理数量")
    private BigDecimal pendingNumber;

    @ApiModelProperty(value = "视图筛选配置guid")
    private String viewFilterConfigGuid;
}
