package xy.server.activiti.entity.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2024-03-18 10:16
 * @apiNote 流程导航消息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProcessNavigationMessageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("智能决策推动引擎消息列表")
    private List<ActMessageDTO> actMessageList;

    @ApiModelProperty("智能流程串连引擎消息列表")
    private List<ErpFormPendingNumberVO> formPendingNumberList;
}
