package xy.server.activiti.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 待开数量记录表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpFormPendingNumberQO对象", description = "待开数量记录表")
public class ErpFormPendingNumberQO {

    @ApiModelProperty(value = "租户guid")
    private String tenantGuid;

    @ApiModelProperty(value = "主键 ")
    private String formPendingNumberGuid;

    @ApiModelProperty(value = "表单标识（采购订单） ")
    private String formKey;

    @ApiModelProperty(value = "表单类型（采购订单）")
    private String formTypeValue;

    @ApiModelProperty(value = "来源表单标识(如：采购申请单) ")
    private String sourceFormKey;

    @ApiModelProperty(value = "来源表单类型(如：采购申请单) ")
    private String sourceFormTypeValue;

    @ApiModelProperty(value = "待处理数量")
    private BigDecimal pendingNumber;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;
}