package xy.server.activiti.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.activiti.common.enums.WorkflowKeyEnum;

/**
 * <AUTHOR>
 * @data 2023/8/29 15:09
 * @apiNote TaskQO对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "TaskQO对象", description = "任务查询对象")
public class TaskQO {

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "流程实例id")
    private String processInstId;

    @ApiModelProperty(value = "流程key")
    private String processKey;

    @ApiModelProperty(value = "流程key枚举")
    private WorkflowKeyEnum workflowKeyEnum;

    /**
     * 用于业务列表查询任务（查询特定指定业务流程任务）
     * @param workflowKeyEnum 流程定义key
     */
    public TaskQO(WorkflowKeyEnum workflowKeyEnum) {
        this.workflowKeyEnum = workflowKeyEnum;
    }
}
