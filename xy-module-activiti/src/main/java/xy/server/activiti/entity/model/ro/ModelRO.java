package xy.server.activiti.entity.model.ro;

import com.xunyue.common.validate.AddGroup;
import com.xunyue.common.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class ModelRO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 模型名称
     */
    @NotBlank(message = "模型图片不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 模型标识key
     */
    @NotBlank(message = "模型图片不能为空", groups = {AddGroup.class, EditGroup.class})
    private String key;

    /**
     * 模型备注
     */
    private String description;

}
