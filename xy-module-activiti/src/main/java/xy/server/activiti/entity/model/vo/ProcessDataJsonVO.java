package xy.server.activiti.entity.model.vo;

import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023-08-16 16:49
 **/
@Data
public class ProcessDataJsonVO {
    @ApiModelProperty("流程数据")
    private Object xyFlowJson;
    @ApiModelProperty("目标表单Key")
    private String formSourceKey;
    @ApiModelProperty("表单定义类型值")
    private String formSourceTypeValue;
    @ApiModelProperty("审核节点自定义设置")
    private String json;

    ObjectNode editorJsonNode;
}