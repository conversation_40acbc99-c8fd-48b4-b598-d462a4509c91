package xy.server.activiti.entity.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TermDTO {

    @ApiModelProperty(value = "")
    private String actNodeTermGuid;

    @ApiModelProperty(value = "审核流ID")
    private Integer authflowID;

    @ApiModelProperty(value = "条件说明")
    private String name;

    @ApiModelProperty(value = "条件类型")
    private Integer type;

    @ApiModelProperty(value = "条件数据来源类型")
    private Integer dataSourceType;

    @ApiModelProperty(value = "条件数据来源")
    private String dataSource;

    @ApiModelProperty("判断条件")
    private Integer logic;
    @ApiModelProperty("条件是否选中")
    private Boolean checked;
    @ApiModelProperty("比较值1")
    private String logicValue;
    @ApiModelProperty("比较值2")
    private String logicValue2;
}
