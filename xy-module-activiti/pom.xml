<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xy-erp</artifactId>
        <groupId>xy.erp.biz</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>xy-module-activiti</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <xerces.version>2.11.0</xerces.version>
        <batik.version>1.10</batik.version>
        <commons.io.version>2.11.0</commons.io.version>
    </properties>

    <dependencies>


        <!--activiti6.0-->
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-spring-boot-starter-rest-api</artifactId>
            <version>${activiti.version}</version>
        </dependency>

        <!-- java绘制activiti流程图 -->
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-image-generator</artifactId>
            <version>${activiti.version}</version>
        </dependency>

        <!-- activiti json转换器-->
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-json-converter</artifactId>
            <version>${activiti.version}</version>
        </dependency>

       <dependency>
            <groupId>com.xunyue</groupId>
            <artifactId>xy-erp-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xunyue</groupId>
            <artifactId>xy-erp-activiti</artifactId>
        </dependency>

        <dependency>
            <groupId>xy.erp.biz</groupId>
            <artifactId>xy-module-mes</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>xy.erp.biz</groupId>
            <artifactId>xy-module-basic-data</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- svg转png图片工具-->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-all</artifactId>
            <version>${batik.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>xalan</groupId>
                    <artifactId>xalan</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-bpmn-layout</artifactId>
            <version>${activiti.version}</version>
            <scope>compile</scope>
        </dependency>

        <!--解决部署xml错误-->
        <dependency>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
            <version>${xerces.version}</version>
        </dependency>

        <!-- 表达式解析 -->
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.xunyue</groupId>
            <artifactId>xy-erp-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>xy.erp.biz</groupId>
            <artifactId>xy-module-view-filter</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>xy.erp.biz</groupId>
            <artifactId>xy-module-policy-engine</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>
