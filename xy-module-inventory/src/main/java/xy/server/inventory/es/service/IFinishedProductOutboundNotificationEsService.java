package xy.server.inventory.es.service;

import com.xunyue.config.util.PageParams;
import org.dromara.easyes.core.biz.EsPageInfo;
import xy.server.inventory.es.entity.FinishedProductOutboundNotification;
import xy.server.inventory.es.entity.qo.FinishedProductOutboundNotificationQO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-06-06 08:42
 **/
public interface IFinishedProductOutboundNotificationEsService {
    /**
     * 同步成品出库通知数据到es
     * @param pageParams
     * @return
     */

    Boolean syncExperienceDataToEs(PageParams<FinishedProductOutboundNotificationQO> pageParams);

    /**
     * 从es中获取成品出库通知数据
     * @param model
     * @return
     */
    EsPageInfo<FinishedProductOutboundNotification> getExperienceData(PageParams<FinishedProductOutboundNotificationQO> model);

    /**
     * 清空成品出库通知所有数据
     * @return
     */
    Boolean clearExperienceDataToEs();

    /**
     * 保存成品出库通知明细数据后同步到es
     * @param orderDataGuid
     */
    void syncSaveExperienceDataToEs(String orderDataGuid);

    /**
     * 删除成品出库通知明细数据
     * @param collect
     */
    void deleteExperienceDataToEs(List<String> collect);
}
