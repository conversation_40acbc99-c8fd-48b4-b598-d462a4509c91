package xy.server.inventory.es.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.RefreshPolicy;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 纸板信息
 */
@Data
@IndexName(value = "finished_product_outbound_notification", refreshPolicy = RefreshPolicy.IMMEDIATE)
@Settings(maxResultWindow = 10000000)
public class FinishedProductOutboundNotification implements Cloneable {

    @IndexId
    @ApiModelProperty(value = "PK")
    private String esGuid;

    @ApiModelProperty(value = "工单guid")
    @IndexField(fieldType = FieldType.TEXT, fieldData = true)
    private String workorderGuid;

    @ApiModelProperty(value = "成品出库通知单号")
    @IndexField(fieldType = FieldType.KEYWORD, ignoreCase = true)
    private String workorderNumber;

    @ApiModelProperty(value = "订单号")
    @IndexField(fieldType = FieldType.KEYWORD, ignoreCase = true)
    private String orderNumber;

    @ApiModelProperty(value = "订单数量")
    private BigDecimal orderQuantity;

    @ApiModelProperty(value = "纸质全称")
    @IndexField(fieldType = FieldType.KEYWORD, ignoreCase = true)
    private String fullPaperName;

    @ApiModelProperty(value = "搜索码")
    @IndexField(fieldType = FieldType.KEYWORD, ignoreCase = true)
    private String searchCode;

    @ApiModelProperty(value = "单据日期(可变更的)")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "成品出库通知类型")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "申请类型（字典值）")
    @XyTrans(dictionaryKey = "FINISHED_PRODUCT_OUTBOUND_NOTIFICATION_TYPE", dictionaryValue = "workorderTypeGuid")
    private String workorderTypeDictValue;

    @ApiModelProperty(value = "工单属性")
    private Integer workorderProperties;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @ApiModelProperty(value = "客户/供应商guid")
    private String customerOrSupplierGuid;

    @ApiModelProperty(value = "客户/供应商Name")
    private String customerOrSupplierName;

    @ApiModelProperty(value = "楞别")
    private String corrugatedPaperName;

    @ApiModelProperty(value = "单重")
    private String pieceWeight;
    @ApiModelProperty(value = "体积")
    private String volume;
    @ApiModelProperty(value = "纸箱面积")
    private String cartonArea;

    @ApiModelProperty(value = "结算客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String settlementCustomerOrSupplierGuid;

    @ApiModelProperty(value = "结算客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String settlementCustomerOrSupplierName;

    @ApiModelProperty(value = "唯一父guid")
    private String onlyParentClassificationGuid;

    @ApiModelProperty(value = "送货客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String deliveryCustomerOrSupplierGuid;

    @ApiModelProperty(value = "送货客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String deliveryCustomerOrSupplierName;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "物料分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料GUID(成品、物料)")
    private String materialGuid;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;


    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty(value = "本币单价(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyUnitPrice;

    @ApiModelProperty(value = "本币金额(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalAmount;

    @ApiModelProperty(value = "结算单价（默认与含税单价相等），写入库存价格字段")
    private BigDecimal settlementUnitPrice;

    @ApiModelProperty(value = "结算金额（默认与含税总金额相等），写入库存价格字段")
    private BigDecimal settlementTotalAmount;

    @ApiModelProperty(value = "外部单号(例客户PO，采购送货单号)")
    private String externalTrackingNumber;

    @ApiModelProperty(value = "外部物料条码(BarCode)")
    private String externalMaterialBarCode;

    @ApiModelProperty(value = "外部物料编码(例客户产品编码)")
    private String externalMaterialCode;

    @ApiModelProperty(value = "外部物料名称(例客户产品名称)")
    private String externalMaterialName;

    @ApiModelProperty(value = "货期")
    private LocalDateTime requiredDeliveryTime;

    @ApiModelProperty(value = "行政区域")
    private String administrativeAreaName;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @IndexField(fieldType = FieldType.KEYWORD, ignoreCase = true)
    @ApiModelProperty(value = "物料编码，产品编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String  materialName;

    @ApiModelProperty(value = "物料规格")
    private String materialSpecification;

    @ApiModelProperty(value = "物料分类名称")
    private String materialClassificationName;

    @ApiModelProperty(value = "客户确认时间")
    private LocalDateTime customerConfirmationDate;

    @ApiModelProperty(value = "客户名称")
    private String customerFullName;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "客户简称")
    private String customerShortName;

    @ApiModelProperty(value = "比例")
    private BigDecimal proportion;

    @ApiModelProperty(value = "长")
    private String length;

    @ApiModelProperty(value = "宽")
    private String width;

    @ApiModelProperty(value = "高")
    private String height;

    @ApiModelProperty(value = "备品数量")
    private BigDecimal spareQuantity;
}
