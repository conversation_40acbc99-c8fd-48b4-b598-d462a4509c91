package xy.server.inventory.es.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xunyue.config.exception.FlowException;
import com.xunyue.config.util.PageParams;
import com.xy.util.BaseContext;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Service;
import xy.server.inventory.entity.model.vo.FinishedProductOutboundNotificationVO;
import xy.server.inventory.es.entity.FinishedProductOutboundNotification;
import xy.server.inventory.es.entity.qo.FinishedProductOutboundNotificationQO;
import xy.server.inventory.es.mapper.EsFinishedProductOutboundNotificationMapper;
import xy.server.inventory.es.service.IFinishedProductOutboundNotificationEsService;
import xy.server.inventory.mapper.ErpFinishedProductOutboundNotificationMapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024-06-06 08:43
 **/
@Service
public class FinishedProductOutboundNotificationServiceImpl implements IFinishedProductOutboundNotificationEsService {
    @Resource
    private EsFinishedProductOutboundNotificationMapper esFinishedProductOutboundNotificationMapper;
    @Resource
    private ErpFinishedProductOutboundNotificationMapper notificationMapper;



    public static <T> List<List<T>> splitListIntoFourEqualParts(List<T> originalList, int spiltSize) {
        List<List<T>> parts = new ArrayList<>();
        int totalSize = originalList.size();
        int partSize = totalSize / spiltSize;
        int remainder = totalSize % spiltSize;

        int startIndex = 0;
        for (int i = 0; i < spiltSize; i++) {
            int endIndex = startIndex + partSize + (i < remainder ? 1 : 0); // 如果有余数，则分配一个元素到当前子列表
            parts.add(new ArrayList<>(originalList.subList(startIndex, endIndex)));
            startIndex = endIndex;
        }
        return parts;
    }

    /**
     * 同步成品出库通知数据到es
     *
     * @param pageParams
     * @return
     */
    @Override
    public Boolean syncExperienceDataToEs(PageParams<FinishedProductOutboundNotificationQO> pageParams) {
        // 先清空数据
        clearExperienceDataToEs();
        long current = pageParams.getCurrent();
        FinishedProductOutboundNotificationQO model = pageParams.getModel();
        while (true) {
            pageParams.setCurrent(current);
            pageParams.setSize(2000);
            IPage<FinishedProductOutboundNotificationVO> page = pageParams.buildPage();
            IPage<FinishedProductOutboundNotificationVO> page1=notificationMapper.syncExperienceDataToEs(page,model);
            List<FinishedProductOutboundNotificationVO> collect = page1.getRecords().stream().distinct().collect(Collectors.toList());
            current++;
            List<FinishedProductOutboundNotificationVO> list = collect.stream().distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(list)) {
                return false;
            }

            // 创建一个固定大小的线程池
            ExecutorService fixedThreadPool = new ThreadPoolExecutor(16, 16, 0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<>(128),
                    new ThreadFactoryBuilder().setNameFormat("indicator-thread-%d").build());

            List<List<FinishedProductOutboundNotificationVO>> divideList = FinishedProductOutboundNotificationServiceImpl
                    .splitListIntoFourEqualParts(list, 16);

            try {
                // 16个线程
                CountDownLatch latch = new CountDownLatch(16);
                for (int i = 0; i < divideList.size(); i++) {

                    List<FinishedProductOutboundNotificationVO> subList = divideList.get(i);
                    fixedThreadPool.execute(() -> {
                        try {
                            BaseContext.setCreator("系统导入");
                            BaseContext.setCreatorGuid("8e6534a2d226225d443643599db1f4f3");
                            BaseContext.setTenantGuid("2caf3cb6216111eea71b49e0880a97d9");
                            BaseContext.setStaffGuid("8e6534a2d226225d443643599db1f4f3");
                            BaseContext.setStaffShortName("系统导入");
                            List<FinishedProductOutboundNotification> productionList = new ArrayList<>();
                            subList.forEach(product -> {
                                FinishedProductOutboundNotification newProduction = new FinishedProductOutboundNotification();
                                BeanUtil.copyProperties(product, newProduction);
                                productionList.add(newProduction);
                            });
                            esFinishedProductOutboundNotificationMapper.insertBatch(productionList);
                        } catch (Exception e) {
                            e.printStackTrace();
                        } finally {
                            latch.countDown();
                        }
                    });
                }

                latch.await();

                fixedThreadPool.shutdown();

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 从es中获取成品出库通知数据
     *
     * @param qo
     * @return
     */
    @Override
    public EsPageInfo<FinishedProductOutboundNotification> getExperienceData(PageParams<FinishedProductOutboundNotificationQO> qo) {
        LambdaEsQueryWrapper<FinishedProductOutboundNotification> wrapper = new LambdaEsQueryWrapper<>();

        FinishedProductOutboundNotificationQO orderQO = qo.getModel();

        // todo 增加查询条件
        if (Objects.nonNull(orderQO.getCreateTimeStart())) {
            wrapper.ge(FinishedProductOutboundNotification::getCreateDate, DateUtil.format(orderQO.getCreateTimeStart(), "yyyy-MM-dd HH:mm:ss"));
        }
        if (Objects.nonNull(orderQO.getCreateTimeEnd())) {
            wrapper.le(FinishedProductOutboundNotification::getCreateDate, DateUtil.format(orderQO.getCreateTimeEnd(), "yyyy-MM-dd HH:mm:ss"));
        }
        if (Objects.nonNull(orderQO.getDeliveryTimeStart())) {
            wrapper.ge(FinishedProductOutboundNotification::getRequiredDeliveryTime, DateUtil.format(orderQO.getDeliveryTimeStart(), "yyyy-MM-dd HH:mm:ss"));
        }
        if (Objects.nonNull(orderQO.getDeliveryTimeEnd())) {
            wrapper.le(FinishedProductOutboundNotification::getRequiredDeliveryTime, DateUtil.format(orderQO.getDeliveryTimeEnd(), "yyyy-MM-dd HH:mm:ss"));
        }
        if (CollectionUtils.isNotEmpty(orderQO.getAuditStatus())){
            wrapper.in(FinishedProductOutboundNotification::getAuditStatus, orderQO.getAuditStatus());
        }
//查询条件
        if (StrUtil.isNotBlank(orderQO.getLength())) {
            wrapper.eq(FinishedProductOutboundNotification::getLength, orderQO.getLength());
        }
        if (StrUtil.isNotBlank(orderQO.getWidth())) {
            wrapper.eq(FinishedProductOutboundNotification::getWidth, orderQO.getWidth());
        }
        if (StrUtil.isNotBlank(orderQO.getHeight())) {
            wrapper.eq(FinishedProductOutboundNotification::getHeight, orderQO.getHeight());
        }
        if (StrUtil.isNotBlank(orderQO.getMaterialCode())) {
            wrapper.like(FinishedProductOutboundNotification::getMaterialCode, orderQO.getMaterialCode());
        }
        if (StrUtil.isNotBlank(orderQO.getMaterialName())) {
            wrapper.like(FinishedProductOutboundNotification::getMaterialName, orderQO.getMaterialName());
        }
        if (StrUtil.isNotBlank(orderQO.getExternalMaterialCode())) {
            wrapper.like(FinishedProductOutboundNotification::getExternalMaterialCode, orderQO.getExternalMaterialCode());
        }
        if (StrUtil.isNotBlank(orderQO.getCustomerCode())) {
            wrapper.like(FinishedProductOutboundNotification::getCustomerCode, orderQO.getCustomerCode());
        }
        if (StrUtil.isNotBlank(orderQO.getCustomerShortName())) {
            wrapper.like(FinishedProductOutboundNotification::getCustomerShortName, orderQO.getCustomerShortName());
        }
        if (StrUtil.isNotBlank(orderQO.getSearchCode())) {
            wrapper.like(FinishedProductOutboundNotification::getSearchCode, orderQO.getSearchCode());
        }
        if (StrUtil.isNotBlank(orderQO.getMaterialClassificationName())) {
            wrapper.like(FinishedProductOutboundNotification::getMaterialClassificationName, orderQO.getMaterialClassificationName());
        }
        if (StrUtil.isNotBlank(orderQO.getOrderNumber())) {
            wrapper.like(FinishedProductOutboundNotification::getOrderNumber, orderQO.getOrderNumber());
        }
        if (StrUtil.isNotBlank(orderQO.getWorkorderNumber())) {
            wrapper.like(FinishedProductOutboundNotification::getWorkorderNumber, orderQO.getWorkorderNumber());
        }
        if (StrUtil.isNotBlank(orderQO.getStartWorkorderNumber())){
            wrapper.ge(FinishedProductOutboundNotification::getWorkorderNumber, orderQO.getStartWorkorderNumber());
        }
        if (StrUtil.isNotBlank(orderQO.getEndWorkorderNumber())){
            wrapper.le(FinishedProductOutboundNotification::getWorkorderNumber, orderQO.getEndWorkorderNumber());
        }


        // 按时间到序
        wrapper.orderByDesc(FinishedProductOutboundNotification::getCreateDate)
                .orderByAsc(FinishedProductOutboundNotification::getSerialNumber);
        // wrapper.distinct(WorkOrderExperienceProduction::getExperienceProductionProcessGuid);

        // 获取下一页

        return esFinishedProductOutboundNotificationMapper.pageQuery(wrapper,
                Long.valueOf(qo.getCurrent()).intValue(),
                Long.valueOf(qo.getSize()).intValue());
    }

    /**
     * 清空成品出库通知所有数据
     *
     * @return
     */
    @Override
    public Boolean clearExperienceDataToEs() {
        esFinishedProductOutboundNotificationMapper.deleteIndex("finished_product_outbound_notification");
        esFinishedProductOutboundNotificationMapper.createIndex();
        LambdaEsQueryWrapper<FinishedProductOutboundNotification> wrapper = new LambdaEsQueryWrapper<>();
        int deleteCount = esFinishedProductOutboundNotificationMapper.delete(wrapper);
        return true;
    }

    /**
     * 保存成品出库通知明细数据后同步到es
     *
     * @param workorderGuid
     */
    @Override
    public void syncSaveExperienceDataToEs(String workorderGuid) {
        PageParams<FinishedProductOutboundNotificationQO> params = new PageParams<>();
        FinishedProductOutboundNotificationQO model = new FinishedProductOutboundNotificationQO();
        model.setWorkorderGuid(workorderGuid);
        params.setModel(model);
        IPage<FinishedProductOutboundNotificationVO> page = params.buildPage();
        // 同步工单数据到纸板工单
        IPage<FinishedProductOutboundNotificationVO> page1=notificationMapper.syncExperienceDataToEs(page,model);
        LambdaEsQueryWrapper<FinishedProductOutboundNotification> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.eq(FinishedProductOutboundNotification::getWorkorderGuid, workorderGuid);
        esFinishedProductOutboundNotificationMapper.delete(wrapper);
        List<FinishedProductOutboundNotificationVO> list = page1.getRecords().stream().distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(list)) {
            return ;
        }

        for (int i = 0; i < list.size(); i++) {
            FinishedProductOutboundNotificationVO subList = list.get(i);
            BaseContext.setCreator(BaseContext.getCreator());
            BaseContext.setCreatorGuid(BaseContext.getCreatorGuid());
            BaseContext.setTenantGuid(BaseContext.getTenantGuid());
            BaseContext.setStaffGuid(BaseContext.getStaffGuid());
            BaseContext.setStaffShortName(BaseContext.getStaffShortName());
            FinishedProductOutboundNotification productionList = BeanUtil.copyProperties(subList, FinishedProductOutboundNotification.class);
            Integer result = esFinishedProductOutboundNotificationMapper.insert(productionList);
            if (result < 0) {
                throw new FlowException("同步数据错误！", 1);
            }
        }
    }

    /**
     * 删除成品出库通知明细数据
     *
     * @param collect
     */
    @Override
    public void deleteExperienceDataToEs(List<String> collect) {
        if (CollectionUtils.isNotEmpty(collect)) {
            LambdaEsQueryWrapper<FinishedProductOutboundNotification> wrapper = new LambdaEsQueryWrapper<>();
            wrapper.in(FinishedProductOutboundNotification::getWorkorderGuid, collect);
            Integer delete = esFinishedProductOutboundNotificationMapper.delete(wrapper);
        }
    }
}
