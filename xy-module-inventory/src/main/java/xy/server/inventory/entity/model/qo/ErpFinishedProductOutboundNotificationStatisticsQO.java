package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.common.entity.model.qo.ErpStatisticsTotalQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <p>
 * 成品出库通知统计QO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpFinishedProductOutboundNotificationStatisticsQO对象", description = "成品出库通知统计")
public class ErpFinishedProductOutboundNotificationStatisticsQO extends ErpStatisticsTotalQO {

    @ApiModelProperty(value = "客户编码/简称/全称")
    private String customer;

    @ApiModelProperty(value = "出库单号")
    private String outBoundNumber;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料类型")
    private Integer materialType;

    @ApiModelProperty(value = "物料类型名称")
    private String materialClassificationName;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "开始日期（销货日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（销货日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;
}
