package xy.server.inventory.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>
 * 物料批次货明细RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMaterialPackageMaterialDetailRO对象", description = "物料批次货明细")
public class ErpMaterialPackageMaterialDetailRO {

    @ApiModelProperty(value = "PK")
    private String materialBatchGoodsDetailGuid;

    @ApiModelProperty(value = "货guid")
    private String materialBatchGoodsGuid;

    //@NotBlank
    @ApiModelProperty(value = "物料批次guid")
    private String materialBatchGuid;

    @NotBlank
    @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

    @ApiModelProperty(value = "每包数量")
    private BigDecimal quantityPerPackage;

    @NotNull(message = "包装数量不能为空")
    @ApiModelProperty(value = "包装数量")
    private BigDecimal packagingQuantity;

    @NotBlank
    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @NotNull
    @ApiModelProperty(value = "来源类型值")
    private Integer sourceValue;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "规格类型")
    private String specificationMaterial;

    @ApiModelProperty(value = "实际到货数量")
    private BigDecimal actQuantity;

    @ApiModelProperty(value = "单位")
    private String unitName;

    @ApiModelProperty(value = "来源单号")
    private String sourceWorkorderNumber;

    @ApiModelProperty(value = "待开数")
    private BigDecimal waitingQuantity;
}