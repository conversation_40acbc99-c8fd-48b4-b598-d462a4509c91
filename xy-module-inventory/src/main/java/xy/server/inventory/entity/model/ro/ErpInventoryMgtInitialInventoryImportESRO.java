package xy.server.inventory.entity.model.ro;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelTemplate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @data 2024-07-24 9:29
 * @apiNote 期初库存导入RO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
@XyExcelTemplate("期初库存导入模板")
@ApiModel(value = "ErpInventoryMgtInitialInventoryImportRO", description = "期初库存导入RO")
public class ErpInventoryMgtInitialInventoryImportESRO {

    @ApiModelProperty("物料分类guid")
    private String materialGuid;

    @ApiModelProperty("物料分类guid")
    private String materialClassificationGuid;

    @ExcelProperty("物料标签码")
    private String materialGoodsCode;

    @NotBlank(message = "物料/产品编码(不能为空)")
    @ExcelProperty("物料/产品编码")
    private String materialCode;

    @ExcelProperty("物料/产品名称")
    private String materialName;

    @NotNull(message = "原件数量不能为空")
    @ExcelProperty("原件数量")
    private BigDecimal quantity;

    @NotNull(message = "剩余数量不能为空")
    @ExcelProperty("剩余数量")
    private BigDecimal remainingQuantity;

    @NotNull(message = "含税报价单价不能为空")
    @ExcelProperty("含税报价单价")
    private BigDecimal quotationUnitPriceIncludingTax;

    @NotNull(message = "含税总金额不能为空")
    @ExcelProperty("含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @NotNull(message = "不含税报价单价不能为空")
    @ExcelProperty("不含税报价单价")
    private BigDecimal quotationUnitPriceWithoutTax;

    @NotNull(message = "不含税总金额不能为空")
    @ExcelProperty("不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ExcelProperty("幅宽")
    private BigDecimal widthOfFabric;

    @ExcelProperty(value = "条码备注")
    private String description;

    @ApiModelProperty("批次号（格式：客户名称+客户PO）")
    private String materialProductionBatchNumber;
}
