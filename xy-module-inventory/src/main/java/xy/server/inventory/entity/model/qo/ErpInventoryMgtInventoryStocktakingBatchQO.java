package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
                                                                                                                                                                /**
* <p>
* 库存盘点批次表QO
* </p>
*
* <AUTHOR>
* @since 2023-10-19
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryStocktakingBatchQO对象", description = "库存盘点批次表")
public class ErpInventoryMgtInventoryStocktakingBatchQO{

        @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

        @ApiModelProperty(value = "库单guid")
    private String inventoryReceiptGuid;

            @ApiModelProperty(value = "PK")
    private String inventoryStocktakingBatch;

        @ApiModelProperty(value = "批次库存guid")
    private String inventoryMaterialBatchInventoryGuid;

        @ApiModelProperty(value = "物料批次guid")
    private String materialBatchGuid;

        @ApiModelProperty(value = "物分类guid")
    private String materialClassificationGuid;

        @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

            @ApiModelProperty(value = "批次库存数量")
    private BigDecimal batchInventoryQuantity;

            @ApiModelProperty(value = "批次盘点数量")
    private BigDecimal batchInventoryStocktakingQuantity;

            @ApiModelProperty(value = "批次盘盈数量")
    private BigDecimal batchInventorySurplusQuantity;

            @ApiModelProperty(value = "批次盘亏数量")
    private BigDecimal batchInventoryLossQuantity;

            @ApiModelProperty(value = "备注或描述")
    private String description;

        @ApiModelProperty(value = "创建人ID")
    private String creatorGuid;

            @ApiModelProperty(value = "创建人")
    private String creator;

            @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

        @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

            @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

            @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

        @ApiModelProperty(value = "扩展字段")
    private Object toJson;

}
