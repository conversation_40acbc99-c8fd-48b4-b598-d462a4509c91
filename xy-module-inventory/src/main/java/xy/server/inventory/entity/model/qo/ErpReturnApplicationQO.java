package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 退货申请QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpReturnApplicationQO对象", description = "退货申请")
public class ErpReturnApplicationQO {

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "workorderGuid")
    private String workorderGuid;


    @ApiModelProperty(value = "工单类型_guid")
    private List<String> workorderTypeGuids;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "审核状态")
    private List<String> auditStatus;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "开始日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @NotNull(message = "工单属性不能为空")
    @ApiModelProperty(value = "工单属性")
    private Integer workorderProperties;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;

    @ApiModelProperty(value = "动态路由菜单")
    private String dynamicComponent;

    @ApiModelProperty(value = "生产工单号")
    private String productionOrderNumberStr;

    @ApiModelProperty(value = "处理方式")
    private String handlingMethod;

    @ApiModelProperty(value = "物料(名称/编码)")
    private String material;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

}
