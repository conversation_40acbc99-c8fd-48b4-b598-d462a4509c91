package xy.server.inventory.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <p>
 * 库存结转表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtBalanceURO对象", description = "库存结转表")
public class ErpInventoryMgtBalanceRO extends BaseEntity {

    @ApiModelProperty(value = "pk")
    private String balanceGuid;

    @NotNull(message = "结转名称不能为空")
    @ApiModelProperty(value = "结转名称")
    private String balanceName;

    @NotNull(message = "结转属性(可用字典区分：1、物料，2、成品，3、半成品，4、模具)不能为空")
    @ApiModelProperty(value = "结转属性(可用字典区分：1、物料，2、成品，3、半成品，4、模具)")
    private Integer balanceProperties;

    @NotNull(message = "结转类型(字典)(1全月加权，2移动加权)不能为空")
    @ApiModelProperty(value = "结转类型(字典)(1全月加权，2移动加权)")
    private String balanceType;

    @ApiModelProperty(value = "结转状态")
    private Boolean balanceStatus;

    @NotNull(message = "开始日期不能为空")
    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @NotNull(message = "结束日期不能为空")
    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

}
