package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryBasicMgtUnitVO对象", description = "")
public class ErpInventoryBasicMgtUnitVO {

    @ApiModelProperty(value = "")
    private String unitGuid;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

}
