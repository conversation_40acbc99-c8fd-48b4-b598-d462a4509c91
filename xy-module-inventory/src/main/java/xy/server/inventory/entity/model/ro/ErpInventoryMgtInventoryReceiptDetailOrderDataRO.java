package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 库单明细管理扩展表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryReceiptDetailOrderDataURO对象", description = "库单明细管理扩展表")
public class ErpInventoryMgtInventoryReceiptDetailOrderDataRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptDetailOrderGuid;

    @ApiModelProperty(value = "库单明细_guid")
    private String inventoryReceiptDetailGuid;

    @ApiModelProperty(value = "订单数据guid")
    private String orderDataGuid;

}
