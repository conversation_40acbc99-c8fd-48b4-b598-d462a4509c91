package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpSupplierBusinessMgtOrderQO对象", description = "订单表")
public class ErpSupplierBusinessMgtOrderQO {

    @ApiModelProperty(value = "工单号")
    private String workOrderNumber;

    @ApiModelProperty(value = "工单号")
    private String sourceNumber;

    @ApiModelProperty(value = "开单开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "物料名称")
    private String  materialName;

    @NotBlank(message = "客户/供应商guid不能为空")
    @ApiModelProperty(value = "客户/供应商guid")
    private String customerOrSupplierGuid;
}