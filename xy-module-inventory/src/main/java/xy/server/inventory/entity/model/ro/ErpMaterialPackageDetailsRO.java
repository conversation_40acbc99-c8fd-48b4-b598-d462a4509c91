package xy.server.inventory.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 物料批次货RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMaterialPackageDetailsRO对象", description = "物料批次货")
public class ErpMaterialPackageDetailsRO {

    @ApiModelProperty(value = "PK")
    private String materialBatchGoodsGuid;

    @ApiModelProperty(value = "位置_guid（该批次放到那个仓）")
    private String warehouseSpaceGuid;

    @ApiModelProperty(value = "位置_guid（该批次放到那个仓）")
    private String warehouseSpaceName;

    @ApiModelProperty(value = "guid")
    private String workorderGuid;

    @ApiModelProperty(value = "物料货号码(供应商提供或者自动生成)")
    private String materialGoodsCode;

    @NotNull(message = "每包数量不能为空")
    @ApiModelProperty(value = "每包数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源类型值")
    private Integer sourceValue;

    @ApiModelProperty(value = "是否入库")
    private Boolean isInventory;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "第几包")
    private Integer serialNumber;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @ApiModelProperty(value = "关联表数据")
    private String abelAndInventoryGuid;

    @ApiModelProperty(value = "包装剩余数量")
    private BigDecimal remainingQuantity;

    @ApiModelProperty(value = "出库数量")
    private BigDecimal outboundQuantity;

    @ApiModelProperty(value = "tmpSourceGuid")
    private String tmpSourceGuid;

    @ApiModelProperty(value = "实际到货数量")
    private BigDecimal actQuantity;

    @Valid
    @ApiModelProperty(value = "产品明细列表")
    private List<ErpMaterialPackageMaterialDetailRO> detailList;

    @ApiModelProperty(value = "物料批次guid")
    private String materialBatchGuid;

    @JsonIgnore
    private String inventoryReceiptBatchDetail;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;
}
