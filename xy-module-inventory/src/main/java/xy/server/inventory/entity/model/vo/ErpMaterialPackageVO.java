package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.inventory.entity.model.ro.ErpMaterialPackageDetailsRO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
* <p>
* VO
* </p>
*
* <AUTHOR>
* @since 2024-01-23
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpMaterialPackageVO对象", description = "")
public class ErpMaterialPackageVO {

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "包装类型")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "包装类型字典值")
    @XyTrans(dictionaryKey = "MATERIAL_PACKAGE_TYPE", dictionaryValue = "workorderTypeGuid")
    private String workorderTypeDictValue;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核状态字典值")
    @XyTrans(dictionaryKey = "AUDIT_STATUS", dictionaryValue = "auditStatus")
    private String auditStatusDictValue;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "工单属性(1工单，2工序工单，3采购工单，4外发工单，5车间工单，6作业工单，维修工单，保养工单)")
    private Integer workorderProperties;

    @ApiModelProperty(value = "包装方式")
    private String productionProcessesDescription;

    @ApiModelProperty(value = "每包数量")
    private BigDecimal proportion;

    @ApiModelProperty(value = "包装总数")
    private BigDecimal quantity;

    @ApiModelProperty(value = "包数")
    private BigDecimal outputQuantity;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "入库情况")
    private String incomingStatus;

    @ApiModelProperty(value = "入库情况字典值")
    @XyTrans(dictionaryKey = "INCOMING_STATUS", dictionaryValue = "incomingStatus")
    private String incomingStatusDictValue;

    @ApiModelProperty(value = "散装总数")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "包装明细列表")
    @XyTransCycle
    private List<ErpMaterialPackageDetailsRO> detailList;

    /**
     * 来源值
     */
    @ApiModelProperty("source_value")
    private Integer sourceValue;


}
