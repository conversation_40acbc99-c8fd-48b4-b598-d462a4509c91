package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 库单明细管理扩展表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryReceiptDetailOrderDataQO对象", description = "库单明细管理扩展表")
public class ErpInventoryMgtInventoryReceiptDetailOrderDataQO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptDetailOrderGuid;

    @ApiModelProperty(value = "库单明细_guid")
    private String inventoryReceiptDetailGuid;

    @ApiModelProperty(value = "订单数据guid")
    private String orderDataGuid;

    @ApiModelProperty(value = "创建人ID")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

}
