package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 库单批次明细管理RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtOutboundReceiptBatchDetailURO对象", description = "库单批次明细管理")
public class ErpInventoryMgtOutboundReceiptBatchDetailRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptBatchDetail;

    @ApiModelProperty(value = "库单明细guid")
    private String inventoryReceiptDetailGuid;

    @ApiModelProperty(value = "物批次guid")
    private String materialBatchGuid;

    @ApiModelProperty(value = "物guid")
    private String materialGuid;

    @ApiModelProperty(value = "库位guid")
    private String warehouseSpaceGuid;

    @NotNull
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "包装明细列表")
    private List<ErpMaterialPackageDetailsRO> detailList;
}
