package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 出仓单管理VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpWarehouseMgtOutWarehouseReceiptVO对象", description = "出仓单管理VO")
public class ErpWarehouseMgtOutWarehouseReceiptVO {

    @ApiModelProperty(value = "PK")
    private String warehouseReceiptGuid;

    @ApiModelProperty(value = "仓单属性")
    private Integer warehouseReceiptProperties;

    @ApiModelProperty(value = "仓单类型(字典)")
    private String warehouseReceiptType;

    @ApiModelProperty(value = "仓单类型字典值")
    @XyTrans(dictionaryKey = "OUT_WAREHOUSE_TYPE", dictionaryValue = "warehouseReceiptType")
    private String warehouseReceiptTypeDictValue;

    @ApiModelProperty(value = "仓单单号")
    private String warehouseReceiptNumbers;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "单据日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "出仓明细列表")
    @XyTransCycle
    private List<ErpWarehouseMgtOutWarehouseReceiptDetailVO> detailList;

}
