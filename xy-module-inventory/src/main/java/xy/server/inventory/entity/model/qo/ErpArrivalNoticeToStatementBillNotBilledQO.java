package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @data 2023/11/7 9:21
 * @apiNote 到货单-对账单待开列表明细QO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpArrivalNoticeToStatementBillNotBilledDetailQO对象", description = "到货单-对账单待开列表明细QO")
public class ErpArrivalNoticeToStatementBillNotBilledQO {

    @ApiModelProperty(value = "到货单号")
    private String workorderNumber;

    @ApiModelProperty(value = "供应商guid")
    private String supplierGuid;

    @ApiModelProperty(value = "供应商分类属性")
    private String supplierClassificationAttributeValue;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料（名称/编码/全称）")
    private String material;

    @ApiModelProperty(value = "开始日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "工单属性（103物料退货申请；104半成品退货申请）")
    private Integer workorderProperties;

    @ApiModelProperty(value = "订单号（采购/外发订单）")
    private String orderNumber;


    @ApiModelProperty(value = "加工商查询条件")
    private String supplierOrProcessorSearch;

    @ApiModelProperty(value = "物料（名称/编码/全称）")
    private String materialSearch;

}
