package xy.server.inventory.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.work.entity.ErpProductionMgtWorkorderOrderData;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 生产退料申请RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpProductionReturnApplicationRO对象", description = "生产退料申请")
public class ErpProductionReturnApplicationRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @NotNull(message = "工单号不能为空")
    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "工单类型_guid")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "长")
    private BigDecimal specificationsLength;

    @ApiModelProperty(value = "宽")
    private BigDecimal specificationsWidth;


    @ApiModelProperty(value = "工单状态")
    private String workorderState;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "工单属性")
    private Integer workorderProperties;

    @ApiModelProperty(value = "物料分类(成品)")
    private String materialGuid;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "来源数量")
    private BigDecimal sourceQuantity;

    @ApiModelProperty(value = "总数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "单位")
    private String unitGuid;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "生产退料明细数据")
    private List<ErpProductionReturnApplicationRO> productionReturnApplicationDetailList;

    @ApiModelProperty(value = "生产退料明细形态转变数据")
    private List<ErpProductionReturnApplicationRO> morphologicalTransformationList;

    @ApiModelProperty(value = "生产退料明细工单-订单数据")
    private ErpProductionMgtWorkorderOrderData workorderOrderData;

    @ApiModelProperty(value = "退料批次")
    private List<ErpInventoryMaterialMgtMaterialBatchRO> materialBatchList;
}
