package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpSupplierBusinessMgtOrderVO对象", description = "订单表")
public class ErpSupplierBusinessMgtOrderVO {

    @ApiModelProperty(value = "工单号")
    private String workOrderNumber;

    @ApiModelProperty(value = "工单号")
    private String sourceNumber;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "客户/供应商guid")
    private String customerOrSupplierGuid;

    @ApiModelProperty(value = "客户/供应商名称")
    private String customerOrSupplierName;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "源头订单数据id")
    private String orderDataGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "物料GUID(成品、物料)")
    private String materialGuid;

    @ApiModelProperty(value = "物料Obj")
    private ErpMaterialMgtMaterialVO materialObj;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "交货日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty(value = "PK")
    private String advancePaymentDetailGuid;
}