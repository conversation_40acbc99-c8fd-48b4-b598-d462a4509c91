package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
* <p>
* 每日批次结转表RO
* </p>
*
* <AUTHOR>
* @since 2024-11-25
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtBatchBalanceDayRO对象", description = "每日批次结转表")
public class ErpInventoryMgtBatchBalanceDayRO extends BaseEntity {

        @ApiModelProperty(value = "pk")
    private String batchBalanceDayGuid;

        @ApiModelProperty(value = "结转guid")
    private String balanceGuid;

        @ApiModelProperty(value = "物批次guid")
    private String materialBatchGuid;

        @NotNull(message = "结转数量不能为空")
        @ApiModelProperty(value = "结转数量")
    private BigDecimal inventoryBalanceQuantity;

        @NotNull(message = "结转金额不能为空")
        @ApiModelProperty(value = "结转金额")
    private BigDecimal inventoryBalanceTotalamount;

        @ApiModelProperty(value = "备注或描述")
    private String description;

        @ApiModelProperty(value = "入库数量")
    private BigDecimal stockInQuantity;

        @ApiModelProperty(value = "入库金额")
    private BigDecimal stockInTotalAmount;

        @ApiModelProperty(value = "出库数量")
    private BigDecimal stockOutQuantity;

        @ApiModelProperty(value = "出库金额")
    private BigDecimal stockOutTotalAmount;

        @ApiModelProperty(value = "结转金额含税")
    private BigDecimal inventoryBalanceTaxIncludedTotalamount;

        @ApiModelProperty(value = "入库金额含税")
    private BigDecimal stockInTotalTaxIncludedAmount;

        @ApiModelProperty(value = "出库金额含税")
    private BigDecimal stockOutTotalTaxIncludedAmount;

}
