package xy.server.inventory.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.inventory.entity.model.ro.ErpMaterialPackageMaterialDetailRO;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 物料批次货明细RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpMaterialPackageCodeGoodsVO对象", description = "物料批次货明细")
public class ErpMaterialPackageCodeGoodsVO {
    @ApiModelProperty(value = "物料批次guid")
    private String materialBatchGuid;

    @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "规格类型")
    private String materialSpecifications;

    @ApiModelProperty(value = "包数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "第几包")
    private Integer serialNumber;

    @ApiModelProperty(value = "单位")
    private String unitName;

    @ApiModelProperty(value = "物料分类名称")
    private String materialClassificationName;

    @ApiModelProperty(value = "标签包id")
    private String materialBatchGoodsGuid;

    @ApiModelProperty(value = "批次有效期")
    private LocalDateTime materialPeriodOfValidity;

    /**
     * 物料批次号码(自动生成)，根据批次库存规则定义来生成。如果规则条件相同，批次号码会生成相同。
     */
    @ApiModelProperty(value = "物料批次号码")
    private String materialBatchNumber;

    @ApiModelProperty(value = "生产批次号")
    private String materialProductionBatchNumber;

    @ApiModelProperty(value = "批次生产日期")
    private LocalDateTime materialDateOfManufacture;

    @ApiModelProperty(value = "位置_guid（该批次放到那个仓）")
    private String warehouseSpaceGuid;

    @ApiModelProperty(value = "位置_guid（该批次放到那个仓）")
    private String warehouseSpaceName;

    @ApiModelProperty(value = "来源id")
    private String sourceGuid;

    @ApiModelProperty(value = "来源单号")
    private String sourceWorkorderNumber;


    @ApiModelProperty(value = "物料批次货明细")
    private ErpMaterialPackageMaterialDetailRO detailRO;

    @ApiModelProperty(value = "包装主id")
    private String workorderGuid;

    @ApiModelProperty(value = "包装类型")
    private String workorderTypeGuid;



    @ApiModelProperty(value = "包装来源工单主表guid")
    private String workorderParentGuid;


    @ApiModelProperty(value = "订单明细")
    private String orderDataGuid;

    @ApiModelProperty(value = "物料到货或者成品入库通知guid")
    private String sourceParentGuid;
    @ApiModelProperty(value = "包装剩余数量")
    private BigDecimal remainingQuantity;

    @ApiModelProperty(value = "入库数")
    private BigDecimal inQuantity;

    @ApiModelProperty(value = "物料Obj")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "本币单价(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyUnitPrice;

    @ApiModelProperty(value = "结算单价（默认与含税单价相等），写入库存价格字段")
    private BigDecimal settlementUnitPrice;
}
