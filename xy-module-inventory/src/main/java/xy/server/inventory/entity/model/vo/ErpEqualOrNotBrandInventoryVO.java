package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @data 2023/11/7 9:21
 * @apiNote 到货单-对账单待开列表明细QO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpEqualOrNotBrandInventoryVO对象")
public class ErpEqualOrNotBrandInventoryVO {

    @ApiModelProperty(value = "同品牌物料库存")
    private BigDecimal equalBrandInventory;

    @ApiModelProperty(value = "不同品牌物料库存")
    private BigDecimal notBrandInventory;

    @ApiModelProperty(value = "库存数")
    private BigDecimal inventoryQuantity;

    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "搜索码")
    private String searchCode;

    @ApiModelProperty(value = "幅宽")
    private BigDecimal widthOfFabric;

}
