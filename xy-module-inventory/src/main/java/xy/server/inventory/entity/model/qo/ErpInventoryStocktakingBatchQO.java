package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 库存盘点批次表QO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryStocktakingBatchQO", description = "库单明细管理")
public class ErpInventoryStocktakingBatchQO {

    @ApiModelProperty(value = "库单guid")
    @NotNull(message = "库单guid不能为空")
    List<String> inventoryReceiptGuid;

    @ApiModelProperty(value = "客户名称/客户编码")
    private String customerShortName;
}
