package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xunyue.config.mybatisplus.BaseEntity;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 发货通知\到货\退货数据表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpInventoryReturnDataVO对象", description = "物料到货管理")
public class ErpInventoryReturnDataVO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String noticeArrivalReturnDataGuid;

    @ApiModelProperty(value = "工单表_guid")
    private String workorderGuid;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "工单类型_guid")
    private String workorderTypeGuid;

    @XyTrans(dictionaryKey = "FINISHED_GOODS_ENTRY_NOTICE_TYPE", dictionaryValue = "workorderTypeGuid")
    @ApiModelProperty(value = "成品入库通知类型名称")
    private String workorderTypeName;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @ApiModelProperty(value = "工单相关数据")
    @XyTransCycle
    private ErpInventoryProductionMgtWorkorderInventoryVO workorderInventoryVO;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)")
    private Integer decimalMethod;

    @ApiModelProperty(value = "结算单价保留小数位(默认9位，可手动变更)")
    private Integer settlementUnitPriceKeepDecimalPlace;

    @ApiModelProperty(value = "结算金额保留小数位(默认2位，可手动变更)")
    private Integer settlementTotalAmountKeepDecimalPlace;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @XyTrans(dictionaryKey = "MATERIAL_RECEIPT_SOURCE_TYPE",dictionaryValue = "sourceValue")
    @ApiModelProperty(value = "来源值")
    private String sourceValueName;

    @NotEmpty(message = "到货明细")
    @ApiModelProperty(value = "到货明细")
    @XyTransCycle
    private List<ErpInventoryMgtNoticeArrivalReturnDataVO> arrivalDetailsList;

    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "退货数量")
    private BigDecimal returnQuantity;

    @ApiModelProperty(value = "待退货数量")
    private BigDecimal notReturnQuantity;

    @ApiModelProperty(value = "处理方式(字典：物料入库/成品入库/半成品入库等)")
    private String handlingMethod;

    private Integer workorderProperties;

    private String workorderNumber;

    @ApiModelProperty(value = "到货供应商/客户")
    private String customerGuid;

    @ApiModelProperty(value = "到货供应商/客户")
    private String customerOrSupplierName;

    /**
     * 结算供应商/客户
     */
    @ApiModelProperty(value = "结算供应商guid")
    private String settlementCustomerOrSupplierGuid;
    /**
     * 结算供应商/客户
     */
    @ApiModelProperty(value = "结算供应商name")
    private String settlementCustomerOrSupplierName;

    @ApiModelProperty(value = "税率guid")
    private String invoiceTypeTaxRateGuid;

    @ApiModelProperty(value = "税率名称")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "汇率guid")
    private String currencyExchangeRateGuid;

    @ApiModelProperty(value = "币种guid")
    private String currencyGuid;

    @ApiModelProperty(value = "汇率名称")
    private String exchangeRate;

    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    @ApiModelProperty(value = "是否本币")
    private Boolean isLocalCurrency;

    @ApiModelProperty(value = "来源生产单号")
    private String sourceProductOrderNumber;

    @ApiModelProperty(value = "对账情况")
    private String statementSituation;

    @XyTrans(dictionaryKey = "RECONCILIATION",dictionaryValue = "statementSituation")
    @ApiModelProperty(value = "对账情况名称")
    private String statementSituationName;

    @JsonIgnore
    private String orderDataGuid;

}
