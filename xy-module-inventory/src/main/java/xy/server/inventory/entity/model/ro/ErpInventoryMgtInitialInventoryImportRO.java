package xy.server.inventory.entity.model.ro;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xunyue.config.excel.annotation.XyExcelTemplate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @data 2024-07-24 9:29
 * @apiNote 期初库存导入RO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
@XyExcelTemplate("期初库存导入模板")
@ApiModel(value = "ErpInventoryMgtInitialInventoryImportRO", description = "期初库存导入RO")
public class ErpInventoryMgtInitialInventoryImportRO {

    @ApiModelProperty("物料分类guid")
    private String materialGuid;

    @ApiModelProperty("物料分类guid")
    private String materialClassificationGuid;

    @NotBlank
    @ExcelProperty("物料/产品编码")
    private String materialCode;

    @ExcelProperty("物料/产品名称")
    private String materialName;

    @ExcelProperty("规格类型")
    private String specificationTypeStr;

    @NotNull
    @ExcelProperty("数量")
    private BigDecimal quantity;

    @NotNull
    @ExcelProperty("不含税报价单价")
    private BigDecimal quotationUnitPriceWithoutTax;

    @NotNull
    @ExcelProperty("不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ExcelProperty("批次号（格式：客户名称+客户PO）")
    private String materialProductionBatchNumber;
}
