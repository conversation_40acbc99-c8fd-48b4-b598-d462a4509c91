package xy.server.inventory.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * 库单明细管理RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpProdutionReturnWaitingTreeVO对象")
public class ErpProdutionReturnWaitingTreeVO {

    @ApiModelProperty(value = "来源单号")
    private String sourceWorkorderNumber;

    @ApiModelProperty(value = "工单guid")
    private String workorderGuid;

    @XyTransCycle
    private List<ErpProdutionReturnWaitingVO> list;

}
