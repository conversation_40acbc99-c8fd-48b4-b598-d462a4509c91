package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 库存结转表写入后台表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryBalanceVO对象", description = "库存结转表写入后台表")
public class ErpInventoryMgtInventoryBalanceVO {

    @ApiModelProperty(value = "pk")
    private String inventoryBalanceGuid;

    @ApiModelProperty(value = "结转guid")
    private String balanceGuid;

    @ApiModelProperty(value = "物guid")
    private String materialGuid;

    @ApiModelProperty(value = "结转数量")
    private BigDecimal inventoryBalanceQuantity;

    @ApiModelProperty(value = "结转金额")
    private BigDecimal inventoryBalanceTotalamount;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "入库数量")
    private BigDecimal stockInQuantity;

    @ApiModelProperty(value = "入库金额")
    private BigDecimal stockInTotalAmount;

    @ApiModelProperty(value = "出库数量")
    private BigDecimal stockOutQuantity;

    @ApiModelProperty(value = "出库金额")
    private BigDecimal stockOutTotalAmount;

}
