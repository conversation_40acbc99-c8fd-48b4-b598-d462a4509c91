package xy.server.inventory.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBusinessMgtOrderURO对象", description = "订单表")
public class ErpBusinessMgtOrderInventoryRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String orderGuid;

    @NotNull(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "订单需求描述")
    private String orderDemandGuid;

    @NotNull(message = "单据日期(可变更的)不能为空")
    @ApiModelProperty(value = "单据日期(可变更的)")
    private LocalDateTime receiptDate;

    @NotNull(message = "订单类型不能为空")
    @ApiModelProperty(value = "订单类型")
    private String orderType;

    @NotNull(message = "总费用(含税)不能为空")
    @ApiModelProperty(value = "总费用(含税)")
    private BigDecimal totalExpensesIncludingTax;

    @NotNull(message = "总费用(不含税)不能为空")
    @ApiModelProperty(value = "总费用(不含税)")
    private BigDecimal totalExpensesWithoutTax;

    @NotNull(message = "本币总费用(根据币种，外币才显示)，外币不含税不能为空")
    @ApiModelProperty(value = "本币总费用(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalExpenses;

    @NotNull(message = "结算费用（默认与含税总金额相等)不能为空")
    @ApiModelProperty(value = "结算费用（默认与含税总金额相等)")
    private BigDecimal settlementTotalExpenses;

    @NotNull(message = "总体积不能为空")
    @ApiModelProperty(value = "总体积")
    private BigDecimal totalVolume;

    @NotNull(message = "总重量不能为空")
    @ApiModelProperty(value = "总重量")
    private BigDecimal totalWeight;

    @NotNull(message = "保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)不能为空")
    @ApiModelProperty(value = "保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)")
    private Integer decimalMethod;

    @NotNull(message = "结算单价保留小数位(默认9位，可手动变更)不能为空")
    @ApiModelProperty(value = "结算单价保留小数位(默认9位，可手动变更)")
    private Integer settlementUnitPriceKeepDecimalPlace;

    @NotNull(message = "结算金额保留小数位(默认2位，可手动变更)不能为空")
    @ApiModelProperty(value = "结算金额保留小数位(默认2位，可手动变更)")
    private Integer settlementTotalAmountKeepDecimalPlace;

    @NotNull(message = "订单属性(1业务合同，2采购订单，3外发订单)不能为空")
    @ApiModelProperty(value = "订单属性(1业务合同，2采购订单，3外发订单)")
    private Integer orderProperties;

    @ApiModelProperty(value = "税率guid")
    private String invoiceTypeTaxRateGuid;

    @ApiModelProperty(value = "汇率guid")
    private String currencyExchangeRateGuid;

    @NotNull(message = "是否加急不能为空")
    @ApiModelProperty(value = "是否加急")
    private Boolean isUrgent;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "打印次数不能为空")
    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @ApiModelProperty(value = "客户/供应商guid")
    private String customerOrSupplierGuid;

    @ApiModelProperty(value = "结算客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String settlementCustomerOrSupplierGuid;

    @ApiModelProperty(value = "送货客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String deliveryCustomerOrSupplierGuid;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "送货类型 _guid")
    private String deliveryTypeGuid;

    @ApiModelProperty(value = "结算类型 SettlementType_guid")
    private String settlementTypeGuid;

    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @ApiModelProperty(value = "手机")
    private String mobilephone;


}
