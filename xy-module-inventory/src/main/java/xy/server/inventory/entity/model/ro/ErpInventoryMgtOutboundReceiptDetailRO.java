package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 库单明细管理RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtOutboundReceiptDetailURO对象", description = "库单明细管理")
public class ErpInventoryMgtOutboundReceiptDetailRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptDetailGuid;

    @ApiModelProperty(value = "库单_guid")
    private String inventoryReceiptGuid;

    @ApiModelProperty(value = "库位guid")
    private String warehouseSpaceGuid;

    @ApiModelProperty(value = "物分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

    @NotNull
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "库存数量(当单据触发时，记录当前库存数量，库存数量流水)")
    private BigDecimal inventoryQuantity;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源类型值")
    private Integer sourceValue;

    @ApiModelProperty(value = "金额(不含税)，汇总批次的总价值，不用理会单价。原则：总价值不会变化")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "明细订单数据Obj")
    private ErpInventoryMgtInventoryReceiptDetailOrderDataRO detailOrderDataObj;

    @ApiModelProperty(value = "出库批次明细列表")
    @Valid
    @NotEmpty
    private List<ErpInventoryMgtOutboundReceiptBatchDetailRO> batchDetailList;

    @ApiModelProperty(value = "出库批次明细删除ids")
    private List<String> delBatchDetailIds;

    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String inventoryReceiptState;

    @ApiModelProperty(value = "订单数据guid")
    private String orderDataGuid;

}
