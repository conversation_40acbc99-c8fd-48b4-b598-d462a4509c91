package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.basic.entity.model.vo.ErpBusinessMgtOtherExpensesVO;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2023/11/7 10:05
 * @apiNote 销货单-对账单待开列表数据VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "FinishedInvoicingDataVo", description = "到货单-对账单待开列表数据VO")
public class FinishedInvoicingDataVo {

    @ApiModelProperty(value = "工单表_guid")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "单据日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "物_guid")
    private String materialGuid;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "待对账数量")
    private BigDecimal quantityToBeReconciled;

    @ApiModelProperty(value = "保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)")
    private Integer decimalMethod;

    @ApiModelProperty(value = "结算单价保留小数位(默认9位，可手动变更)")
    private Integer settlementUnitPriceKeepDecimalPlace;

    @ApiModelProperty(value = "结算金额保留小数位(默认2位，可手动变更)")
    private Integer settlementTotalAmountKeepDecimalPlace;

    @ApiModelProperty(value = "订单数据表guid")
    private String orderDataGuid;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;



    @ApiModelProperty(value = "客户料号")
    private String externalMaterialCode;

    @ApiModelProperty(value = "客户PO")
    private String externalTrackingNumber;

    @ApiModelProperty(value = "客户产品名称")
    private String externalMaterialName;

    @ApiModelProperty(value = "销货明细备用字段")
    private Object toJson;

    @ApiModelProperty(value = "客户或供应商guid")
    private String customerOrSupplierGuid;

    @ApiModelProperty(value = "客户名称")
    private String customerShortName;

    @ApiModelProperty(value = "货期")
    private LocalDateTime deliveryDate;

    @ApiModelProperty(value = "物料Obj")
    private ErpMaterialMgtMaterialVO materialObj;


    @ApiModelProperty(value = "其他费用")
    private List<ErpBusinessMgtOtherExpensesVO> otherExpensesList;

    @ApiModelProperty(value = "已删除文件")
    private List<String> isdelectFile;

    @ApiModelProperty(value = "来源值")
    private String sourceValue;

    @ApiModelProperty("含税单价")
    private BigDecimal unitPriceIncludingTax;

    @ApiModelProperty("含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty("不含税报价单价")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty("不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty("含税报价单价")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty("不含税报价单价")
    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty("结算单价")
    private BigDecimal settlementUnitPrice;

    @ApiModelProperty("结算总金额")
    private BigDecimal settlementTotalAmount;

    @ApiModelProperty("本币单价")
    private BigDecimal localCurrencyUnitPrice;

    @ApiModelProperty("本币总金额")
    private BigDecimal localCurrencyTotalAmount;
}
