package xy.server.inventory.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>
 * 物料批次库存表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Builder
@ApiModel(value = "ErpInventoryMgtMaterialBatchInventoryURO对象", description = "物料批次库存表")
public class ErpInventoryMgtMaterialBatchInventoryRO {

    @NotBlank
    @ApiModelProperty(value = "物料批次guid")
    private String materialBatchGuid;

    @NotNull
    @ApiModelProperty(value = "批次库存数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "库存总金额(净价，不包含任何税率，汇率等)")
    private BigDecimal batchInventoryTotalamount;

}
