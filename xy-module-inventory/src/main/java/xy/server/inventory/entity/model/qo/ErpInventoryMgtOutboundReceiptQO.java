package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 出出库单管理QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtOutboundReceiptQO对象", description = "出库单管理")
public class ErpInventoryMgtOutboundReceiptQO {

    @ApiModelProperty(value = "操作类型_guids")
    private List<Integer> operationTypeGuids;

    @ApiModelProperty(value = "库单属性")
    private Integer inventoryReceiptProperties;

    @ApiModelProperty(value = "库单单号")
    private String inventoryReceiptNumbers;

    @ApiModelProperty(value = "对账月份")
    private String reconciliationMonth;

    @ApiModelProperty(value = "工单属性")
    private Integer workorderProperties;

    @ApiModelProperty(value = "工单类型")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "开始日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "审核状态")
    private List<String> auditStatus;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "来源单号")
    private String sourceWorkorderNumber;

    @ApiModelProperty(value = "成品/半成品")
    private Integer materialType;

    @ApiModelProperty(value = "客户简称")
    private String customerShortName;
    @ApiModelProperty(value = "客户guid")
    private String customerGuid;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "客户编码/简称/全称")
    private String customer;

    @ApiModelProperty(value = "客户po")
    private String externalTrackingNumber;

    @ApiModelProperty(value = "来源单号/出库通知单号")
    private String sourceNumber;

    @ApiModelProperty(value = "产品编码/名称")
    private String production;

    @ApiModelProperty(value = "数据来源/来源类型")
    private List<String> sourceDictValues;

    @ApiModelProperty(value = "出仓情况")
    private List<String> outWarehouseStates;

    @ApiModelProperty(value = "动态路由菜单")
    private String dynamicComponent;

    @ApiModelProperty(value = "2级来源单号/对应出库通知的来源单号")
    private String sourceTwoLevelNumber;

    @ApiModelProperty(value = "来源值-字典值：FINISH_SALES_SOURCE")
    private String sourceValue;

    @ApiModelProperty("产品（编码、名称）")
    private String productMaterial;

    @ApiModelProperty(value = "业务订单号")
    private String businessOrderNumber;

    @ApiModelProperty(value = "生产工单号")
    private String productWorkorderNumber;

//    @ApiModelProperty(value = "生产工单号")
    private String productionOrderNumberStr;


    @ApiModelProperty(value = "订单号")
    private String sonWorkorderNumber;

    @ApiModelProperty(value = "产品编码/产品名称")
    private String externalMaterial;

    @ApiModelProperty("销货单号")
    private String salesWorkorderNumber;

    @ApiModelProperty("是否查询异常数据")
    private Boolean exceptionFind = false;

    @ApiModelProperty("外部物料编码")
    private String externalMaterialCode;

    @ApiModelProperty("外部物料名称")
    private String externalMaterialName;
}
