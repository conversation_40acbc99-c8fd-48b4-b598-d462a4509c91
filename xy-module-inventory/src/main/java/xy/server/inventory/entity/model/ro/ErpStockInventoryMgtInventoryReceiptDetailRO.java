package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 库单明细管理RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpStockInventoryMgtInventoryReceiptDetailRO对象", description = "库单明细管理")
public class ErpStockInventoryMgtInventoryReceiptDetailRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptDetailGuid;

    @ApiModelProperty
    private String orderDataGuid;

    @ApiModelProperty(value = "库单_guid")
    private String inventoryReceiptGuid;

    @ApiModelProperty(value = "库位guid")
    private String warehouseSpaceGuid;
    /**
     * 通过物料id查询物料类型
     */
    /*@ApiModelProperty(value = "物分类guid")
    private String materialClassificationGuid;*/

    @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

    @NotNull(message = "数量不能为空")
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "来源数量")
    private BigDecimal sourceQuantity;
    /**
     * 根据当前仓库下物料的总库存数
     */
   /* @NotNull(message = "库存数量(当单据触发时，记录当前库存数量，库存数量流水)不能为空")
    @ApiModelProperty(value = "库存数量(当单据触发时，记录当前库存数量，库存数量流水)")
    private BigDecimal inventoryQuantity;*/

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "父来源guid")
    private String sourceParentArrivalGuid;

    @ApiModelProperty(value = "来源类型值")
    private Integer sourceValue;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "是否生产控制数据")
    private Boolean isproductionControlData=false;

    @ApiModelProperty(value = "计费方案id")
    private String costSchemeGuid;

    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String inventoryReceiptState;

    @ApiModelProperty(value = "入库明细扩展数据")
    private ErpStockInventoryMgtInventoryReceiptDetailInoutstockRO receiptDetailInoutstockRO;

    @ApiModelProperty(value = "库单批次明细管理")
    private List<ErpInventoryMaterialMgtMaterialBatchRO> materialBatchROS;


    /**
     * 顺序号(每一个层级有对应的顺序号)
     */
    @TableField("serial_number")
    private Integer serialNumber;

}