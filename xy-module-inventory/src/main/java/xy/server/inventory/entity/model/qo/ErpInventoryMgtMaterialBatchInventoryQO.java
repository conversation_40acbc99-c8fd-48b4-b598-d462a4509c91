package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.inventory.entity.model.vo.ErpInventoryMgtMaterialBatchInventoryVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 物料批次库存表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtMaterialBatchInventoryQO对象", description = "物料批次库存表")
public class ErpInventoryMgtMaterialBatchInventoryQO {

    @ApiModelProperty(value = "物分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料 _guid")
    private String materialGuid;

    @ApiModelProperty(value = "物料 _guids")
    private List<String> materialGuids;

    @ApiModelProperty(value = "物料批次号码")
    private String materialBatchNumber;

    @ApiModelProperty(value = "生成批次号")
    private String materialProductionBatchNumber;

    @ApiModelProperty(value = "批次有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate materialPeriodOfValidity;

    @ApiModelProperty(value = "是否忽略")
    private Boolean isIgnore;

    @ApiModelProperty(value = "筛选条件，1批次有效期，2批次生产日期")
    private String filteringCriteria;

    @ApiModelProperty(value = "总数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "已选中批次库存列表")
    private List<ErpInventoryMgtMaterialBatchInventoryVO> selectedBatchInventoryList;

}
