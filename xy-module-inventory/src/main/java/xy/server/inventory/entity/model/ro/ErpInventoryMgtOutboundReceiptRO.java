package xy.server.inventory.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 出库单管理RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtOutboundReceiptURO对象", description = "出库单管理")
public class ErpInventoryMgtOutboundReceiptRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptGuid;

    @NotNull(message = "出库类型不能为空")
    @ApiModelProperty(value = "出库类型")
    private Integer operationTypeGuid;

    @NotNull(message = "库单属性不能为空")
    @ApiModelProperty(value = "库单属性")
    private Integer inventoryReceiptProperties;

    @ApiModelProperty(value = "库单单号")
    private String inventoryReceiptNumbers;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源类型值")
    private Integer sourceValue;

    @NotNull(message = "单据日期不能为空")
    @ApiModelProperty(value = "单据日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    /**
     * 关联表数据
     */

    @ApiModelProperty(value = "出库明细列表")
    @Valid
    @NotEmpty
    private List<ErpInventoryMgtOutboundReceiptDetailRO> detailList;

    @ApiModelProperty(value = "出库明细删除ids")
    private List<String> delDetailIds;

    @ApiModelProperty(value = "是否辅料")
    private Boolean isItMaterial;

}
