package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2024-07-24 9:29
 * @apiNote 期初库存导入VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpInventoryMgtInitialInventoryImportVO", description = "期初库存导入VO")
public class ErpInventoryMgtInitialInventoryImportVO {

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptDetailGuid;

    @ApiModelProperty(value = "库单_guid")
    private String inventoryReceiptGuid;

    @ApiModelProperty("物料分类guid")
    private String materialGuid;

    @ApiModelProperty("物料分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty("物料分类name")
    private String materialClassificationName;

    @ApiModelProperty("物料/产品编码")
    private String materialCode;

    @ApiModelProperty("物料/产品名称")
    private String materialName;

    @ApiModelProperty("规格类型")
    private String specificationTypeStr;

    @ApiModelProperty("单位名称")
    private String unitName;

    @ApiModelProperty("数量")
    private BigDecimal quantity;

    @ApiModelProperty("含税报价单价")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty("含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty("不含税报价单价")
    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty("不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty("批次明细列表")
    private List<ErpInventoryMaterialMgtMaterialBatchVO> materialBatchList;
}
