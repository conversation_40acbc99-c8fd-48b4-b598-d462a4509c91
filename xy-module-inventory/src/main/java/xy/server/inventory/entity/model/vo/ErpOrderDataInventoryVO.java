package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 订单表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpOrderDataVO对象", description = "订单表")
public class ErpOrderDataInventoryVO {

    @ApiModelProperty(value = "订单数据id")
    private String orderDataGuid;

    @ApiModelProperty(value = "完成数量(后面流程将其更新)")
    private BigDecimal completionQuantity;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

}