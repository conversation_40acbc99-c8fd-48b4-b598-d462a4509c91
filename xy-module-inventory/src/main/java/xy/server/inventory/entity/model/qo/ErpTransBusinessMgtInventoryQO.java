package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 订单表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpTransBusinessMgtOrderQO对象", description = "订单表")
public class ErpTransBusinessMgtInventoryQO {

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "开单开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "产品名称")
    private String  materialName;

    @ApiModelProperty(value = "客户/供应商名称")
    private String  customerOrSupplierName;

    @ApiModelProperty(value = "订单属性(1业务合同，2采购订单，3外发订单)")
    private Integer orderProperties;

    @ApiModelProperty(value = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "库单单号")
    private String inventoryReceiptNumbers;

    @ApiModelProperty(value = "外部单号(例客户PO，采购送货单号)")
    private String externalTrackingNumber;

    @ApiModelProperty(value = "是装舱单还是物流运输单")
    private String sourceType;
}