package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/19
 * @description
 **/
@Data
public class MaterialRequisitionByWorkorderNumbersQO {


    @ApiModelProperty("物料类型")
    private String materialType;

    @ApiModelProperty("等于的物料分类guid")
    private String eqMaterialClassificationGuid;

    @ApiModelProperty("不等于的物料分类guid")
    private String notEqMaterialClassificationGuid;

    @ApiModelProperty("工单号workorderNumber ， 成品销货统计表传入")
    private List<String> workorderNumbers;

}
