package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
        /**
* <p>
* 批次结转表写入后台表QO
* </p>
*
* <AUTHOR>
* @since 2024-08-26
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtBatchBalanceQO对象", description = "批次结转表写入后台表")
public class ErpInventoryMgtBatchBalanceQO{

            @ApiModelProperty(value = "pk")
    private String batchBalanceGuid;

            @ApiModelProperty(value = "结转guid")
    private String balanceGuid;

            @ApiModelProperty(value = "物批次guid")
    private String materialBatchGuid;

                @ApiModelProperty(value = "结转数量")
    private BigDecimal inventoryBalanceQuantity;

                @ApiModelProperty(value = "结转金额")
    private BigDecimal inventoryBalanceTotalamount;

            @ApiModelProperty(value = "备注或描述")
    private String description;

            @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

                @ApiModelProperty(value = "创建人")
    private String creator;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

            @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

                @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

            @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

            @ApiModelProperty(value = "入库数量")
    private BigDecimal stockInQuantity;

            @ApiModelProperty(value = "入库金额")
    private BigDecimal stockInTotalAmount;

            @ApiModelProperty(value = "出库数量")
    private BigDecimal stockOutQuantity;

            @ApiModelProperty(value = "出库金额")
    private BigDecimal stockOutTotalAmount;

}
