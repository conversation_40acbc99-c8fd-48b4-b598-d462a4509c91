package xy.server.inventory.entity.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 成品出库统计VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpFinishedProductOutboundStatisticsVO对象", description = "成品出库统计")
public class ErpFinishedProductOutboundStatisticsVO {

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptDetailGuid;

    @ApiModelProperty(value = "产品类型")
    private String materialClassificationName;

    @ApiModelProperty(value = "产品Guid")
    private String materialGuid;

    @ApiModelProperty(value = "产品编码")
    private String materialCode;

    @ApiModelProperty(value = "产品名称")
    private String  materialName;

    @ApiModelProperty(value = "规格类型")
    private String materialSpecification;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "出库数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "出库单号")
    private String inventoryReceiptNumbers;

    @ApiModelProperty(value = "出库日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "制单人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核日期")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "审核人")
    private String auditBy;

    @ApiModelProperty(value = "客户")
    private String customerName;

    @ApiModelProperty(value = "客户PO")
    private String externalTrackingNumber;

    @ApiModelProperty(value = "客户料号")
    private String externalMaterialCode;

    @ApiModelProperty(value = "客户产品名称")
    private String externalMaterialName;

    @ApiModelProperty(value = "出仓总数")
    private BigDecimal completionQuantity;

    @ApiModelProperty(value = "出仓状态")
    private String completionStatus;

    @ApiModelProperty(value = "物料出仓状态")
    @XyTrans(dictionaryKey = "OUT_WAREHOUSE_STATE", dictionaryValue = "completionStatus")
    private String outWarehouseStateDictValue;

    @ApiModelProperty(value = "异常处理原因")
    private String exceptionHandlingReason;

    @ApiModelProperty(value = "异常处理人")
    private String exceptionHandlerName;

    @ApiModelProperty(value = "异常处理日期")
    private String exceptionHandlingDate;

    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String inventoryReceiptState;

    @XyTrans(dictionaryKey = "DOCUMENT_STATUS",dictionaryValue = "inventoryReceiptState")
    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String inventoryReceiptStateName;

    @ApiModelProperty(value = "生成凭证")
    private boolean isGenerateProof;

    /**
     * 关联数据
     */
    @ApiModelProperty(value = "订单id")
    private String orderGuid;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(value = "来源单号")
    private String sourceWorkorderNumber;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "来源数量")
    private String sourceQuantity;


    @ApiModelProperty(value = "单价")
    private BigDecimal unitPriceWithoutTax;

    /**
     * 含税单价(默认隐藏，根据计费方案转换计算得出)
     */
    @TableField("unit_price_including_tax")
    private BigDecimal unitPriceIncludingTax;

    /**
     * 含税总金额
     */
    @TableField("total_amount_including_tax")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalAmountWithoutTax;



    @ApiModelProperty(value = "面积")
    private BigDecimal productArea;

    @ApiModelProperty(value = "生产工单号")
    private String productionOrderNumberStr;

    @ApiModelProperty(value = "材质")
    private String usageMaterialNameStr;


    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "产品工单guid")
    private String productWorkOrderGuid;



}
