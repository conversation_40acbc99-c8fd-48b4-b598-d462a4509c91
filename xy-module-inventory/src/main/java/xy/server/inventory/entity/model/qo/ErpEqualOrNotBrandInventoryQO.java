package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @data 2023/11/7 9:21
 * @apiNote 到货单-对账单待开列表明细QO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpEqualOrNotBrandInventoryQO对象")
public class ErpEqualOrNotBrandInventoryQO {

    @ApiModelProperty(value = "搜索码")
    private String searchCode;

    @ApiModelProperty(value = "幅宽")
    private BigDecimal widthOfFabric;

    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

}
