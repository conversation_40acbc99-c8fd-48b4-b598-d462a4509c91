package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>
 * 物料批次货明细RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMaterialMgtMaterialBatchGoodsDetailRO对象", description = "物料批次货明细")
public class ErpMaterialMgtMaterialBatchGoodsDetailRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String materialBatchGoodsDetailGuid;

    @ApiModelProperty(value = "货guid")
    private String materialBatchGoodsGuid;

    //@NotBlank
    @ApiModelProperty(value = "物料批次guid")
    private String materialBatchGuid;

    @NotBlank
    @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

    @ApiModelProperty(value = "每包数量")
    private BigDecimal quantityPerPackage;

    @NotNull(message = "包装数量不能为空")
    @ApiModelProperty(value = "包装数量")
    private BigDecimal packagingQuantity;

    @NotBlank
    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @NotNull
    @ApiModelProperty(value = "来源类型值")
    private Integer sourceValue;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

}
