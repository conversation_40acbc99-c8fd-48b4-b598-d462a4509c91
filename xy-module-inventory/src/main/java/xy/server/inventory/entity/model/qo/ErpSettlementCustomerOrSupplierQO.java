package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2023/11/7 9:21
 * @apiNote 到货单-对账单待开列表明细QO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpSettlementCustomerOrSupplierQO对象", description = "结算供应商/客户列表查询")
public class ErpSettlementCustomerOrSupplierQO {

    @NotNull(message = "工单属性不能为空")
    @ApiModelProperty(value = "工单属性")
    private List<String> workorderProperties;

    @ApiModelProperty(value = "是否显示数量为0的客户")
    private Boolean isShowZero = false;
}
