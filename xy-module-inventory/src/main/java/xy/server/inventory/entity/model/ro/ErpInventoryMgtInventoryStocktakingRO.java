package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>
 * 库存盘点表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryStocktakingURO对象", description = "库存盘点表")
public class ErpInventoryMgtInventoryStocktakingRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String inventoryStocktakingGuid;

    @ApiModelProperty(value = "库存guid")
    private String materialInventoryGuid;

    @ApiModelProperty(value = "库单guid")
    private String inventoryReceiptGuid;

    @ApiModelProperty(value = "物分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

    @NotNull(message = "库存数量不能为空")
    @ApiModelProperty(value = "库存数量")
    private BigDecimal inventoryQuantity;

    @NotNull(message = "盘点数量不能为空")
    @ApiModelProperty(value = "盘点数量")
    private BigDecimal stocktakingQuantity;

    @NotNull(message = "盘盈数量不能为空")
    @ApiModelProperty(value = "盘盈数量")
    private BigDecimal stocktakingSurplusQuantity;

    @NotNull(message = "盘亏数量不能为空")
    @ApiModelProperty(value = "盘亏数量")
    private BigDecimal stocktakingLossQuantity;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

}
