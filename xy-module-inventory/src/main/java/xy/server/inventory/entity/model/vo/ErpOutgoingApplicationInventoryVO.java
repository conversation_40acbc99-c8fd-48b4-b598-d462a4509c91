package xy.server.inventory.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.util.List;

/**
* <p>
* 工单表RO
* </p>
*
* <AUTHOR>
* @since 2023-09-15
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpOutgoingApplicationVO对象", description = "工单表")
public class ErpOutgoingApplicationInventoryVO {

    @ApiModelProperty(value = "工单表_guid")
    private String workorderGuid;

    @ApiModelProperty(value = "工序工单id")
    private String parentWorkOrderGuid;
    /**
     * 根据物料id查询获取到的印件名称，
     * 以及部件名称，
     * 部件名称是当前id对于的物料名称，印件名称是他的上级物料id获得的
     */
    @ApiModelProperty(value = "物料分类(成品)")
    private String materialGuid;

    @ApiModelProperty(value = "加工与损耗的比例")
    private BigDecimal proportion;

    /**
     * 收货数量
     */
    @ApiModelProperty(value = "产出数量=交货数量")
    private BigDecimal outputQuantity;

    @ApiModelProperty(value = "物料Obj")
    private ErpMaterialMgtMaterialVO materialObj;

    /**
     * 实际到货数
     */
    @ApiModelProperty(value = "实际到货数")
    private BigDecimal actQuantity;
    /**
     * 实际到货物料id
     */
    @ApiModelProperty(value = "实际到货物料id")
    private String actMaterialGuid;
    /**
     * 实际到货物料id
     */
    @ApiModelProperty(value = "实际到货物料id")
    private String actMaterialName;

    @ApiModelProperty(value = "入库状态(完成、故障、未开始)")
    private String workorderState;

    @ApiModelProperty(value = "处理方式(字典：物料入库/成品入库/半成品入库等)")
    private String handlingMethod;

    @XyTrans(dictionaryKey = "HANDLING_METHOD_UPON_ARRIVAL",dictionaryValue = "handlingMethod")
    @ApiModelProperty(value = "处理方式(字典：物料入库/成品入库/半成品入库等)")
    private String handlingMethodName;

    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty(value = "外发订单id")
    private String orderDataGuid;

    @ApiModelProperty(value = "到货批次")
    private List<ErpInventoryMaterialMgtMaterialBatchVO> materialBatchROS;

    @ApiModelProperty(value = "加工商id")
    private String supplierGuid;

    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "本币单价(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyUnitPrice;

    @ApiModelProperty(value = "本币金额(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalAmount;

    @ApiModelProperty(value = "结算单价（默认与含税单价相等），写入库存价格字段")
    private BigDecimal settlementUnitPrice;

    @ApiModelProperty(value = "结算金额（默认与含税总金额相等），写入库存价格字段")
    private BigDecimal settlementTotalAmount;
}
