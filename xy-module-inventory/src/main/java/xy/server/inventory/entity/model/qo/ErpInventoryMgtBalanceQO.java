package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <p>
 * 库存结转表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtBalanceQO对象", description = "库存结转表")
public class ErpInventoryMgtBalanceQO {

    @ApiModelProperty(value = "结转名称")
    private String balanceName;

    @ApiModelProperty(value = "结转属性(可用字典区分：1、物料，2、成品，3、半成品，4、模具)")
    private Integer balanceProperties;

    @ApiModelProperty(value = "结转类型(字典)(1全月加权，2移动加权)")
    private String balanceType;

    @ApiModelProperty(value = "结转状态")
    private Boolean balanceStatus;

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

}