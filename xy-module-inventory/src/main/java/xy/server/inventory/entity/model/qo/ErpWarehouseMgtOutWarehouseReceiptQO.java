package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 出仓单管理QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpWarehouseMgtOutWarehouseReceiptQO对象", description = "出仓单管理QO")
public class ErpWarehouseMgtOutWarehouseReceiptQO {

    @ApiModelProperty(value = "出仓类型")
    private List<String> warehouseReceiptType;

    @ApiModelProperty(value = "出仓单属性")
    private Integer warehouseReceiptProperties;

    @ApiModelProperty(value = "出仓单号")
    private String warehouseReceiptNumbers;

    @ApiModelProperty(value = "开始日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "审核状态")
    private List<String> auditStatus;

    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;

    @ApiModelProperty(value = "动态路由菜单")
    private String dynamicComponent;
}