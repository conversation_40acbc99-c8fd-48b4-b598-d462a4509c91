package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单数据表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBusinessMgtOrderDataURO对象", description = "订单数据表")
public class ErpBusinessMgtOrderDataInventoryRO extends BaseEntity {

    @NotNull(message = "顺序号不能为空")
    @ApiModelProperty(value = "顺序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "PK")
    private String orderDataGuid;

    @ApiModelProperty(value = "订单guid  一对多")
    private String orderGuid;

    @ApiModelProperty(value = "物料分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料GUID(成品、物料)")
    private String materialGuid;

    @ApiModelProperty(value = "单位guid")
    private String unitGuid;

    @ApiModelProperty(value = "FSC声明_guid")
    private String fscDeclarationGuid;

    @ApiModelProperty(value = "经验工序guid")
    private String experienceProductionProcessGuid;

    @NotNull(message = "数量不能为空")
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @NotNull(message = "赠品数量不能为空")
    @ApiModelProperty(value = "赠品数量")
    private BigDecimal giftQuantity;

    @NotNull(message = "备用数量不能为空")
    @ApiModelProperty(value = "备用数量")
    private BigDecimal spareQuantity;

    @NotNull(message = "使用库存数量不能为空")
    @ApiModelProperty(value = "使用库存数量")
    private BigDecimal usingInventoryQuantity;

    @NotNull(message = "总数量不能为空")
    @ApiModelProperty(value = "总数量")
    private BigDecimal totalQuantity;

    @NotNull(message = "含税报价单价(吨价，平方价，千平方英寸)不能为空")
    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @NotNull(message = "含税单价(默认隐藏，根据计费方案转换计算得出)不能为空")
    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

    @NotNull(message = "含税总金额不能为空")
    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @NotNull(message = "不含税报价单价(吨价，平方价，千平方英寸)不能为空")
    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @NotNull(message = "不含税单价(默认隐藏，根据计费方案转换计算得出)不能为空")
    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @NotNull(message = "不含税总金额不能为空")
    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @NotNull(message = "本币单价(根据币种，外币才显示)，外币不含税不能为空")
    @ApiModelProperty(value = "本币单价(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyUnitPrice;

    @NotNull(message = "本币金额(根据币种，外币才显示)，外币不含税不能为空")
    @ApiModelProperty(value = "本币金额(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalAmount;

    @NotNull(message = "结算单价（默认与含税单价相等），写入库存价格字段不能为空")
    @ApiModelProperty(value = "结算单价（默认与含税单价相等），写入库存价格字段")
    private BigDecimal settlementUnitPrice;

    @NotNull(message = "结算金额（默认与含税总金额相等），写入库存价格字段不能为空")
    @ApiModelProperty(value = "结算金额（默认与含税总金额相等），写入库存价格字段")
    private BigDecimal settlementTotalAmount;

    @ApiModelProperty(value = "外部单号(例客户PO，采购送货单号)")
    private String externalTrackingNumber;

    @ApiModelProperty(value = "外部物料条码(BarCode)")
    private String externalMaterialBarCode;

    @ApiModelProperty(value = "外部物料编码(例客户产品编码)")
    private String externalMaterialCode;

    @ApiModelProperty(value = "外部物料名称(例客户产品名称)")
    private String externalMaterialName;

    @ApiModelProperty(value = "货期")
    private LocalDateTime deliveryDate;

    @ApiModelProperty(value = "送货类型")
    private String deliveryTypeGuid;

    @NotNull(message = "完成状态(0未完成，1部分完成，2已完成，3终止)不能为空")
    @ApiModelProperty(value = "完成状态(0未完成，1部分完成，2已完成，3终止)")
    private String completionStatus;

    @NotNull(message = "完成数量(后面流程将其更新)不能为空")
    @ApiModelProperty(value = "完成数量(后面流程将其更新)")
    private BigDecimal completionQuantity;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

}
