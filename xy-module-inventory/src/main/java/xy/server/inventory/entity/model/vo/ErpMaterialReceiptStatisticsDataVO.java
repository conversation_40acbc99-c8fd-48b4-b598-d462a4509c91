package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.time.LocalDateTime;

@Data
public class ErpMaterialReceiptStatisticsDataVO {

    @ApiModelProperty(value = "物料Obj")
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "入仓数量")
    private Integer inventoryQuantity;

    @ApiModelProperty(value = "入仓单号")
    private String warehouseReceiptNumbers;

    @ApiModelProperty(value = "入仓日期")
    private LocalDateTime receiptDate;
}
