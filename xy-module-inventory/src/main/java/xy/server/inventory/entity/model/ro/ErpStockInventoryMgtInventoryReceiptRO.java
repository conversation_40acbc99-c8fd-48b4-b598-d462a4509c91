package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 库单管理RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpStockInventoryMgtInventoryReceiptRO对象", description = "库单管理")
public class ErpStockInventoryMgtInventoryReceiptRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptGuid;

    @NotNull(message = "操作类型_guid不能为空")
    @ApiModelProperty(value = "操作类型_guid,(用于入库类型)")
    private Integer operationTypeGuid;

    @ApiModelProperty(value = "库单单号")
    private String inventoryReceiptNumbers;

//    @NotNull(message = "来源类型值不能为空")
    @ApiModelProperty(value = "来源类型值")
    private Integer sourceValue;

    @ApiModelProperty(value = "来源id")
    private String sourceGuid;

    @NotNull(message = "单据日期不能为空")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @NotNull(message = "库单属性不能为空")
    @ApiModelProperty(value = "库单属性")
    private Integer inventoryReceiptProperties=-1;

    @ApiModelProperty(value = "入库明细")
    private List<ErpStockInventoryMgtInventoryReceiptDetailRO> inventoryReceiptDetailList;

    @ApiModelProperty(value = "入库单扩展字段")
    private ErpStockInventoryMgtInventoryReceiptInoutstockRO inventoryReceiptInoutstockRO;

    @ApiModelProperty(value = "是否辅料")
    private Boolean isItMaterial;

}
