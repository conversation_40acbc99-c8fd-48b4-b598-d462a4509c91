package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
        /**
* <p>
* 库单数据异常处理表QO
* </p>
*
* <AUTHOR>
* @since 2024-06-06
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryReceiptBatchDetailExceptionQO对象", description = "库单数据异常处理表")
public class ErpInventoryMgtInventoryReceiptBatchDetailExceptionQO{

            @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

            @ApiModelProperty(value = "PK")
    private String inventoryDetaExceptionGuid;

            @ApiModelProperty(value = "订单数据guid")
    private String inventoryReceiptBatchDetail;

            @ApiModelProperty(value = "异常原因")
    private String exceptionReason;

            @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

            @ApiModelProperty(value = "创建人")
    private String creator;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

            @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

            @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

}
