package xy.server.inventory.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.inventory.entity.model.vo.ErpScrapOutboundApplicationVO;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-09-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpProductionMgtWorkorderRO对象", description = "报废出库申请单RO")
public class ErpScrapOutboundApplicationRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "工单类型_guid")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "工单状态(完成、故障、未开始)")
    private String workorderState;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "工单属性(1工单，2工序工单，3采购工单，4外发工单，5车间工单，6作业工单，维修工单，保养工单)")
    private Integer workorderProperties;

    @ApiModelProperty(value = "客户/供应商guid")
    private String customerGuid;

    @ApiModelProperty(value = "物料分类(成品)")
    private String materialGuid;

    @ApiModelProperty(value = "数量(根据工单属性:1工单成品数，2工序投入数量，3采购申请数量，4外发申请数)")
    private BigDecimal quantity;

    @ApiModelProperty(value = "使用库存数量")
    private BigDecimal usingInventoryQuantity;

    @ApiModelProperty(value = "总数量")
    private BigDecimal totalQuantity;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "要求交货期")
    private LocalDateTime requiredDeliveryTime;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String currentStatus;

    @ApiModelProperty(value = "完成数量(后面流程将其更新)")
    private BigDecimal completionQuantity;

    @ApiModelProperty(value = "申请原因")
    private String reason;

    @NotEmpty
    @ApiModelProperty(value = "物料出库申请单明细")
    private List<ErpScrapOutboundApplicationVO> detailList;
}
