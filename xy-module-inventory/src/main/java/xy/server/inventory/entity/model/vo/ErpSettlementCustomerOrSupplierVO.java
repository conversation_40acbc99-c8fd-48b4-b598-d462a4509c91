package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @data 2023/10/7
 * @apiNote 成品销货
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpSettlementCustomerOrSupplierVO对象", description = "")
public class ErpSettlementCustomerOrSupplierVO{

    @ApiModelProperty(value = "结算客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String customerOrSupplierGuid;

    /**
     * 结算供应商/客户
     */
    @ApiModelProperty(value = "结算供应商/客户name")
    private String customerOrSupplierGuidName;

}
