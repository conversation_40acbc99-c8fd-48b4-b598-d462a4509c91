package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2023/11/7 10:05
 * @apiNote 销货单-对账单待开列表VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "finishedInvoicingVO", description = "到货单-对账单待开列表VO")
public class FinishedInvoicingVO {
    @ApiModelProperty(value = "客户名称")
    private String customerShortName;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "工单表_guid")
    private String workorderGuid;

    @ApiModelProperty(value = "单据日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptDate;


    @ApiModelProperty(value = "明细列表")
    private List<FinishedInvoicingDataVo> detailList;

}
