package xy.server.inventory.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 包装单管理RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpProductionMgtPackageWorkOrderRO对象", description = "包装单管理RO")
public class ErpProductionMgtPackageWorkOrderRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @NotBlank
    @ApiModelProperty(value = "包装类型")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "包装方式")
    private String productionProcessesDescription;

    @NotNull
    @ApiModelProperty(value = "每包数量")
    private BigDecimal proportion;

    @NotNull
    @ApiModelProperty(value = "包装总数")
    private BigDecimal quantity;

    @NotNull
    @ApiModelProperty(value = "包数")
    private BigDecimal outputQuantity;

    @ApiModelProperty(value = "散装总数")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @Valid
    @NotEmpty
    @ApiModelProperty(value = "包装明细列表")
    private List<ErpMaterialMgtMaterialBatchGoodsRO> detailList;

    @ApiModelProperty(value = "要删除的明细guids")
    private List<String> delDetailGuids;

    @ApiModelProperty(value = "物料明细来源guid，处理物料到货生成物料标签的情况")
    private String workArrivalGuid;

    @ApiModelProperty(value = "物料批次guid，处理物料到货生成物料标签的情况")
    private String materialBatchGuid;
}
