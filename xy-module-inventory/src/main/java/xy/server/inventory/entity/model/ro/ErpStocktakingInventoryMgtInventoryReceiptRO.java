package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 库单管理RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryReceiptURO对象", description = "库单管理")
public class ErpStocktakingInventoryMgtInventoryReceiptRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptGuid;

    @NotNull(message = "盘点扩展数据不能为空")
    @ApiModelProperty(value = "盘点扩展数据")
    private ErpInventoryMgtInventoryStocktakingReceiptRO stocktakingReceiptVO;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @NotNull(message = "库单属性不能为空")
    @ApiModelProperty(value = "库单属性")
    private Integer inventoryReceiptProperties;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @NotEmpty(message = "盘点汇总不能为空")
    @ApiModelProperty(value = "盘点汇总")
    private List<ErpInventoryMgtInventoryStocktakingRO> inventoryStocktakingROList;

    @NotEmpty(message = "盘点明细不能为空")
    @ApiModelProperty(value = "盘点明细")
    private List<ErpInventoryMgtInventoryStocktakingBatchRO> inventoryStocktakingBatchROList;

    @ApiModelProperty(value = "是否辅料")
    private Boolean isItMaterial;

}
