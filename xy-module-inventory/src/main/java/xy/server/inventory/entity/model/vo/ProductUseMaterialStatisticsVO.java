package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;


@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ProductUseMaterialStatisticsVO对象")
public class ProductUseMaterialStatisticsVO {

        @ApiModelProperty(value = "排度单号")
        private String planNumber;

        @ApiModelProperty(value = "原纸领料单")
        private String rollPaperOutAndReturnNumber;

        @ApiModelProperty(value = "班组名称")
        private String teamName;

        @ApiModelProperty(value = "机台名称")
        private String equipmentName;

        @ApiModelProperty(value = "原纸条码")
        private String materialGoodsCode;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @ApiModelProperty(value = "购进日期（物料到货单日期）")
        private LocalDateTime receiptDate;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @ApiModelProperty(value = "领料日期")
        private LocalDateTime createDate;

        @ApiModelProperty(value = "供应商简称")
        private String supplierShortName;

        @ApiModelProperty(value = "物料分类名称")
        private String materialClassificationName;

        @ApiModelProperty(value = "搜索码")
        private String searchCode;

        @ApiModelProperty(value = "克重")
        private BigDecimal basisWeight;

        @ApiModelProperty(value = "门幅")
        private BigDecimal widthOfFabric;

        @ApiModelProperty(value = "领料数量")
        private BigDecimal productCount;

        @ApiModelProperty(value = "退料数量")
        private BigDecimal returnCount;

        @ApiModelProperty(value = "实际用量")
        private BigDecimal actCount;

        @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
        private BigDecimal unitPriceIncludingTax;

        @ApiModelProperty(value = "含税总金额")
        private BigDecimal totalAmountIncludingTax;

        @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
        private BigDecimal unitPriceWithoutTax;

        @ApiModelProperty(value = "不含税总金额")
        private BigDecimal totalAmountWithoutTax;

        @ApiModelProperty(value = "备注")
        private String description;

        @ApiModelProperty(value = "等级")
        private Object toJson;

    }


