package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpDieModelDataVO对象")
public class ErpDieModelDataVO {

    @ApiModelProperty(value = "刀模编码")
    private String materialCode;

    @ApiModelProperty(value = "刀模名称")
    private String materialName;

    @ApiModelProperty(value = "刀模id")
    private String materialGuid;

    @ApiModelProperty(value = "使用日期")
    private LocalDateTime useDate;

   @ApiModelProperty(value = "使用寿命")
    private Integer frequencyLifespan;

}