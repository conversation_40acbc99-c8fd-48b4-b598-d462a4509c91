package xy.server.inventory.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 发货通知\到货\退货数据表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryReturnDataRO对象", description = "物料到货管理")
public class ErpInventoryReturnDataRO extends BaseEntity {

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "PK")
    private String noticeArrivalReturnDataGuid;

    @ApiModelProperty(value = "工单类型_guid")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "工单表_guid")
    private String workorderGuid;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "到货日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    private Integer workorderProperties;

    @ApiModelProperty(value = "保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)")
    private Integer decimalMethod;

    @ApiModelProperty(value = "结算单价保留小数位(默认9位，可手动变更)")
    private Integer settlementUnitPriceKeepDecimalPlace;

    @ApiModelProperty(value = "结算金额保留小数位(默认2位，可手动变更)")
    private Integer settlementTotalAmountKeepDecimalPlace;


    @ApiModelProperty(value = "到货客户/供应商guid")
    private String parentCustomerGuid;

    @ApiModelProperty(value = "客户/供应商guid")
    private String customerGuid;

    /**
     * 结算供应商/客户
     */
    @ApiModelProperty(value = "结算供应商guid")
    private String settlementCustomerOrSupplierGuid;

    @ApiModelProperty(value = "税率guid")
    private String invoiceTypeTaxRateGuid;

    @ApiModelProperty(value = "汇率guid")
    private String currencyExchangeRateGuid;

    @NotEmpty(message = "到货明细")
    @ApiModelProperty(value = "到货明细")
    private List<ErpInventoryMgtNoticeArrivalReturnDataRO> arrivalDetailsList;

    /**
     * 审核状态
     */
    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "是否辅料")
    private Boolean isItMaterial;
}
