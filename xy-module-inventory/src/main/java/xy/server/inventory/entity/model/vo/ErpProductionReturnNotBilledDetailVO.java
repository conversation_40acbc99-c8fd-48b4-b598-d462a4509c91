package xy.server.inventory.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialBatchVO;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2024/4/1 15:03
 * @apiNote 生产退料待开明细VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpProductionReturnNotBilledDetailVO对象", description = "库单待开明细VO")
public class ErpProductionReturnNotBilledDetailVO {

    @ApiModelProperty(value = "明细guid")
    private String inventoryReceiptDetailGuid;

    @ApiModelProperty(value = "物guid")
    private String materialGuid;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    /**
     * 关联表数据
     */

    @ApiModelProperty(value = "库单单号")
    private String inventoryReceiptNumbers;

    @ApiModelProperty(value = "待生产退料数")
    private BigDecimal notOutboundQuantity;

    @ApiModelProperty(value = "出库申请单号")
    private String outboundApplicationNumber;

    @ApiModelProperty(value = "物料Obj")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "物料批次Obj")
    private List<ErpMaterialMgtMaterialBatchVO> batchDetailList;
}
