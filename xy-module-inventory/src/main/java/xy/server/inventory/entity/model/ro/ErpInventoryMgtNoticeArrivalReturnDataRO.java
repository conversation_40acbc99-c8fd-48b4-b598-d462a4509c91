package xy.server.inventory.entity.model.ro;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.tenant.history.SystemFieldHistoryCycle;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.basic.entity.ErpBusinessMgtOtherExpenses;
import xy.server.basic.entity.model.ro.ErpBusinessMgtOtherExpensesRO;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 发货通知\到货\退货数据表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtNoticeArrivalReturnDataURO对象", description = "物料到货管理")
public class ErpInventoryMgtNoticeArrivalReturnDataRO extends BaseEntity {

    @ApiModelProperty(value = "物料GUID(成品、物料)")
    @NotBlank(message = "物料GUID不能为空")
    private String materialGuid;

    @ApiModelProperty(value = "PK")
    private String noticeArrivalReturnDataGuid;

    @ApiModelProperty(value = "工单表_guid")
    private String workorderGuid;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "父来源guid")
    private String sourceParentGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @NotNull(message = "数量不能为空")
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 实际到货数
     */
    @ApiModelProperty(value = "实际到货数")
    private BigDecimal actQuantity;
    /**
     * 实际到货物料id
     */
    @ApiModelProperty(value = "实际到货物料id")
    private String actMaterialGuid;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "交货日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "单位（(根据工单属性:1工单成品单位，2工序加工单位）")
    private String unitGuid;

    @ApiModelProperty(value = "订单明细guid")
    private String orderDataGuid;

    @ApiModelProperty(value = "处理方式(字典：物料入库/成品入库/半成品入库等)")
    private String handlingMethod;

    @ApiModelProperty(value = "到货批次")
    private List<ErpInventoryMaterialMgtMaterialBatchRO> materialBatchROS;

    @ApiModelProperty(value = "来源单号")
    private String sourceWorkorderNumber;

    @ApiModelProperty(value = "计费方案id")
    private String costSchemeGuid;

    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "本币单价(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyUnitPrice;

    @ApiModelProperty(value = "本币金额(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalAmount;

    @ApiModelProperty(value = "结算单价（默认与含税单价相等），写入库存价格字段")
    private BigDecimal settlementUnitPrice;

    @ApiModelProperty(value = "结算金额（默认与含税总金额相等），写入库存价格字段")
    private BigDecimal settlementTotalAmount;

    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @SystemFieldHistoryCycle(targetEntity = ErpBusinessMgtOtherExpenses.class)
    @Valid
    @ApiModelProperty(value = "其他费用")
    List<ErpBusinessMgtOtherExpensesRO> otherExpensesList;

    @ApiModelProperty(value = "已删除文件")
    List<String> isdelectFile;

    @ApiModelProperty(value = "件数")
    private BigDecimal packagingQuantity;


    /**
     * 顺序号(每一个层级有对应的顺序号)
     */
    @TableField("serial_number")
    private Integer serialNumber;
}
