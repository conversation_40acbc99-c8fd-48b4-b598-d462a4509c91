package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
* <p>
* 工单订单数据表VO
* </p>
*
* <AUTHOR>
* @since 2024-04-25
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMaterialLabelAndStockInventoryVO对象", description = "工单订单数据表")
public class ErpMaterialLabelAndStockInventoryVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String abelAndInventoryGuid;

    @ApiModelProperty(value = "库存批次id")
    private String inventoryReceiptBatchDetail;

    @ApiModelProperty(value = "物料标签包guid")
    private String materialBatchGoodsGuid;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

}
