package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 物料批次货VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpMaterialMgtMaterialBatchGoodsVO对象", description = "物料批次货")
public class ErpMaterialMgtMaterialBatchGoodsVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String materialBatchGoodsGuid;

    @ApiModelProperty(value = "等于PK（用于待开列表组装树）")
    private String workorderGuid;

    @ApiModelProperty(value = "父_guid（用于组装树）")
    private String parentGuid;

    @ApiModelProperty(value = "位置_guid（该批次放到那个仓）")
    private String warehouseSpaceGuid;

    @ApiModelProperty(value = "位置name")
    private String warehouseSpaceName;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "物料货号码(供应商提供或者自动生成)")
    private String materialGoodsCode;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源类型值")
    private Integer sourceValue;

    @ApiModelProperty(value = "数据来源字典值")
    @XyTrans(dictionaryKey = "PACKAGE_DATA_SOURCE", dictionaryValue = "sourceValue")
    private String sourceDictValue;

    @ApiModelProperty(value = "是否入库")
    private Boolean isInventory;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "包装单号")
    private String workorderNumber;

    @ApiModelProperty(value = "子级列表")
    @XyTransCycle
    private List<ErpMaterialMgtMaterialBatchGoodsVO> children;

    @ApiModelProperty(value = "产品明细列表")
    @XyTransCycle
    private List<ErpMaterialMgtMaterialBatchGoodsDetailVO> detailList;

}
