package xy.server.inventory.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.tenant.history.SystemFieldHistoryCycle;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.basic.entity.ErpBusinessMgtOtherExpenses;
import xy.server.basic.entity.model.ro.ErpBusinessMgtOtherExpensesRO;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 成品出库通知单RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpFinishedProductOutboundNotificationRO对象", description = "成品出库通知单")
public class ErpFinishedProductOutboundNotificationRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "申请类型")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "工单状态(出库情况)")
    private String workorderState;

    @ApiModelProperty(value = "工单属性")
    private Integer workorderProperties;

    @ApiModelProperty(value = "紧急程度(非常紧急，紧急，一般)")
    private String isUrgent;

    @ApiModelProperty(value = "物料guid")
    @NotBlank(message = "物料不能为空")
    private String materialGuid;

    @ApiModelProperty(value = "待出库数")
    private BigDecimal quantity;


    @ApiModelProperty(value = "数量")
    @NotNull(message = "数量不能为空")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "单位")
    @NotBlank(message = "单位不能为空")
    private String unitGuid;

    @ApiModelProperty(value = "客户")
    @NotBlank(message = "客户不能为空")
    private String customerGuid;

    @ApiModelProperty(value = "订单数据guid")
    private String orderDataGuid;

    @NotBlank(message = "订单guid")
    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

    @ApiModelProperty(value = "来源数量")
    private BigDecimal sourceQuantity;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "要求交期")
    private LocalDateTime requiredDeliveryTime;

    @ApiModelProperty(value = "赠送数量")
    private BigDecimal giftQuantity;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)")
    private Integer decimalMethod;

    @ApiModelProperty(value = "结算单价保留小数位(默认9位，可手动变更)")
    private Integer settlementUnitPriceKeepDecimalPlace;

    @ApiModelProperty(value = "结算金额保留小数位(默认2位，可手动变更)")
    private Integer settlementTotalAmountKeepDecimalPlace;

    /**
     * 结算供应商/客户
     */
    @ApiModelProperty(value = "结算供应商guid")
    private String settlementCustomerOrSupplierGuid;
    /**
     * 送货供应商/客户
     */
    @ApiModelProperty(value = "送货供应商guid")
    private String receiptCustomerOrSupplierGuid;

    @ApiModelProperty(value = "税率guid")
    private String invoiceTypeTaxRateGuid;

    @ApiModelProperty(value = "汇率guid")
    private String currencyExchangeRateGuid;

    @ApiModelProperty(value = "计费方案id")
    private String costSchemeGuid;

    @ApiModelProperty(value = "计费方案名称")
    private String schemeName;

    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "本币单价(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyUnitPrice;

    @ApiModelProperty(value = "本币金额(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalAmount;

    @ApiModelProperty(value = "结算单价（默认与含税单价相等），写入库存价格字段")
    private BigDecimal settlementUnitPrice;

    @ApiModelProperty(value = "结算金额（默认与含税总金额相等），写入库存价格字段")
    private BigDecimal settlementTotalAmount;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty(value = "PK")
    private String noticeArrivalReturnDataGuid;

    @ApiModelProperty(value = "成品出库通知单明细")
    @NotEmpty(message = "成品出库通知明细不能为空")
    @Valid
    private List<ErpFinishedProductOutboundNotificationRO> finishedProductOutboundNotificationDetailList;


    @ApiModelProperty(value = "行政区划guid")
    private String administrativeAreaGuid;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @SystemFieldHistoryCycle(targetEntity = ErpBusinessMgtOtherExpenses.class)
    @Valid
    @ApiModelProperty(value = "其他费用")
    List<ErpBusinessMgtOtherExpensesRO> otherExpensesList;

    @ApiModelProperty(value = "已删除文件")
    List<String> isdelectFile;

//    ---------------------------------永久---------------------------------------
    @ApiModelProperty(value = "比例")
    private BigDecimal proportion;

    @ApiModelProperty(value = "成品出库通知单明细")
    @NotEmpty(message = "成品出库通知明细不能为空")
    @Valid
    private List<ErpFinishedProductOutboundNotificationRO> children;

    @ApiModelProperty(value = "备品数量")
    private BigDecimal spareQuantity;

    private List<String> isDeletedWorkIds;

}
