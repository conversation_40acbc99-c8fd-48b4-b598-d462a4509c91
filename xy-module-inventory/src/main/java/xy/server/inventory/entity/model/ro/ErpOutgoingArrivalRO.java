package xy.server.inventory.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.inventory.entity.model.vo.ErpInventoryOutgoingArrivalDataVO;

import java.time.LocalDateTime;
import java.util.List;

/**
* <p>
* RO
* </p>
*
* <AUTHOR>
* @since 2023-12-01
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpProductionMgtWorkorderRO对象", description = "")
public class ErpOutgoingArrivalRO extends BaseEntity {

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "工单类型_guid")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "到货日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)")
    private Integer decimalMethod;

    @ApiModelProperty(value = "结算单价保留小数位(默认9位，可手动变更)")
    private Integer settlementUnitPriceKeepDecimalPlace;

    @ApiModelProperty(value = "结算金额保留小数位(默认2位，可手动变更)")
    private Integer settlementTotalAmountKeepDecimalPlace;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "到货客户/供应商guid")
    private String parentCustomerGuid;

    @ApiModelProperty(value = "客户/供应商guid")
    private String customerGuid;

    /**
     * 结算供应商/客户
     */
    @ApiModelProperty(value = "结算供应商guid")
    private String settlementCustomerOrSupplierGuid;

    @ApiModelProperty(value = "税率guid")
    private String invoiceTypeTaxRateGuid;

    @ApiModelProperty(value = "汇率guid")
    private String currencyExchangeRateGuid;

    @ApiModelProperty(value = "外发订单相关数据")
    private List<ErpInventoryOutgoingArrivalDataVO> outgoingArrivalDataVOList;

}
