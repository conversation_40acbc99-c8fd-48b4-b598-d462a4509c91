package xy.server.inventory.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 库单明细管理VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpInventoryMgtOutboundReceiptDetailVO对象", description = "库单明细管理")
public class ErpInventoryMgtOutboundReceiptDetailVO {

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptDetailGuid;

    @ApiModelProperty(value = "库单_guid")
    private String inventoryReceiptGuid;

    @ApiModelProperty(value = "库位guid")
    private String warehouseSpaceGuid;

    @ApiModelProperty(value = "物分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "库存数量(当单据触发时，记录当前库存数量，库存数量流水)")
    private BigDecimal inventoryQuantity;

    @ApiModelProperty(value = "完成状态(0未完成，1部分完成，2已完成，3终止)")
    private String completionStatus;

    @ApiModelProperty(value = "完成数量(后面流程将其更新)")
    private BigDecimal completionQuantity;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源类型值")
    private Integer sourceValue;

    @ApiModelProperty(value = "来源类型字典值")
    @XyTrans(dictionaryKey = "OUTBOUND_SOURCE_TYPE", dictionaryValue = "sourceValue")
    private String sourceDictValue;

    @ApiModelProperty(value = "金额(不含税)，汇总批次的总价值，不用理会单价。原则：总价值不会变化")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人ID")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "库位name")
    private String warehouseSpaceName;

    @ApiModelProperty(value = "来源父guid")
    private String sourcePGuid;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(value = "申请的来源工单号/订单号")
    private String workNumber;

    @ApiModelProperty(value = "来源数量")
    private BigDecimal sourceQuantity;

    @ApiModelProperty(value = "物料Obj")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "明细订单数据Obj")
    private ErpInventoryMgtInventoryReceiptDetailOrderDataVO detailOrderDataObj;

    @ApiModelProperty(value = "出库批次明细列表")
    private List<ErpInventoryMgtOutboundReceiptBatchDetailVO> batchDetailList;

    @ApiModelProperty(value = "申请类型")
    private Integer workorderTypeGuid;

    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String inventoryReceiptState;

    @XyTrans(dictionaryKey = "FINANCE_MGT_STATE",dictionaryValue = "inventoryReceiptState")
    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String inventoryReceiptStateName;

    @ApiModelProperty(value = "产品面积")
    private BigDecimal productArea;

    @ApiModelProperty(value = "总产品面积")
    private BigDecimal totalProductArea;

    @ApiModelProperty(value = "产品体积")
    private BigDecimal productVolume;

    @ApiModelProperty(value = "总产品体积")
    private BigDecimal totalProductVolume;

    @ApiModelProperty(value = "订单号")
    private String sonWorkorderNumber;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "车次")
    private String vehicleBatch;
}
