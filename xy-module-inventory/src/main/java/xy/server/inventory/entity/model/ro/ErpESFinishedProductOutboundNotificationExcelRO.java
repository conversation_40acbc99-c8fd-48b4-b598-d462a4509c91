package xy.server.inventory.entity.model.ro;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <p>
 * 送货计划excelRO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpESFinishedProductOutboundNotificationExcelRO对象", description = "送货计划excel")
public class ErpESFinishedProductOutboundNotificationExcelRO {

    @ApiModelProperty(value = "客户编码")
    @ExcelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "物料")
    @ExcelProperty(value = "物料")
    private String materialGuid;

    @ApiModelProperty(value = "订单明细")
    @ExcelProperty(value = "订单明细")
    private String orderDataGuid;

    @ApiModelProperty(value = "客方货号")
    @ExcelProperty(value = "客方货号")
    private String externalMaterialCode;

    @ApiModelProperty(value = "产品名称")
    @ExcelProperty(value = "产品名称")
    private String materialName;

    @ApiModelProperty(value = "产品名称")
    @ExcelProperty(value = "产品名称")
    private String materialCode;

    @ApiModelProperty(value = "数量")
    @ExcelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "结算客户")
    @ExcelProperty(value = "结算客户")
    private String settlementCustomerOrSupplierGuid;

    @ApiModelProperty(value = "送货客户")
    @ExcelProperty(value = "送货客户")
    private String receiptCustomerOrSupplierGuid;

    @ApiModelProperty(value = "客户")
    @ExcelProperty(value = "客户")
    private String customerGuid;

    @ApiModelProperty(value = "交货日期")
    @ExcelProperty(value = "交货日期")
    private String requiredDeliveryTime;

    @ApiModelProperty(value = "备注（送货）")
    @ExcelProperty(value = "备注（送货）")
    private String description;

    @ApiModelProperty(value = "送货仓位")
    @ExcelProperty(value = "送货仓位")
    private String warehouseSpaceName;

    @ApiModelProperty(value = "顺序号")
    @ExcelProperty(value = "顺序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "卸货仓库")
    @ExcelProperty(value = "卸货仓库")
    private String address;
}
