package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.inventory.entity.model.ro.ErpMaterialPackageDetailsRO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 原纸领退料单VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpRollPaperOutAndReturnVO对象", description = "原纸领退料单")
public class ErpRollPaperOutAndReturnVO {

    @ApiModelProperty(value = "")
    private String rollPaperOutAndReturnGuid;

    @ApiModelProperty(value = "原纸领退料单")
    private String rollPaperOutAndReturnNumber;

    @ApiModelProperty(value = "排度单号")
    private String planNumber;

    @ApiModelProperty(value = "班组")
    private String teamGuid;

    @ApiModelProperty(value = "班组名称")
    private String teamName;

    @ApiModelProperty(value = "机台")
    private String equipmentGuid;

    @ApiModelProperty(value = "机台名称")
    private String equipmentName;

    @ApiModelProperty(value = "物料guid")
    private String materialGuid;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料规格")
    private String materialScale;

    @ApiModelProperty(value = "用于")
    private String useFor;

    @ApiModelProperty(value = "物料标签")
    private String materialGoodsCode;

    @ApiModelProperty(value = "领料数量")
    private BigDecimal productCount;

    @ApiModelProperty(value = "退料数量")
    private BigDecimal returnCount;

    @ApiModelProperty(value = "操作类型")
    private Integer type;

    @ApiModelProperty(value = "操作类型")
    @XyTrans(dictionaryKey = "ERP_ROLL_PAPER_OUT_AND_RETURN_TYPE_ENUM", dictionaryValue = "type")
    private String typeName;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "物料标签信息")
    private List<ErpMaterialPackageDetailsRO> batchGoods;

    @ApiModelProperty(value = "门幅")
    private BigDecimal widthOfFabric;

    @ApiModelProperty(value = "基本纸质")
    private String basicPaper;

    @ApiModelProperty(value = "楞别")
    private String corrugatedPaperUsageName;

    @ApiModelProperty(value = "纸类")
    private String corrugatedPaperUsageRateName;

    @ApiModelProperty(value = "搜索码")
    private String searchCode;

    @ApiModelProperty(value = "实际使用数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "不含税单价")
    private BigDecimal purchasePriceWithoutTax;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalAmount;


}
