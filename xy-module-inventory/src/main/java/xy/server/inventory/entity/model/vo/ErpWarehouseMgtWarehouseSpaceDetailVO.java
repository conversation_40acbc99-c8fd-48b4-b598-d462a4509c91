package xy.server.inventory.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialBatchVO;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;

/**
 * <p>
 * 仓储空间明细表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpWarehouseMgtWarehouseSpaceDetailVO对象", description = "仓储空间明细表")
public class ErpWarehouseMgtWarehouseSpaceDetailVO {

    @ApiModelProperty(value = "PK")
    private String warehouseSpaceDetailGuid;

    @ApiModelProperty(value = "仓储空间guid")
    private String warehouseSpaceGuid;

    @ApiModelProperty(value = "物料批次货码guid")
    private String materialBatchGoodsGuid;

    @ApiModelProperty(value = "物料批次guid")
    private String materialBatchGuid;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "物料 _guid")
    private String materialGuid;

    /**
     * 关联表查询
     */
    @ApiModelProperty(value = "仓储空间编码")
    private String warehouseSpaceCode;

    @ApiModelProperty(value = "仓储空间名称")
    private String warehouseSpaceName;

    @ApiModelProperty(value = "物料Obj")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "物料批次Obj")
    private ErpMaterialMgtMaterialBatchVO materialBatchObj;

}
