package xy.server.inventory.entity.model.ro;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.excel.converter.LocalDateTimeConverter;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 送货计划excelRO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpFinishedProductOutboundNotificationExcelRO对象", description = "送货计划excel")
public class ErpFinishedProductOutboundNotificationExcelRO extends BaseEntity {

    @ApiModelProperty(value = "客户编码")
    @ExcelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "客户货号")
    @ExcelProperty(value = "客户货号")
    private String externalMaterialCode;

    @ApiModelProperty(value = "产品名称")
    @ExcelProperty(value = "产品名称")
    private String materialName;

    @ApiModelProperty(value = "数量")
    @ExcelProperty(value = "数量")
    private BigDecimal quantity;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "交货日期")
    @ExcelProperty(value = "交货日期", converter = LocalDateTimeConverter.class)
    private LocalDateTime requiredDeliveryTime;

    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "送货仓位")
    @ExcelProperty(value = "送货仓位")
    private String warehouseSpaceName;

}
