package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @data 2024-7-29
 * @apiNote MaterialRequisitionVO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "MaterialRequisitionVO对象", description = "MaterialRequisitionVO")
public class MaterialRequisitionVO {

    @ApiModelProperty("订单明细guid")
    private String orderDataGuid;

    @ApiModelProperty("生产工单号")
    private String produceWorkOrderNumber;

    @ApiModelProperty(value = "纸板领料数量")
    private BigDecimal cardboardMaterialRequisitionQuantity;

    @ApiModelProperty(value = "纸板领料金额(出库含税)")
    private BigDecimal cardboardMaterialRequisitionAmount;

    @ApiModelProperty(value = "纸板领料税前金额(出库不含税)")
    private BigDecimal cardboardMaterialRequisitionPreTaxAmount;

    @ApiModelProperty(value = "纸板领料面积")
    private BigDecimal cardboardMaterialRequisitionArea;



}
