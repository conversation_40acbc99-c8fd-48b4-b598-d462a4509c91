package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
                                                                                                                                /**
* <p>
* 库单批次明细管理QO
* </p>
*
* <AUTHOR>
* @since 2023-10-09
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryReceiptBatchDetailQO对象", description = "库单批次明细管理")
public class ErpStockInventoryMgtInventoryReceiptBatchDetailQO {

            @ApiModelProperty(value = "PK")
    private String inventoryReceiptBatchDetail;

        @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

        @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

            @ApiModelProperty(value = "创建人")
    private String creator;

            @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

        @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

            @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

            @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

        @ApiModelProperty(value = "库单明细guid")
    private String inventoryReceiptDetailGuid;

        @ApiModelProperty(value = "物批次guid")
    private String materialBatchGuid;

        @ApiModelProperty(value = "物guid")
    private String materialGuid;

        @ApiModelProperty(value = "库位guid")
    private String warehouseSpaceGuid;

            @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

            @ApiModelProperty(value = "备注或描述")
    private String description;

        @ApiModelProperty(value = "扩展字段")
    private Object toJson;

}