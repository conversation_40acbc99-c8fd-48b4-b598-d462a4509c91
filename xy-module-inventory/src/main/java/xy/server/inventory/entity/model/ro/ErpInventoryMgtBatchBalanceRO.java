package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
* <p>
* 批次结转表写入后台表RO
* </p>
*
* <AUTHOR>
* @since 2024-08-26
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtBatchBalanceRO对象", description = "批次结转表写入后台表")
public class ErpInventoryMgtBatchBalanceRO extends BaseEntity {

        @ApiModelProperty(value = "pk")
    private String batchBalanceGuid;

        @ApiModelProperty(value = "结转guid")
    private String balanceGuid;

        @ApiModelProperty(value = "物批次guid")
    private String materialBatchGuid;

        @NotNull(message = "结转数量不能为空")
        @ApiModelProperty(value = "结转数量")
    private BigDecimal inventoryBalanceQuantity;

        @NotNull(message = "结转金额不能为空")
        @ApiModelProperty(value = "结转金额")
    private BigDecimal inventoryBalanceTotalamount;

        @ApiModelProperty(value = "备注或描述")
    private String description;

        @ApiModelProperty(value = "入库数量")
    private BigDecimal stockInQuantity;

        @ApiModelProperty(value = "入库金额")
    private BigDecimal stockInTotalAmount;

        @ApiModelProperty(value = "出库数量")
    private BigDecimal stockOutQuantity;

        @ApiModelProperty(value = "出库金额")
    private BigDecimal stockOutTotalAmount;

}
