package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @data 2024/12/16
 * @apiNote 运输数量统计表（送货单数量+备品数量+退货数量） 同送货统计表
 */
@Data
@ApiModel(value = "ErpTransportQuantityCollectVO对象", description = "运输数量统计VO")
public class ErpTransportQuantityCollectVO {

    @ApiModelProperty(value = "送货数量")
    private BigDecimal outPutQuantity;

    @ApiModelProperty(value = "退货数量")
    private BigDecimal returnQuantity;

    @ApiModelProperty(value = "备用数量")
    private BigDecimal backQuantity;

    @ApiModelProperty(value = "总数量数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "总面积")
    private BigDecimal cartonArea;

    @ApiModelProperty(value = "送货供应商")
    private String customerShortName;

}
