package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>
 * 工单批次明细关联表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpProductionMgtWorkorderBatchDetailRO对象", description = "工单批次明细关联表")
public class ErpProductionMgtWorkorderBatchDetailRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String workorderBatchDetailGuid;

    @ApiModelProperty(value = "工单表guid")
    private String workorderGuid;

    @ApiModelProperty(value = "物批次guid")
    private String materialBatchGuid;

    @ApiModelProperty(value = "物guid")
    private String materialGuid;

    @ApiModelProperty(value = "库位guid")
    private String warehouseSpaceGuid;

    @NotNull(message = "数量不能为空")
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "物料批次RO")
    private ErpInventoryMaterialMgtMaterialBatchRO newMaterialBatchRO;



}
