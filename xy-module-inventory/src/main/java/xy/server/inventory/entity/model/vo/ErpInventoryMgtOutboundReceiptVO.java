package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 出出库单管理VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpInventoryMgtOutboundReceiptVO对象", description = "出库单管理")
public class ErpInventoryMgtOutboundReceiptVO {

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptGuid;

    @ApiModelProperty(value = "出库类型")
    private Integer operationTypeGuid;

    @ApiModelProperty(value = "出库类型字典值")
    @XyTrans(dictionaryKey = "OUTBOUND_TYPE", dictionaryValue = "operationTypeGuid")
    private String operationTypeGuidDictValue;

    @ApiModelProperty(value = "库单单号")
    private String inventoryReceiptNumbers;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源类型值")
    private Integer sourceValue;

    @ApiModelProperty(value = "来源类型字典值")
    @XyTrans(dictionaryKey = "OUTBOUND_SOURCE_TYPE", dictionaryValue = "sourceValue")
    private String sourceDictValue;

    @ApiModelProperty(value = "库单属性")
    private Integer inventoryReceiptProperties;

    @ApiModelProperty(value = "单据日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核状态字典值")
    @XyTrans(dictionaryKey = "AUDIT_STATUS", dictionaryValue = "auditStatus")
    private String auditStatusDictValue;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    /**
     * 关联数据
     */
    @ApiModelProperty(value = "出仓情况")
    private String outWarehouseState;

    @ApiModelProperty(value = "出仓情况字典值")
    @XyTrans(dictionaryKey = "OUT_WAREHOUSE_STATE", dictionaryValue = "outWarehouseState")
    private String outWarehouseStateDictValue;

    @ApiModelProperty(value = "出库明细列表")
    private List<ErpInventoryMgtOutboundReceiptDetailVO> detailList;


}
