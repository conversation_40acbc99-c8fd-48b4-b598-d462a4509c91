package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xunyue.config.mybatisplus.BaseEntity;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.basic.entity.model.vo.ErpBusinessMgtOtherExpensesVO;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2023/10/7
 * @apiNote 成品出库通知单VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpFinishedProductOutboundNotificationVO对象", description = "成品出库通知单VO")
public class ErpFinishedProductOutboundNotificationVO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "合同guid")
    private String contractGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "工单号")
    private String sonWorkorderNumber;


    @ApiModelProperty(value = "工单号")
    private String workorderNumberS;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "申请类型")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "申请类型（字典值）")
    @XyTrans(dictionaryKey = "FINISHED_PRODUCT_OUTBOUND_NOTIFICATION_TYPE", dictionaryValue = "workorderTypeGuid")
    private String workorderTypeDictValue;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;


    @ApiModelProperty(value = "来源")
    @XyTrans(dictionaryKey = "FINISHED_PRODUCT_OUTBOUND_NOTIFICATION_SOURCE", dictionaryValue = "sourceValue")
    private String sourceDictValue;

    @ApiModelProperty(value = "工单状态(出库情况)")
    private String workorderState;

    @ApiModelProperty(value = "工单状态(出库情况)")
    @XyTrans(dictionaryKey = "OUTBOUND_STATE", dictionaryValue = "workorderState")
    private String workorderStateDictValue;

    @ApiModelProperty(value = "紧急程度(非常紧急，紧急，一般)")
    private String isUrgent;

    @ApiModelProperty(value = "物料guid")
    private String materialGuid;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
    @ApiModelProperty(value = "赠送数量")
    private BigDecimal giftQuantity;

    @ApiModelProperty(value = "出库赠送数量")
    private BigDecimal giftQuantitys;

    @ApiModelProperty(value = "备用数量")
    private BigDecimal spareQuantity;

    @ApiModelProperty(value = "总数量数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "单位guid")
    private String unitGuid;

    @ApiModelProperty(value = "客户guid")
    private String customerGuid;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核状态字典值")
    @XyTrans(dictionaryKey = "AUDIT_STATUS", dictionaryValue = "auditStatus")
    private String auditStatusDictValue;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    /**
     * 关联表数据
     */

    @ApiModelProperty(value = "订单id")
    private String orderGuid;

    @ApiModelProperty(value = "订单数据guid")
    private String orderDataGuid;

    @ApiModelProperty(value = "待出库数")
    private BigDecimal notOutboundQuantity;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(value = "来源数量")
    private BigDecimal sourceQuantity;

    @ApiModelProperty(value = "区分数据来源guid")
    private String sourceParentGuid;

    @ApiModelProperty(value = "客户简称")
    private String customerShortName;

    @ApiModelProperty(value = "物料Obj")
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "外部单号(例客户PO，采购送货单号)")
    private String externalTrackingNumber;

    @ApiModelProperty(value = "外部物料编码(例客户产品编码)")
    private String externalMaterialCode;

    @ApiModelProperty("客户料号条码")
    public String externalMaterialBarCode;

    @ApiModelProperty(value = "外部物料名称(例客户产品名称)")
    private String externalMaterialName;

    @ApiModelProperty(value = "货期")
    private LocalDateTime deliveryDate;

    @ApiModelProperty(value = "送货日期")
    private LocalDateTime requiredDeliveryTime;

    @ApiModelProperty(value = "行政区域guid")
    private String administrativeAreaGuid;


    @ApiModelProperty(value = "行政区域名称")
    private String administrativeAreaName;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "计费方案id")
    private String costSchemeGuid;

    @ApiModelProperty(value = "计费方案名称")
    private String schemeName;

    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "本币单价(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyUnitPrice;

    @ApiModelProperty(value = "本币金额(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalAmount;

    @ApiModelProperty(value = "结算单价（默认与含税单价相等），写入库存价格字段")
    private BigDecimal settlementUnitPrice;

    @ApiModelProperty(value = "结算金额（默认与含税总金额相等），写入库存价格字段")
    private BigDecimal settlementTotalAmount;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty(value = "保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)")
    private Integer decimalMethod;

    @ApiModelProperty(value = "结算单价保留小数位(默认9位，可手动变更)")
    private Integer settlementUnitPriceKeepDecimalPlace;

    @ApiModelProperty(value = "结算金额保留小数位(默认2位，可手动变更)")
    private Integer settlementTotalAmountKeepDecimalPlace;

    /**
     * 结算供应商/客户
     */
    @ApiModelProperty(value = "结算供应商guid")
    private String settlementCustomerOrSupplierGuid;
    /**
     * 送货供应商/客户
     */
    @ApiModelProperty(value = "送货供应商guid")
    private String receiptCustomerOrSupplierGuid;

    @ApiModelProperty(value = "送货供应商")
    private String receiptCustomerOrSupplierName;

    @ApiModelProperty(value = "税率guid")
    private String invoiceTypeTaxRateGuid;

    @ApiModelProperty(value = "汇率guid")
    private String currencyExchangeRateGuid;

    @ApiModelProperty(value = "到货供应商/客户")
    private String customerOrSupplierName;
    /**
     * 结算供应商/客户
     */
    @ApiModelProperty(value = "结算供应商name")
    private String settlementCustomerOrSupplierName;


    @ApiModelProperty(value = "税率名称")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "币种guid")
    private String currencyGuid;

    @ApiModelProperty(value = "汇率名称")
    private String exchangeRate;

    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    @ApiModelProperty(value = "是否本币")
    private Boolean isLocalCurrency;

    @ApiModelProperty(value = "PK")
    private String noticeArrivalReturnDataGuid;

    @ApiModelProperty(value = "成品出库通知单明细")
    @XyTransCycle
    private List<ErpFinishedProductOutboundNotificationVO> finishedProductOutboundNotificationDetailList;

    @ApiModelProperty(value = "其他费用")
    private List<ErpBusinessMgtOtherExpensesVO> otherExpensesList;


    @ApiModelProperty(value = "产品面积")
    private BigDecimal productArea;

    @ApiModelProperty(value = "总产品面积")
    private BigDecimal totalProductArea;

    @ApiModelProperty(value = "产品体积")
    private BigDecimal productVolume;

    @ApiModelProperty(value = "总产品体积")
    private BigDecimal totalProductVolume;

    //---------------------------永久
    @ApiModelProperty(value = "单重")
    private BigDecimal pieceWeight;
    @ApiModelProperty(value = "体积")
    private BigDecimal volume;
    @ApiModelProperty(value = "纸箱面积")
    private BigDecimal cartonArea;
    @ApiModelProperty(value = "楞别")
    private String corrugatedPaperName;

    @ApiModelProperty(value = "平均重量")
    private BigDecimal pieceWeightPer;
    @ApiModelProperty(value = "平均体积")
    private BigDecimal volumePer;
    @ApiModelProperty(value = "平均纸箱面积")
    private BigDecimal cartonAreaPer;
    @ApiModelProperty(value = "比例")
    private BigDecimal proportion;
    @ApiModelProperty(value = "订单类型")
    private String orderTypeName;

    @ApiModelProperty(value = "成品出库通知单明细")
    @XyTransCycle
    private List<ErpFinishedProductOutboundNotificationVO> children;

    @ApiModelProperty(value = "订单状态")
    private String orderState;

    @XyTrans(dictionaryKey = "ORDER_DATA_STATE", dictionaryValue = "orderState")
    @ApiModelProperty(value = "订单状态")
    private String orderStateName;

    @JsonIgnore
    private String workorderGuidFepmw14;
}
