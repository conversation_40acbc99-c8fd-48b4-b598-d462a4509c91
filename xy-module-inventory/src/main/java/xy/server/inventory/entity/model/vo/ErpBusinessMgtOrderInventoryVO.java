package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* <p>
* 订单表VO
* </p>
*
* <AUTHOR>
* @since 2023-09-28
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBusinessMgtOrderVO对象", description = "订单表")
public class ErpBusinessMgtOrderInventoryVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String orderGuid;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "订单需求描述")
    private String orderDemandGuid;

    @ApiModelProperty(value = "单据日期(可变更的)")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "订单类型")
    private String orderType;

    @ApiModelProperty(value = "总费用(含税)")
    private BigDecimal totalExpensesIncludingTax;

    @ApiModelProperty(value = "总费用(不含税)")
    private BigDecimal totalExpensesWithoutTax;

    @ApiModelProperty(value = "本币总费用(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalExpenses;

    @ApiModelProperty(value = "结算费用（默认与含税总金额相等)")
    private BigDecimal settlementTotalExpenses;

    @ApiModelProperty(value = "总体积")
    private BigDecimal totalVolume;

    @ApiModelProperty(value = "总重量")
    private BigDecimal totalWeight;

    @ApiModelProperty(value = "保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)")
    private Integer decimalMethod;

    @ApiModelProperty(value = "结算单价保留小数位(默认9位，可手动变更)")
    private Integer settlementUnitPriceKeepDecimalPlace;

    @ApiModelProperty(value = "结算金额保留小数位(默认2位，可手动变更)")
    private Integer settlementTotalAmountKeepDecimalPlace;

    @ApiModelProperty(value = "订单属性(1业务合同，2采购订单，3外发订单)")
    private Integer orderProperties;

    @ApiModelProperty(value = "税率guid")
    private String invoiceTypeTaxRateGuid;

    @ApiModelProperty(value = "汇率guid")
    private String currencyExchangeRateGuid;

    @ApiModelProperty(value = "是否加急")
    private Boolean isUrgent;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @ApiModelProperty(value = "客户/供应商guid")
    private String customerOrSupplierGuid;

    @ApiModelProperty(value = "结算客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String settlementCustomerOrSupplierGuid;

    @ApiModelProperty(value = "送货客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String deliveryCustomerOrSupplierGuid;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "送货类型 _guid")
    private String deliveryTypeGuid;

    @ApiModelProperty(value = "结算类型 SettlementType_guid")
    private String settlementTypeGuid;

    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @ApiModelProperty(value = "手机")
    private String mobilephone;


}
