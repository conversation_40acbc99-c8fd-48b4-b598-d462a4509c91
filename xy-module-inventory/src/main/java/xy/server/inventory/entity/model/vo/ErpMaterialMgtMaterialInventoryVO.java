package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.material.entity.model.vo.ErpMaterialMgtSpecificationValueVO;
import xy.server.material.entity.model.vo.ErpMaterialSpecificationVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 物料表模块VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMaterialMgtMaterialVO对象", description = "物料表模块")
public class ErpMaterialMgtMaterialInventoryVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String materialGuid;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料分类 _guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "单位 _guid")
    private String unitGuid;

    @ApiModelProperty(value = "状态(0停用，1启用)")
    private Boolean state;

    @ApiModelProperty(value = "已被使用(0未被使用，1已被使用)")
    private Boolean isUsed;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "是否自动编码")
    private boolean isCode;

    @ApiModelProperty(value = "规格值")
    private List<ErpMaterialMgtSpecificationValueVO> specificationValueROS;

    @ApiModelProperty(value = "工单使用规格值")
    private List<ErpMaterialSpecificationVO> materialSpecificationVOS;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

}
