package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ErpInventoryStatisticsListTotalVO {

    @ApiModelProperty("申购数量")
    private BigDecimal purchaseWorkorderQuantity;

    @ApiModelProperty("采购数量")
    private BigDecimal purchasedQuantity;

    @ApiModelProperty("来源数量")
    private BigDecimal sourceQuantity;

    @ApiModelProperty("入库数量")
    private BigDecimal stockInQuantity;

    @ApiModelProperty("含税总金额")
    private BigDecimal totalAmountIncludingTax;

//    @ApiModelProperty("报价不含税单价")
//    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty("不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty("入仓总数")
    private BigDecimal inventoryQuantity;
}
