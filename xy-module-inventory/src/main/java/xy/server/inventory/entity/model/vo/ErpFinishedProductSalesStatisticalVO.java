package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.basic.entity.model.vo.ErpBusinessMgtOtherExpensesVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2024-7-29
 * @apiNote 成品销货统计
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpFinishedProductSalesStatisticalVO对象", description = "成品销货统计VO")
public class ErpFinishedProductSalesStatisticalVO {

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "产品类型")
    private String materialClassificationName;

    @ApiModelProperty(value = "产品编码")
    private String materialCode;

    @ApiModelProperty(value = "产品名称")
    private String  materialName;

    @ApiModelProperty(value = "规格类型")
    private String materialSpecification;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "销货数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "订单数据guid")
    private String orderDataGuid;

    @ApiModelProperty(value = "销货单号")
    private String workorderNumber;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "销货日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "制单人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核日期")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "审核人")
    private String auditBy;

    @ApiModelProperty(value = "客户")
    private String customerShortName;

    @ApiModelProperty(value = "客户PO")
    private String externalTrackingNumber;

    @ApiModelProperty(value = "客户料号")
    private String externalMaterialCode;

    @ApiModelProperty(value = "客户产品名称")
    private String externalMaterialName;

    @ApiModelProperty(value = "异常处理原因")
    private String exceptionHandlingReason;

    @ApiModelProperty(value = "异常处理人")
    private String exceptionHandlerName;

    @ApiModelProperty(value = "异常处理日期")
    private String exceptionHandlingDate;

    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String currentStatus;

    @XyTrans(dictionaryKey = "DOCUMENT_STATUS",dictionaryValue = "currentStatus")
    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String currentStatusDictValue;

    @ApiModelProperty(value = "生成凭证")
    private boolean isGenerateProof;

    @ApiModelProperty(value = "工单状态")
    private String workorderState;

    @ApiModelProperty(value = "工单状态")
    @XyTrans(dictionaryKey = "OUTBOUND_STATE", dictionaryValue = "workorderState")
    private String workorderStateDictValue;

    @ApiModelProperty(value = "物料guid")
    private String materialGuid;

    @ApiModelProperty(value = "总数量数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "单位guid")
    private String unitGuid;

    @ApiModelProperty(value = "客户guid")
    private String customerGuid;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核状态字典值")
    @XyTrans(dictionaryKey = "AUDIT_STATUS", dictionaryValue = "auditStatus")
    private String auditStatusDictValue;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty(value = "面积")
    private BigDecimal productArea;

    @ApiModelProperty(value = "生产工单号")
    private String productionOrderNumberStr;

    @ApiModelProperty(value = "材质")
    private String usageMaterialNameStr;

    @ApiModelProperty(value = "开票状态")
    private String invoiceStatus;

    @ApiModelProperty(value = "开票状态字典")
    @XyTrans(dictionaryKey = "INVOICE_STATUS", dictionaryValue = "invoiceStatus")
    private String invoiceStatusName;

    @ApiModelProperty("已开票金额")
    private BigDecimal invoicedAmount;

    @ApiModelProperty("顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty("父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty("打印次数")
    private Integer printTimes;

    @ApiModelProperty("单据日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptDate;

    @ApiModelProperty("工单类型_guid")
    private String workorderTypeGuid;

    @ApiModelProperty("来源guid")
    private String sourceGuid;

    @ApiModelProperty("来源值")
    private Integer sourceValue;

    @ApiModelProperty("备注或描述")
    private String description;

    @ApiModelProperty("创建人_guid")
    private String creatorGuid;

    @ApiModelProperty("最后修改人_guid")
    private String lastUpdaterGuid;

    @ApiModelProperty("最后修改人")
    private String lastUpdater;

    @ApiModelProperty("最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty("流程实例id")
    private String processInstanceId;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("")
    private String materialClassificationGuid;

    @ApiModelProperty("客户全称")
    private String customerFullName;

    @ApiModelProperty("结算客户")
    private String settlementCustomerCode;

    @ApiModelProperty("结算客户")
    private String settlementCustomerShortName;

    @ApiModelProperty("结算客户")
    private String settlementCustomerFullName;

    @ApiModelProperty("")
    private String externalMaterialBarCode;

    @ApiModelProperty("含税单价")
    private String unitPriceIncludingTax;

    @ApiModelProperty("含税单价")
    private String totalAmountIncludingTax;

    @ApiModelProperty("含税总金额")
    private String settlementUnitPrice;

    @ApiModelProperty("结算")
    private String settlementTotalAmount;

    @ApiModelProperty("含税单价")
    private String localCurrencyUnitPrice;

    @ApiModelProperty("含税单价")
    private String localCurrencyTotalAmount;

    @ApiModelProperty("业务员")
    private String salesmanName;

    @ApiModelProperty("跟单员")
    private String merchandiserName;

    @ApiModelProperty(value = "长")
    private String length;

    @ApiModelProperty(value = "宽")
    private String width;

    @ApiModelProperty(value = "高")
    private String height;

    @ApiModelProperty(value = "是否采购/是否外发")
    private Boolean isPurchase;

    @ApiModelProperty(value = "纸板领料数量")
    private BigDecimal cardboardMaterialRequisitionQuantity;

    @ApiModelProperty(value = "纸板领料金额(出库含税)")
    private BigDecimal cardboardMaterialRequisitionAmount;

    @ApiModelProperty(value = "纸板领料税前金额(出库不含税)")
    private BigDecimal cardboardMaterialRequisitionPreTaxAmount;

    @ApiModelProperty(value = "纸板领料面积")
    private BigDecimal cardboardMaterialRequisitionArea;

    @ApiModelProperty(value = "外发产品金额")
    private BigDecimal outsourcedProductsAmount;

    @ApiModelProperty(value = "外发产品税前金额")
    private BigDecimal outsourcedProductsPreTaxAmount;

    @ApiModelProperty(value = "对账情况")
    private String  statementState;

    @ApiModelProperty(value = "对账情况字典")
    @XyTrans(dictionaryKey = "RECONCILIATION", dictionaryValue = "statementState")
    private String statementStateName;


    private List<ErpFinanceMgtStatementBillDetailVO> erpFinanceMgtStatementBillDetailVOS;

    @ApiModelProperty(value = "销货单不含税单价")
    private BigDecimal finishedUnitPriceWithoutTax;

    @ApiModelProperty(value = "销货单不含税金额")
    private BigDecimal finishedTotalAmountWithoutTax;

    @ApiModelProperty(value = "销货单含税单价")
    private BigDecimal finishedUnitPriceIncludingTax;

    @ApiModelProperty(value = "销货单含税金额")
    private BigDecimal finishedTotalAmountIncludingTax;

    @ApiModelProperty(value = "对账日期")
    private LocalDateTime efmsbReceiptDate;

    @ApiModelProperty(value = "税率名称")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "汇率名称")
    private String exchangeRate;

    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    @ApiModelProperty(value = "是否本币")
    private Boolean isLocalCurrency;

    @ApiModelProperty(value = "币种id")
    private String currencyGuid;

    @ApiModelProperty(value = "结算币种名称")
    private String settlementCurrencyName;

    @ApiModelProperty(value = "结算是否本币")
    private Boolean settlementIsLocalCurrency;

    @ApiModelProperty(value = "结算币种id")
    private String settlementCurrencyGuid;

    @ApiModelProperty(value = "其他费用")
    private List<ErpBusinessMgtOtherExpensesVO> otherExpensesList;

    @ApiModelProperty(value = "货期")
    private LocalDateTime deliveryDate;

    @JsonIgnore
    private String fepmw14WorkorderGuid;

    @JsonIgnore
    private String pepmwWorkorderGuid;

    @JsonIgnore
    private String pepmwProcessInstanceId;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal purchasedQuantity;

    @JsonIgnore
    private String orderGuid;

}
