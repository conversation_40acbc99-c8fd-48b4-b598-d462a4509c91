package xy.server.inventory.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 入仓单管理RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpWarehouseMgtInWarehouseReceiptRO对象", description = "入仓单管理RO")
public class ErpWarehouseMgtInWarehouseReceiptRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String warehouseReceiptGuid;

    @NotNull(message = "仓单属性不能为空")
    @ApiModelProperty(value = "仓单属性")
    private Integer warehouseReceiptProperties;

    @ApiModelProperty(value = "仓单类型(字典)")
    private String warehouseReceiptType;

    @ApiModelProperty(value = "仓单单号")
    private String warehouseReceiptNumbers;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "单据日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "入仓明细列表")
    @Valid
    @NotEmpty
    private List<ErpWarehouseMgtInWarehouseReceiptDetailRO> detailList;

    @ApiModelProperty(value = "入仓明细删除ids")
    private List<String> delDetailIds;

}
