package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>
 * 移仓单明细管理RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpWarehouseMgtTransferWarehouseReceiptDetailRO对象", description = "移仓单明细管理RO")
public class ErpWarehouseMgtTransferWarehouseReceiptDetailRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String warehouseReceiptDetailGuid;

    @ApiModelProperty(value = "仓单 _guid")
    private String warehouseReceiptGuid;

    @NotBlank
    @ApiModelProperty(value = "库位guid")
    private String warehouseSpaceGuid;

    @NotBlank
    @ApiModelProperty(value = "物分类guid")
    private String materialClassificationGuid;

    @NotBlank
    @ApiModelProperty(value = "物料批次 _guid")
    private String materialBatchGuid;

    @NotBlank
    @ApiModelProperty(value = "物料 _guid")
    private String materialGuid;

    @NotNull
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源类型值")
    private Integer sourceValue;

    @NotNull
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @Valid
    @ApiModelProperty(value = "移仓明细扩展表RO")
    private ErpWarehouseMgtWarehouseReceiptMovingDetailRO movingDetailObj;

}
