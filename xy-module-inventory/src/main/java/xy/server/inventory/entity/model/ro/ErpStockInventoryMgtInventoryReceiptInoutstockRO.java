package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 库单管理扩展表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpStockInventoryMgtInventoryReceiptInoutstockRO对象", description = "库单管理扩展表")
public class ErpStockInventoryMgtInventoryReceiptInoutstockRO extends BaseEntity {

    @ApiModelProperty(value = "库单guid")
    private String inventoryReceiptGuid;

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptInoutstockGuid;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)")
    private Integer decimalMethod;

    @ApiModelProperty(value = "结算单价保留小数位(默认9位，可手动变更)")
    private Integer settlementUnitPriceKeepDecimalPlace;

    @ApiModelProperty(value = "结算金额保留小数位(默认2位，可手动变更)")
    private Integer settlementTotalAmountKeepDecimalPlace;

}
