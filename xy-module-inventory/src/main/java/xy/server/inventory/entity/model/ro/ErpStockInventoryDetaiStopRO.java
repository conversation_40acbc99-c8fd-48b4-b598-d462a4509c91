package xy.server.inventory.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 装舱明细表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpStockInventoryDetaiStopRO对象", description = "")
public class ErpStockInventoryDetaiStopRO {

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptDetailGuid;

    @ApiModelProperty(value = "状态")
    private String inventoryReceiptState;

}