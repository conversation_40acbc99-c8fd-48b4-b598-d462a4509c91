package xy.server.inventory.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.inventory.entity.model.ro.ErpStockInventoryMgtInventoryReceiptDetailInoutstockRO;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 库单明细管理RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpProdutionReturnWaitingVO对象")
public class ErpProdutionReturnWaitingVO {

    @ApiModelProperty
    private String orderDataGuid;

    @ApiModelProperty(value = "库位guid")
    private String warehouseSpaceGuid;

    @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "来源数量")
    private BigDecimal sourceQuantity;

    @ApiModelProperty(value = "待开数量")
    private BigDecimal waitOutgoingQuantity;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "工单guid")
    private String workorderGuid;

    @ApiModelProperty(value = "父级guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "来源单号")
    private String sourceWorkorderNumber;

    @ApiModelProperty(value = "来源单号")
    private String parentWorkorderNumber;

    @ApiModelProperty(value = "来源类型值")
    private Integer sourceValue;

    @XyTrans(dictionaryKey = "INCOMING_INVENTORY_DETAIL_DATA", dictionaryValue = "sourceValue")
    @ApiModelProperty(value = "来源")
    private String sourceName;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "是否生产控制数据")
    private Boolean isproductionControlData=false;

    @ApiModelProperty(value = "入库明细扩展数据")
    private ErpStockInventoryMgtInventoryReceiptDetailInoutstockRO receiptDetailInoutstockRO;

    @ApiModelProperty(value = "库单批次明细管理")
    private List<ErpInventoryMaterialMgtMaterialBatchVO> materialBatchROS;

    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "物料Obj")
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "物料")
    private String materialName;

    @ApiModelProperty(value = "来源生产单号")
    private String sourceProductOrderNumber;
}
