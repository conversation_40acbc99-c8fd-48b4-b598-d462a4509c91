package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 物料批次货RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMaterialMgtMaterialBatchGoodsRO对象", description = "物料批次货")
public class ErpMaterialMgtMaterialBatchGoodsRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String materialBatchGoodsGuid;

    @ApiModelProperty(value = "位置_guid（该批次放到那个仓）")
    private String warehouseSpaceGuid;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "物料货号码(供应商提供或者自动生成)")
    private String materialGoodsCode;

    @NotNull(message = "数量不能为空")
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源类型值")
    private Integer sourceValue;

    @ApiModelProperty(value = "是否入库")
    private Boolean isInventory;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "子级列表")
    private List<ErpMaterialMgtMaterialBatchGoodsRO> children;

    @ApiModelProperty(value = "要移除的子级guids")
    private List<String> delChildGuids;

    @Valid
    @ApiModelProperty(value = "产品明细列表")
    private List<ErpMaterialMgtMaterialBatchGoodsDetailRO> detailList;

    @ApiModelProperty(value = "要移除的产品明细Guids")
    private List<String> delDetailGuids;

}
