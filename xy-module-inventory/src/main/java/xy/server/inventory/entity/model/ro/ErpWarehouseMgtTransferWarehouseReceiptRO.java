package xy.server.inventory.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 移仓单管理RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpWarehouseMgtTransferWarehouseReceiptRO对象", description = "移仓单管理RO")
public class ErpWarehouseMgtTransferWarehouseReceiptRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String warehouseReceiptGuid;

    @NotNull
    @ApiModelProperty(value = "仓单属性")
    private Integer warehouseReceiptProperties;

    @ApiModelProperty(value = "仓单类型(字典)")
    private String warehouseReceiptType;

    @ApiModelProperty(value = "仓单单号")
    private String warehouseReceiptNumbers;

    @NotNull
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @NotNull
    @ApiModelProperty(value = "单据日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "移仓明细列表")
    @Valid
    @NotEmpty
    private List<ErpWarehouseMgtTransferWarehouseReceiptDetailRO> detailList;

    @ApiModelProperty(value = "移仓明细删除ids")
    private List<String> delDetailIds;

}
