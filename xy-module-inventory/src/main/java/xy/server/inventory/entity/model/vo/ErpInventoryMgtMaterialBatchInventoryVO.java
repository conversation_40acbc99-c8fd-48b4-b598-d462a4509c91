package xy.server.inventory.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.inventory.entity.model.ro.ErpMaterialPackageDetailsRO;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialBatchVO;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 物料批次库存表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtMaterialBatchInventoryVO对象", description = "物料批次库存表")
public class ErpInventoryMgtMaterialBatchInventoryVO {

    @ApiModelProperty(value = "PK")
    private String inventoryMaterialBatchInventoryGuid;

    @ApiModelProperty(value = "物料批次guid")
    private String materialBatchGuid;

    @ApiModelProperty(value = "生产批次")
    private String materialProductionBatchNumber;

    @ApiModelProperty(value = "物分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料 _guid")
    private String materialGuid;

    @ApiModelProperty(value = "批次库存数量")
    private BigDecimal batchInventoryQuantity;

    @ApiModelProperty(value = "库存总金额(净价，不包含任何税率，汇率等)")
    private BigDecimal batchInventoryTotalamount;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(value = "来源Guid")
    private String sourceGuid;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "物料批次Obj")
    private ErpMaterialMgtMaterialBatchVO materialBatchObj;

    @ApiModelProperty(value = "包装明细列表")
    private List<ErpMaterialPackageDetailsRO> detailList;

    //-------------------------------------永久----------------------------------------
    @ApiModelProperty(value = "客户库存数")
    private BigDecimal customerQuantity;

    @ApiModelProperty(value = "客方货号")
    private String externalMaterialCode;

    @ApiModelProperty(value = "客户批次号")
    private String customerBatchNumber;


    @ApiModelProperty(value = "呆滞天数")
    private Integer daysInInventory;

    @ApiModelProperty(value = "物料Obj")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialObj;
    //    库存实时查询新增字段
    @ApiModelProperty(value = "库存面积")
    private BigDecimal inventoryArea;
    @ApiModelProperty(value = "平均单价")
    private BigDecimal unitPrice;
    @ApiModelProperty(value = "订单数量")
    private BigDecimal orderDataQuantity;
    @ApiModelProperty(value = "批次数")
    private BigDecimal batchQuantity;
    @ApiModelProperty(value = "纸质")
    private String basicPaper;
    @ApiModelProperty(value = "批次号")
    private String materialBatchNumber;

}
