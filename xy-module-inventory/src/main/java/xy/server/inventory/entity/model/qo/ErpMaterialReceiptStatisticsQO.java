package xy.server.inventory.entity.model.qo;

import com.xunyue.common.entity.model.qo.ErpStatisticsTotalQO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
public class ErpMaterialReceiptStatisticsQO extends ErpStatisticsTotalQO {
    @ApiModelProperty(value = "供应商简称")
    private String supplierShortName;

    @ApiModelProperty(value = "入库单号")
    private String inventoryReceiptNumbers;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(value = "工单号")
    public String workorderNumber;

    @ApiModelProperty(value = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;


    @ApiModelProperty(value = "开单开始日期")
    private LocalDate startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDate  endTime;


}
