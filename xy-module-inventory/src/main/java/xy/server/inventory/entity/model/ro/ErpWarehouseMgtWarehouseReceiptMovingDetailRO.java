package xy.server.inventory.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 仓单明细管理扩展仓单明细RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpWarehouseMgtWarehouseReceiptMovingDetailURO对象", description = "仓单明细管理扩展仓单明细")
public class ErpWarehouseMgtWarehouseReceiptMovingDetailRO {

    @ApiModelProperty(value = "PK")
    private String warehouseReceiptMovingDetailGuid;

    @ApiModelProperty(value = "仓单明细 _guid")
    private String warehouseReceiptDetailGuid;

    @NotBlank
    @ApiModelProperty(value = "目标仓位guid")
    private String movingWarehouseSpaceGuid;

}