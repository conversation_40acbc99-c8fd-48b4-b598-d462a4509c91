package xy.server.inventory.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 物料库存表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Builder
@ApiModel(value = "ErpInventoryMgtMaterialInventoryURO对象", description = "物料库存表")
public class ErpInventoryMgtMaterialInventoryRO {

    @ApiModelProperty(value = "物分类guid")
    @NotBlank(message = "物分类guid不能为空")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料_guid")
    @NotBlank(message = "物料guid不能为空")
    private String materialGuid;

    @NotNull(message = "数量不能为空")
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "物料批次库存列表")
    @Valid
    private List<ErpInventoryMgtMaterialBatchInventoryRO> batchInventoryList;

}