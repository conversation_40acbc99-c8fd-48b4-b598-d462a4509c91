package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.joda.time.LocalDate;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @data 2023/11/7 9:21
 * @apiNote 到货单-对账单待开列表明细QO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpFindArrivalDataVO对象")
public class ErpFindArrivalDataVO {

    @ApiModelProperty(value = "物料到货单号")
    private String arrivalReturnNumber;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "物料到货单据日期")
    private LocalDate arrivalReturnDate;

    @ApiModelProperty(value = "供应商简称")
    private String supplierShortName;

    @ApiModelProperty(value = "搜索码")
    private String searchCode;

    @ApiModelProperty(value = "供方货号")
    private String externalMaterialCode;

    @ApiModelProperty(value = "物料Obj")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "条码")
    private String materialGoodsCode;

    @ApiModelProperty(value = "实际到货数量")
    private BigDecimal actQuantity;

    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "采购单号")
    private String purchaseOrderNumber;

    @ApiModelProperty(value = "物料id")
    private String materialGuid;

}
