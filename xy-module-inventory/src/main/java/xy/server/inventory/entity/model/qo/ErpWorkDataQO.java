package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 发货通知\到货\退货数据表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpWorkDataQO对象", description = "")
public class ErpWorkDataQO {
    @ApiModelProperty(value = "开单开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "产品名称")
    private String  materialName;

    @ApiModelProperty(value = "来源单号")
    private String sourceWorkorderNumber;

    @ApiModelProperty(value = "客户PO")
    private String externalTrackingNumber;

    @ApiModelProperty(value = "是否忽略待开数量")
    private Boolean isIgnore;

    @ApiModelProperty(value = "工单号s")
    private List<String> workorderNumbers;

    @ApiModelProperty(value = "审核状态")
    private String  auditStatus;

    @ApiModelProperty(value = "客户名称/简称/编码")
    private String customer;

    @ApiModelProperty(value = "是否忽略外发")
    private Boolean isIgnoreOutsource ;

    @ApiModelProperty(value = "tenantGuid")
    private String tenantGuid;
}
