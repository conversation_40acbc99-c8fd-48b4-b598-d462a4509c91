package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
                                                                                                /**
* <p>
* 库存盘点扩展表QO
* </p>
*
* <AUTHOR>
* @since 2023-10-19
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryStocktakingReceiptQO对象", description = "库存盘点扩展表")
public class ErpInventoryMgtInventoryStocktakingReceiptQO{

        @ApiModelProperty(value = "库单guid")
    private String inventoryReceiptGuid;

        @ApiModelProperty(value = "PK")
    private String inventoryStocktakingReceiptGuid;

        @ApiModelProperty(value = "物分类guid")
    private String materialClassificationGuid;

        @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

        @ApiModelProperty(value = "扩展字段")
    private Object toJson;

        @ApiModelProperty(value = "创建人ID")
    private String creatorGuid;

            @ApiModelProperty(value = "创建人")
    private String creator;

            @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

        @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

            @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

            @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

}