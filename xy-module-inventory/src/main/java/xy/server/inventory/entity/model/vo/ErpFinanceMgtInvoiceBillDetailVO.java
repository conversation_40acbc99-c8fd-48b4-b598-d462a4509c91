package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 发票结算表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpFinanceMgtInvoiceBillDetailVO对象", description = "发票结算表")
public class ErpFinanceMgtInvoiceBillDetailVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String invoiceBillDetailGuid;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "来源")
    @XyTrans(dictionaryKey = "INVOICE_BILL_SOURCE", dictionaryValue = "sourceValue")
    private String sourceDictValue;

    @ApiModelProperty(value = "发票guid")
    private String invoiceBillGuid;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

    @ApiModelProperty(value = "自动调整金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "")
    private BigDecimal actAmount;

    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "订单id")
    private String orderGuid;

    @ApiModelProperty(value = "订单数据guid")
    private String orderDataGuid;

    @ApiModelProperty(value = "区分数据来源guid")
    private String sourceParentGuid;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(value = "来源数量")
    private String sourceQuantity;

    @ApiModelProperty(value = "")
    private BigDecimal statementBillPrice;

    @ApiModelProperty(value = "对账单数量")
    private BigDecimal statementBillQuantity;

    @ApiModelProperty(value = "物料guid")
    private String materialGuid;

    @ApiModelProperty(value = "物料Obj")
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "货期")
    private LocalDateTime deliveryDate;

    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;


    @ApiModelProperty(value = "成品销货明细guid")
    private String productSaleDetailGuid;

}
