package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CardboardPaperLossStatisticsVO对象")
public class CardboardPaperLossStatisticsVO {

        @ApiModelProperty(value = "班组名称")
        private String teamName;

        @ApiModelProperty(value = "搜索码")
        private String searchCode;

        @ApiModelProperty(value = "物料类别")
        private String materialCategory;

        @ApiModelProperty(value = "领料数量")
        private BigDecimal productCount;

        @ApiModelProperty(value = "退料数量")
        private BigDecimal returnCount;

        @ApiModelProperty(value = "实际数量")
        private BigDecimal actCount;

        @ApiModelProperty(value = "理论数量")
        private BigDecimal theoryCount;

        @ApiModelProperty(value = "损耗数量")
        private BigDecimal lossCount;

        @ApiModelProperty(value = "门幅")
        private BigDecimal widthOfFabric;

        @ApiModelProperty(value = "单价")
        private BigDecimal unitPrice;

        @ApiModelProperty(value = "总金额")
        private BigDecimal lossAmount;

        @ApiModelProperty(value = "货码")
        private String materialGoodsCode;

        @ApiModelProperty(value = "设备名称")
        private String equipmentName;

        @ApiModelProperty(value = "排度单号")
        private String planNumber;

        @ApiModelProperty(value = "领退料单id")
        private String rollPaperOutAndReturnGuid;
    }


