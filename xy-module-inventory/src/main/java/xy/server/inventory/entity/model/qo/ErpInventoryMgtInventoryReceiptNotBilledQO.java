package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @data 2023/10/19 16:10
 * @apiNote 出库单待开QO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryReceiptNotBilledQO对象", description = "出库单待开QO")
public class ErpInventoryMgtInventoryReceiptNotBilledQO {

    @ApiModelProperty(value = "库单属性")
    private Integer inventoryReceiptProperties;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "库单单号")
    private String inventoryReceiptNumbers;

    @ApiModelProperty(value = "开始日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptGuid;


    @ApiModelProperty(value = "出库申请单号")
    private String outboundApplicationNumber;


}
