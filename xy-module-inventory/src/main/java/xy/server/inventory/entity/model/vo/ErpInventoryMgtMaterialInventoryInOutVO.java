package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 物料库存表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtMaterialInventoryInOutVO对象")
public class ErpInventoryMgtMaterialInventoryInOutVO {

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "订单明细id")
    private String orderDataGuid;

    @ApiModelProperty(value = "物料 _guid")
    private String materialGuid;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal inventoryQuantity;

    @ApiModelProperty(value = "送货数量")
    private BigDecimal outPutQuantity;

    @ApiModelProperty(value = "入库数量")
    private BigDecimal inQuantity;

    @ApiModelProperty(value = "工单数量")
    private BigDecimal workQuantity;

}