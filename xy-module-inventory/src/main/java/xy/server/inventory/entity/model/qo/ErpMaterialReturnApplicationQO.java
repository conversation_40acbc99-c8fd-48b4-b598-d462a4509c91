package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 物料退货QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMaterialReturnApplicationQO对象", description = "物料退货")
public class ErpMaterialReturnApplicationQO {

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "工单类型_guid")
    private List<String> workorderTypeGuids;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "审核状态")
    private List<String> auditStatus;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "开始日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;


    @ApiModelProperty(value = "生产工单号")
    private String productionOrderNumberStr;

    @ApiModelProperty(value = "供应商（简称/全称/编码）")
    private String supplier;

    @ApiModelProperty(value = "结算供应商（简称/全称/编码）")
    private String settlementSupplier;

    @ApiModelProperty(value = "对账情况（多选）")
    private List<String> statementStates;

    @ApiModelProperty(value = "出库情况（多选）")
    private List<String> outboundStates;


    @ApiModelProperty(value = "工单guids")
    private List<String> workorderGuids;

}
