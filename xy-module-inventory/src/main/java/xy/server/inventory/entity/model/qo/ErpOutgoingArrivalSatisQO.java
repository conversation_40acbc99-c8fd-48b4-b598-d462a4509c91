package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpOutgoingArrivalSatisQO对象")
public class ErpOutgoingArrivalSatisQO {
    @ApiModelProperty(value = "加工商编码")
    private String processorCode;
    @ApiModelProperty(value = "加工商信息查询")
    private String processorKey;
    @ApiModelProperty(value = "加工商简称")
    private String processorShortName;
    @ApiModelProperty(value = "加工商全称")
    private String processorFullName;
    @ApiModelProperty(value = "外发订单单号")
    private String orderOutNumber;
    @ApiModelProperty(value = "外发申请单号")
    private String applicationOutNumber;
    @ApiModelProperty(value = "外发到货单号")
    private String arrivalOutNumber;
    @ApiModelProperty(value = "生产工单号")
    private String workorderNumber;
    @ApiModelProperty(value = "产品编码")
    private String productionCode;
    @ApiModelProperty(value = "产品名称")
    private String  productionName;
    @ApiModelProperty(value = "部件名称")
    private String partName;
    @ApiModelProperty(value = "工序名称")
    private String processName;
    @ApiModelProperty(value = "订单号")
    private String orderNumber;
    @ApiModelProperty(value = "客户PO")
    private String externalTrackingNumber;
    @ApiModelProperty(value = "客户产品编码")
    private String externalMaterialCode;
    @ApiModelProperty(value = "客户产品名称")
    private String externalMaterialName;
    @ApiModelProperty(value = "外发到货审核开始日期")
    private LocalDateTime arrivalOutAuditStartDate;
    @ApiModelProperty(value = "外发到货审核结束日期")
    private LocalDateTime arrivalOutAuditEndDate;
    @ApiModelProperty(value = "外发到货审核制单开始日期")
    private LocalDateTime arrivalOutStartDate;
    @ApiModelProperty(value = "外发到货审核制单结束日期")
    private LocalDateTime arrivalOutEndDate;
    @ApiModelProperty(value = "外发到货制单人")
    private String arrivalOutCreator;
    @ApiModelProperty(value = "外发到货审核人")
    private String arrivalOutAuditCreator;
    @ApiModelProperty(value = "对账情况")
    private List<String> statementStatus;
    @ApiModelProperty(value = "外发到货状态")
    private List<String> arrivalOutStatus;
    @ApiModelProperty(value = "入库情况")
    private List<String> inventoryStatus;
    @ApiModelProperty(value = "外发到货审核状态")
    private List<String> arrivalOutAuditStatus;


}
