package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* <p>
* 凭证表明细VO
* </p>
*
* <AUTHOR>
* @since 2023-11-22
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpFinanceMgtProofDetailVO对象", description = "凭证表明细")
public class ErpFinanceMgtProofDetailVO {
    @ApiModelProperty(value = "PK")
    private String proofDetailGuid;

    @ApiModelProperty(value = "凭证guid")
    private String proofGuid;

    @ApiModelProperty(value = "来源guid(工单，订单，工单与订单关联)")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "凭证内容(摘要)")
    private String proofContent;

    @ApiModelProperty(value = "科目guid")
    private String subjectGuid;

    @ApiModelProperty(value = "科目name")
    private String subjectName;

    @ApiModelProperty(value = "来源单号")
    private String number;

    @ApiModelProperty(value = "借方金额")
    private BigDecimal debitAmount;

    @ApiModelProperty(value = "贷方金额")
    private BigDecimal creditAmount;
}
