package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 物料批次RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpInventoryMaterialMgtMaterialBatchRO对象", description = "物料批次")
public class ErpInventoryMaterialMgtMaterialBatchRO extends BaseEntity {

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "PK")
    private String materialBatchGuid;

    @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

    //    @NotNull(message = "批次有效期不能为空")
    @ApiModelProperty(value = "批次有效期")
    private LocalDateTime materialPeriodOfValidity;

    /**
     * 物料批次号码(自动生成)，根据批次库存规则定义来生成。如果规则条件相同，批次号码会生成相同。
     */
    @ApiModelProperty(value = "物料批次号码")
    private String materialBatchNumber;

    //    @NotNull(message = "生产批次号不能为空")
    @ApiModelProperty(value = "生产批次号")
    private String materialProductionBatchNumber;

    //    @NotNull(message = "批次生产日期不能为空")
    @ApiModelProperty(value = "批次生产日期")
    private LocalDateTime materialDateOfManufacture;

    //    @NotNull(message = "当前批次数量不能为空")
    @ApiModelProperty(value = "当前批次数量")
    private BigDecimal batchQuantity;

    //    @NotNull(message = "采购单价(含税单价)不能为空")
    @ApiModelProperty(value = "采购单价(含税单价)")
    private BigDecimal purchasePriceIncludingTax;

    //    @NotNull(message = "采购成本(含税金额)不能为空")
    @ApiModelProperty(value = "采购成本(含税金额)")
    private BigDecimal purchaseCostIncludingTax;

    @ApiModelProperty(value = "库位guid")
    private String warehouseSpaceGuid;

    @ApiModelProperty(value = "采购不含税单价(不含税单价)内部成本")
    private BigDecimal purchasePriceWithoutTax;

    @ApiModelProperty(value = "采购成本(不含税金额)内部成本")
    private BigDecimal purchaseCostWithoutTax;

    /**
     * 这里是兼容旧数据
     */
    @ApiModelProperty(value = "包装明细列表")
    private ErpMaterialPackageRO detailList;
    /**
     * todo 后续需要全部改成List
     */
    @ApiModelProperty(value = "包装明细列表")
    private List<ErpMaterialPackageRO> detailCol = new ArrayList<>();

    @ApiModelProperty(value = "成品包装")
    private ErpProductionMgtPackageWorkOrderRO packageWorkOrderRO;

    @ApiModelProperty(value = "成品包装列表")
    private List<ErpProductionMgtPackageWorkOrderRO> packageWorkOrderList= new ArrayList<>();

    @ApiModelProperty(value = "包装明细列表")
    private List<ErpMaterialPackageDetailsRO> packageDetailsROList;

    @ApiModelProperty(value = "包装单属性（35：物料标签；101：包装单）")
    private Integer packageWorkorderProperties;

    @ApiModelProperty(value = "物料货号码(供应商提供或者自动生成)")
    private String materialGoodsCode;

    @ApiModelProperty(value = "管理批次")
    private String manageBatchNumber;

}
