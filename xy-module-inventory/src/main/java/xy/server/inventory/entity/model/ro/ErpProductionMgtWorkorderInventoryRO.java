package xy.server.inventory.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 工单表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpProductionMgtWorkorderURO对象", description = "工单表")
public class ErpProductionMgtWorkorderInventoryRO extends BaseEntity {

    @ApiModelProperty(value = "")
    private String workorderGuid;

    @NotNull(message = "工单号不能为空")
    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @NotNull(message = "顺序号(每一个层级有对应的顺序号)不能为空")
    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "工单类型_guid")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "工单状态(完成、故障、未开始)")
    private String workorderState;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "工单属性(1工单，2工序工单，3采购工单，4外发工单，5车间工单，6作业工单，维修工单，保养工单)")
    private Integer workorderProperties;

    @ApiModelProperty(value = "紧急程度(非常紧急，紧急，一般)")
    private String isUrgent;

    @ApiModelProperty(value = "FSC声明_guid")
    private String fscDeclarationGuid;

    @ApiModelProperty(value = "客户")
    private String customerGuid;

    @ApiModelProperty(value = "物料分类(成品)")
    private String materialGuid;

    @ApiModelProperty(value = "工艺类型guid")
    private String productionProcessesTypeGuid;

    @ApiModelProperty(value = "占比、比例(根据工单属性:1工单占比数，2是工序工单占工单占比，工序工单产出比)")
    private BigDecimal proportion;

    @ApiModelProperty(value = "工艺描述")
    private String productionProcessesDescription;

    @ApiModelProperty(value = "数量(根据工单属性:1工单成品数，2工序投入数量，3采购申请数量，4外发申请数)")
    private BigDecimal quantity;

    @ApiModelProperty(value = "使用库存数量")
    private BigDecimal usingInventoryQuantity;

    @ApiModelProperty(value = "总数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "单位（(根据工单属性:1工单成品单位，2工序加工单位）")
    private String unitGuid;

    @ApiModelProperty(value = "规格长(根据工单属性:1工单成品规格，2工序加工规格)")
    private BigDecimal specificationsLength;

    @ApiModelProperty(value = "规格宽(根据工单属性:1工单成品规格，2工序加工规格)")
    private BigDecimal specificationsWidth;

    @ApiModelProperty(value = "规格高(根据工单属性:1工单成品规格，2工序加工规格)")
    private BigDecimal specificationsHeight;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "要求交货期")
    private LocalDateTime requiredDeliveryTime;

    @ApiModelProperty(value = "是否显示(前端使用)")
    private Boolean isShow;

    @ApiModelProperty(value = "产品物料id")
    private String productMaterialGuid;

    @ApiModelProperty(value = "工艺流")
    private String processFlowchart;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "是否成品")
    private Boolean isProduct;

}
