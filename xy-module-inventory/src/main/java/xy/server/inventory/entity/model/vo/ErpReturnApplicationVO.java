package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.inventory.entity.ErpInventoryMgtNoticeArrivalReturnData;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;
import xy.server.work.entity.ErpProductionMgtWorkorderApplyReason;
import xy.server.work.entity.ErpProductionMgtWorkorderOrderData;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 退货申请VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpReturnApplicationVO对象", description = "退货申请")
public class ErpReturnApplicationVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "工单类型_guid")
    private String workorderTypeGuid;

    @XyTrans(dictionaryKey = "RETURN_TYPE", dictionaryValue = "workorderTypeGuid")
    private String workorderTypeDictValue;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @XyTrans(dictionaryKey = "RETURN_APPLICATION_SOURCE", dictionaryValue = "sourceValue")
    private String sourceDictValue;

    @ApiModelProperty(value = "工单状态(完成、故障、未开始)")
    private String workorderState;

    @XyTrans(dictionaryKey = "INCOMING_STATUS", dictionaryValue = "workorderState")
    private String workorderStateDictValue;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "工单属性(1工单，2工序工单，3采购工单，4外发工单，5车间工单，6作业工单，维修工单，保养工单)")
    private Integer workorderProperties;

    @ApiModelProperty(value = "客户")
    private String customerGuid;

    @ApiModelProperty(value = "物料分类(成品)")
    private String materialGuid;

    @ApiModelProperty(value = "数量(根据工单属性:1工单成品数，2工序投入数量，3采购申请数量，4外发申请数)")
    private BigDecimal quantity;

    @ApiModelProperty(value = "总数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "单位（(根据工单属性:1工单成品单位，2工序加工单位）")
    private String unitGuid;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "退货明细数据")
    private List<ErpReturnApplicationVO> returnApplicationList;

    @ApiModelProperty(value = "退货数据扩展表---相关联")
    private ErpInventoryMgtNoticeArrivalReturnData returnData;

    @ApiModelProperty(value = "工单-订单数据表---相关联")
    private ErpProductionMgtWorkorderOrderData workorderOrderData;

    @ApiModelProperty(value = "退货批次")
    private List<ErpProductionMgtWorkorderBatchDetailVO> batchDetailList;

    @ApiModelProperty(value = "退货原因")
    private ErpProductionMgtWorkorderApplyReason returnReason;


    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "订单数据guid")
    private String orderDataGuid;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(value = "订单单号")
    private String orderNumber;

    @ApiModelProperty(value = "来源数量")
    private BigDecimal sourceQuantity;

    @ApiModelProperty(value = "区分数据来源guid")
    private String sourceParentGuid;

    @ApiModelProperty(value = "物料Obj")
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "外部单号(例客户PO，采购送货单号)")
    private String externalTrackingNumber;

    @ApiModelProperty(value = "外部物料编码(例客户产品编码)")
    private String externalMaterialCode;

    @ApiModelProperty(value = "外部物料名称(例客户产品名称)")
    private String externalMaterialName;

    @ApiModelProperty("客户料号条码")
    public String externalMaterialBarCode;

    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "不含税报价单价")
    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty(value = "货期")
    private LocalDateTime deliveryDate;

    @ApiModelProperty(value = "待出库或入库数量")
    private BigDecimal notInventoryQuantity;

    @ApiModelProperty(value = "生产工单号")
    private String productionOrderNumberStr;

    @ApiModelProperty(value = "供应商简称")
    private String supplierShortName;

    @ApiModelProperty(value = "结算供应商简称")
    private String settlementSupplierShortName;

    @ApiModelProperty(value = "客户简称")
    private String customerShortName;

    @ApiModelProperty(value = "结算客户简称")
    private String settlementCustomerShortName;

    @ApiModelProperty(value = "面积")
    private String cartonArea;

    @ApiModelProperty(value = "金额")
    private String amount;
}
