package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024-07-09 10:30
 * <p>
 * 原纸领退料
 * </p>
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpRollPaperOutAndReturnRO", description = "原纸领退料")
public class ErpRollPaperOutAndReturnRO extends BaseEntity {

    @ApiModelProperty(value = "")
    private String rollPaperOutAndReturnGuid;

    @ApiModelProperty(value = "原纸领退料单")
    private String rollPaperOutAndReturnNumber;

    @ApiModelProperty(value = "排度单号")
    private String planNumber;

    @ApiModelProperty(value = "班组")
    private String teamGuid;

    @ApiModelProperty(value = "机台")
    private String equipmentGuid;

    @ApiModelProperty(value = "用于")
    private String useFor;

    @ApiModelProperty(value = "物料标签")
    private String materialGoodsCode;

    @ApiModelProperty(value = "物料guid")
    private String materialGuid;

    @ApiModelProperty(value = "领料数量")
    private BigDecimal productCount;

    @ApiModelProperty(value = "退料数量")
    private BigDecimal returnCount;

    @ApiModelProperty(value = "操作类型")
    private Integer type;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "库单id")
    private String inventoryReceiptGuid;

    @ApiModelProperty(value = "是否辅料")
    private Boolean isItMaterial;
}
