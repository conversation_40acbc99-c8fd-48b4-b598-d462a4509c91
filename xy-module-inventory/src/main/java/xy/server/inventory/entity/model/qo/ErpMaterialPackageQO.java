package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
* <p>
* QO
* </p>
*
* <AUTHOR>
* @since 2024-01-23
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMaterialPackageQO对象", description = "")
public class ErpMaterialPackageQO {

    @ApiModelProperty(value = "开单开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "物料到货单状态")
    private List<String> auditStatus;

    @ApiModelProperty(value = "到货单号")
    private String workorderNumber;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料包装码")
    private String materialGoodsCode;

    @ApiModelProperty(value = "物料到货明细id")
    private List<String> workGuidS;

    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;

    @ApiModelProperty(value = "来源")
    private Integer sourceValue;

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

}
