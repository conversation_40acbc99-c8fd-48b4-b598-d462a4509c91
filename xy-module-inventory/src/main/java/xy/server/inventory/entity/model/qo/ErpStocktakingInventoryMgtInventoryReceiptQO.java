package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
* <p>
* 库单管理QO
* </p>
*
* <AUTHOR>
* @since 2023-10-19
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpStocktakingInventoryMgtInventoryReceiptQO对象", description = "库单管理")
public class ErpStocktakingInventoryMgtInventoryReceiptQO {
    @ApiModelProperty(value = "开单开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "仓位")
    private List<String> warehouseSpaceGuidS;

    @ApiModelProperty(value = "盘点库单号")
    private String inventoryReceiptNumbers;

    @ApiModelProperty(value = "物分类guid")
    private List<String> materialClassificationGuidS;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @NotNull(message = "库单类型不能为空")
    @ApiModelProperty(value = "库单类型")
    private Integer inventoryReceiptProperties;

    @ApiModelProperty(value = "物分类guid")
    private List<String> customClassificationGuidS;

    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;

    @ApiModelProperty(value = "动态路由菜单")
    private String dynamicComponent;

}