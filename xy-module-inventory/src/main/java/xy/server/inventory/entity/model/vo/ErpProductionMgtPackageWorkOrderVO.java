package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 包装单管理VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpProductionMgtPackageWorkOrderVO对象", description = "包装单管理VO")
public class ErpProductionMgtPackageWorkOrderVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "包装类型")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "包装类型字典值")
    @XyTrans(dictionaryKey = "PACKAGE_TYPE", dictionaryValue = "workorderTypeGuid")
    private String workorderTypeDictValue;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核状态字典值")
    @XyTrans(dictionaryKey = "AUDIT_STATUS", dictionaryValue = "auditStatus")
    private String auditStatusDictValue;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "工单属性(1工单，2工序工单，3采购工单，4外发工单，5车间工单，6作业工单，维修工单，保养工单)")
    private Integer workorderProperties;

    @ApiModelProperty(value = "包装方式")
    private String productionProcessesDescription;

    @ApiModelProperty(value = "每包数量")
    private BigDecimal proportion;

    @ApiModelProperty(value = "包装总数")
    private BigDecimal quantity;

    @ApiModelProperty(value = "包数")
    private BigDecimal outputQuantity;

    @ApiModelProperty(value = "散装总数")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "入库情况")
    private String incomingStatus;

    @ApiModelProperty(value = "入库情况字典值")
    @XyTrans(dictionaryKey = "INCOMING_STATUS", dictionaryValue = "incomingStatus")
    private String incomingStatusDictValue;

    @ApiModelProperty(value = "包装明细列表")
    @XyTransCycle
    private List<ErpMaterialMgtMaterialBatchGoodsVO> detailList;

}
