package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 物料出库申请表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMaterialMgtMaterialOutboundRequisitionQO对象", description = "物料出库申请表")
public class ErpMaterialMgtMaterialOutboundRequisitionQO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "工单guid集合")
    private List<String> workorderGuids;

    @ApiModelProperty(value = "申请类型")
    private List<String> workorderTypeGuids;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(value = "工单属性")
    private Integer workorderProperties;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;


    @ApiModelProperty(value = "来源的来源值")
    private Integer sourceOfSourceValue;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料类型")
    private List<String> materialType;

    @ApiModelProperty(value = "开始日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "审核状态")
    private List<String> auditStatus;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;

    @ApiModelProperty("来源guid")
    private List<String> sourceGuid;

    @ApiModelProperty("来源值集合")
    private List<Integer> sourceValues;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("是否补货")
    private Boolean isIgnore;

}
