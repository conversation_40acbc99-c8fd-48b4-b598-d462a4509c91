package xy.server.inventory.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 送货计划excelRO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpFinishedProductOutboundNotificationCompletionRO对象", description = "送货计划excel")
public class ErpFinishedProductOutboundNotificationCompletionRO {

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "客户料号")
    private String externalMaterialCode;

    @ApiModelProperty(value = "产品名称")
    private String materialName;

    @ApiModelProperty("产品编码")
    public String materialCode;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "交货日期")
    private String requiredDeliveryTime;

    @ApiModelProperty(value = "备注（送货）")
    private String description;

    @ApiModelProperty(value = "送货仓位")
    private String settlementCustomerName;

    @ApiModelProperty(value = "版本号")
    private String boxVersion;

    @ApiModelProperty("客户PO")
    public String externalTrackingNumber;

    @ApiModelProperty(value = "缺少的数量")
    private BigDecimal lackQuantity;

    @ApiModelProperty(value = "缺少的数量")
    private BigDecimal actQuantity;

    @ApiModelProperty(value = "开单开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "物料guid")
    private String materialGuid;

    @ApiModelProperty(value = "订单明细guid")
    private String orderDataGuid;

    @ApiModelProperty(value = "结算客户")
    private String settlementCustomerOrSupplierGuid;

    @ApiModelProperty(value = "送货客户")
    private String receiptCustomerOrSupplierGuid;

    @ApiModelProperty(value = "客户")
    private String customerGuid;

    @ApiModelProperty(value = "卸货仓库")
    private String address;

}
