package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 成品出库通知单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpFinishedSaleBackQO对象", description = "成品出库通知单")
public class ErpFinishedSaleBackQO {

    @ApiModelProperty(value = "销货主表id")
    private List<String> list;

    @ApiModelProperty(value = "回签状态")
    private String backState;


}
