package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* <p>
* 库单明细管理扩展明细表QO
* </p>
*
* <AUTHOR>
* @since 2023-10-09
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryReceiptDetailInoutstockQO对象", description = "库单明细管理扩展明细表")
public class ErpStockInventoryMgtInventoryReceiptDetailInoutstockQO {

        @ApiModelProperty(value = "库单明细管理guid")
    private String inventoryReceiptDetailGuid;

        @ApiModelProperty(value = "PK")
    private String inventoryReceiptDetailInstockGuid;

            @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private String quotationUnitPriceIncludingTax;

            @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

            @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

            @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

        @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

        @ApiModelProperty(value = "扩展字段")
    private Object toJson;

        @ApiModelProperty(value = "创建人ID")
    private String creatorGuid;

            @ApiModelProperty(value = "创建人")
    private String creator;

            @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

        @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

            @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

            @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

}
