package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* <p>
* 库单数据异常处理表RO
* </p>
*
* <AUTHOR>
* @since 2024-06-06
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryReceiptBatchDetailExceptionRO对象", description = "库单数据异常处理表")
public class ErpInventoryMgtInventoryReceiptBatchDetailExceptionRO extends BaseEntity {

        @ApiModelProperty(value = "PK")
    private String inventoryDetaExceptionGuid;

        @ApiModelProperty(value = "订单数据guid")
    private String inventoryReceiptBatchDetail;

        @ApiModelProperty(value = "异常原因")
    private String exceptionReason;

}
