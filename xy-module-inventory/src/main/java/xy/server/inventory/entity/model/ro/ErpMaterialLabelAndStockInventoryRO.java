package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
* <p>
* 工单订单数据表RO
* </p>
*
* <AUTHOR>
* @since 2024-04-25
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMaterialLabelAndStockInventoryRO对象", description = "工单订单数据表")
public class ErpMaterialLabelAndStockInventoryRO extends BaseEntity {

        @ApiModelProperty(value = "PK")
    private String abelAndInventoryGuid;

        @NotNull(message = "库存批次id不能为空")
        @ApiModelProperty(value = "库存批次id")
    private String inventoryReceiptBatchDetail;

        @ApiModelProperty(value = "物料标签包guid")
    private String materialBatchGoodsGuid;

}
