package xy.server.inventory.entity.model.ro;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.inventory.entity.ErpInventoryMgtNoticeArrivalReturnData;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;
import xy.server.work.entity.ErpProductionMgtWorkorderApplyReason;
import xy.server.work.entity.ErpProductionMgtWorkorderOrderData;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 物料退货RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpMaterialReturnApplicationRO对象", description = "物料退货")
public class ErpMaterialReturnApplicationRO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "工单类型_guid")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "工单状态(完成、故障、未开始)")
    private String workorderState;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "工单属性")
    private Integer workorderProperties;

    @ApiModelProperty(value = "物料分类(成品)")
    private String materialGuid;

    @ApiModelProperty(value = "数量(根据工单属性:1工单成品数，2工序投入数量，3采购申请数量，4外发申请数)")
    private BigDecimal quantity;

    @ApiModelProperty(value = "总数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "退货明细数据")
    private List<ErpMaterialReturnApplicationRO> returnApplicationList;

    @ApiModelProperty(value = "形态改变子数据")
    private List<ErpMaterialReturnApplicationRO> materialChangeList;

    @ApiModelProperty(value = "退货原因")
    private ErpProductionMgtWorkorderApplyReason returnReason;

    @ApiModelProperty(value = "退货数据扩展表---相关联")
    private ErpInventoryMgtNoticeArrivalReturnData returnData;

    @ApiModelProperty(value = "工单---订单数据表---明细")
    private ErpProductionMgtWorkorderOrderData workorderOrderData;

    @ApiModelProperty(value = "退货批次")
    private List<ErpProductionMgtWorkorderBatchDetailRO> batchDetailList;

    @ApiModelProperty("物料信息")
    private ErpMaterialMgtMaterialVO materialObj;

    /**
     * 关联表数据---数据来源guid
     */
    @ApiModelProperty(value = "订单数据guid")
    private String orderDataGuid;

    @ApiModelProperty(value = "订单数据guid")
    private BigDecimal sourceQuantity;

    @ApiModelProperty(value = "是否辅料")
    private Boolean isItMaterial;


}
