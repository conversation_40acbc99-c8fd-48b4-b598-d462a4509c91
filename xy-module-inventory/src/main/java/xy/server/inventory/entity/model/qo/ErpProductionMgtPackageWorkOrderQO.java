package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpProductionMgtWorkorderQO对象", description = "")
public class ErpProductionMgtPackageWorkOrderQO {

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "工单类型_guid")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "开始日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "审核状态")
    private List<String> auditStatus;

    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;

    @ApiModelProperty(value = "包装编码")
    private String materialGoodsCode;

    @ApiModelProperty(value = "包装编码")
    private List<String> materialGoodsCodes;


}
