package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2023/11/23 16:16
 * @apiNote 包装单-待开列表QO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpPackageWorkOrderNotBilledQO", description = "包装单-待开列表QO")
public class ErpPackageWorkOrderNotBilledQO {

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @NotEmpty
    @ApiModelProperty(value = "数据来源")
    private List<String> sourceValues;

    @ApiModelProperty(value = "物料name")
    private String materialName;

    @ApiModelProperty(value = "忽略已经开入库通知")
    private Boolean ignoreInStock;
}
