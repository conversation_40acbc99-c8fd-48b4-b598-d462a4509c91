package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpTransBusinessMgtInventoryVO对象", description = "订单表")
public class ErpTransBusinessMgtInventoryVO {

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "库单单号")
    private String inventoryReceiptNumbers;

    @ApiModelProperty(value = "外部单号(例客户PO，采购送货单号)")
    private String externalTrackingNumber;

    @ApiModelProperty(value = "成品出库单数量")
    private BigDecimal finishQuantity;

    @ApiModelProperty(value = "订单数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "客户/供应商guid")
    private String customerOrSupplierGuid;

    @ApiModelProperty(value = "客户/供应商名称")
    private String customerOrSupplierName;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @ApiModelProperty(value = "手机")
    private String mobilephone;

    @ApiModelProperty(value = "物料GUID(成品、物料)")
    private String materialGuid;

    @ApiModelProperty(value = "行政区域 _guid")
    private String administrativeAreaGuid;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "物料Obj")
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "物流运输明细guid")
    private String transportationLogisticsDataGuid;

    @ApiModelProperty(value = "数据来源是成品出库单的装舱单明细guid")
    private String packingListDetailInventory;

    @ApiModelProperty(value = "数据来源是订单的装舱单明细guid")
    private String packingListDetailOrder;

    @ApiModelProperty(value = "外部物料编码(例客户产品编码)")
    private String externalMaterialCode;

    @ApiModelProperty(value = "外部物料名称(例客户产品名称)")
    private String externalMaterialName;

    @ApiModelProperty(value = "货期")
    private LocalDateTime deliveryDate;

    @ApiModelProperty(value = "订单数据guid")
    private String orderDataGuid;

}