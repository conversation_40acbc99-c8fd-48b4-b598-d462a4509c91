package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/19
 * @description
 **/
@Data
public class MaterialRequisitionByOrderDataGuidsQO {


    @ApiModelProperty("物料类型")
    private String materialType;

    @ApiModelProperty("等于的物料分类guid")
    private String eqMaterialClassificationGuid;

    @ApiModelProperty("不等于的物料分类guid")
    private String notEqMaterialClassificationGuid;

    @ApiModelProperty("orderDataGuids")
    private List<String> orderDataGuids;

}
