package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.basic.entity.model.vo.ErpBusinessMgtOtherExpensesVO;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
/**
 * <AUTHOR>
 * @data 2023/10/7
 * @apiNote 成品销货
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpFinishedProductSalesVO对象", description = "成品销货VO")
public class ErpFinishedProductSalesVO extends BaseEntity {

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "")
    private String noticeArrivalReturnDataGuid;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "申请类型")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "申请类型（字典值）")
    @XyTrans(dictionaryKey = "FINISH_SALES_TYPE", dictionaryValue = "workorderTypeGuid")
    private String workorderTypeDictValue;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "来源")
//    @XyTrans(dictionaryKey = "FINISHED_PRODUCT_OUTBOUND_NOTIFICATION_SOURCE", dictionaryValue = "sourceValue")
    @XyTrans(dictionaryKey = "FINISH_SALES_SOURCE", dictionaryValue = "sourceValue")
    private String sourceDictValue;

    @ApiModelProperty(value = "工单状态(出库情况)")
    private String workorderState;

    @ApiModelProperty(value = "工单状态(出库情况)")
    @XyTrans(dictionaryKey = "OUTBOUND_STATE", dictionaryValue = "workorderState")
    private String workorderStateDictValue;

    @ApiModelProperty(value = "紧急程度(非常紧急，紧急，一般)")
    private String isUrgent;

    @ApiModelProperty(value = "物料guid")
    private String materialGuid;

    @ApiModelProperty("客户料号条码")
    public String externalMaterialBarCode;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "备用数量")
    private BigDecimal spareQuantity;

    @ApiModelProperty(value = "赠送数量")
    private BigDecimal giftQuantity;

    @ApiModelProperty(value = "总数量数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "单位guid")
    private String unitGuid;

    @ApiModelProperty(value = "客户guid")
    private String customerGuid;

    @ApiModelProperty(value = "备用字段")
    private Object toJson;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核状态字典值")
    @XyTrans(dictionaryKey = "AUDIT_STATUS", dictionaryValue = "auditStatus")
    private String auditStatusDictValue;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;


    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty(value = "结算单价（默认与含税单价相等），写入库存价格字段")
    private BigDecimal settlementUnitPrice;

    @ApiModelProperty(value = "结算金额（默认与含税总金额相等），写入库存价格字段")
    private BigDecimal settlementTotalAmount;

    @ApiModelProperty(value = "总费用(含税)")
    private BigDecimal totalExpensesIncludingTax;

    @ApiModelProperty(value = "总费用(不含税)")
    private BigDecimal totalExpensesWithoutTax;

    @ApiModelProperty(value = "本币总费用(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalExpenses;

    @ApiModelProperty(value = "结算费用（默认与含税总金额相等)")
    private BigDecimal settlementTotalExpenses;

    @ApiModelProperty(value = "保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)")
    private Integer decimalMethod;

    @ApiModelProperty(value = "结算单价保留小数位(默认9位，可手动变更)")
    private Integer settlementUnitPriceKeepDecimalPlace;

    @ApiModelProperty(value = "结算金额保留小数位(默认2位，可手动变更)")
    private Integer settlementTotalAmountKeepDecimalPlace;

    @ApiModelProperty(value = "订单属性(1业务合同，2采购订单，3外发订单)")
    private Integer orderProperties;

    @ApiModelProperty(value = "税率guid")
    private String invoiceTypeTaxRateGuid;

    @ApiModelProperty(value = "税率名称")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "汇率guid")
    private String currencyExchangeRateGuid;

    @ApiModelProperty(value = "汇率名称")
    private String exchangeRate;

    @ApiModelProperty(value = "币种ID")
    private String currencyGuid;

    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    @ApiModelProperty(value = "是否本币")
    private Boolean isLocalCurrency;

    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;


    @ApiModelProperty(value = "本币单价(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyUnitPrice;

    @ApiModelProperty(value = "本币金额(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalAmount;


    /**
     * 关联表数据
     */

    @ApiModelProperty(value = "订单id")
    private String orderGuid;

    @ApiModelProperty(value = "订单数据guid")
    private String orderDataGuid;

    @ApiModelProperty(value = "待出库数")
    private BigDecimal notOutboundQuantity;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(value = "来源单号")
    private String sourceWorkorderNumber;

    @ApiModelProperty(value = "来源数量")
    private String sourceQuantity;

    @ApiModelProperty(value = "区分数据来源guid")
    private String sourceParentGuid;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户简称")
    private String customerShortName;

    @ApiModelProperty("客户全称")
    private String customerFullName;

    @ApiModelProperty(value = "物料Obj")
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "外部单号(例客户PO，采购送货单号)")
    private String externalTrackingNumber;

    @ApiModelProperty(value = "外部物料编码(例客户产品编码)")
    private String externalMaterialCode;

    @ApiModelProperty(value = "外部物料名称(例客户产品名称)")
    private String externalMaterialName;

    @ApiModelProperty(value = "货期")
    private LocalDateTime deliveryDate;

    @ApiModelProperty(value = "成品出库通知单明细")
    @XyTransCycle
    private List<ErpFinishedProductSalesVO> finishedProductOutboundNotificationDetailList;

    @ApiModelProperty(value = "计费方案id")
    private String costSchemeGuid;

    @ApiModelProperty(value = "计费方案名称")
    private String schemeName;

    @ApiModelProperty(value = "结算客户/供应商guid(如A客户落单，送B客户，结算找C客户)")
    private String settlementCustomerOrSupplierGuid;

    /**
     * 结算供应商/客户
     */
    @ApiModelProperty(value = "结算供应商name")
    private String settlementCustomerOrSupplierName;


    /**
     * 送货供应商/客户
     */
    @ApiModelProperty(value = "送货供应商guid")
    private String receiptCustomerOrSupplierGuid;

    @ApiModelProperty(value = "送货供应商")
    private String receiptCustomerOrSupplierName;


    @ApiModelProperty(value = "其他费用")
    List<ErpBusinessMgtOtherExpensesVO> otherExpensesList;

    @ApiModelProperty(value = "已删除文件")
    List<String> isdelectFile;

    @ApiModelProperty(value = "开票状态")
    private String invoiceStatus;

    @ApiModelProperty(value = "开票状态字典")
    @XyTrans(dictionaryKey = "INVOICE_STATUS", dictionaryValue = "invoiceStatus")
    private String invoiceStatusName;

    @ApiModelProperty(value = "对账情况")
    private String statementState;

    @ApiModelProperty(value = "对账情况字典")
    @XyTrans(dictionaryKey = "RECONCILIATION", dictionaryValue = "statementState")
    private String statementStateDictValue;

    @ApiModelProperty(value = "业务订单号")
    private String businessOrderNumber;

    @ApiModelProperty(value = "生产工单号")
    private String productWorkorderNumber;

    @ApiModelProperty("销货单toJson")
    private Object saleToJson;
    @ApiModelProperty("生产单号")
    private String productionOrderNumberStr;

    @ApiModelProperty("工单号")
    private String sonWorkorderNumber;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "车次")
    private String vehicleBatch;

    @ApiModelProperty(value = "出库单id")
    private String inventoryReceiptGuid;

    @ApiModelProperty(value = "回签状态")
    private String backState;

    @XyTrans(dictionaryKey = "INVENTORY_FINISH-SALES_VISARETURN-STATUS",dictionaryValue = "backState")
    @ApiModelProperty(value = "回签状态")
    private String backStateName;

    @ApiModelProperty(value = "回签时间")
    private LocalDateTime backTime;

}
