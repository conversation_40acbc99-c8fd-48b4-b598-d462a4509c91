package xy.server.inventory.entity.model.vo;

import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 物料库存表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtMaterialInventoryVO对象", description = "物料库存表")
public class ErpInventoryMgtMaterialInventoryVO {

    @ApiModelProperty(value = "PK")
    private String materialInventoryGuid;

    @ApiModelProperty(value = "物分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料 _guid")
    private String materialGuid;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal inventoryQuantity;

    @ApiModelProperty(value = "计划库存数量")
    private BigDecimal planInventoryQuantity;

    @ApiModelProperty(value = "库存总金额(净价，不包含任何税率，汇率等)")
    private BigDecimal inventoryTotalamount;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "规格类型")
    private String specificationTypeStr;

    @ApiModelProperty(value = "单位")
    private String unitName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料类型name")
    private String materialClassificationName;

    @ApiModelProperty(value = "批次库存明细")
    private List<ErpInventoryMgtMaterialBatchInventoryVO> batchInventoryList;

    @ApiModelProperty(value = "是否忽略为零的，true忽略，false不忽略")
    private Boolean isIgnore;

    @ApiModelProperty(value = "销售总金额")
    private BigDecimal totalAmount;


    @ApiModelProperty(value = "物料Obj")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "呆滞天数")
    private int inactiveDays;

    @ApiModelProperty(value = "实际呆滞天数")
    private long practicalInactiveDays;

}
