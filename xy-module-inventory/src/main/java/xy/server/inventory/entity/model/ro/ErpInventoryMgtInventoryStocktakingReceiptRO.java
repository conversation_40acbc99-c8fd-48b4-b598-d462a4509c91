package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 库存盘点扩展表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryStocktakingReceiptURO对象", description = "库存盘点扩展表")
public class ErpInventoryMgtInventoryStocktakingReceiptRO extends BaseEntity {

    @ApiModelProperty(value = "库单guid")
    private String inventoryReceiptGuid;

    @ApiModelProperty(value = "PK")
    private String inventoryStocktakingReceiptGuid;

    @ApiModelProperty(value = "物分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "客户分类guid")
    private String customerClassificationGuid;

    @ApiModelProperty(value = "客户guid")
    private String customerGuid;

}
