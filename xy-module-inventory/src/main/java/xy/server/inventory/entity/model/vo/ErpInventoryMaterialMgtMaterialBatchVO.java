package xy.server.inventory.entity.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.mybatisplus.BaseEntity;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.inventory.entity.model.ro.ErpMaterialPackageDetailsRO;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 物料批次RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMaterialMgtMaterialBatchVO对象", description = "物料批次")
public class ErpInventoryMaterialMgtMaterialBatchVO extends BaseEntity {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "pk")
    private String inventoryReceiptBatchDetail;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "库位guid")
    private String warehouseSpaceGuid;

    @ApiModelProperty(value = "库位name")
    private String warehouseSpaceName;

    @ApiModelProperty(value = "PK")
    private String materialBatchGuid;

    @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "生产批次号")
    private String materialProductionBatchNumber;

    @ApiModelProperty(value = "批次生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime materialDateOfManufacture;

    @ApiModelProperty(value = "批次有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime materialPeriodOfValidity;

    @NotNull(message = "当前批次数量不能为空")
    @ApiModelProperty(value = "当前批次数量")
    private BigDecimal batchQuantity;

    @ApiModelProperty(value = "采购不含税单价(不含税单价)内部成本")
    private BigDecimal purchasePriceWithoutTax;

    @ApiModelProperty(value = "采购成本(不含税金额)内部成本")
    private BigDecimal purchaseCostWithoutTax;

    @ApiModelProperty(value = "采购单价(含税单价)")
    private BigDecimal purchasePriceIncludingTax;

    @ApiModelProperty(value = "采购成本(含税金额)")
    private BigDecimal purchaseCostIncludingTax;

    @ApiModelProperty(value = "生产成本(不含税金额)内部成本")
    private BigDecimal productionCost;

    @ApiModelProperty(value = "运输成本(不含税金额)内部成本")
    private BigDecimal transportCost;

    @ApiModelProperty(value = "仓储成本(不含税金额)内部成本")
    private BigDecimal storageCost;

    /**
     * 物料批次号码(自动生成)，根据批次库存规则定义来生成。如果规则条件相同，批次号码会生成相同。
     */
    @ApiModelProperty(value = "物料批次号码")
    private String materialBatchNumber;

    @ApiModelProperty(value = "包装明细列表")
    private List<ErpMaterialPackageDetailsRO> packageDetailsROList;

    @ApiModelProperty(value = "包装明细列表")
    private ErpMaterialPackageVO detailList ;

    @ApiModelProperty(value = "workGuid")
    private String workGuid;

    @ApiModelProperty(value = "业务订单号")
    private String businessOrderNumber;

    @ApiModelProperty(value = "业务订单明细guid")
    private String businessOrderDataGuid;

    @ApiModelProperty(value = "批次货码列表")
    @XyTransCycle
    private List<ErpMaterialMgtMaterialBatchGoodsVO> materialBatchGoodsList;

    @ApiModelProperty(value = "物料入库明细id")
    private String inventoryReceiptDetailGuid;

    /**
     * 顺序号(每一个层级有对应的顺序号)
     */
    @TableField("serial_number")
    private Integer serialNumber;


}
