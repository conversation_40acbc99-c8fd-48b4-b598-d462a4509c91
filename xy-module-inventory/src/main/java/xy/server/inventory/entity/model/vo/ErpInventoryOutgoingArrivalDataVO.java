package xy.server.inventory.entity.model.vo;

import com.xunyue.config.mybatisplus.BaseEntity;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.basic.entity.model.vo.ErpBusinessMgtOtherExpensesVO;
import xy.server.purchase.entity.model.vo.ErpOutgoingArrivalVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
* <p>
* 工单表RO
* </p>
*
* <AUTHOR>
* @since 2023-09-15
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpInventoryOutgoingArrivalDataVO对象", description = "工单表")
public class ErpInventoryOutgoingArrivalDataVO extends BaseEntity {

    @ApiModelProperty(value = "外发订单的工单id")
    private String workorderGuid;

    @ApiModelProperty(value = "外发订单的工单id")
    private String sourceWorkorderGuid;

    @ApiModelProperty(value = "外发到货单主表id")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "外发到货单数据表id")
    private String sonWorkOrderGuid;

    @ApiModelProperty(value = "工序工单号")
    private String processNumber;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "外发单号")
    private String orderNumber;

    @ApiModelProperty(value = "外发订单id")
    private String orderDataGuid;
    /**
     * 根据物料id查询获取到的印件名称，
     * 以及部件名称，
     * 部件名称是当前id对于的物料名称，印件名称是他的上级物料id获得的
     */
    @ApiModelProperty(value = "物料分类(成品)")
    private String materialGuid;
    /**
     * 是物料分类对应物料的名称
     */
    @ApiModelProperty(value = "部件名称")
    private String partName;

    @ApiModelProperty(value = "产品物料id")
    public String productMaterialGuid;

    @ApiModelProperty(value = "产品名称")
    private String productName;
    /**
     * 根据工序类型id获取到，工序这个字段的名称
     * 根据工序id能够一对多获取到工序项的名称，根据工序项的id获取到工序参数的值然后拼接起来，得到工序参数字段
     */
    @ApiModelProperty(value = "工序类型guid")
    private String productionProcessesTypeGuid;
    /**
     * 根据工序类型id获取对应的名称
     */
    @ApiModelProperty(value = "工序")
    private String processName;
    /**
     * 根据工序类型id查询对应的参数项集合，然后组合相关的参数值组合起来
     */
    @ApiModelProperty(value = "工序参数")
    private String processParameters;

    @ApiModelProperty(value = "来源数量")
    private BigDecimal sourceQuantity;
    /**
     * 收货数量
     */
    @ApiModelProperty(value = "到货数量")
    private BigDecimal quantity;

    /**
     * 要求交期
     */
    @ApiModelProperty(value = "外发订单的要求交期")
    private LocalDateTime requiredDeliveryTime;

    @ApiModelProperty(value = "产出单位")
    private String unitGuid;

    @ApiModelProperty(value = "外发订单描述")
    private String description;

    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty(value = "入库状态(完成、故障、未开始)")
    private String workorderState;

    @ApiModelProperty(value = "产出工单数据")
    @XyTransCycle
    private List<ErpOutgoingApplicationInventoryVO> sonList;

    @ApiModelProperty(value = "加工商id")
    private String supplierGuid;

    @ApiModelProperty(value = "工序工单的扩展表id")
    private String workorderDataProcessGuid;

    @ApiModelProperty(value = "外发订单相关数据")
    private ErpOutgoingArrivalVO outgoingOrderVO;

    @ApiModelProperty(value = "外发订单相关数据")
    private List<ErpOutgoingArrivalVO> outgoingOrderVOs;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "本单据状态(0-新建，1-正常处理，2-正常结束，3-异常结束)")
    private String currentStatus;

    @ApiModelProperty(value = "计费方案id")
    private String costSchemeGuid;

    @ApiModelProperty(value = "计费方案名称")
    private String schemeName;

    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "本币单价(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyUnitPrice;

    @ApiModelProperty(value = "本币金额(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalAmount;

    @ApiModelProperty(value = "结算单价（默认与含税单价相等），写入库存价格字段")
    private BigDecimal settlementUnitPrice;

    @ApiModelProperty(value = "结算金额（默认与含税总金额相等），写入库存价格字段")
    private BigDecimal settlementTotalAmount;

    /**
     * 规格长(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @ApiModelProperty(value = "规格长")
    private BigDecimal specificationsLength;
    /**
     * 规格宽(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @ApiModelProperty(value = "规格宽")
    private BigDecimal specificationsWidth;
    /**
     * 规格高(根据工单属性:1工单成品规格，2工序加工规格)
     */
    @ApiModelProperty(value = "规格高")
    private BigDecimal specificationsHeight;

    @ApiModelProperty(value = "其他费用")
    private List<ErpBusinessMgtOtherExpensesVO> otherExpensesList;

    @ApiModelProperty(value = "已删除文件")
    private List<String> isdelectFile;

    @ApiModelProperty(value = "结算数量")
    private BigDecimal settlementQuantity;

    /**
     * 赠品数量
     */
    @ApiModelProperty(value = "赠品数量")
    private BigDecimal giftQuantity;

}
