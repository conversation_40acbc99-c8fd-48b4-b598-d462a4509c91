package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>
 * 库存盘点批次表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryStocktakingBatchURO对象", description = "库存盘点批次表")
public class ErpInventoryMgtInventoryStocktakingBatchRO extends BaseEntity {

    @ApiModelProperty(value = "库单guid")
    private String inventoryReceiptGuid;

    @ApiModelProperty(value = "PK")
    private String inventoryStocktakingBatch;

    @ApiModelProperty(value = "批次库存guid")
    private String inventoryMaterialBatchInventoryGuid;

    @ApiModelProperty(value = "物料批次guid")
    private String materialBatchGuid;

    @ApiModelProperty(value = "物分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

    @NotNull(message = "批次库存数量不能为空")
    @ApiModelProperty(value = "批次库存数量")
    private BigDecimal batchInventoryQuantity;

    @NotNull(message = "批次盘点数量不能为空")
    @ApiModelProperty(value = "批次盘点数量")
    private BigDecimal batchInventoryStocktakingQuantity;

    @NotNull(message = "批次盘盈数量不能为空")
    @ApiModelProperty(value = "批次盘盈数量")
    private BigDecimal batchInventorySurplusQuantity;

    @NotNull(message = "批次盘亏数量不能为空")
    @ApiModelProperty(value = "批次盘亏数量")
    private BigDecimal batchInventoryLossQuantity;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "库位guid")
    private String warehouseSpaceGuid;

    @ApiModelProperty(value = "单价")
    private BigDecimal purchasePriceWithoutTax;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

}
