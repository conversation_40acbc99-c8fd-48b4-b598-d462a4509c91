package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xunyue.config.cross.CustomizeNullJsonSerializer;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;
import xy.server.work.entity.ErpProductionMgtWorkorderOrderData;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 生产退料申请VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpProductionReturnApplicationVO对象", description = "生产退料申请")
public class ErpProductionReturnApplicationVO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @ApiModelProperty(value = "父节点_guid")
    private String parentClassificationGuid;

    @ApiModelProperty(value = "打印次数")
    private Integer printTimes;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "工单类型_guid")
    private String workorderTypeGuid;

    @ApiModelProperty(value = "申请类型（字典值）")
    @XyTrans(dictionaryKey = "PRODUCTION_RETURN_APPLICATION_TYPE", dictionaryValue = "workorderTypeGuid")
    private String workorderTypeDictValue;

    @ApiModelProperty(value = "来源guid")
    private String sourceGuid;

    @ApiModelProperty(value = "区分数据的guid")
    private String sourceParentGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "来源")
    @XyTrans(dictionaryKey = "PRODUCTION_RETURN_APPLICATION_SOURCE", dictionaryValue = "sourceValue")
    private String sourceDictValue;

    @ApiModelProperty(value = "工单状态")
    private String workorderState;

    @ApiModelProperty(value = "工单状态(字典值)")
    @XyTrans(dictionaryKey = "INCOMING_STATUS", dictionaryValue = "workorderState")
    private String workorderStateDictValue;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "审核状态字典值")
    @XyTrans(dictionaryKey = "AUDIT_STATUS", dictionaryValue = "auditStatus")
    private String auditStatusDictValue;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "工单属性")
    private Integer workorderProperties;

    @ApiModelProperty(value = "物料分类")
    private String materialGuid;

    @ApiModelProperty(value = "长")
    private BigDecimal specificationsLength;

    @ApiModelProperty(value = "宽")
    private BigDecimal specificationsWidth;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "总数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "单位")
    private String unitGuid;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;
    /**
     * 关联表数据
     */
    @ApiModelProperty(value = "物料Obj")
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(value = "来源数量")
    private String sourceQuantity;

    @ApiModelProperty(value = "生产退料明细数据")
    private List<ErpProductionReturnApplicationVO> productionReturnApplicationDetailList;

    @ApiModelProperty(value = "生产退料明细形态转变数据")
    private List<ErpProductionReturnApplicationVO> morphologicalTransformationList;

    @ApiModelProperty(value = "生产退料明细工单-订单数据")
    private ErpProductionMgtWorkorderOrderData workorderOrderData;

    @ApiModelProperty(value = "退料批次")
    @JsonSerialize(nullsUsing = CustomizeNullJsonSerializer.NullArrayJsonSerializer.class)
    private List<ErpInventoryMaterialMgtMaterialBatchVO> materialBatchList;

    @ApiModelProperty(value = "生产订单号")
    private String productionOrderNumberStr;

}
