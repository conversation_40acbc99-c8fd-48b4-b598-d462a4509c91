package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>
 * 库存结转表写入后台表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryBalanceURO对象", description = "库存结转表写入后台表")
public class ErpInventoryMgtInventoryBalanceRO extends BaseEntity {

    @ApiModelProperty(value = "pk")
    private String inventoryBalanceGuid;

    @ApiModelProperty(value = "结转guid")
    private String balanceGuid;

    @ApiModelProperty(value = "物guid")
    private String materialGuid;

    @NotNull(message = "结转数量不能为空")
    @ApiModelProperty(value = "结转数量")
    private BigDecimal inventoryBalanceQuantity;

    @NotNull(message = "结转金额不能为空")
    @ApiModelProperty(value = "结转金额")
    private BigDecimal inventoryBalanceTotalamount;

    @NotNull(message = "备注或描述不能为空")
    @ApiModelProperty(value = "备注或描述")
    private String description;

}
