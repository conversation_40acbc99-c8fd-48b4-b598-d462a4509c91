package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2023/11/23 16:02
 * @apiNote 包装单-待开列表VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpPackageWorkOrderNotBilledVO", description = "包装单-待开列表VO")
public class ErpPackageWorkOrderNotBilledVO {

    @ApiModelProperty(value = "PK")
    private String workorderGuid;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "父_guid（用于组装树）")
    private String parentGuid;

    @ApiModelProperty(value = "顺序号(每一个层级有对应的顺序号)")
    private Integer serialNumber;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private LocalDateTime receiptDate;

    @ApiModelProperty(value = "数据来源")
    private String sourceValue;

    @ApiModelProperty(value = "数据来源字典值")
    @XyTrans(dictionaryKey = "PACKAGE_DATA_SOURCE", dictionaryValue = "sourceValue")
    private String sourceDictValue;

    @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

    @ApiModelProperty(value = "物料批次_guid")
    private String materialBatchGuid;

    @ApiModelProperty(value = "工单数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "包装数量")
    private BigDecimal packagingQuantity;

    @ApiModelProperty(value = "每包数量")
    private BigDecimal quantityPerPackage;

    @ApiModelProperty(value = "待包装数")
    private BigDecimal unPackageQuantity;

    @ApiModelProperty(value = "物料Obj")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "包装明细列表")
    @XyTransCycle
    private List<ErpMaterialMgtMaterialBatchGoodsVO> detailList;
}
