package xy.server.inventory.entity.model.ro;

import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>
 * 库单明细管理扩展明细表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpStockInventoryMgtInventoryReceiptDetailInoutstockRO对象", description = "库单明细管理扩展明细表")
public class ErpStockInventoryMgtInventoryReceiptDetailInoutstockRO extends BaseEntity {

    @ApiModelProperty(value = "库单明细管理guid")
    private String inventoryReceiptDetailGuid;

    @ApiModelProperty(value = "PK")
    private String inventoryReceiptDetailInstockGuid;

    @NotNull(message = "含税报价单价(吨价，平方价，千平方英寸)不能为空")
    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @NotNull(message = "含税单价(默认隐藏，根据计费方案转换计算得出)不能为空")
    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;

    @NotNull(message = "含税总金额不能为空")
    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @NotNull(message = "不含税报价单价(吨价，平方价，千平方英寸)不能为空")
    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;

    @ApiModelProperty(value = "本币单价(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyUnitPrice;

    @ApiModelProperty(value = "本币金额(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalAmount;

    @ApiModelProperty(value = "结算单价（默认与含税单价相等），写入库存价格字段")
    private BigDecimal settlementUnitPrice;

    @ApiModelProperty(value = "结算金额（默认与含税总金额相等），写入库存价格字段")
    private BigDecimal settlementTotalAmount;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

}
