package xy.server.inventory.entity.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.xunyue.common.util.JsonbTypeHandler;
import com.xunyue.config.mybatisplus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 对账单明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
@Data
public class ErpFinanceMgtStatementBillDetailVO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * PK
     */
    @TableId("statement_bill_detail_guid")
    private String statementBillDetailGuid;
    /**
     * 对账单guid
     */
    @TableField("statement_bill_guid")
    private String statementBillGuid;
    /**
     * 来源guid(工单，订单，工单与订单关联)
     */
    @TableField("source_guid")
    private String sourceGuid;
    /**
     * 来源值
     */
    @TableField("source_value")
    private Integer sourceValue;
    /**
     * 物guid
     */
    @TableField("material_guid")
    private String materialGuid;
    /**
     * 对账数量
     */
    @TableField("statement_bill_quantity")
    private BigDecimal statementBillQuantity;
    /**
     * 对账金额
     */
    @TableField("statement_bill_expense")
    private BigDecimal statementBillExpense;
    /**
     * 对账单价（含税）
     */
    @TableField("statement_bill_price")
    private BigDecimal statementBillPrice;
    /**
     * 原单价
     */
    @TableField("original_unit_price")
    private BigDecimal originalUnitPrice;
    /**
     * 原金额
     */
    @TableField("original_amount")
    private BigDecimal originalAmount;
    /**
     * 状态：0-新建 1-正常处理 2-异常处理 3-正常结束 4-异常结束(终止)
     */
    @TableField("statement_bill_state")
    private Integer statementBillState;
    /**
     * 支付结果状态：0-未结清、1-部分结清、2-已结清
     */
    @TableField("statement_bill_status")
    private Integer statementBillStatus;
    /**
     * 已转应收/应付金额
     */
    @TableField("payment_plan_expense")
    private BigDecimal paymentPlanExpense;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 扩展字段
     */
    @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
    private Object toJson;


    @ApiModelProperty(value = "成品销货明细guid")
    private String finishedProductSalesDetailWorkorderGuid;


}
