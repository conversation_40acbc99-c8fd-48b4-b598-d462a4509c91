package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2024-07-09 10:30
 * <p>
 * 原纸领退料
 * </p>
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpRollPaperOutAndReturnRO", description = "原纸领退料")
public class ErpRollPaperOutAndReturnQO {

    @ApiModelProperty("排度单号")
    private String planNumber;

    @ApiModelProperty("班组")
    private String teamGuid;

    @ApiModelProperty("机台")
    private String equipmentGuid;

    @ApiModelProperty("物料货号码")
    private String materialGoodsCode;

    @ApiModelProperty("开始时间")
    private LocalDate startTime;

    @ApiModelProperty("结束时间")
    private LocalDate endTime;
}
