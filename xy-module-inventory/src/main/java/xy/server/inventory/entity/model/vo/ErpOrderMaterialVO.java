package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Title: ErpOrderMaterialVO
 * <AUTHOR>
 * @Package xy.server.inventory.entity.model.vo
 * @Date 2024/11/19 15:42
 * @description: 物料出库单的数量总和
 */
@Data
public class ErpOrderMaterialVO {
    @ApiModelProperty(value = "物料id")
    private String materialGuid;
    @ApiModelProperty(value = "物料出库单的数量总和")
    private BigDecimal quantity;

}
