package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
                                                                                                                                                /**
* <p>
* 库存盘点表QO
* </p>
*
* <AUTHOR>
* @since 2023-10-19
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryStocktakingQO对象", description = "库存盘点表")
public class ErpInventoryMgtInventoryStocktakingQO{

        @ApiModelProperty(value = "PK")
    private String inventoryStocktakingGuid;

        @ApiModelProperty(value = "库存guid")
    private String materialInventoryGuid;

        @ApiModelProperty(value = "库单guid")
    private String inventoryReceiptGuid;

        @ApiModelProperty(value = "物分类guid")
    private String materialClassificationGuid;

        @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

            @ApiModelProperty(value = "库存数量")
    private BigDecimal inventoryQuantity;

            @ApiModelProperty(value = "盘点数量")
    private BigDecimal stocktakingQuantity;

            @ApiModelProperty(value = "盘盈数量")
    private BigDecimal stocktakingSurplusQuantity;

            @ApiModelProperty(value = "盘亏数量")
    private BigDecimal stocktakingLossQuantity;

        @ApiModelProperty(value = "创建人ID")
    private String creatorGuid;

            @ApiModelProperty(value = "创建人")
    private String creator;

            @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

        @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

            @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

            @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

        @ApiModelProperty(value = "扩展字段")
    private Object toJson;

        @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

}
