package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 库存结转表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpInventoryMgtBalanceVO对象", description = "库存结转表")
public class ErpInventoryMgtBalanceVO {

    @ApiModelProperty(value = "pk")
    private String balanceGuid;

    @ApiModelProperty(value = "结转名称")
    private String balanceName;

    @ApiModelProperty(value = "结转属性(可用字典区分：1、物料，2、成品，3、半成品，4、模具)")
    private Integer balanceProperties;

    @ApiModelProperty(value = "结转类型(字典)(1全月加权，2移动加权)")
    private String balanceType;

    @ApiModelProperty(value = "结转类型字典值")
    @XyTrans(dictionaryKey = "BALANCE_TYPE", dictionaryValue = "balanceType")
    private String balanceTypeDictValue;

    @ApiModelProperty(value = "结转状态")
    private Boolean balanceStatus;

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "备注或描述")
    private String description;

    @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

    @ApiModelProperty(value = "后修改日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

}
