package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.inventory.entity.model.vo.ErpInventoryMgtMaterialBatchInventoryVO;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 物料库存表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtMaterialInventoryQO对象", description = "物料库存表")
public class ErpInventoryMgtMaterialInventoryQO {

    @ApiModelProperty(value = "物分类guid")
    private List<String> materialClassificationGuid;

    @ApiModelProperty(value = "物分类name")
    private String materialClassificationName;

    @ApiModelProperty(value = "物料 _guid")
    private String materialGuid;

    @ApiModelProperty(value = "物料类型")
    private List<String> materialType;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "规则类型")
    private String specificationType;

    @ApiModelProperty(value = "是否忽略为零的，true忽略，false不忽略")
    private Boolean isIgnore;

    @ApiModelProperty(value = "物料guid集合")
    private List<String> materialGuids;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "物料货号码(供应商提供或者自动生成)")
    private String materialGoodsCode;

    @ApiModelProperty(value = "计划开始日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "计划结束日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "是否启用")
    private Boolean state;

    @ApiModelProperty(value = "长")
    private String length;

    @ApiModelProperty(value = "宽")
    private String width;

    @ApiModelProperty(value = "高")
    private String height;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;
    //兼容只查询批次数据的情况
    @ApiModelProperty(value = "已选中批次库存列表")
    private List<ErpInventoryMgtMaterialBatchInventoryVO> selectedBatchInventoryList;

}
