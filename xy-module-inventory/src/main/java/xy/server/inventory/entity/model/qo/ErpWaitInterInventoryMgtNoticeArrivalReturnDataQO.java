package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 发货通知\到货\退货数据表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpWaitInterInventoryMgtNoticeArrivalReturnDataQO对象", description = "")
public class ErpWaitInterInventoryMgtNoticeArrivalReturnDataQO {

    @ApiModelProperty(value = "开单开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "来源单号或者物料名称")
    private String keyName;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "来源单号")
    private String sourceWorkorderNumber;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "工单属性")
    private List<Integer> workorderProperties;

    @ApiModelProperty(value = "是否忽略待入库数为零的")
    private Boolean isIgnore;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "处理方式(字典：物料入库/成品入库/半成品入库等)")
    private String handlingMethod;

    @ApiModelProperty(value = "工单号")
    private String workOrderNumber;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "来源生产单号")
    private String sourceProductOrderNumber;

    @ApiModelProperty(value = "产品（编码/名称）")
    private String productMaterial;

    @ApiModelProperty(value = "业务订单号")
    private String businessOrderNumber;

    @ApiModelProperty(value = "生产工单号")
    private String productWorkorderNumber;

    @ApiModelProperty(value = "")
    private List<String> workorderGuids;

    @ApiModelProperty(value = "是否走iqc流程")
    private Boolean isIqcFlow;

    @ApiModelProperty(value = "标签码")
    private List<String> materialBatchGoods;

}
