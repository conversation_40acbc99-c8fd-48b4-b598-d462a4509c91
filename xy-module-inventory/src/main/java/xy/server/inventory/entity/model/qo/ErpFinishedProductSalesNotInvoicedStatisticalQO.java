package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 成品销货统计QO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-7-29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpFinishedProductSalesStatisticalQO对象", description = "成品销货统计")
public class ErpFinishedProductSalesNotInvoicedStatisticalQO {

    @ApiModelProperty(value = "客户编码/简称/全称")
    private String customer;

    @ApiModelProperty(value = "销货单号")
    private String salesNumber;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料类型名称")
    private String materialClassificationName;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "开始日期（销货日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（销货日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "开始日期（送货日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate deliverStartDate;

    @ApiModelProperty(value = "结束日期（送货日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate deliverEndDate;

    @ApiModelProperty(value = "是否忽略已开票数据")
    private Boolean isIgnoreInvoiced = true;

    @ApiModelProperty(value = "开始日期（审核）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate auditStartDate;

    @ApiModelProperty(value = "结束日期（审核）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate auditEndDate;

    @ApiModelProperty(value = "开始日期（对账）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate efmsbReceiptStartDate;

    @ApiModelProperty(value = "结束日期（对账）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate efmsbReceiptEndDate;

    @ApiModelProperty(value = "审核人")
    private String auditBy;

    @ApiModelProperty(value = "审核状态")
    private List<String> auditStatus;

    @ApiModelProperty(value = "成品销货状态")
    private List<String> workorderStateList;

    @ApiModelProperty(value = "产品类型")
    private List<String> materialType;

    @ApiModelProperty(value = "出库状态")
    private List<String> outboundState;

    @ApiModelProperty(value = "对账情况")
    private List<String> statementState;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

}
