package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 物料库存表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpStocktakingQO对象", description = "物料库存表")
public class ErpStocktakingQO {

    @ApiModelProperty(value = "分类父guid")
    private List<String> parentClassificationGuid;

    @ApiModelProperty(value = "物分类guid")
    private List<String> materialClassificationGuid;

    @ApiModelProperty(value = "物分类name")
    private String materialClassificationName;

    @ApiModelProperty(value = "物料 _guid")
    private String materialGuid;

    @ApiModelProperty(value = "物料类型")
    private List<String> materialType;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "规则类型")
    private String specificationType;

    @ApiModelProperty(value = "客户guid")
    private String customerGuid;

    @ApiModelProperty(value = "查询库存数大于零以及等于零的")
    private Boolean isAll;

}