package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
                                                                                                                                                
/**
* <p>
* 库存盘点表VO
* </p>
*
* <AUTHOR>
* @since 2023-10-19
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryStocktakingVO对象", description = "库存盘点表")
public class ErpInventoryMgtInventoryStocktakingVO {

    @ApiModelProperty(value = "PK")
    private String inventoryStocktakingGuid;

    @ApiModelProperty(value = "库存guid")
    private String materialInventoryGuid;

    @ApiModelProperty(value = "库单guid")
    private String inventoryReceiptGuid;

    @ApiModelProperty(value = "物分类guid")
    private String materialClassificationGuid;

    @ApiModelProperty(value = "物料_guid")
    private String materialGuid;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal inventoryQuantity;

    @ApiModelProperty(value = "盘点数量")
    private BigDecimal stocktakingQuantity;

    @ApiModelProperty(value = "盘盈数量")
    private BigDecimal stocktakingSurplusQuantity;

    @ApiModelProperty(value = "盘亏数量")
    private BigDecimal stocktakingLossQuantity;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "物料Obj")
    private ErpMaterialMgtMaterialVO materialObj;

    @ApiModelProperty(value = "盘点总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "产品编码")
    private String materialCode;

    @ApiModelProperty(value = "产品名称")
    private String materialName;

    @ApiModelProperty(value = "单位")
    private String unitName;

    @ApiModelProperty(value = "规格类型")
    private String specificationTypeStr;

    @ApiModelProperty(value = "客方货号")
    private String customerNo;

    @ApiModelProperty(value = "客户简称")
    private String customerShortName;

    @ApiModelProperty(value = "客户全称")
    private String customerFullName;

}
