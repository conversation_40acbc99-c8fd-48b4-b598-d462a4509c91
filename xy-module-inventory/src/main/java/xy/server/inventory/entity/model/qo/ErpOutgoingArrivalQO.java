package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpProductionMgtWorkorderQO对象", description = "")
public class ErpOutgoingArrivalQO {

    @ApiModelProperty(value = "开单开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "物料到货单状态")
    private List<String> auditStatus;

    @ApiModelProperty(value = "到货单号")
    private String workorderNumber;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "工单类型_guid")
    private List<String> workorderTypeGuid;

    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;

    @ApiModelProperty(value = "到货供应商编码/简称/全称")
    private String arrivalSupplier;

    @ApiModelProperty(value = "结算供应商编码/简称/全称")
    private String settleSupplier;

    @ApiModelProperty(value = "外发单号")
    private String outgoingOrderNumber;

    @ApiModelProperty(value = "生产工单号")
    private String productionWorkNumber;

    @ApiModelProperty(value = "产品名称")
    private String productionName;

    @ApiModelProperty(value = "创建人")
    private String creator;


}
