package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xunyue.config.trans.annotation.XyTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "ErpMaterialReceiptStatisticsVO对象", description = "物料入库明细统计")
public class ErpMaterialReceiptStatisticsVO {

    @ApiModelProperty(value = "入库单号")
    private String inventoryReceiptNumbers;

    @ApiModelProperty(value = "入库日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptDate;

    @ApiModelProperty("制单人")
    public String creatorName;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "入库类型")
    private String operationTypeGuid;

    @XyTrans(dictionaryKey = "INCOMING_TYPE", dictionaryValue = "operationTypeGuid")
    @ApiModelProperty(value = "操作类型名称,(用于入库类型)")
    private String operationTypeName;

    @ApiModelProperty(value = "供应商简称")
    private String supplierShortName;

    @ApiModelProperty(value = "结算供应商简称")
    private String settlementCustomerOrSupplierName;

    @ApiModelProperty(value = "物料Obj")
    private ErpMaterialMgtMaterialVO materialObj;


    @ApiModelProperty(value = "入库数量")
    private BigDecimal stockInQuantity;

    @ApiModelProperty(value = "来源单号")
    private String sourceWorkorderNumber;

    @ApiModelProperty(value = "来源数量")
    private BigDecimal sourceQuantity;

    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;

    @ApiModelProperty(value = "生产工单号")
    public String workorderNumber;

    @ApiModelProperty("采购单号")
    public String orderNumber;

    @ApiModelProperty(value = "入仓数量")
    private Integer inventoryQuantity;

    @ApiModelProperty(value ="入仓状态")
    private String inventoryStatuss;

    @XyTrans(dictionaryKey = "WAREHOUSE_STATUS", dictionaryValue = "inventoryStatuss")
    private String inventoryStatus;

    @ApiModelProperty(value = "异常处理原因")
    private String exceptionHandlingReason;

    @ApiModelProperty(value = "异常处理人")
    private String exceptionHandlerName;

    @ApiModelProperty(value = "异常处理日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exceptionHandlingDate;

    @ApiModelProperty(value = "入库状态(完成、故障、未开始)")
    private String workorderState;

    @XyTrans(dictionaryKey = "DOCUMENT_STATUS", dictionaryValue = "workorderState")
    private String workorderStateValue;

    @ApiModelProperty(value = "申购数量(采购申请数量)")
    private BigDecimal purchaseWorkorderQuantity;

    @ApiModelProperty(value = "采购数量(采购订单数量)")
    private BigDecimal purchasedQuantity;

    @ApiModelProperty(value = "物料入库明细id")
    private String inventoryReceiptDetailGuid;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @JsonIgnore
    private String sourceGuid;
    @JsonIgnore
    private String materialGuid;

}
