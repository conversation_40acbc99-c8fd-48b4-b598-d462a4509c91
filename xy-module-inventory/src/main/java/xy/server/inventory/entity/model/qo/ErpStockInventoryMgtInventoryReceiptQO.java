package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 库单管理QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpStockInventoryMgtInventoryReceiptQO对象", description = "库单管理")
public class ErpStockInventoryMgtInventoryReceiptQO {

    @ApiModelProperty(value = "开单开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "物料入库单状态")
    private List<String> auditStatus;

    @ApiModelProperty(value = "入库单号")
    private String inventoryReceiptNumbers;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "库单类型")
    private Integer inventoryReceiptProperties;

    @ApiModelProperty(value = "操作类型_guid,(用于入库类型)")
    private List<Integer> operationTypeGuid;

    @ApiModelProperty(value = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "工单号")
    private String workOrderNumber;

    @ApiModelProperty(value = "产品编号/名称")
    private String production;

    @ApiModelProperty(value = "客户Po(客户订单号)")
    private String customerPo;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "到货供应商编码/简称/全称")
    private String arrivalSupplier;

    @ApiModelProperty(value = "结算供应商编码/简称/全称")
    private String settleSupplier;


    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;

    @ApiModelProperty(value = "动态路由菜单")
    private String dynamicComponent;

    @ApiModelProperty(value = "来源生产单号")
    private String sourceProductOrderNumber;

    @ApiModelProperty(value = "库单id")
    private List<String> inventoryReceiptIds;

}
