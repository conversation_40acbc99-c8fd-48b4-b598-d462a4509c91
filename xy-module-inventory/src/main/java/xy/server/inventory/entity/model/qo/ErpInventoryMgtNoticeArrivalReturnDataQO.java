package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 发货通知\到货\退货数据表QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtNoticeArrivalReturnDataQO对象", description = "物料到货")
public class ErpInventoryMgtNoticeArrivalReturnDataQO {

    @ApiModelProperty(value = "开单开始日期")
    private LocalDate startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDate endTime;

    @ApiModelProperty(value = "物料到货单状态")
    private List<String> auditStatus;

    @ApiModelProperty(value = "到货单号")
    private String workorderNumber;

    @ApiModelProperty(value = "到货guid")
    private String workorderGuid;

    @ApiModelProperty(value = "到货guid集合")
    private List<String> workorderGuidList;

    @ApiModelProperty(value = "到货pk")
    private String noticeArrivalReturnDataGuid;

    @ApiModelProperty(value = "生产工单号")
    private String giveWorkorderNumber;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "工单类型_guid")
    private List<String> workorderTypeGuid;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "供应商简称/全称/编码")
    private String supplierName;

    @ApiModelProperty(value = "物料名称/编码")
    public String materialName;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(value = "制单人/工号")
    private String creator;

    @ApiModelProperty(value = "客户PO")
    private String customerPo;

    @ApiModelProperty(value = "入库状态")
    private List<String> inventoryStatuses;

    @ApiModelProperty(value = "结算状态")
    private List<String> statementStatuses;


    @ApiModelProperty(value = "来源生产单号")
    private String sourceProductOrderNumber;

}
