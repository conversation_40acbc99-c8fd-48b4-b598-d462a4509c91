package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 成品出库通知单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpFinishedProductOutboundNotificationQO对象", description = "成品出库通知单")
public class ErpFinishedProductOutboundNotificationQO {

    @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

    @ApiModelProperty(value = "工单guid集合")
    private List<String> workorderGuids;

    @ApiModelProperty(value = "工单号")
    private String workorderNumber;

    @ApiModelProperty(value = "工单号")
    private String sourceTwoLevelNumber;

    @ApiModelProperty(value = "申请类型")
    private List<String> workorderTypeGuids;

    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    @ApiModelProperty(value = "来源值")
    private Integer sourceValue;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "外部单号(例客户PO，采购送货单号)")
    private String externalTrackingNumber;

    @ApiModelProperty(value = "开始日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（开单日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "开始日期（到货日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate deliveryStartDate;

    @ApiModelProperty(value = "结束日期（到货日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate deliveryEndDate;

    @ApiModelProperty(value = "流程实例IDs")
    private Set<String> processInstanceIds;

    @ApiModelProperty(value = "审核状态")
    private List<String> auditStatus;

    @ApiModelProperty(value = "扩展字段")
    private Object toJson;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "客户编码/简称/全称")
    private String customer;

    @ApiModelProperty(value = "供应商编码/简称/全称")
    private String supplier;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;


    @ApiModelProperty(value = "工单属性")
    private Integer workorderProperties;

    @ApiModelProperty(value = "查询属性(成品/半成品)")
    private Integer materialType;

    @ApiModelProperty("业务单据ids")
    private Set<String> businessKeys;

    @ApiModelProperty("下单客户编码/名称")
    private String placeOrderCustomer;

    @ApiModelProperty("结算客户编码/名称")
    private String settlementCustomer;

    @ApiModelProperty("送货客户编码/名称")
    private String deliveryCustomer;

    @ApiModelProperty("来源集合")
    private List<Integer> sourceValues;

    @ApiModelProperty("业务订单明细guids")
    private List<String> orderDataGuids;

    @ApiModelProperty("产品（编码、名称）")
    private String productMaterial;

    @ApiModelProperty(value = "对账情况")
    private String statementState;

    @ApiModelProperty(value = "出库情况")
    private String outboundState;

    @ApiModelProperty(value = "业务订单号")
    private String businessOrderNumber;

    @ApiModelProperty(value = "是否忽略待开数量")
    private Boolean isIgnore;

    @ApiModelProperty(value = "工序")
    private String productionProcessesTypeName;

    @ApiModelProperty(value = "生产工单号")
    private String productionOrderNumberStr;

    @ApiModelProperty(value = "结算供应商编码/简称/全称")
    private String settlementSupplier;

    @ApiModelProperty(value = "客方货号")
    private String externalMaterialCode;

    @ApiModelProperty(value = "回签状态")
    private List<String> backStateS;

    @ApiModelProperty(value = "是否查询回签状态大于三天的")
    private Boolean isBackTimeOver;

    @ApiModelProperty(value = "工单号")
    private List<String>  workorderNumberList;

    @ApiModelProperty(value = "sonWorkorderNumber")
    private String sonWorkorderNumber;

    @ApiModelProperty(value = "上流程是否有iqc质检")
    private Boolean isGoodsReturn = false;

    @ApiModelProperty(value = "是否补货")
    private String isReplenishment;

    @ApiModelProperty(value = "查询物料属性")
    private List<String> materialTypeList;

}
