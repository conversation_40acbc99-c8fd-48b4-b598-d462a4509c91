package xy.server.inventory.entity.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xunyue.config.trans.annotation.XyTrans;
import com.xunyue.config.trans.annotation.XyTransCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import xy.server.material.entity.model.vo.ErpMaterialMgtMaterialVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
* <p>
* VO
* </p>
*
* <AUTHOR>
* @since 2023-12-01
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpOutgoingArrivalSatisVO对象")
public class ErpOutgoingArrivalSatisVO {
    //订单
    @ApiModelProperty(value = "客户简称")
    private String customerShotName;
    @ApiModelProperty(value = "客户编码")
    private String customerCode;
    @ApiModelProperty(value = "客户全称")
    private String customerFullName;
    @ApiModelProperty(value = "默认业务员名称")
    private String salesmanName;
    @ApiModelProperty(value = "默认跟单员名称")
    private String merchandiserName;
    @ApiModelProperty(value = "订单号")
    private String orderNumber;
    @ApiModelProperty(value = "客户PO")
    private String externalTrackingNumber;
    @ApiModelProperty(value = "客户产品编码")
    private String externalMaterialCode;
    @ApiModelProperty(value = "客户产品名称")
    private String externalMaterialName;
    @ApiModelProperty(value = "产品guid")
    private String materialGuid;
    @ApiModelProperty(value = "产品规格")
    private ErpMaterialMgtMaterialVO materialObj;
    @ApiModelProperty(value = "交货日期")
    private LocalDateTime deliveryDate;
    //生成工单
    @ApiModelProperty(value = "生产工单号")
    private String workorderNumber;
    @ApiModelProperty(value = "工单制单日期")
    private LocalDateTime workReceiptDate;
    @ApiModelProperty(value = "工单审核日期")
    private LocalDateTime workAuditDate;
    @ApiModelProperty(value = "工艺参数")
    private String processParameters;
    @ApiModelProperty(value = "工艺描述")
    private String productionProcessesDescription;
    @ApiModelProperty(value = "工序备注")
    private String processesDescription;
    @ApiModelProperty(value = "部件名称")
    private String partName;
    @ApiModelProperty(value = "工序名称")
    private String processName;
    @ApiModelProperty(value = "部件guid")
    private String materialProcessGuid;
    @ApiModelProperty(value = "加工规格")
    @XyTransCycle
    private ErpMaterialMgtMaterialVO materialProcessObj;
    @ApiModelProperty(value = "工单加工数量")
    private BigDecimal processQuantity;
    @ApiModelProperty(value = "工单损耗数量")
    private BigDecimal lossQuantity;
    @ApiModelProperty(value = "工单加工总数")
    private BigDecimal totalQuantity;
    @ApiModelProperty(value = "产出比")
    private BigDecimal proportion;
    //外发申请单
    @ApiModelProperty(value = "外发申请单号")
    private String applicationOutNumber;
    @ApiModelProperty(value = "外发申请审核日期")
    private LocalDateTime applicationOutAuditDate;
    @ApiModelProperty(value = "外发申请制单日期")
    private LocalDateTime applicationOutDate;
    @ApiModelProperty(value = "外发申请制单人")
    private String applicationOutCreator;
    @ApiModelProperty(value = "外发申请审核人")
    private String applicationOutAuditCreator;
    //外发订单
    @ApiModelProperty(value = "外发订单单号")
    private String orderOutNumber;
    @ApiModelProperty(value = "外发订单审核日期")
    private LocalDateTime orderOutAuditDate;
    @ApiModelProperty(value = "外发订单审核制单日期")
    private LocalDateTime orderOutDate;
    @ApiModelProperty(value = "外发订单制单人")
    private String orderOutCreator;
    @ApiModelProperty(value = "外发订单审核人")
    private String orderOutAuditCreator;
    //外发到货
    @ApiModelProperty(value = "外发到货id")
    private String arrivalOutGuid;
    @ApiModelProperty(value = "外发到货单号")
    private String arrivalOutNumber;
    @ApiModelProperty(value = "加工商编码")
    private String processorCode;
    @ApiModelProperty(value = "加工商简称")
    private String processorShortName;
    @ApiModelProperty(value = "加工商全称")
    private String processorFullName;
    @ApiModelProperty(value = "结算供应商编码")
    private String supplierCode;
    @ApiModelProperty(value = "结算供应商简称")
    private String supplierShortName;
    @ApiModelProperty(value = "结算供应商全称")
    private String supplierFullName;
    @ApiModelProperty(value = "税率名称")
    private BigDecimal taxRate;
    @ApiModelProperty(value = "汇率名称")
    private String exchangeRate;
    @ApiModelProperty(value = "币种名称")
    private String currencyName;
    @ApiModelProperty(value = "含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceIncludingTax;
    @ApiModelProperty(value = "含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceIncludingTax;
    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalAmountIncludingTax;
    @ApiModelProperty(value = "不含税报价单价(吨价，平方价，千平方英寸)")
    private BigDecimal quotationUnitPriceWithoutTax;
    @ApiModelProperty(value = "不含税单价(默认隐藏，根据计费方案转换计算得出)")
    private BigDecimal unitPriceWithoutTax;
    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalAmountWithoutTax;
    @ApiModelProperty(value = "本币单价(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyUnitPrice;
    @ApiModelProperty(value = "本币金额(根据币种，外币才显示)，外币不含税")
    private BigDecimal localCurrencyTotalAmount;
    @ApiModelProperty(value = "结算单价（默认与含税单价相等），写入库存价格字段")
    private BigDecimal settlementUnitPrice;
    @ApiModelProperty(value = "结算金额（默认与含税总金额相等），写入库存价格字段")
    private BigDecimal settlementTotalAmount;
    @ApiModelProperty(value = "到货数量")
    private BigDecimal arrivalOutQuantity;
    @ApiModelProperty(value = "到货备注")
    private String arrivalDescription;
    @ApiModelProperty(value = "外发到货审核日期")
    private LocalDateTime arrivalOutAuditDate;
    @ApiModelProperty(value = "外发到货审核制单日期")
    private LocalDateTime arrivalOutDate;
    @ApiModelProperty(value = "外发到货制单人")
    private String arrivalOutCreator;
    @ApiModelProperty(value = "外发到货审核人")
    private String arrivalOutAuditCreator;
    @ApiModelProperty(value = "外发到货审核状态")
    private String auditStatus;
    //状态
    @ApiModelProperty(value = "对账情况")
    private String statementStatus;
    @XyTrans(dictionaryKey = "RECONCILIATION",dictionaryValue = "statementStatus")
    @ApiModelProperty(value = "对账情况")
    private String statementStatusName;
    @ApiModelProperty(value = "外发到货状态")
    private String arrivalOutStatus;
    @XyTrans(dictionaryKey = "DOCUMENT_STATUS",dictionaryValue = "arrivalOutStatus")
    @ApiModelProperty(value = "外发到货状态")
    private String arrivalOutStatusName;
    @ApiModelProperty(value = "异常处理人")
    private String errorHandlePeople;
    @ApiModelProperty(value = "异常处理日期")
    private LocalDateTime exceptionHandlingDate;
    @ApiModelProperty(value = "异常原因")
    private String errorHandleReason;
    @ApiModelProperty(value = "是否生成凭证")
    private Boolean isProof;
    @ApiModelProperty(value = "入库情况")
    private String inventoryStatus;
    @ApiModelProperty(value = "入库总数")
    private BigDecimal inventoryTotalQuantity;
    @XyTrans(dictionaryKey = "INCOMING_STATUS",dictionaryValue = "inventoryStatus")
    @ApiModelProperty(value = "入库情况")
    private String inventoryStatusName;
    @ApiModelProperty(value = "产出工单数据")
    @XyTransCycle
    private List<ErpOutgoingApplicationInventoryVO> sonList;

    @JsonIgnore
    private String epmw9WorkorderGuid;
    @JsonIgnore
    private String epmw9ProcessInstanceId;
    @JsonIgnore
    private String epmw8WorkorderGuid;
    @JsonIgnore
    private String epmw8ProcessInstanceId;
    @JsonIgnore
    private String ebmoOrderGuid;
    @JsonIgnore
    private String ebmoProcessInstanceId;
    @JsonIgnore
    private String ebmo1OrderGuid;


}
