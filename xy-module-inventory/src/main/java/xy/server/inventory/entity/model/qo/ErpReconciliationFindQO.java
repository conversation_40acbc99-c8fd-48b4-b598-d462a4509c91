package xy.server.inventory.entity.model.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
* <p>
* 订单数据异常处理表VO
* </p>
*
* <AUTHOR>
* @since 2024-06-14
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpBusinessMgtPurchaseOrderDataExtendVO对象", description = "订单数据异常处理表")
public class ErpReconciliationFindQO {

    @ApiModelProperty(value = "供应商信息")
    private String supplierCode;
    @ApiModelProperty(value = "对账情况")
    private String statementSituation;
    @ApiModelProperty(value = "到货状态")
    private String completionStatus;
    @ApiModelProperty(value = "单据状态")
    private String currentStatus;
    @ApiModelProperty(value = "外发单号")
    private String orderNumber;
    @ApiModelProperty(value = "订单|工单")
    private String orderDataNumber;
    @ApiModelProperty(value = "开单日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;
    @ApiModelProperty(value = "产品信息")
    private String production;

    @ApiModelProperty(value = "到货单号")
    private String workorderNumber;

    @ApiModelProperty(value = "订单创建人")
    private String orderCreatorName;


}