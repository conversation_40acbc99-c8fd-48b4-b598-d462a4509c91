package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2024-07-24 9:29
 * @apiNote 期初库存导入QO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInitialInventoryImportQO", description = "期初库存导入QO")
public class ErpInventoryMgtInitialInventoryImportQO {

    @ApiModelProperty(value = "开单开始日期")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "开单结束日期")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "物料入库单状态")
    private List<String> auditStatus;

    @ApiModelProperty(value = "入库单号")
    private String inventoryReceiptNumbers;

    @ApiModelProperty(value = "库单类型")
    private Integer inventoryReceiptProperties;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "关键字")
    private List<String> keywords;

    @ApiModelProperty(value = "动态路由菜单")
    private String dynamicComponent;

}
