package xy.server.inventory.entity.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RawPaperMaterialAccountingStatisticsVO对象")
public class RawPaperMaterialAccountingStatisticsVO {

        @ApiModelProperty(value = "排度单号")
        private String planNumber;

        @ApiModelProperty(value = "原纸条码")
        private String materialGoodsCode;

        @ApiModelProperty(value = "领料数量")
        private BigDecimal productCount;

        @ApiModelProperty(value = "退料数量")
        private BigDecimal returnCount;

        @ApiModelProperty(value = "克重")
        private BigDecimal basisWeight;

        @ApiModelProperty(value = "实克")
        private BigDecimal actualWeight;

    }


