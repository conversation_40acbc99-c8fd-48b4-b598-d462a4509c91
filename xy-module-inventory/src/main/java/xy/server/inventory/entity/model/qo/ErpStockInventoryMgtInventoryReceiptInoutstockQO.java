package xy.server.inventory.entity.model.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
* <p>
* 库单管理扩展表QO
* </p>
*
* <AUTHOR>
* @since 2023-10-09
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ErpInventoryMgtInventoryReceiptInoutstockQO对象", description = "库单管理扩展表")
public class ErpStockInventoryMgtInventoryReceiptInoutstockQO {

        @ApiModelProperty(value = "库单guid")
    private String inventoryReceiptGuid;

        @ApiModelProperty(value = "PK")
    private String inventoryReceiptInoutstockGuid;

        @ApiModelProperty(value = "租户_guid")
    private String tenantGuid;

        @ApiModelProperty(value = "创建人_guid(取用户表User_guid)")
    private String creatorGuid;

            @ApiModelProperty(value = "创建人")
    private String creator;

            @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

        @ApiModelProperty(value = "最后修改人_guid(取用户表User_guid)")
    private String lastUpdaterGuid;

            @ApiModelProperty(value = "最后修改人")
    private String lastUpdater;

            @ApiModelProperty(value = "最后修改日期")
    private LocalDateTime lastUpdateDate;

        @ApiModelProperty(value = "扩展字段")
    private Object toJson;

            @ApiModelProperty(value = "保留小数位方法(1四舍五入，2有小数进位，默认四舍五入)")
    private Integer decimalMethod;

            @ApiModelProperty(value = "结算单价保留小数位(默认9位，可手动变更)")
    private Integer settlementUnitPriceKeepDecimalPlace;

            @ApiModelProperty(value = "结算金额保留小数位(默认2位，可手动变更)")
    private Integer settlementTotalAmountKeepDecimalPlace;

}
