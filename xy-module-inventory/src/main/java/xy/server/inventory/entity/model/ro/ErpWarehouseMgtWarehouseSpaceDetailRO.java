package xy.server.inventory.entity.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>
 * 仓储空间明细表RO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "ErpWarehouseMgtWarehouseSpaceDetailURO对象", description = "仓储空间明细表")
public class ErpWarehouseMgtWarehouseSpaceDetailRO {

    @NotBlank
    @ApiModelProperty(value = "仓储空间guid")
    private String warehouseSpaceGuid;

    @ApiModelProperty(value = "物料批次货码guid")
    private String materialBatchGoodsGuid;

    @NotBlank
    @ApiModelProperty(value = "物料批次guid")
    private String materialBatchGuid;

    @NotNull(message = "数量不能为空")
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @NotBlank
    @ApiModelProperty(value = "物料 _guid")
    private String materialGuid;

}