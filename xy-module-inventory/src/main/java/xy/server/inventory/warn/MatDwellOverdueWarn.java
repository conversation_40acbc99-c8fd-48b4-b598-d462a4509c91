package xy.server.inventory.warn;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.xunyue.common.enums.MaterialTypeEnum;
import com.xunyue.tenant.sign.UserApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import xy.server.dto.XyMemberDto;
import xy.server.material.entity.model.vo.MaterialBatchInOutVO;
import xy.server.material.mapper.ErpMaterialMgtMaterialBatchMapper;
import xy.server.policy.common.constant.EarlyWarningConstant;
import xy.server.policy.common.enums.EarlyWarningEnum;
import xy.server.policy.entity.ErpPolicyEngineNode;
import xy.server.policy.entity.model.vo.ErpPolicyEngineNodeMetaBroadcastVO;
import xy.server.policy.entity.model.vo.NodeMetaDelayRedisDataDTO;
import xy.server.policy.event.PolicyEngineBroadcastEventService;
import xy.server.policy.service.IErpPolicyEngineNodeService;
import xy.server.policy.service.IErpPolicyEngineService;
import xy.server.policy.service.impl.BaseEarlyWarningBroadcastReceive;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/12/8
 * @description 物料呆滞天数逾期 事件处理
 **/
@Component("MAT_DWELL_OVERDUE")
public class MatDwellOverdueWarn extends BaseEarlyWarningBroadcastReceive implements PolicyEngineBroadcastEventService {

    @Resource
    private ErpMaterialMgtMaterialBatchMapper erpMaterialMgtMaterialBatchMapper;

    @Lazy
    @Resource
    private IErpPolicyEngineService policyEngineService;

    @Lazy
    @Resource
    private IErpPolicyEngineNodeService policyEngineNodeService;

    @Resource
    private UserApi userApi;

    @Value("${tenant.id}")
    private String tenantId;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 呆滞天数减留存天数
     */
    private final String INACTIVE_DAYS_SUB_RETENTION = "INACTIVE_DAYS_SUB_RETENTION";


    @Override
    public void onReceive(ErpPolicyEngineNodeMetaBroadcastVO broadcast) {

        LocalDateTime now = LocalDateTimeUtil.now();

//        Set<String> keys = redisTemplate.keys(XY_EARLY_WARNING_REDIS_KEY + EarlyWarningEnum.MATERIAL_DWELLING_OVERDUE.getCode() + "*");
//        if (ObjUtil.isNotNull(keys)) {
//            keys.forEach(e -> {
//                redisTemplate.delete(e);
//            });
//        }

        ErpPolicyEngineNode node = policyEngineNodeService.getById(broadcast.getPolicyEngineNodeGuid());

        XyMemberDto xyMember = new XyMemberDto();
        xyMember.setTenantGUID(tenantId);
        userApi.setThreadLocal(xyMember);
        // 分批处理
        int current = 1;
        long size = 1000L;

        while (true) {
            // 查询出入库情况
            IPage<MaterialBatchInOutVO> materialBatchInOutVOIPage = erpMaterialMgtMaterialBatchMapper.pageMaterialBatchInOut
                    (new Page<>(current, size), Lists.newArrayList(MaterialTypeEnum.MAIN_INGREDIENT.getCode(), MaterialTypeEnum.SEASONING.getCode()));
            List<MaterialBatchInOutVO> records = materialBatchInOutVOIPage.getRecords();
            if (ObjUtil.isEmpty(records)) {
                break;
            }

            records.forEach(item -> {
                // 如果入库时间为空则计算下一个
                if (ObjUtil.isEmpty(item.getInDateTime())) {
                    return;
                }

                if (ObjUtil.isEmpty(item.getInactiveDays()) || item.getInactiveDays().compareTo(BigDecimal.ZERO) == 0) {
                    return;
                }

                if (ObjUtil.isEmpty(item.getInQuantity())) {
                    return;
                }

                if (ObjUtil.isEmpty(item.getBatchInventoryQuantity()) || item.getBatchInventoryQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    return;
                }

                long between = LocalDateTimeUtil.between(item.getCreateDate(), now, ChronoUnit.DAYS);
                BigDecimal subtractResult = item.getInactiveDays().subtract(BigDecimal.valueOf(between));

                // 设置变量给前端判断
                broadcast.getVariables().put(INACTIVE_DAYS_SUB_RETENTION, JSON.toJSONString(subtractResult.longValue()));
                broadcast.getVariables().put(EarlyWarningConstant.EARLY_WARNING_CATEGORY_KEY, EarlyWarningEnum.MATERIAL_DWELLING_OVERDUE.getCode());

                // 设置当前对象给下一个节点处理
                broadcast.getVariables().put(EarlyWarningConstant.EARLY_WARNING_BUSINESS_KEY, item.getMaterialBatchGuid());

                NodeMetaDelayRedisDataDTO nodeMetaDelayRedisDataDTO = new NodeMetaDelayRedisDataDTO();
                nodeMetaDelayRedisDataDTO.setCurrentNodeGuid(broadcast.getPolicyEngineNodeGuid());
                nodeMetaDelayRedisDataDTO.setVariables(broadcast.getVariables());
                nodeMetaDelayRedisDataDTO.setPolicyEngineGuid(node.getPolicyEngineGuid());
                nodeMetaDelayRedisDataDTO.setTaskId(broadcast.getTaskId());
                // 开始执行下一个节点
                nodeMetaDelayRedisDataDTO.setTenantGuid(tenantId);

                // 出库数不为0 且 入库数量小于等于出库数量 移除消息
                if (ObjUtil.isNotEmpty(item.getOutQuantity()) && item.getInQuantity().compareTo(item.getOutQuantity()) <= 0) {
                    removeByBusinessKey(EarlyWarningEnum.MATERIAL_DWELLING_OVERDUE.getCode() , item.getMaterialBatchGuid());
                }

                policyEngineService.specifyNodeExecute(nodeMetaDelayRedisDataDTO);

            });

            current++;
        }

    }

}
