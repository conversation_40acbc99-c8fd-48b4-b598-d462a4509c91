package xy.server.inventory.i18n;

import com.xunyue.common.i18n.BaseResultErrorCode;

/**
 * <AUTHOR>
 * @Date 2023-05-01
 * <p>
 * 错误代码枚举
 * </p>
 **/
public enum ResultErrorCode implements BaseResultErrorCode {
    STATE_NOT_UN_OUTBOUND("已经出库（出库情况），不能编辑、删除、撤审", 1070000001),
    BATCH_INVENTORY_LIST_IS_EMPTY("库单批次明细不能为空", 1070000002),
    BATCH_QUANTITY_SUM_NEQ_QUANTITY("操作失败！批次库存变动数量之和不等于库存变动数量", 1070000003),
    INVENTORY_NOT_ENOUGH("物料：${0}，库存不足，请检查", 1070000004),
    BATCH_INVENTORY_NOT_ENOUGH("物料：${0}，批次库存不足，请检查", 1070000005),
    STATE_NOT_UN_OUT_WAREHOUSE("已经出仓（出仓情况），不能编辑、删除、撤审", 1070000006),
    INVENTORY_RECEIPT_PROPERTIES_IS_ERROR("库单属性错误，请联系管理员！", 1070000007),
    INVENTORY_RECEIPT_ALREADY_AUDIT("盘点单已经审核不可以删除或编辑", 1070000008),
    WAREHOUSE_RECEIPT_ALREADY_AUDIT("仓单已经审核不可以删除或编辑", 1070000009),
    OUT_WAREHOUSE_QUANTITY_IS_EXCESS("出仓数量已超出，不能保存", 1070000010),
    NOT_OVERSTOCK_DONT_AUDIT("还未审核，不可进行盘盈或盘亏操作",1070000011),
    WAREHOUSE_RECEIPT_PROPERTIES_IS_ERROR("仓单属性错误，请联系管理员！", 1070000012),
    TRANSFER_WAREHOUSE_OLD_EQUALS_NEW("移仓原仓位与目标仓位相同，保存失败！", 1070000013),
    BALANCE_STATUS_IS_TRUE("已【结转】，不允许编辑、删除", 1070000014),
    BALANCE_NAME_IS_REPEAT("结转名称不能重复！", 1070000015),
    BALANCE_DATE_IS_BEFORE_MIN_DATE("结转日期不能在最小结转日期之前", 1070000016),
    BALANCE_DATE_IS_DISCONTINUITY_OR_OVERLAP("结转日期区间不能跨区或重叠", 1070000017),
    HAVE_SUBSEQUENT_BALANCE_DATE("存在后续结转数据，不允许删除、反结转", 1070000018),
    BEFORE_BALANCE_STATUS_IS_FALSE("上一期未结转，请先结转上一期", 1070000019),
    INVENTORY_RECEIPT_IS_BALANCE("库单已经结转，不允许操作！", 1070000020),
    DONT_STATUS_INVENTORY("盘盈入库或者出库单生成失败", 1070000021),
    INCOMING_STATUS_NOT_UN_FINISH("已经入库（入库情况），不能编辑、删除、撤审", 1070000022),
    WORKORDER_PROPERTIES_IS_ERROR("工单属性错误，请联系管理员！", 1070000023),
    MATERIAL_INVENTORY_RECEIPT_IS_NOT_AUDIT("以下库单未审核，不允许结转：<br/>物料期初库存导入单号列表：${0}<br/>物料入库单号列表：${1}<br/>物料出库单号列表：${2}", 1070000024),
    FINISHED_PRODUCT_INVENTORY_RECEIPT_IS_NOT_AUDIT("以下库单未审核，不允许结转：<br/>成品期初库存导入单号列表：${0}<br/>成品入库单号列表：${1}<br/>成品出库单号列表：${2}", 1070000025),
    SEMI_FINISHED_PRODUCT_INVENTORY_RECEIPT_IS_NOT_AUDIT("以下库单未审核，不允许结转：<br/>半成品入库单号列表：${0}<br/>半成品出库单号列表：${1}", 1070000026),
    MOULD_INVENTORY_RECEIPT_IS_NOT_AUDIT("以下库单未审核，不允许结转：<br/>模具入库单号列表：${0}<br/>模具出库单号列表：${1}", 1070000027),
    MATERIAL_BATCH_NUMBER_REPEAT("物料批次号存在重复的", 1070000028),
    RECEIPT_DATE_IS_BALANCE_AND_CANNOT_BE_SAVED("该日期已经结转，不能保存", 1070000029),
    ASSOCIATED_DAILY_REPORT_IS_NOT_CARRIED_FORWARD("关联的日报表未结转，不允许结转", 1070000030),
    STOCKTAKING_RECEIPT_IS_NOT_AUDIT("以下盘点单未审核，不允许出入库：<br/> ${0}", 1070000031),
    IN_WAREHOUSE_QUANTITY_IS_EXCESS_INVENTORY("入仓数量超出入库数量，不能保存", 1070000032),
    OUT_QUANTITY_IS_EXCESS_INVENTORY("出库批次总数大于库存批次总数，不能保存", 1070000033),
    APO_RETURN_OUT_STOCK("物料退货单已经开了物料出库单，无法重启流程", 1070000041),
    CANNOT_RESTART_THE_PROCESS_HAS_INBOUND_RECEIPT("已开入库单，不能重启流程！单号：${0}", 1070000034),
    CANNOT_RESTART_THE_PROCESS_HAS_SUPPLIER_STATEMENT_BILL("已开物料对账单，不能重启流程！单号：${0}", 1070000035),
    CANNOT_RESTART_THE_PROCESS_HAS_OUTBOUND_RECEIPT("已开出库单，不能重启流程！单号：${0}", 1070000036),
    CANNOT_RESTART_THE_PROCESS_HAS_CUSTOMER_STATEMENT_BILL("已开客户对账单，不能重启流程！单号：${0}", 1070000037),
    CANNOT_RESTART_THE_PROCESS_HAS_FINISHED_PRODUCT("已开成品销货，不能重启流程！单号：${0}", 1070000038),
    CANNOT_RESTART_THE_PROCESS_HAS_PROCESSOR_STATEMENT_BILL("已开加工商对账单，不能重启流程！单号：${0}", 1070000039),
    AMONG_DRIVEN_REFRESH("其中已有开过成品销货单请刷新代开！！", 1070000040),

    INVENTORY_QUANTITY_IS_NULL("库存数为空", 1070000041),

    TARGET_PACKAGE_NO_STATUS("目标物料标签不存在", 1070000042),
    STATEMENT_IS_STATUS("对账单已经存在，不可修改单价金额", 1070000043),
    ;
    private String msg;
    private int code;

    ResultErrorCode(String msg, int code) {
        this.msg = msg;
        this.code = code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public int getCode() {
        return code;
    }
}
